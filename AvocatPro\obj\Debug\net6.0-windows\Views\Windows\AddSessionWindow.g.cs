﻿#pragma checksum "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "BD965391CFF0C6C442A9A5DC9D603E66DF97154C"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Windows {
    
    
    /// <summary>
    /// AddSessionWindow
    /// </summary>
    public partial class AddSessionWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 125 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CaseComboBox;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SessionTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ProcedureTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker SessionDatePicker;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox HourComboBox;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox MinuteComboBox;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox JudgeTextBox;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CourtRoomTextBox;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LawyerComboBox;
        
        #line default
        #line hidden
        
        
        #line 231 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DecisionTextBox;
        
        #line default
        #line hidden
        
        
        #line 236 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AttendanceTextBox;
        
        #line default
        #line hidden
        
        
        #line 241 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DurationTextBox;
        
        #line default
        #line hidden
        
        
        #line 247 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ArgumentsTextBox;
        
        #line default
        #line hidden
        
        
        #line 252 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox EvidenceTextBox;
        
        #line default
        #line hidden
        
        
        #line 257 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ExpensesTextBox;
        
        #line default
        #line hidden
        
        
        #line 274 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker NextSessionDatePicker;
        
        #line default
        #line hidden
        
        
        #line 285 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox NextHourComboBox;
        
        #line default
        #line hidden
        
        
        #line 288 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox NextMinuteComboBox;
        
        #line default
        #line hidden
        
        
        #line 297 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 306 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 313 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveAndNewButton;
        
        #line default
        #line hidden
        
        
        #line 321 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/windows/addsessionwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CaseComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 2:
            this.SessionTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 3:
            this.ProcedureTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.SessionDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 5:
            this.HourComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            this.MinuteComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.StatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.JudgeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.CourtRoomTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.LawyerComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.DecisionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.AttendanceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.DurationTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.ArgumentsTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.EvidenceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.ExpensesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.NextSessionDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 18:
            this.NextHourComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 19:
            this.NextMinuteComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 20:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 306 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.SaveAndNewButton = ((System.Windows.Controls.Button)(target));
            
            #line 314 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
            this.SaveAndNewButton.Click += new System.Windows.RoutedEventHandler(this.SaveAndNewButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 321 "..\..\..\..\..\Views\Windows\AddSessionWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

