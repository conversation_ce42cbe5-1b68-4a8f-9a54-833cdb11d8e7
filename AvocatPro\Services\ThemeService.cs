using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Windows;
using System.Windows.Media;
using AvocatPro.Models;

namespace AvocatPro.Services
{
    public class ThemeService
    {
        private readonly string _settingsPath;
        private ThemeSettings _currentTheme;
        private readonly Dictionary<string, ThemeSettings> _predefinedThemes;

        public event EventHandler<ThemeChangedEventArgs>? ThemeChanged;

        public ThemeService()
        {
            _settingsPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                                        "AvocatPro", "theme-settings.json");
            _predefinedThemes = InitializePredefinedThemes();
            _currentTheme = LoadThemeSettings();
            
            Directory.CreateDirectory(Path.GetDirectoryName(_settingsPath)!);
        }

        // 🌓 إدارة الوضع الليلي/النهاري
        public void SetThemeMode(ThemeMode mode)
        {
            _currentTheme.Mode = mode;
            
            if (mode == ThemeMode.Auto)
            {
                ApplyAutoTheme();
            }
            else
            {
                ApplyTheme(mode == ThemeMode.Dark ? GetDarkTheme() : GetLightTheme());
            }
            
            SaveThemeSettings();
            OnThemeChanged();
        }

        public void ApplyAutoTheme()
        {
            var currentHour = DateTime.Now.Hour;
            var isDarkTime = currentHour < 6 || currentHour >= 18; // 6 PM to 6 AM
            
            var theme = isDarkTime ? GetDarkTheme() : GetLightTheme();
            ApplyTheme(theme);
        }

        // 🎨 تخصيص الألوان
        public void SetCustomColors(CustomColorScheme colorScheme)
        {
            _currentTheme.CustomColors = colorScheme;
            _currentTheme.Mode = ThemeMode.Custom;
            
            ApplyCustomTheme(colorScheme);
            SaveThemeSettings();
            OnThemeChanged();
        }

        public void SetAccentColor(string accentColor)
        {
            _currentTheme.AccentColor = accentColor;
            UpdateAccentColor(accentColor);
            SaveThemeSettings();
            OnThemeChanged();
        }

        // 🎭 الثيمات المحددة مسبقاً
        public void ApplyPredefinedTheme(string themeName)
        {
            if (_predefinedThemes.TryGetValue(themeName, out var theme))
            {
                _currentTheme = theme.Clone();
                ApplyTheme(theme);
                SaveThemeSettings();
                OnThemeChanged();
            }
        }

        public List<string> GetAvailableThemes()
        {
            return new List<string>(_predefinedThemes.Keys);
        }

        // 🔧 تطبيق الثيمات
        private void ApplyTheme(ThemeSettings theme)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                var resources = Application.Current.Resources;
                
                // الألوان الأساسية
                resources["PrimaryColor"] = new SolidColorBrush((Color)ColorConverter.ConvertFromString(theme.PrimaryColor));
                resources["SecondaryColor"] = new SolidColorBrush((Color)ColorConverter.ConvertFromString(theme.SecondaryColor));
                resources["AccentColor"] = new SolidColorBrush((Color)ColorConverter.ConvertFromString(theme.AccentColor));
                resources["BackgroundColor"] = new SolidColorBrush((Color)ColorConverter.ConvertFromString(theme.BackgroundColor));
                resources["SurfaceColor"] = new SolidColorBrush((Color)ColorConverter.ConvertFromString(theme.SurfaceColor));
                resources["TextColor"] = new SolidColorBrush((Color)ColorConverter.ConvertFromString(theme.TextColor));
                resources["TextSecondaryColor"] = new SolidColorBrush((Color)ColorConverter.ConvertFromString(theme.TextSecondaryColor));
                
                // ألوان الحالة
                resources["SuccessColor"] = new SolidColorBrush((Color)ColorConverter.ConvertFromString(theme.SuccessColor));
                resources["WarningColor"] = new SolidColorBrush((Color)ColorConverter.ConvertFromString(theme.WarningColor));
                resources["ErrorColor"] = new SolidColorBrush((Color)ColorConverter.ConvertFromString(theme.ErrorColor));
                resources["InfoColor"] = new SolidColorBrush((Color)ColorConverter.ConvertFromString(theme.InfoColor));
                
                // الشفافية والظلال
                resources["CardOpacity"] = theme.CardOpacity;
                resources["ShadowOpacity"] = theme.ShadowOpacity;
                resources["BorderRadius"] = new CornerRadius(theme.BorderRadius);
            });
        }

        private void ApplyCustomTheme(CustomColorScheme colorScheme)
        {
            var customTheme = new ThemeSettings
            {
                Mode = ThemeMode.Custom,
                PrimaryColor = colorScheme.Primary,
                SecondaryColor = colorScheme.Secondary,
                AccentColor = colorScheme.Accent,
                BackgroundColor = colorScheme.Background,
                SurfaceColor = colorScheme.Surface,
                TextColor = colorScheme.Text,
                TextSecondaryColor = colorScheme.TextSecondary,
                SuccessColor = colorScheme.Success,
                WarningColor = colorScheme.Warning,
                ErrorColor = colorScheme.Error,
                InfoColor = colorScheme.Info,
                CustomColors = colorScheme
            };
            
            ApplyTheme(customTheme);
        }

        private void UpdateAccentColor(string accentColor)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                Application.Current.Resources["AccentColor"] = new SolidColorBrush((Color)ColorConverter.ConvertFromString(accentColor));
            });
        }

        // 🌅 الثيمات المحددة مسبقاً
        private Dictionary<string, ThemeSettings> InitializePredefinedThemes()
        {
            return new Dictionary<string, ThemeSettings>
            {
                ["Light"] = GetLightTheme(),
                ["Dark"] = GetDarkTheme(),
                ["Blue"] = GetBlueTheme(),
                ["Green"] = GetGreenTheme(),
                ["Purple"] = GetPurpleTheme(),
                ["Orange"] = GetOrangeTheme(),
                ["Professional"] = GetProfessionalTheme(),
                ["Elegant"] = GetElegantTheme()
            };
        }

        private ThemeSettings GetLightTheme()
        {
            return new ThemeSettings
            {
                Mode = ThemeMode.Light,
                PrimaryColor = "#FFFFFF",
                SecondaryColor = "#F8FAFC",
                AccentColor = "#3B82F6",
                BackgroundColor = "#F8FAFC",
                SurfaceColor = "#FFFFFF",
                TextColor = "#1F2937",
                TextSecondaryColor = "#6B7280",
                SuccessColor = "#10B981",
                WarningColor = "#F59E0B",
                ErrorColor = "#EF4444",
                InfoColor = "#3B82F6",
                CardOpacity = 1.0,
                ShadowOpacity = 0.1,
                BorderRadius = 12
            };
        }

        private ThemeSettings GetDarkTheme()
        {
            return new ThemeSettings
            {
                Mode = ThemeMode.Dark,
                PrimaryColor = "#1F2937",
                SecondaryColor = "#111827",
                AccentColor = "#60A5FA",
                BackgroundColor = "#111827",
                SurfaceColor = "#1F2937",
                TextColor = "#F9FAFB",
                TextSecondaryColor = "#D1D5DB",
                SuccessColor = "#34D399",
                WarningColor = "#FBBF24",
                ErrorColor = "#F87171",
                InfoColor = "#60A5FA",
                CardOpacity = 0.95,
                ShadowOpacity = 0.3,
                BorderRadius = 12
            };
        }

        private ThemeSettings GetBlueTheme()
        {
            return new ThemeSettings
            {
                Mode = ThemeMode.Custom,
                PrimaryColor = "#EBF8FF",
                SecondaryColor = "#BEE3F8",
                AccentColor = "#3182CE",
                BackgroundColor = "#F7FAFC",
                SurfaceColor = "#EBF8FF",
                TextColor = "#2D3748",
                TextSecondaryColor = "#4A5568",
                SuccessColor = "#38A169",
                WarningColor = "#D69E2E",
                ErrorColor = "#E53E3E",
                InfoColor = "#3182CE",
                CardOpacity = 0.98,
                ShadowOpacity = 0.15,
                BorderRadius = 15
            };
        }

        private ThemeSettings GetGreenTheme()
        {
            return new ThemeSettings
            {
                Mode = ThemeMode.Custom,
                PrimaryColor = "#F0FDF4",
                SecondaryColor = "#DCFCE7",
                AccentColor = "#16A34A",
                BackgroundColor = "#F9FDF9",
                SurfaceColor = "#F0FDF4",
                TextColor = "#14532D",
                TextSecondaryColor = "#166534",
                SuccessColor = "#22C55E",
                WarningColor = "#EAB308",
                ErrorColor = "#DC2626",
                InfoColor = "#0EA5E9",
                CardOpacity = 0.97,
                ShadowOpacity = 0.12,
                BorderRadius = 10
            };
        }

        private ThemeSettings GetPurpleTheme()
        {
            return new ThemeSettings
            {
                Mode = ThemeMode.Custom,
                PrimaryColor = "#FAF5FF",
                SecondaryColor = "#E9D5FF",
                AccentColor = "#9333EA",
                BackgroundColor = "#FEFBFF",
                SurfaceColor = "#FAF5FF",
                TextColor = "#581C87",
                TextSecondaryColor = "#7C3AED",
                SuccessColor = "#059669",
                WarningColor = "#D97706",
                ErrorColor = "#DC2626",
                InfoColor = "#7C3AED",
                CardOpacity = 0.96,
                ShadowOpacity = 0.18,
                BorderRadius = 16
            };
        }

        private ThemeSettings GetOrangeTheme()
        {
            return new ThemeSettings
            {
                Mode = ThemeMode.Custom,
                PrimaryColor = "#FFF7ED",
                SecondaryColor = "#FFEDD5",
                AccentColor = "#EA580C",
                BackgroundColor = "#FFFBF5",
                SurfaceColor = "#FFF7ED",
                TextColor = "#9A3412",
                TextSecondaryColor = "#C2410C",
                SuccessColor = "#16A34A",
                WarningColor = "#D97706",
                ErrorColor = "#DC2626",
                InfoColor = "#0EA5E9",
                CardOpacity = 0.98,
                ShadowOpacity = 0.14,
                BorderRadius = 14
            };
        }

        private ThemeSettings GetProfessionalTheme()
        {
            return new ThemeSettings
            {
                Mode = ThemeMode.Custom,
                PrimaryColor = "#F8F9FA",
                SecondaryColor = "#E9ECEF",
                AccentColor = "#495057",
                BackgroundColor = "#FFFFFF",
                SurfaceColor = "#F8F9FA",
                TextColor = "#212529",
                TextSecondaryColor = "#6C757D",
                SuccessColor = "#28A745",
                WarningColor = "#FFC107",
                ErrorColor = "#DC3545",
                InfoColor = "#17A2B8",
                CardOpacity = 1.0,
                ShadowOpacity = 0.08,
                BorderRadius = 8
            };
        }

        private ThemeSettings GetElegantTheme()
        {
            return new ThemeSettings
            {
                Mode = ThemeMode.Custom,
                PrimaryColor = "#FEFEFE",
                SecondaryColor = "#F5F5F5",
                AccentColor = "#8B5A3C",
                BackgroundColor = "#FAFAFA",
                SurfaceColor = "#FEFEFE",
                TextColor = "#2C2C2C",
                TextSecondaryColor = "#757575",
                SuccessColor = "#4CAF50",
                WarningColor = "#FF9800",
                ErrorColor = "#F44336",
                InfoColor = "#2196F3",
                CardOpacity = 0.99,
                ShadowOpacity = 0.06,
                BorderRadius = 20
            };
        }

        // 💾 حفظ وتحميل الإعدادات
        private void SaveThemeSettings()
        {
            try
            {
                var json = JsonSerializer.Serialize(_currentTheme, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(_settingsPath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ إعدادات الثيم: {ex.Message}");
            }
        }

        private ThemeSettings LoadThemeSettings()
        {
            try
            {
                if (File.Exists(_settingsPath))
                {
                    var json = File.ReadAllText(_settingsPath);
                    return JsonSerializer.Deserialize<ThemeSettings>(json) ?? GetLightTheme();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل إعدادات الثيم: {ex.Message}");
            }
            
            return GetLightTheme();
        }

        public ThemeSettings GetCurrentTheme()
        {
            return _currentTheme.Clone();
        }

        private void OnThemeChanged()
        {
            ThemeChanged?.Invoke(this, new ThemeChangedEventArgs(_currentTheme.Clone()));
        }
    }

    // 📊 نماذج البيانات للثيمات
    public class ThemeSettings
    {
        public ThemeMode Mode { get; set; } = ThemeMode.Light;
        public string PrimaryColor { get; set; } = "#FFFFFF";
        public string SecondaryColor { get; set; } = "#F8FAFC";
        public string AccentColor { get; set; } = "#3B82F6";
        public string BackgroundColor { get; set; } = "#F8FAFC";
        public string SurfaceColor { get; set; } = "#FFFFFF";
        public string TextColor { get; set; } = "#1F2937";
        public string TextSecondaryColor { get; set; } = "#6B7280";
        public string SuccessColor { get; set; } = "#10B981";
        public string WarningColor { get; set; } = "#F59E0B";
        public string ErrorColor { get; set; } = "#EF4444";
        public string InfoColor { get; set; } = "#3B82F6";
        public double CardOpacity { get; set; } = 1.0;
        public double ShadowOpacity { get; set; } = 0.1;
        public double BorderRadius { get; set; } = 12;
        public CustomColorScheme? CustomColors { get; set; }

        public ThemeSettings Clone()
        {
            return new ThemeSettings
            {
                Mode = Mode,
                PrimaryColor = PrimaryColor,
                SecondaryColor = SecondaryColor,
                AccentColor = AccentColor,
                BackgroundColor = BackgroundColor,
                SurfaceColor = SurfaceColor,
                TextColor = TextColor,
                TextSecondaryColor = TextSecondaryColor,
                SuccessColor = SuccessColor,
                WarningColor = WarningColor,
                ErrorColor = ErrorColor,
                InfoColor = InfoColor,
                CardOpacity = CardOpacity,
                ShadowOpacity = ShadowOpacity,
                BorderRadius = BorderRadius,
                CustomColors = CustomColors
            };
        }
    }

    public class CustomColorScheme
    {
        public string Primary { get; set; } = "#3B82F6";
        public string Secondary { get; set; } = "#6B7280";
        public string Accent { get; set; } = "#8B5CF6";
        public string Background { get; set; } = "#F8FAFC";
        public string Surface { get; set; } = "#FFFFFF";
        public string Text { get; set; } = "#1F2937";
        public string TextSecondary { get; set; } = "#6B7280";
        public string Success { get; set; } = "#10B981";
        public string Warning { get; set; } = "#F59E0B";
        public string Error { get; set; } = "#EF4444";
        public string Info { get; set; } = "#3B82F6";
    }

    public class ThemeChangedEventArgs : EventArgs
    {
        public ThemeSettings NewTheme { get; }

        public ThemeChangedEventArgs(ThemeSettings newTheme)
        {
            NewTheme = newTheme;
        }
    }

    public enum ThemeMode
    {
        Light,
        Dark,
        Auto,
        Custom
    }
}
