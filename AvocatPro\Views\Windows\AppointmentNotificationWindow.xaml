<Window x:Class="AvocatPro.Views.Windows.AppointmentNotificationWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تنبيهات المواعيد" 
        Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        ResizeMode="CanResize"
        Topmost="True">

    <Window.Resources>
        <!-- أنماط النافذة -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1976D2"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="NotificationCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="UrgentNotificationCardStyle" TargetType="Border" BasedOn="{StaticResource NotificationCardStyle}">
            <Setter Property="BorderBrush" Value="#F44336"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Background" Value="#FFEBEE"/>
        </Style>

        <Style x:Key="TodayNotificationCardStyle" TargetType="Border" BasedOn="{StaticResource NotificationCardStyle}">
            <Setter Property="BorderBrush" Value="#4CAF50"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Background" Value="#E8F5E8"/>
        </Style>

        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#1976D2"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="3"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1565C0"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="CloseButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#F44336"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#D32F2F"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="TabHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
        </Style>
    </Window.Resources>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- رأس النافذة -->
        <Border Grid.Row="0" Background="#1976D2" Padding="20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="🔔" FontSize="24" Foreground="White" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="تنبيهات المواعيد والإشعارات" FontSize="18" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="إدارة ومتابعة جميع التنبيهات والإشعارات الخاصة بالمواعيد" FontSize="12" Foreground="#E3F2FD"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- المحتوى الرئيسي -->
        <TabControl Grid.Row="1" Margin="20" Background="White">
            <!-- تبويب التنبيهات الفورية -->
            <TabItem>
                <TabItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="⚠️" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="تنبيهات فورية" Style="{StaticResource TabHeaderStyle}"/>
                        <Border Name="UrgentBadge" Background="#F44336" CornerRadius="10" Padding="5,2" Margin="5,0">
                            <TextBlock Name="UrgentCount" Text="0" FontSize="10" Foreground="White" FontWeight="Bold"/>
                        </Border>
                    </StackPanel>
                </TabItem.Header>
                
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="10">
                    <StackPanel Name="UrgentNotificationsPanel">
                        <!-- سيتم ملؤها ديناميكياً -->
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- تبويب مواعيد اليوم -->
            <TabItem>
                <TabItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📅" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="مواعيد اليوم" Style="{StaticResource TabHeaderStyle}"/>
                        <Border Name="TodayBadge" Background="#4CAF50" CornerRadius="10" Padding="5,2" Margin="5,0">
                            <TextBlock Name="TodayCount" Text="0" FontSize="10" Foreground="White" FontWeight="Bold"/>
                        </Border>
                    </StackPanel>
                </TabItem.Header>
                
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="10">
                    <StackPanel Name="TodayNotificationsPanel">
                        <!-- سيتم ملؤها ديناميكياً -->
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- تبويب مواعيد الغد -->
            <TabItem>
                <TabItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📆" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="مواعيد الغد" Style="{StaticResource TabHeaderStyle}"/>
                        <Border Name="TomorrowBadge" Background="#FF9800" CornerRadius="10" Padding="5,2" Margin="5,0">
                            <TextBlock Name="TomorrowCount" Text="0" FontSize="10" Foreground="White" FontWeight="Bold"/>
                        </Border>
                    </StackPanel>
                </TabItem.Header>
                
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="10">
                    <StackPanel Name="TomorrowNotificationsPanel">
                        <!-- سيتم ملؤها ديناميكياً -->
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- تبويب المواعيد المتأخرة -->
            <TabItem>
                <TabItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🔴" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="مواعيد متأخرة" Style="{StaticResource TabHeaderStyle}"/>
                        <Border Name="OverdueBadge" Background="#D32F2F" CornerRadius="10" Padding="5,2" Margin="5,0">
                            <TextBlock Name="OverdueCount" Text="0" FontSize="10" Foreground="White" FontWeight="Bold"/>
                        </Border>
                    </StackPanel>
                </TabItem.Header>
                
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="10">
                    <StackPanel Name="OverdueNotificationsPanel">
                        <!-- سيتم ملؤها ديناميكياً -->
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- تبويب إعدادات التنبيهات -->
            <TabItem>
                <TabItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="⚙️" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="إعدادات التنبيهات" Style="{StaticResource TabHeaderStyle}"/>
                    </StackPanel>
                </TabItem.Header>
                
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
                    <StackPanel>
                        <TextBlock Text="🔧 إعدادات التنبيهات" Style="{StaticResource HeaderTextStyle}"/>
                        
                        <GroupBox Header="طرق التنبيه" Margin="0,0,0,20" Padding="15">
                            <StackPanel>
                                <CheckBox Name="EnablePopupNotifications" Content="تنبيهات منبثقة على الشاشة" 
                                         FontSize="14" Margin="0,5" IsChecked="True"/>
                                <CheckBox Name="EnableEmailNotifications" Content="تنبيهات عبر البريد الإلكتروني" 
                                         FontSize="14" Margin="0,5" IsChecked="True"/>
                                <CheckBox Name="EnableSmsNotifications" Content="تنبيهات عبر الرسائل النصية" 
                                         FontSize="14" Margin="0,5" IsChecked="False"/>
                                <CheckBox Name="EnableSoundNotifications" Content="تنبيهات صوتية" 
                                         FontSize="14" Margin="0,5" IsChecked="True"/>
                            </StackPanel>
                        </GroupBox>

                        <GroupBox Header="أوقات التنبيه الافتراضية" Margin="0,0,0,20" Padding="15">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="التنبيه الأول:" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                                    <ComboBox Name="FirstNotificationComboBox" Height="35" FontSize="13">
                                        <ComboBoxItem Content="5 دقائق قبل الموعد" Tag="5"/>
                                        <ComboBoxItem Content="10 دقائق قبل الموعد" Tag="10"/>
                                        <ComboBoxItem Content="15 دقيقة قبل الموعد" Tag="15"/>
                                        <ComboBoxItem Content="30 دقيقة قبل الموعد" Tag="30" IsSelected="True"/>
                                        <ComboBoxItem Content="ساعة قبل الموعد" Tag="60"/>
                                        <ComboBoxItem Content="ساعتين قبل الموعد" Tag="120"/>
                                    </ComboBox>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                    <TextBlock Text="التنبيه الثاني:" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                                    <ComboBox Name="SecondNotificationComboBox" Height="35" FontSize="13">
                                        <ComboBoxItem Content="لا يوجد تنبيه ثاني" Tag="0" IsSelected="True"/>
                                        <ComboBoxItem Content="يوم قبل الموعد" Tag="1440"/>
                                        <ComboBoxItem Content="3 ساعات قبل الموعد" Tag="180"/>
                                        <ComboBoxItem Content="ساعة قبل الموعد" Tag="60"/>
                                    </ComboBox>
                                </StackPanel>
                            </Grid>
                        </GroupBox>

                        <GroupBox Header="إعدادات متقدمة" Margin="0,0,0,20" Padding="15">
                            <StackPanel>
                                <CheckBox Name="AutoMarkAsNotified" Content="تحديد التنبيهات كمرسلة تلقائياً" 
                                         FontSize="14" Margin="0,5" IsChecked="True"/>
                                <CheckBox Name="ShowOverdueWarnings" Content="إظهار تحذيرات للمواعيد المتأخرة" 
                                         FontSize="14" Margin="0,5" IsChecked="True"/>
                                <CheckBox Name="EnableWeekendNotifications" Content="تفعيل التنبيهات في عطلة نهاية الأسبوع" 
                                         FontSize="14" Margin="0,5" IsChecked="False"/>
                                <CheckBox Name="EnableHolidayNotifications" Content="تفعيل التنبيهات في الأعياد والعطل الرسمية" 
                                         FontSize="14" Margin="0,5" IsChecked="False"/>
                            </StackPanel>
                        </GroupBox>

                        <Button Name="SaveSettingsButton" Style="{StaticResource ActionButtonStyle}" 
                               Background="#4CAF50" HorizontalAlignment="Center" Click="SaveSettingsButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="💾" FontSize="14" Margin="0,0,5,0"/>
                                <TextBlock Text="حفظ الإعدادات"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>

        <!-- تذييل النافذة -->
        <Border Grid.Row="2" Background="White" Padding="15" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Button Name="RefreshNotificationsButton" Style="{StaticResource ActionButtonStyle}" 
                           Background="#4CAF50" Click="RefreshNotificationsButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔄" FontSize="14" Margin="0,0,5,0"/>
                            <TextBlock Text="تحديث"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Name="SendAllNotificationsButton" Style="{StaticResource ActionButtonStyle}" 
                           Background="#FF9800" Click="SendAllNotificationsButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📧" FontSize="14" Margin="0,0,5,0"/>
                            <TextBlock Text="إرسال جميع التنبيهات"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Name="MarkAllAsReadButton" Style="{StaticResource ActionButtonStyle}" 
                           Background="#9C27B0" Click="MarkAllAsReadButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="✅" FontSize="14" Margin="0,0,5,0"/>
                            <TextBlock Text="تحديد الكل كمقروء"/>
                        </StackPanel>
                    </Button>
                </StackPanel>

                <Button Name="CloseButton" Grid.Column="1" Style="{StaticResource CloseButtonStyle}" Click="CloseButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="❌" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="إغلاق"/>
                    </StackPanel>
                </Button>
            </Grid>
        </Border>
    </Grid>
</Window>
