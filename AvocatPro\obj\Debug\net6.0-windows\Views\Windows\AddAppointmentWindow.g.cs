﻿#pragma checksum "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "723737A6FDBEFA1947FA8E61AE51B22C2F7DD9F4"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Windows {
    
    
    /// <summary>
    /// AddAppointmentWindow
    /// </summary>
    public partial class AddAppointmentWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 148 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TitleTextBox;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ClientComboBox;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker AppointmentDatePicker;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StartHourComboBox;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StartMinuteComboBox;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox EndHourComboBox;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox EndMinuteComboBox;
        
        #line default
        #line hidden
        
        
        #line 223 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton LowPriorityRadio;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton MediumPriorityRadio;
        
        #line default
        #line hidden
        
        
        #line 227 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton HighPriorityRadio;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton UrgentPriorityRadio;
        
        #line default
        #line hidden
        
        
        #line 234 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CaseComboBox;
        
        #line default
        #line hidden
        
        
        #line 247 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 257 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LocationTextBox;
        
        #line default
        #line hidden
        
        
        #line 274 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 279 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RequiredAttendeesTextBox;
        
        #line default
        #line hidden
        
        
        #line 284 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CostTextBox;
        
        #line default
        #line hidden
        
        
        #line 290 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 295 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ContactPhoneTextBox;
        
        #line default
        #line hidden
        
        
        #line 299 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ContactEmailTextBox;
        
        #line default
        #line hidden
        
        
        #line 316 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox NotificationComboBox;
        
        #line default
        #line hidden
        
        
        #line 326 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsPaidCheckBox;
        
        #line default
        #line hidden
        
        
        #line 330 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsRecurringCheckBox;
        
        #line default
        #line hidden
        
        
        #line 333 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox RecurrencePatternComboBox;
        
        #line default
        #line hidden
        
        
        #line 348 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 355 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveAndNewButton;
        
        #line default
        #line hidden
        
        
        #line 363 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveAndNotifyButton;
        
        #line default
        #line hidden
        
        
        #line 371 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/windows/addappointmentwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TitleTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.TypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 3:
            this.ClientComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.AppointmentDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 5:
            this.StartHourComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            this.StartMinuteComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.EndHourComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.EndMinuteComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.LowPriorityRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 10:
            this.MediumPriorityRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 11:
            this.HighPriorityRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 12:
            this.UrgentPriorityRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 13:
            this.CaseComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 14:
            this.StatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 15:
            this.LocationTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.RequiredAttendeesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 18:
            this.CostTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.ContactPhoneTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.ContactEmailTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 22:
            this.NotificationComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 23:
            this.IsPaidCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 24:
            this.IsRecurringCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 331 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
            this.IsRecurringCheckBox.Checked += new System.Windows.RoutedEventHandler(this.IsRecurringCheckBox_Checked);
            
            #line default
            #line hidden
            
            #line 331 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
            this.IsRecurringCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.IsRecurringCheckBox_Unchecked);
            
            #line default
            #line hidden
            return;
            case 25:
            this.RecurrencePatternComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 26:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 348 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.SaveAndNewButton = ((System.Windows.Controls.Button)(target));
            
            #line 356 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
            this.SaveAndNewButton.Click += new System.Windows.RoutedEventHandler(this.SaveAndNewButton_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.SaveAndNotifyButton = ((System.Windows.Controls.Button)(target));
            
            #line 364 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
            this.SaveAndNotifyButton.Click += new System.Windows.RoutedEventHandler(this.SaveAndNotifyButton_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 371 "..\..\..\..\..\Views\Windows\AddAppointmentWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

