using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;

namespace AvocatPro.Services
{
    public class ThemeManager
    {
        private static ThemeManager _instance;
        public static ThemeManager Instance => _instance ??= new ThemeManager();

        private Dictionary<string, Theme> _themes;
        private Theme _currentTheme;
        private bool _isDarkMode;
        private bool _isAutoMode;

        public event EventHandler<ThemeChangedEventArgs> ThemeChanged;

        private ThemeManager()
        {
            InitializeThemes();
            LoadUserPreferences();
        }

        private void InitializeThemes()
        {
            _themes = new Dictionary<string, Theme>
            {
                ["Light"] = new Theme
                {
                    Name = "Light",
                    DisplayName = "الوضع النهاري",
                    PrimaryColor = Color.FromRgb(59, 130, 246),      // Blue
                    SecondaryColor = Color.FromRgb(107, 114, 128),   // Gray
                    AccentColor = Color.FromRgb(16, 185, 129),       // Green
                    BackgroundColor = Color.FromRgb(248, 250, 252),  // Light Gray
                    SurfaceColor = Colors.White,
                    TextPrimaryColor = Color.FromRgb(31, 41, 55),    // Dark Gray
                    TextSecondaryColor = Color.FromRgb(107, 114, 128),
                    BorderColor = Color.FromRgb(229, 231, 235),
                    ErrorColor = Color.FromRgb(239, 68, 68),         // Red
                    WarningColor = Color.FromRgb(245, 158, 11),      // Orange
                    SuccessColor = Color.FromRgb(16, 185, 129),      // Green
                    InfoColor = Color.FromRgb(59, 130, 246)          // Blue
                },
                ["Dark"] = new Theme
                {
                    Name = "Dark",
                    DisplayName = "الوضع الليلي",
                    PrimaryColor = Color.FromRgb(96, 165, 250),      // Light Blue
                    SecondaryColor = Color.FromRgb(156, 163, 175),   // Light Gray
                    AccentColor = Color.FromRgb(52, 211, 153),       // Light Green
                    BackgroundColor = Color.FromRgb(17, 24, 39),     // Dark Blue Gray
                    SurfaceColor = Color.FromRgb(31, 41, 55),        // Dark Gray
                    TextPrimaryColor = Color.FromRgb(249, 250, 251), // Light
                    TextSecondaryColor = Color.FromRgb(156, 163, 175),
                    BorderColor = Color.FromRgb(75, 85, 99),
                    ErrorColor = Color.FromRgb(248, 113, 113),       // Light Red
                    WarningColor = Color.FromRgb(251, 191, 36),      // Light Orange
                    SuccessColor = Color.FromRgb(52, 211, 153),      // Light Green
                    InfoColor = Color.FromRgb(96, 165, 250)          // Light Blue
                },
                ["Professional"] = new Theme
                {
                    Name = "Professional",
                    DisplayName = "المهني",
                    PrimaryColor = Color.FromRgb(30, 58, 138),       // Dark Blue
                    SecondaryColor = Color.FromRgb(71, 85, 105),     // Slate
                    AccentColor = Color.FromRgb(180, 83, 9),         // Bronze
                    BackgroundColor = Color.FromRgb(248, 250, 252),
                    SurfaceColor = Colors.White,
                    TextPrimaryColor = Color.FromRgb(15, 23, 42),    // Slate 900
                    TextSecondaryColor = Color.FromRgb(71, 85, 105),
                    BorderColor = Color.FromRgb(203, 213, 225),
                    ErrorColor = Color.FromRgb(185, 28, 28),
                    WarningColor = Color.FromRgb(180, 83, 9),
                    SuccessColor = Color.FromRgb(21, 128, 61),
                    InfoColor = Color.FromRgb(30, 58, 138)
                },
                ["Elegant"] = new Theme
                {
                    Name = "Elegant",
                    DisplayName = "الأنيق",
                    PrimaryColor = Color.FromRgb(139, 92, 246),      // Purple
                    SecondaryColor = Color.FromRgb(107, 114, 128),
                    AccentColor = Color.FromRgb(236, 72, 153),       // Pink
                    BackgroundColor = Color.FromRgb(250, 250, 250),
                    SurfaceColor = Colors.White,
                    TextPrimaryColor = Color.FromRgb(31, 41, 55),
                    TextSecondaryColor = Color.FromRgb(107, 114, 128),
                    BorderColor = Color.FromRgb(229, 231, 235),
                    ErrorColor = Color.FromRgb(239, 68, 68),
                    WarningColor = Color.FromRgb(245, 158, 11),
                    SuccessColor = Color.FromRgb(16, 185, 129),
                    InfoColor = Color.FromRgb(139, 92, 246)
                }
            };

            _currentTheme = _themes["Light"];
        }

        public async Task ApplyThemeAsync(string themeName, bool animate = true)
        {
            if (!_themes.ContainsKey(themeName))
                return;

            var newTheme = _themes[themeName];
            var oldTheme = _currentTheme;
            _currentTheme = newTheme;

            if (animate)
            {
                await AnimateThemeTransition(oldTheme, newTheme);
            }
            else
            {
                ApplyThemeToApplication(newTheme);
            }

            ThemeChanged?.Invoke(this, new ThemeChangedEventArgs(oldTheme, newTheme));
            await SaveUserPreferences();
        }

        private async Task AnimateThemeTransition(Theme oldTheme, Theme newTheme)
        {
            var duration = TimeSpan.FromMilliseconds(300);
            var storyboard = new Storyboard();

            // تحريك تغيير الألوان
            var colorAnimation = new ColorAnimation
            {
                From = oldTheme.PrimaryColor,
                To = newTheme.PrimaryColor,
                Duration = duration,
                EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseInOut }
            };

            storyboard.Children.Add(colorAnimation);
            storyboard.Completed += (s, e) => ApplyThemeToApplication(newTheme);

            storyboard.Begin();
            await Task.Delay(duration);
        }

        private void ApplyThemeToApplication(Theme theme)
        {
            var app = Application.Current;
            if (app?.Resources == null) return;

            // تطبيق الألوان على الموارد العامة
            app.Resources["PrimaryColor"] = new SolidColorBrush(theme.PrimaryColor);
            app.Resources["SecondaryColor"] = new SolidColorBrush(theme.SecondaryColor);
            app.Resources["AccentColor"] = new SolidColorBrush(theme.AccentColor);
            app.Resources["BackgroundColor"] = new SolidColorBrush(theme.BackgroundColor);
            app.Resources["SurfaceColor"] = new SolidColorBrush(theme.SurfaceColor);
            app.Resources["TextPrimaryColor"] = new SolidColorBrush(theme.TextPrimaryColor);
            app.Resources["TextSecondaryColor"] = new SolidColorBrush(theme.TextSecondaryColor);
            app.Resources["BorderColor"] = new SolidColorBrush(theme.BorderColor);
            app.Resources["ErrorColor"] = new SolidColorBrush(theme.ErrorColor);
            app.Resources["WarningColor"] = new SolidColorBrush(theme.WarningColor);
            app.Resources["SuccessColor"] = new SolidColorBrush(theme.SuccessColor);
            app.Resources["InfoColor"] = new SolidColorBrush(theme.InfoColor);

            // تطبيق الألوان الخام
            app.Resources["PrimaryColorRaw"] = theme.PrimaryColor;
            app.Resources["SecondaryColorRaw"] = theme.SecondaryColor;
            app.Resources["AccentColorRaw"] = theme.AccentColor;
            app.Resources["BackgroundColorRaw"] = theme.BackgroundColor;
            app.Resources["SurfaceColorRaw"] = theme.SurfaceColor;
            app.Resources["TextPrimaryColorRaw"] = theme.TextPrimaryColor;
            app.Resources["TextSecondaryColorRaw"] = theme.TextSecondaryColor;
            app.Resources["BorderColorRaw"] = theme.BorderColor;
        }

        public void ToggleDarkMode()
        {
            var targetTheme = _isDarkMode ? "Light" : "Dark";
            _isDarkMode = !_isDarkMode;
            _ = ApplyThemeAsync(targetTheme);
        }

        public async Task SetAutoMode(bool enabled)
        {
            _isAutoMode = enabled;
            if (enabled)
            {
                await ApplyAutoTheme();
                StartAutoThemeTimer();
            }
            else
            {
                StopAutoThemeTimer();
            }
        }

        private System.Windows.Threading.DispatcherTimer _autoThemeTimer;

        private void StartAutoThemeTimer()
        {
            _autoThemeTimer?.Stop();
            _autoThemeTimer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromMinutes(30) // فحص كل 30 دقيقة
            };
            _autoThemeTimer.Tick += async (s, e) => await ApplyAutoTheme();
            _autoThemeTimer.Start();
        }

        private void StopAutoThemeTimer()
        {
            _autoThemeTimer?.Stop();
            _autoThemeTimer = null;
        }

        private async Task ApplyAutoTheme()
        {
            var currentHour = DateTime.Now.Hour;
            var shouldUseDarkMode = currentHour < 6 || currentHour >= 18; // 6 صباحاً إلى 6 مساءً نهاري

            var targetTheme = shouldUseDarkMode ? "Dark" : "Light";
            if (_currentTheme.Name != targetTheme)
            {
                await ApplyThemeAsync(targetTheme);
            }
        }

        public IEnumerable<Theme> GetAvailableThemes() => _themes.Values;
        public Theme GetCurrentTheme() => _currentTheme;
        public bool IsDarkMode => _isDarkMode;
        public bool IsAutoMode => _isAutoMode;

        private async Task SaveUserPreferences()
        {
            try
            {
                var preferences = new ThemePreferences
                {
                    CurrentTheme = _currentTheme.Name,
                    IsDarkMode = _isDarkMode,
                    IsAutoMode = _isAutoMode
                };

                var json = JsonSerializer.Serialize(preferences, new JsonSerializerOptions { WriteIndented = true });
                var path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "AvocatPro", "theme-preferences.json");
                Directory.CreateDirectory(Path.GetDirectoryName(path));
                await File.WriteAllTextAsync(path, json);
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ تفضيلات الثيم: {ex.Message}");
            }
        }

        private void LoadUserPreferences()
        {
            try
            {
                var path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "AvocatPro", "theme-preferences.json");
                if (File.Exists(path))
                {
                    var json = File.ReadAllText(path);
                    var preferences = JsonSerializer.Deserialize<ThemePreferences>(json);
                    
                    if (preferences != null)
                    {
                        _isDarkMode = preferences.IsDarkMode;
                        _isAutoMode = preferences.IsAutoMode;
                        
                        if (_themes.ContainsKey(preferences.CurrentTheme))
                        {
                            _currentTheme = _themes[preferences.CurrentTheme];
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل تفضيلات الثيم: {ex.Message}");
            }
        }
    }

    public class Theme
    {
        public string Name { get; set; }
        public string DisplayName { get; set; }
        public Color PrimaryColor { get; set; }
        public Color SecondaryColor { get; set; }
        public Color AccentColor { get; set; }
        public Color BackgroundColor { get; set; }
        public Color SurfaceColor { get; set; }
        public Color TextPrimaryColor { get; set; }
        public Color TextSecondaryColor { get; set; }
        public Color BorderColor { get; set; }
        public Color ErrorColor { get; set; }
        public Color WarningColor { get; set; }
        public Color SuccessColor { get; set; }
        public Color InfoColor { get; set; }
    }

    public class ThemeChangedEventArgs : EventArgs
    {
        public Theme OldTheme { get; }
        public Theme NewTheme { get; }

        public ThemeChangedEventArgs(Theme oldTheme, Theme newTheme)
        {
            OldTheme = oldTheme;
            NewTheme = newTheme;
        }
    }

    public class ThemePreferences
    {
        public string CurrentTheme { get; set; }
        public bool IsDarkMode { get; set; }
        public bool IsAutoMode { get; set; }
    }
}
