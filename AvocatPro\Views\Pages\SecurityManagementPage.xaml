<Page x:Class="AvocatPro.Views.Pages.SecurityManagementPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="إدارة الأمان والحماية" FlowDirection="RightToLeft"
      Background="#F8FAFC">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="White" CornerRadius="15" Padding="20" Margin="20,20,20,10">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border CornerRadius="50" Width="60" Height="60" Margin="0,0,20,0">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#EF4444" Offset="0"/>
                                <GradientStop Color="#DC2626" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <Border.Effect>
                            <DropShadowEffect Color="#EF4444" Opacity="0.4" BlurRadius="15" ShadowDepth="0"/>
                        </Border.Effect>
                        <TextBlock Text="🔐" FontSize="30" Foreground="White" 
                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <StackPanel>
                        <TextBlock Text="إدارة الأمان والحماية" FontSize="24" FontWeight="Bold" Foreground="#1F2937"/>
                        <TextBlock Text="المصادقة الثنائية، التشفير، النسخ الاحتياطية وسجل المراجعة" FontSize="14" Foreground="#6B7280"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="RefreshBtn" Background="#3B82F6" Foreground="White" Padding="12,8" 
                            Margin="0,0,10,0" Click="Refresh_Click" BorderThickness="0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔄" FontSize="14" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث"/>
                        </StackPanel>
                    </Button>
                    <Button x:Name="SettingsBtn" Background="#6B7280" Foreground="White" Padding="12,8" 
                            Click="Settings_Click" BorderThickness="0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="⚙️" FontSize="14" Margin="0,0,8,0"/>
                            <TextBlock Text="الإعدادات"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="20,0,20,20">
            <StackPanel>
                <!-- بطاقات الأمان الرئيسية -->
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- المصادقة الثنائية -->
                    <Border Grid.Column="0" Background="White" CornerRadius="15" Padding="20">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                        </Border.Effect>
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                <TextBlock Text="🔐" FontSize="24" Margin="0,0,10,0"/>
                                <TextBlock Text="المصادقة الثنائية" FontSize="16" FontWeight="SemiBold" Foreground="#1F2937"/>
                            </StackPanel>
                            <TextBlock x:Name="TwoFactorStatusText" Text="غير مفعل" FontSize="14" Foreground="#EF4444" Margin="0,0,0,15"/>
                            <Button x:Name="EnableTwoFactorBtn" Background="#10B981" Foreground="White" Padding="10,8" 
                                    Click="EnableTwoFactor_Click" BorderThickness="0">
                                <TextBlock Text="تفعيل المصادقة الثنائية"/>
                            </Button>
                        </StackPanel>
                    </Border>

                    <!-- التشفير -->
                    <Border Grid.Column="2" Background="White" CornerRadius="15" Padding="20">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                        </Border.Effect>
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                <TextBlock Text="🔒" FontSize="24" Margin="0,0,10,0"/>
                                <TextBlock Text="التشفير المتقدم" FontSize="16" FontWeight="SemiBold" Foreground="#1F2937"/>
                            </StackPanel>
                            <TextBlock x:Name="EncryptionStatusText" Text="مفعل" FontSize="14" Foreground="#10B981" Margin="0,0,0,15"/>
                            <Button x:Name="EncryptionSettingsBtn" Background="#3B82F6" Foreground="White" Padding="10,8" 
                                    Click="EncryptionSettings_Click" BorderThickness="0">
                                <TextBlock Text="إعدادات التشفير"/>
                            </Button>
                        </StackPanel>
                    </Border>

                    <!-- النسخ الاحتياطية -->
                    <Border Grid.Column="4" Background="White" CornerRadius="15" Padding="20">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                        </Border.Effect>
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                <TextBlock Text="💾" FontSize="24" Margin="0,0,10,0"/>
                                <TextBlock Text="النسخ الاحتياطية" FontSize="16" FontWeight="SemiBold" Foreground="#1F2937"/>
                            </StackPanel>
                            <TextBlock x:Name="BackupStatusText" Text="آخر نسخة: أمس" FontSize="14" Foreground="#6B7280" Margin="0,0,0,15"/>
                            <Button x:Name="CreateBackupBtn" Background="#F59E0B" Foreground="White" Padding="10,8" 
                                    Click="CreateBackup_Click" BorderThickness="0">
                                <TextBlock Text="إنشاء نسخة احتياطية"/>
                            </Button>
                        </StackPanel>
                    </Border>

                    <!-- سجل المراجعة -->
                    <Border Grid.Column="6" Background="White" CornerRadius="15" Padding="20">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                        </Border.Effect>
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                <TextBlock Text="📋" FontSize="24" Margin="0,0,10,0"/>
                                <TextBlock Text="سجل المراجعة" FontSize="16" FontWeight="SemiBold" Foreground="#1F2937"/>
                            </StackPanel>
                            <TextBlock x:Name="AuditLogCountText" Text="1,234 سجل" FontSize="14" Foreground="#6B7280" Margin="0,0,0,15"/>
                            <Button x:Name="ViewAuditLogBtn" Background="#8B5CF6" Foreground="White" Padding="10,8" 
                                    Click="ViewAuditLog_Click" BorderThickness="0">
                                <TextBlock Text="عرض السجل"/>
                            </Button>
                        </StackPanel>
                    </Border>
                </Grid>

                <!-- تنبيهات الأمان -->
                <Border Background="White" CornerRadius="15" Padding="20" Margin="0,0,0,20">
                    <Border.Effect>
                        <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                    </Border.Effect>
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                            <TextBlock Text="🚨" FontSize="24" Margin="0,0,10,0"/>
                            <TextBlock Text="تنبيهات الأمان" FontSize="18" FontWeight="SemiBold" Foreground="#1F2937"/>
                        </StackPanel>
                        <StackPanel x:Name="SecurityAlertsPanel">
                            <!-- سيتم إضافة التنبيهات ديناميكياً -->
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- إحصائيات الأمان -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- إحصائيات النشاط -->
                    <Border Grid.Column="0" Background="White" CornerRadius="15" Padding="20">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                        </Border.Effect>
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                <TextBlock Text="📊" FontSize="24" Margin="0,0,10,0"/>
                                <TextBlock Text="إحصائيات النشاط" FontSize="18" FontWeight="SemiBold" Foreground="#1F2937"/>
                            </StackPanel>
                            <StackPanel x:Name="ActivityStatsPanel">
                                <!-- سيتم إضافة الإحصائيات ديناميكياً -->
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- إعدادات الأمان -->
                    <Border Grid.Column="2" Background="White" CornerRadius="15" Padding="20">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                        </Border.Effect>
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                <TextBlock Text="⚙️" FontSize="24" Margin="0,0,10,0"/>
                                <TextBlock Text="إعدادات الأمان" FontSize="18" FontWeight="SemiBold" Foreground="#1F2937"/>
                            </StackPanel>
                            <StackPanel>
                                <CheckBox x:Name="AutoLockCheckBox" Content="قفل تلقائي بعد عدم النشاط" Margin="0,0,0,10" IsChecked="True"/>
                                <CheckBox x:Name="LoginNotificationCheckBox" Content="إشعارات تسجيل الدخول" Margin="0,0,0,10" IsChecked="True"/>
                                <CheckBox x:Name="DataEncryptionCheckBox" Content="تشفير البيانات الحساسة" Margin="0,0,0,10" IsChecked="True"/>
                                <CheckBox x:Name="AuditLoggingCheckBox" Content="تسجيل جميع النشاطات" Margin="0,0,0,15" IsChecked="True"/>
                                <Button x:Name="SaveSecuritySettingsBtn" Background="#10B981" Foreground="White" Padding="12,8" 
                                        Click="SaveSecuritySettings_Click" BorderThickness="0">
                                    <TextBlock Text="حفظ الإعدادات"/>
                                </Button>
                            </StackPanel>
                        </StackPanel>
                    </Border>
                </Grid>
            </StackPanel>
        </ScrollViewer>

        <!-- مؤشر التحميل -->
        <Border x:Name="LoadingOverlay" Grid.RowSpan="2" Background="Black" Opacity="0.5" Visibility="Collapsed">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="300" Height="6" 
                             Foreground="#EF4444" Background="Transparent"/>
                <TextBlock Text="جاري تحميل بيانات الأمان..." Foreground="White" 
                           HorizontalAlignment="Center" Margin="0,20,0,0" FontSize="18"/>
            </StackPanel>
        </Border>
    </Grid>
</Page>
