<Window x:Class="AvocatPro.Views.Windows.FileDetailsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تفاصيل الملف" Height="700" Width="900"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize"
        Background="#F8FAFC" FlowDirection="RightToLeft">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="White" CornerRadius="16" Padding="24" Margin="0,0,0,24">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="20" ShadowDepth="0" Opacity="0.4"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <Border Grid.Column="0" Background="#6366F1" CornerRadius="12" Padding="12" Margin="0,0,16,0">
                    <TextBlock Text="📄" FontSize="24" Foreground="White"/>
                </Border>
                
                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock x:Name="FileNumberTitle" Text="تفاصيل الملف" FontSize="24" FontWeight="Bold" Foreground="#1F2937"/>
                    <TextBlock x:Name="FileSubject" Text="موضوع القضية" FontSize="14" Foreground="#6B7280"/>
                </StackPanel>

                <Button x:Name="CloseButton" Grid.Column="2" Content="✕" 
                        Background="#EF4444" Foreground="White" BorderThickness="0" 
                        Width="40" Height="40" FontSize="16" FontWeight="Bold"
                        Cursor="Hand" Click="Close_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" CornerRadius="8">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#DC2626"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
            </Grid>
        </Border>

        <!-- محتوى التفاصيل -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="24"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- العمود الأيسر -->
                <Border Grid.Column="0" Background="White" CornerRadius="16" Padding="24">
                    <Border.Effect>
                        <DropShadowEffect Color="#E2E8F0" BlurRadius="15" ShadowDepth="0" Opacity="0.3"/>
                    </Border.Effect>
                    <StackPanel>
                        <!-- المعلومات الأساسية -->
                        <TextBlock Text="📋 المعلومات الأساسية" FontSize="18" FontWeight="Bold" 
                                   Foreground="#1F2937" Margin="0,0,0,20"/>

                        <!-- رقم الملف -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="رقم الملف" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <TextBlock x:Name="FileNumberText" Text="" FontSize="16" FontWeight="Bold" 
                                       Foreground="#1F2937"/>
                        </StackPanel>

                        <!-- مرجع المحكمة -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="مرجع المحكمة" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <TextBlock x:Name="CourtReferenceText" Text="" FontSize="14" 
                                       Foreground="#374151"/>
                        </StackPanel>

                        <!-- مرجع المكتب -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="مرجع المكتب" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <TextBlock x:Name="OfficeReferenceText" Text="" FontSize="14" 
                                       Foreground="#374151"/>
                        </StackPanel>

                        <!-- نوع الملف -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="نوع الملف" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <Border x:Name="FileTypeBorder" Background="#6366F1" CornerRadius="12" Padding="8,4" HorizontalAlignment="Left">
                                <TextBlock x:Name="FileTypeText" Text="" FontSize="12" FontWeight="SemiBold" 
                                           Foreground="White"/>
                            </Border>
                        </StackPanel>

                        <!-- المحكمة -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="المحكمة المختصة" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <TextBlock x:Name="CourtText" Text="" FontSize="14" 
                                       Foreground="#374151" TextWrapping="Wrap"/>
                        </StackPanel>

                        <!-- نوع القضية -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="نوع القضية" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <Border x:Name="CaseTypeBorder" Background="#10B981" CornerRadius="12" Padding="8,4" HorizontalAlignment="Left">
                                <TextBlock x:Name="CaseTypeText" Text="" FontSize="12" FontWeight="SemiBold" 
                                           Foreground="White"/>
                            </Border>
                        </StackPanel>

                        <!-- الحالة -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="حالة الملف" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <Border x:Name="StatusBorder" Background="#F59E0B" CornerRadius="12" Padding="8,4" HorizontalAlignment="Left">
                                <TextBlock x:Name="StatusText" Text="" FontSize="12" FontWeight="SemiBold" 
                                           Foreground="White"/>
                            </Border>
                        </StackPanel>

                        <!-- الأولوية -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="أولوية الملف" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <Border x:Name="PriorityBorder" Background="#EF4444" CornerRadius="12" Padding="8,4" HorizontalAlignment="Left">
                                <TextBlock x:Name="PriorityText" Text="" FontSize="12" FontWeight="SemiBold" 
                                           Foreground="White"/>
                            </Border>
                        </StackPanel>

                        <!-- التتبع الإلكتروني -->
                        <TextBlock Text="🌐 التتبع الإلكتروني" FontSize="18" FontWeight="Bold" 
                                   Foreground="#1F2937" Margin="0,20,0,20"/>

                        <!-- حالة التتبع -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="حالة التتبع" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock x:Name="TrackingStatusIcon" Text="🔴" FontSize="16" Margin="0,0,8,0"/>
                                <TextBlock x:Name="TrackingStatusText" Text="غير مفعل" FontSize="14" 
                                           Foreground="#374151"/>
                            </StackPanel>
                        </StackPanel>

                        <!-- رمز الملف الإلكتروني -->
                        <StackPanel x:Name="ElectronicCodePanel" Margin="0,0,0,16" Visibility="Collapsed">
                            <TextBlock Text="رمز الملف الإلكتروني" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <TextBlock x:Name="ElectronicCodeText" Text="" FontSize="14" 
                                       Foreground="#374151"/>
                        </StackPanel>

                        <!-- سنة الملف -->
                        <StackPanel x:Name="FileYearPanel" Margin="0,0,0,16" Visibility="Collapsed">
                            <TextBlock Text="سنة الملف" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <TextBlock x:Name="FileYearText" Text="" FontSize="14" 
                                       Foreground="#374151"/>
                        </StackPanel>

                        <!-- محكمة الاستئناف -->
                        <StackPanel x:Name="AppealCourtPanel" Margin="0,0,0,16" Visibility="Collapsed">
                            <TextBlock Text="محكمة الاستئناف" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <TextBlock x:Name="AppealCourtText" Text="" FontSize="14" 
                                       Foreground="#374151" TextWrapping="Wrap"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- العمود الأيمن -->
                <Border Grid.Column="2" Background="White" CornerRadius="16" Padding="24">
                    <Border.Effect>
                        <DropShadowEffect Color="#E2E8F0" BlurRadius="15" ShadowDepth="0" Opacity="0.3"/>
                    </Border.Effect>
                    <StackPanel>
                        <!-- معلومات الأطراف -->
                        <TextBlock Text="👥 معلومات الأطراف" FontSize="18" FontWeight="Bold" 
                                   Foreground="#1F2937" Margin="0,0,0,20"/>

                        <!-- الموكل -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="الموكل" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <TextBlock x:Name="ClientText" Text="" FontSize="16" FontWeight="Bold" 
                                       Foreground="#1F2937"/>
                        </StackPanel>

                        <!-- الخصم -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="الخصم" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <TextBlock x:Name="OpponentText" Text="" FontSize="14" 
                                       Foreground="#374151"/>
                        </StackPanel>

                        <!-- المحامي المكلف -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="المحامي المكلف" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <TextBlock x:Name="AssignedLawyerText" Text="" FontSize="14" 
                                       Foreground="#374151"/>
                        </StackPanel>

                        <!-- موضوع القضية -->
                        <StackPanel Margin="0,0,0,20">
                            <TextBlock Text="موضوع القضية" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <Border Background="#F9FAFB" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="8" Padding="12">
                                <TextBlock x:Name="SubjectText" Text="" FontSize="14" 
                                           Foreground="#374151" TextWrapping="Wrap"/>
                            </Border>
                        </StackPanel>

                        <!-- معلومات الإجراءات -->
                        <TextBlock Text="⚖️ معلومات الإجراءات" FontSize="18" FontWeight="Bold" 
                                   Foreground="#1F2937" Margin="0,0,0,20"/>

                        <!-- نوع الإجراء -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="نوع الإجراء" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <TextBlock x:Name="ProcedureTypeText" Text="" FontSize="14" 
                                       Foreground="#374151"/>
                        </StackPanel>

                        <!-- القرار -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="القرار" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <Border Background="#F9FAFB" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="8" Padding="12">
                                <TextBlock x:Name="DecisionText" Text="" FontSize="14" 
                                           Foreground="#374151" TextWrapping="Wrap"/>
                            </Border>
                        </StackPanel>

                        <!-- التواريخ -->
                        <TextBlock Text="📅 التواريخ المهمة" FontSize="18" FontWeight="Bold" 
                                   Foreground="#1F2937" Margin="0,20,0,20"/>

                        <!-- تاريخ الإنشاء -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="تاريخ الإنشاء" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <TextBlock x:Name="CreatedDateText" Text="" FontSize="14" 
                                       Foreground="#374151"/>
                        </StackPanel>

                        <!-- آخر تحديث -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="آخر تحديث" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <TextBlock x:Name="LastUpdateText" Text="" FontSize="14" 
                                       Foreground="#374151"/>
                        </StackPanel>

                        <!-- الجلسة المقبلة -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="الجلسة المقبلة" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock x:Name="NextSessionIcon" Text="📅" FontSize="16" Margin="0,0,8,0"/>
                                <TextBlock x:Name="NextSessionText" Text="" FontSize="14" 
                                           Foreground="#374151"/>
                            </StackPanel>
                        </StackPanel>

                        <!-- آخر جلسة -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="آخر جلسة" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <TextBlock x:Name="LastSessionText" Text="" FontSize="14" 
                                       Foreground="#374151"/>
                        </StackPanel>

                        <!-- معلومات إضافية -->
                        <TextBlock Text="📊 معلومات إضافية" FontSize="18" FontWeight="Bold" 
                                   Foreground="#1F2937" Margin="0,20,0,20"/>

                        <!-- القيمة المقدرة -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="القيمة المقدرة" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <TextBlock x:Name="EstimatedValueText" Text="" FontSize="16" FontWeight="Bold" 
                                       Foreground="#10B981"/>
                        </StackPanel>

                        <!-- عدد الجلسات -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="عدد الجلسات" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <TextBlock x:Name="SessionsCountText" Text="" FontSize="14" 
                                       Foreground="#374151"/>
                        </StackPanel>

                        <!-- ملاحظات -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="ملاحظات" FontSize="12" FontWeight="SemiBold" 
                                       Foreground="#6B7280" Margin="0,0,0,4"/>
                            <Border Background="#F9FAFB" BorderBrush="#E5E7EB" BorderThickness="1" CornerRadius="8" Padding="12">
                                <TextBlock x:Name="NotesText" Text="" FontSize="14" 
                                           Foreground="#374151" TextWrapping="Wrap"/>
                            </Border>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </Grid>
        </ScrollViewer>

        <!-- أزرار الإجراءات -->
        <Border Grid.Row="2" Background="White" CornerRadius="16" Padding="24" Margin="0,24,0,0">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="15" ShadowDepth="0" Opacity="0.3"/>
            </Border.Effect>
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="EditButton" Content="✏️ تعديل الملف" 
                        Background="#F59E0B" Foreground="White" BorderThickness="0" 
                        Padding="20,12" FontSize="14" FontWeight="SemiBold" 
                        Cursor="Hand" Margin="0,0,12,0" Click="Edit_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" CornerRadius="8" 
                                                Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#D97706"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>

                <Button x:Name="TrackButton" Content="🔄 تتبع في المحاكم" 
                        Background="#10B981" Foreground="White" BorderThickness="0" 
                        Padding="20,12" FontSize="14" FontWeight="SemiBold" 
                        Cursor="Hand" Margin="0,0,12,0" Click="Track_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" CornerRadius="8" 
                                                Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#059669"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>

                <Button x:Name="PrintButton" Content="🖨️ طباعة" 
                        Background="#6366F1" Foreground="White" BorderThickness="0" 
                        Padding="20,12" FontSize="14" FontWeight="SemiBold" 
                        Cursor="Hand" Click="Print_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" CornerRadius="8" 
                                                Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#5B5FE8"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
