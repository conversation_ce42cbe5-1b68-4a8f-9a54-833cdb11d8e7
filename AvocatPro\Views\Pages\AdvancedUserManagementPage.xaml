<Page x:Class="AvocatPro.Views.Pages.AdvancedUserManagementPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="إدارة المستخدمين المتقدمة"
      FlowDirection="RightToLeft">

    <Page.Resources>
        <!-- أنماط مخصصة -->
        <Style x:Key="ModernCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="10"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2E3440"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>

        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#4C566A"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#5E81AC"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="6"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#81A1C1"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#4C566A"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="Margin" Value="0,5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#D8DEE9"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <ScrollViewer x:Name="PART_ContentHost" Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#5E81AC"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="Margin" Value="0,5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#D8DEE9"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Background" Value="White"/>
        </Style>

        <Style x:Key="StatusIndicatorStyle" TargetType="Ellipse">
            <Setter Property="Width" Value="12"/>
            <Setter Property="Height" Value="12"/>
            <Setter Property="Margin" Value="0,0,8,0"/>
        </Style>
    </Page.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- العنوان الرئيسي -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,30">
                <TextBlock Text="👥" FontSize="32" VerticalAlignment="Center" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="إدارة المستخدمين المتقدمة" Style="{StaticResource HeaderTextStyle}"/>
                    <TextBlock Text="نظام شامل لإدارة المستخدمين والصلاحيات مع تتبع الأنشطة والأداء" 
                               FontSize="14" Foreground="#6C7B7F" Margin="0,-10,0,0"/>
                </StackPanel>
            </StackPanel>

            <!-- المحتوى الرئيسي -->
            <TabControl Grid.Row="1" Background="Transparent" BorderThickness="0">
                <TabControl.Resources>
                    <Style TargetType="TabItem">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="TabItem">
                                    <Border Name="Border" Background="#F8F9FA" BorderBrush="#E9ECEF" 
                                            BorderThickness="1,1,1,0" CornerRadius="8,8,0,0" 
                                            Padding="15,10" Margin="0,0,3,0">
                                        <ContentPresenter ContentSource="Header" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter TargetName="Border" Property="Background" Value="White"/>
                                            <Setter TargetName="Border" Property="BorderBrush" Value="#5E81AC"/>
                                            <Setter TargetName="Border" Property="BorderThickness" Value="2,2,2,0"/>
                                        </Trigger>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter TargetName="Border" Property="Background" Value="#E3F2FD"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                        <Setter Property="Foreground" Value="#2E3440"/>
                    </Style>
                </TabControl.Resources>

                <!-- تبويب قائمة المستخدمين -->
                <TabItem>
                    <TabItem.Header>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="👤" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="قائمة المستخدمين"/>
                        </StackPanel>
                    </TabItem.Header>
                    
                    <Border Style="{StaticResource ModernCardStyle}" Background="White">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- شريط البحث والأدوات -->
                            <Grid Grid.Row="0" Margin="0,0,0,20">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBox Name="SearchTextBox" Grid.Column="0" 
                                         Style="{StaticResource ModernTextBoxStyle}"
                                         Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                         Tag="🔍 البحث في المستخدمين..."
                                         Margin="0,0,10,0"/>

                                <ComboBox Name="StatusFilterComboBox" Grid.Column="1" 
                                          Style="{StaticResource ModernComboBoxStyle}"
                                          Width="150" Margin="0,0,10,0"/>

                                <ComboBox Name="RoleFilterComboBox" Grid.Column="2" 
                                          Style="{StaticResource ModernComboBoxStyle}"
                                          Width="150" Margin="0,0,10,0"/>

                                <Button Name="AddUserButton" Grid.Column="3" 
                                        Content="➕ إضافة مستخدم" 
                                        Style="{StaticResource ModernButtonStyle}"
                                        Background="#A3BE8C" Click="AddUserButton_Click"/>
                            </Grid>

                            <!-- إحصائيات سريعة -->
                            <Grid Grid.Row="1" Margin="0,0,0,20">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <Border Grid.Column="0" Background="#E8F5E8" CornerRadius="8" Padding="15,10" Margin="0,0,5,0">
                                    <StackPanel>
                                        <TextBlock Text="👥" FontSize="20" HorizontalAlignment="Center"/>
                                        <TextBlock Name="TotalUsersText" Text="0" FontSize="24" FontWeight="Bold" 
                                                   HorizontalAlignment="Center" Foreground="#2E7D32"/>
                                        <TextBlock Text="إجمالي المستخدمين" FontSize="12" HorizontalAlignment="Center" 
                                                   Foreground="#4CAF50"/>
                                    </StackPanel>
                                </Border>

                                <Border Grid.Column="1" Background="#E3F2FD" CornerRadius="8" Padding="15,10" Margin="5,0">
                                    <StackPanel>
                                        <TextBlock Text="✅" FontSize="20" HorizontalAlignment="Center"/>
                                        <TextBlock Name="ActiveUsersText" Text="0" FontSize="24" FontWeight="Bold" 
                                                   HorizontalAlignment="Center" Foreground="#1976D2"/>
                                        <TextBlock Text="المستخدمين النشطين" FontSize="12" HorizontalAlignment="Center" 
                                                   Foreground="#2196F3"/>
                                    </StackPanel>
                                </Border>

                                <Border Grid.Column="2" Background="#FFF3E0" CornerRadius="8" Padding="15,10" Margin="5,0">
                                    <StackPanel>
                                        <TextBlock Text="🔒" FontSize="20" HorizontalAlignment="Center"/>
                                        <TextBlock Name="LockedUsersText" Text="0" FontSize="24" FontWeight="Bold" 
                                                   HorizontalAlignment="Center" Foreground="#F57C00"/>
                                        <TextBlock Text="الحسابات المقفلة" FontSize="12" HorizontalAlignment="Center" 
                                                   Foreground="#FF9800"/>
                                    </StackPanel>
                                </Border>

                                <Border Grid.Column="3" Background="#FFEBEE" CornerRadius="8" Padding="15,10" Margin="5,0,0,0">
                                    <StackPanel>
                                        <TextBlock Text="🚫" FontSize="20" HorizontalAlignment="Center"/>
                                        <TextBlock Name="InactiveUsersText" Text="0" FontSize="24" FontWeight="Bold" 
                                                   HorizontalAlignment="Center" Foreground="#D32F2F"/>
                                        <TextBlock Text="المستخدمين المعطلين" FontSize="12" HorizontalAlignment="Center" 
                                                   Foreground="#F44336"/>
                                    </StackPanel>
                                </Border>
                            </Grid>

                            <!-- جدول المستخدمين -->
                            <DataGrid Name="UsersDataGrid" Grid.Row="2" 
                                      AutoGenerateColumns="False" 
                                      CanUserAddRows="False"
                                      CanUserDeleteRows="False"
                                      IsReadOnly="True"
                                      GridLinesVisibility="None"
                                      HeadersVisibility="Column"
                                      SelectionMode="Single"
                                      AlternatingRowBackground="#F8F9FA"
                                      RowHeight="50"
                                      FontSize="14"
                                      MouseDoubleClick="UsersDataGrid_MouseDoubleClick">
                                
                                <DataGrid.Columns>
                                    <DataGridTemplateColumn Header="الحالة" Width="80">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                    <Ellipse Style="{StaticResource StatusIndicatorStyle}" 
                                                             Fill="{Binding StatusColor}"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>

                                    <DataGridTextColumn Header="اسم المستخدم" Binding="{Binding Username}" Width="150"/>
                                    <DataGridTextColumn Header="الاسم الكامل" Binding="{Binding FullName}" Width="200"/>
                                    <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding Email}" Width="200"/>
                                    <DataGridTextColumn Header="الدور" Binding="{Binding RoleDisplay}" Width="120"/>
                                    <DataGridTextColumn Header="آخر تسجيل دخول" Binding="{Binding LastLoginDisplay}" Width="150"/>

                                    <DataGridTemplateColumn Header="العمليات" Width="200">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                    <Button Content="✏️" ToolTip="تعديل" Margin="2" Padding="8,4" 
                                                            Background="#4CAF50" Foreground="White" BorderThickness="0"
                                                            Click="EditUserButton_Click"/>
                                                    <Button Content="🔑" ToolTip="الصلاحيات" Margin="2" Padding="8,4" 
                                                            Background="#2196F3" Foreground="White" BorderThickness="0"
                                                            Click="ManagePermissionsButton_Click"/>
                                                    <Button Content="📊" ToolTip="الإحصائيات" Margin="2" Padding="8,4" 
                                                            Background="#FF9800" Foreground="White" BorderThickness="0"
                                                            Click="ViewStatsButton_Click"/>
                                                    <Button Content="🗑️" ToolTip="حذف" Margin="2" Padding="8,4" 
                                                            Background="#F44336" Foreground="White" BorderThickness="0"
                                                            Click="DeleteUserButton_Click"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </Border>
                </TabItem>

                <!-- تبويب إدارة الصلاحيات -->
                <TabItem>
                    <TabItem.Header>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔑" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="إدارة الصلاحيات"/>
                        </StackPanel>
                    </TabItem.Header>
                    
                    <Border Style="{StaticResource ModernCardStyle}" Background="White">
                        <TextBlock Text="صفحة إدارة الصلاحيات قيد التطوير..." 
                                   HorizontalAlignment="Center" VerticalAlignment="Center"
                                   FontSize="18" Foreground="#6C7B7F"/>
                    </Border>
                </TabItem>

                <!-- تبويب سجل الأنشطة -->
                <TabItem>
                    <TabItem.Header>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📋" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="سجل الأنشطة"/>
                        </StackPanel>
                    </TabItem.Header>
                    
                    <Border Style="{StaticResource ModernCardStyle}" Background="White">
                        <TextBlock Text="صفحة سجل الأنشطة قيد التطوير..." 
                                   HorizontalAlignment="Center" VerticalAlignment="Center"
                                   FontSize="18" Foreground="#6C7B7F"/>
                    </Border>
                </TabItem>

                <!-- تبويب التقارير والإحصائيات -->
                <TabItem>
                    <TabItem.Header>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📊" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="التقارير والإحصائيات"/>
                        </StackPanel>
                    </TabItem.Header>
                    
                    <Border Style="{StaticResource ModernCardStyle}" Background="White">
                        <TextBlock Text="صفحة التقارير والإحصائيات قيد التطوير..." 
                                   HorizontalAlignment="Center" VerticalAlignment="Center"
                                   FontSize="18" Foreground="#6C7B7F"/>
                    </Border>
                </TabItem>
            </TabControl>
        </Grid>
    </ScrollViewer>
</Page>
