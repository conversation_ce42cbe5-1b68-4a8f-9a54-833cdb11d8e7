<Window x:Class="AvocatPro.Views.Windows.AddRevenueWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة إيراد جديد" 
        Height="650" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">

    <Window.Resources>
        <!-- أنماط النموذج -->
        <Style x:Key="FormLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#333"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>

        <Style x:Key="FormTextBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Style.Triggers>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="BorderBrush" Value="#4CAF50"/>
                    <Setter Property="BorderThickness" Value="2"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="FormComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="FormDatePickerStyle" TargetType="DatePicker">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#4CAF50"/>
            <Setter Property="Margin" Value="0,20,0,15"/>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#388E3C"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#F5F5F5"/>
            <Setter Property="Foreground" Value="#333"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#E0E0E0"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="AttachmentButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5,0"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1976D2"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- رأس النافذة -->
        <Border Grid.Row="0" Background="#4CAF50" Padding="20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="💰" FontSize="24" Foreground="White" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="إضافة إيراد جديد" FontSize="20" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="تسجيل وإدارة إيرادات المكتب مع إرفاق الفواتير والعقود" FontSize="12" Foreground="#E8F5E8"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- محتوى النموذج -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="30">
            <StackPanel>
                <!-- المعلومات الأساسية -->
                <TextBlock Text="📋 المعلومات الأساسية" Style="{StaticResource SectionHeaderStyle}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="رقم المرجع *" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="ReferenceTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="رقم مرجع الإيراد (سيتم إنشاؤه تلقائياً)" IsReadOnly="True"/>

                        <TextBlock Text="وصف الإيراد *" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="DescriptionTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="وصف تفصيلي للإيراد"/>

                        <TextBlock Text="المبلغ (درهم) *" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="AmountTextBox" Style="{StaticResource FormTextBoxStyle}"
                                Text="" ToolTip="مبلغ الإيراد بالدرهم المغربي"/>

                        <TextBlock Text="تاريخ الإيراد *" Style="{StaticResource FormLabelStyle}"/>
                        <DatePicker Name="RevenueDatePicker" Style="{StaticResource FormDatePickerStyle}"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="نوع الإيراد *" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="TypeComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="⚖️ أتعاب محاماة" Tag="LawyerFees" IsSelected="True"/>
                            <ComboBoxItem Content="💼 أتعاب استشارة" Tag="ConsultationFees"/>
                            <ComboBoxItem Content="📝 أتعاب عقود" Tag="ContractFees"/>
                            <ComboBoxItem Content="🤝 أتعاب تحكيم" Tag="ArbitrationFees"/>
                            <ComboBoxItem Content="💰 أخرى" Tag="Other"/>
                        </ComboBox>

                        <TextBlock Text="الموكل *" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="ClientComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="أحمد محمد علي السعيد" Tag="1" IsSelected="True"/>
                            <ComboBoxItem Content="فاطمة أحمد سالم" Tag="2"/>
                            <ComboBoxItem Content="مريم عبدالله الزهراني" Tag="3"/>
                            <ComboBoxItem Content="شركة النور للتجارة والاستثمار" Tag="4"/>
                            <ComboBoxItem Content="جمعية البر الخيرية" Tag="5"/>
                        </ComboBox>

                        <TextBlock Text="القضية المرتبطة" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="CaseComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="بدون قضية" Tag="0" IsSelected="True"/>
                            <ComboBoxItem Content="دعوى مطالبة مالية - CASE-20241201-001" Tag="1"/>
                            <ComboBoxItem Content="قضية عمالية - فصل تعسفي - CASE-20241215-002" Tag="2"/>
                            <ComboBoxItem Content="قضية تجارية - نزاع شراكة - CASE-20241101-003" Tag="3"/>
                            <ComboBoxItem Content="قضية أسرة - نفقة - CASE-20241120-004" Tag="4"/>
                            <ComboBoxItem Content="قضية عقارية - منازعة ملكية - CASE-20241210-005" Tag="5"/>
                            <ComboBoxItem Content="قضية جنائية - اختلاس - CASE-20241205-006" Tag="6"/>
                        </ComboBox>

                        <TextBlock Text="رقم الفاتورة" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="InvoiceNumberTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="رقم الفاتورة المرسلة للموكل"/>
                    </StackPanel>
                </Grid>

                <!-- معلومات الدفع والاستحقاق -->
                <TextBlock Text="💳 معلومات الدفع والاستحقاق" Style="{StaticResource SectionHeaderStyle}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="طريقة الدفع *" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="PaymentMethodComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="🏦 تحويل بنكي" Tag="BankTransfer" IsSelected="True"/>
                            <ComboBoxItem Content="💵 نقدي" Tag="Cash"/>
                            <ComboBoxItem Content="📄 شيك" Tag="Check"/>
                            <ComboBoxItem Content="💳 بطاقة ائتمان" Tag="CreditCard"/>
                            <ComboBoxItem Content="📱 محفظة إلكترونية" Tag="EWallet"/>
                        </ComboBox>

                        <TextBlock Text="حالة الدفع *" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="PaymentStatusComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="⏳ في الانتظار" Tag="Pending" IsSelected="True"/>
                            <ComboBoxItem Content="✅ مدفوع" Tag="Paid"/>
                            <ComboBoxItem Content="🔵 مدفوع جزئياً" Tag="PartiallyPaid"/>
                            <ComboBoxItem Content="❌ متأخر" Tag="Overdue"/>
                            <ComboBoxItem Content="🚫 ملغي" Tag="Cancelled"/>
                        </ComboBox>

                        <TextBlock Text="تاريخ الدفع" Style="{StaticResource FormLabelStyle}"/>
                        <DatePicker Name="PaymentDatePicker" Style="{StaticResource FormDatePickerStyle}"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="تاريخ الاستحقاق" Style="{StaticResource FormLabelStyle}"/>
                        <DatePicker Name="DueDatePicker" Style="{StaticResource FormDatePickerStyle}"/>

                        <TextBlock Text="شروط الدفع" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="PaymentTermsComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="فوري" Tag="0"/>
                            <ComboBoxItem Content="خلال 7 أيام" Tag="7"/>
                            <ComboBoxItem Content="خلال 15 يوم" Tag="15"/>
                            <ComboBoxItem Content="خلال 30 يوم" Tag="30" IsSelected="True"/>
                            <ComboBoxItem Content="خلال 60 يوم" Tag="60"/>
                            <ComboBoxItem Content="خلال 90 يوم" Tag="90"/>
                        </ComboBox>

                        <Button Name="CalculateDueDateButton" Style="{StaticResource AttachmentButtonStyle}" 
                               Background="#FF9800" Click="CalculateDueDateButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📅" FontSize="14" Margin="0,0,5,0"/>
                                <TextBlock Text="حساب تاريخ الاستحقاق"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>

                <!-- المرفقات والوثائق -->
                <TextBlock Text="📎 المرفقات والوثائق" Style="{StaticResource SectionHeaderStyle}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="العقود والفواتير المرفقة" Style="{StaticResource FormLabelStyle}"/>
                        <ListBox Name="AttachmentsListBox" Height="100" BorderBrush="#DDD" BorderThickness="1" 
                                Margin="0,0,0,15" Background="#F9F9F9">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="📄" FontSize="16" Margin="0,0,10,0"/>
                                        <TextBlock Text="{Binding}" FontSize="12"/>
                                        <Button Content="❌" FontSize="10" Margin="10,0,0,0" 
                                               Background="Transparent" BorderThickness="0" 
                                               Click="RemoveAttachmentButton_Click" Tag="{Binding}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </StackPanel>

                    <StackPanel Grid.Column="1" VerticalAlignment="Center" Margin="20,0,0,0">
                        <Button Name="AddAttachmentButton" Style="{StaticResource AttachmentButtonStyle}" 
                               Click="AddAttachmentButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📎" FontSize="14" Margin="0,0,5,0"/>
                                <TextBlock Text="إضافة مرفق"/>
                            </StackPanel>
                        </Button>
                        
                        <Button Name="GenerateInvoiceButton" Style="{StaticResource AttachmentButtonStyle}" 
                               Background="#9C27B0" Click="GenerateInvoiceButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📄" FontSize="14" Margin="0,0,5,0"/>
                                <TextBlock Text="إنشاء فاتورة"/>
                            </StackPanel>
                        </Button>
                        
                        <Button Name="SendInvoiceButton" Style="{StaticResource AttachmentButtonStyle}" 
                               Background="#FF9800" Click="SendInvoiceButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📧" FontSize="14" Margin="0,0,5,0"/>
                                <TextBlock Text="إرسال فاتورة"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>

                <!-- ملاحظات إضافية -->
                <TextBlock Text="📝 ملاحظات إضافية" Style="{StaticResource SectionHeaderStyle}"/>
                <TextBox Name="NotesTextBox" Style="{StaticResource FormTextBoxStyle}" 
                        Height="80" TextWrapping="Wrap" AcceptsReturn="True"
                        Text="" ToolTip="ملاحظات إضافية حول الإيراد"/>
            </StackPanel>
        </ScrollViewer>

        <!-- أزرار الإجراءات -->
        <Border Grid.Row="2" Background="#F8F9FA" Padding="20" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Name="SaveButton" Style="{StaticResource PrimaryButtonStyle}" Click="SaveButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="💾" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ الإيراد"/>
                    </StackPanel>
                </Button>
                
                <Button Name="SaveAndNewButton" Style="{StaticResource PrimaryButtonStyle}" 
                       Background="#2196F3" Click="SaveAndNewButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="➕" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ وإضافة آخر"/>
                    </StackPanel>
                </Button>
                
                <Button Name="SaveAndInvoiceButton" Style="{StaticResource PrimaryButtonStyle}" 
                       Background="#9C27B0" Click="SaveAndInvoiceButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📄" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ وإنشاء فاتورة"/>
                    </StackPanel>
                </Button>
                
                <Button Name="CancelButton" Style="{StaticResource SecondaryButtonStyle}" Click="CancelButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="❌" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="إلغاء"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
