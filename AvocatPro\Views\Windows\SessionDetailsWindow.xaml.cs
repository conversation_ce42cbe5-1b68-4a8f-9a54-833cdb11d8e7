using System;
using System.Windows;
using System.Windows.Media;
using AvocatPro.Models;

namespace AvocatPro.Views.Windows
{
    /// <summary>
    /// نافذة تفاصيل الجلسة
    /// </summary>
    public partial class SessionDetailsWindow : Window
    {
        #region Fields

        private readonly AdvancedSessionModel _session;

        #endregion

        #region Constructor

        public SessionDetailsWindow(AdvancedSessionModel session)
        {
            InitializeComponent();
            
            _session = session ?? throw new ArgumentNullException(nameof(session));
            
            LoadSessionDetails();
        }

        #endregion

        #region Methods

        private void LoadSessionDetails()
        {
            // تحديث العنوان
            SessionTitleText.Text = $"تفاصيل الجلسة رقم {_session.SessionNumber}";
            SessionSubtitleText.Text = $"ملف رقم {_session.FileNumber} - {_session.Client}";

            // تحديث حالة الجلسة
            UpdateStatusDisplay();

            // تحميل المعلومات الأساسية
            SessionNumberValue.Text = _session.SessionNumber;
            FileNumberValue.Text = _session.FileNumber;
            ClientValue.Text = _session.Client;
            CaseTypeValue.Text = string.IsNullOrEmpty(_session.CaseType) ? "غير محدد" : _session.CaseType;
            CourtValue.Text = _session.Court;

            // تحميل تفاصيل الجلسة
            SessionDateValue.Text = _session.SessionDate.ToString("dddd، dd MMMM yyyy", new System.Globalization.CultureInfo("ar-SA"));
            SessionTimeValue.Text = _session.SessionTimeText;
            SessionTypeValue.Text = _session.SessionType;
            AssignedLawyerValue.Text = _session.AssignedLawyer;
            JudgeValue.Text = string.IsNullOrEmpty(_session.Judge) ? "غير محدد" : _session.Judge;
            CourtRoomValue.Text = string.IsNullOrEmpty(_session.CourtRoom) ? "غير محدد" : _session.CourtRoom;

            // تحميل الأولوية والمصروفات
            UpdatePriorityDisplay();
            ExpensesValue.Text = _session.ExpensesText;

            // تحميل الإجراءات والقرارات
            ProcedureTypeValue.Text = string.IsNullOrEmpty(_session.ProcedureType) ? "غير محدد" : _session.ProcedureType;
            ProcedureDateValue.Text = _session.ProcedureDate.ToString("dd/MM/yyyy");
            DecisionValue.Text = string.IsNullOrEmpty(_session.Decision) ? "لم يتم اتخاذ قرار بعد" : _session.Decision;
            OutcomeValue.Text = string.IsNullOrEmpty(_session.Outcome) ? "لم تكتمل الجلسة بعد" : _session.Outcome;

            // تحميل الجلسة المقبلة
            NextSessionValue.Text = _session.NextSessionText;
            UpdateReminderStatus();

            // تحميل الملاحظات والوثائق
            DocumentsValue.Text = string.IsNullOrEmpty(_session.Documents) ? "لا توجد وثائق محددة" : _session.Documents;
            NotesValue.Text = string.IsNullOrEmpty(_session.Notes) ? "لا توجد ملاحظات" : _session.Notes;
        }

        private void UpdateStatusDisplay()
        {
            StatusIcon.Text = _session.StatusIcon;
            StatusText.Text = _session.Status;
            
            var statusColor = GetColorFromHex(_session.StatusColor);
            StatusBorder.Background = new SolidColorBrush(statusColor);
        }

        private void UpdatePriorityDisplay()
        {
            PriorityIcon.Text = _session.PriorityIcon;
            PriorityValue.Text = _session.Priority;
            
            var priorityColor = GetColorFromHex(_session.PriorityColor);
            PriorityBorder.Background = new SolidColorBrush(priorityColor);
        }

        private void UpdateReminderStatus()
        {
            if (_session.ReminderSent)
            {
                ReminderIcon.Text = "✅";
                ReminderStatusValue.Text = "تم إرسال التذكير";
                ReminderStatusValue.Foreground = new SolidColorBrush(Color.FromRgb(16, 185, 129)); // Green
            }
            else
            {
                ReminderIcon.Text = "⏰";
                ReminderStatusValue.Text = "لم يتم إرسال التذكير";
                ReminderStatusValue.Foreground = new SolidColorBrush(Color.FromRgb(245, 158, 11)); // Orange
            }
        }

        private Color GetColorFromHex(string hexColor)
        {
            try
            {
                if (hexColor.StartsWith("#"))
                {
                    hexColor = hexColor.Substring(1);
                }

                if (hexColor.Length == 6)
                {
                    var r = Convert.ToByte(hexColor.Substring(0, 2), 16);
                    var g = Convert.ToByte(hexColor.Substring(2, 2), 16);
                    var b = Convert.ToByte(hexColor.Substring(4, 2), 16);
                    return Color.FromRgb(r, g, b);
                }
            }
            catch
            {
                // في حالة فشل التحويل، استخدم لون افتراضي
            }

            return Color.FromRgb(107, 114, 128); // Gray default
        }

        #endregion

        #region Event Handlers

        private void EditButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var editWindow = new AdvancedAddSessionWindow(_session);
                if (editWindow.ShowDialog() == true)
                {
                    // تحديث البيانات المعروضة
                    var updatedSession = editWindow.NewSession;
                    
                    // نسخ البيانات المحدثة
                    _session.SessionNumber = updatedSession.SessionNumber;
                    _session.FileNumber = updatedSession.FileNumber;
                    _session.Client = updatedSession.Client;
                    _session.CaseType = updatedSession.CaseType;
                    _session.Court = updatedSession.Court;
                    _session.ProcedureType = updatedSession.ProcedureType;
                    _session.ProcedureDate = updatedSession.ProcedureDate;
                    _session.Decision = updatedSession.Decision;
                    _session.SessionDate = updatedSession.SessionDate;
                    _session.SessionTime = updatedSession.SessionTime;
                    _session.SessionType = updatedSession.SessionType;
                    _session.AssignedLawyer = updatedSession.AssignedLawyer;
                    _session.Judge = updatedSession.Judge;
                    _session.Clerk = updatedSession.Clerk;
                    _session.CourtRoom = updatedSession.CourtRoom;
                    _session.Status = updatedSession.Status;
                    _session.Priority = updatedSession.Priority;
                    _session.Expenses = updatedSession.Expenses;
                    _session.Documents = updatedSession.Documents;
                    _session.Notes = updatedSession.Notes;
                    _session.Outcome = updatedSession.Outcome;
                    _session.NextSessionDate = updatedSession.NextSessionDate;
                    _session.NextSessionTime = updatedSession.NextSessionTime;
                    _session.ReminderSent = updatedSession.ReminderSent;
                    _session.LastUpdated = DateTime.Now;

                    // إعادة تحميل التفاصيل
                    LoadSessionDetails();

                    MessageBox.Show("تم تحديث بيانات الجلسة بنجاح!", "تحديث ناجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث الجلسة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // هنا يمكن إضافة كود الطباعة
                // يمكن إنشاء تقرير PDF أو طباعة مباشرة
                
                MessageBox.Show($"تم إرسال تفاصيل الجلسة رقم {_session.SessionNumber} للطباعة!", 
                    "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        #endregion
    }
}
