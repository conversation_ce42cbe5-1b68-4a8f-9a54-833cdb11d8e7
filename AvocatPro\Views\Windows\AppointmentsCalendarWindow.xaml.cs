using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using AvocatPro.Models;

namespace AvocatPro.Views.Windows
{
    /// <summary>
    /// تقويم المواعيد المتقدم
    /// </summary>
    public partial class AppointmentsCalendarWindow : Window
    {
        #region Fields

        private List<AdvancedAppointmentModel> _appointments;
        private DateTime _currentMonth;
        private DateTime? _selectedDate;
        private readonly string[] _monthNames = {
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        };

        #endregion

        #region Constructor

        public AppointmentsCalendarWindow(List<AdvancedAppointmentModel> appointments)
        {
            InitializeComponent();
            
            _appointments = appointments ?? new List<AdvancedAppointmentModel>();
            _currentMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            
            UpdateCalendar();
        }

        #endregion

        #region Calendar Generation

        private void UpdateCalendar()
        {
            try
            {
                // تحديث عنوان الشهر
                MonthYearTitle.Text = $"{_monthNames[_currentMonth.Month - 1]} {_currentMonth.Year}";
                
                // تحديث إحصائيات الشهر
                var monthAppointments = GetAppointmentsForMonth(_currentMonth);
                MonthStatsText.Text = $"{monthAppointments.Count} موعد هذا الشهر";

                // مسح التقويم الحالي
                CalendarGrid.Children.Clear();
                CalendarGrid.RowDefinitions.Clear();
                CalendarGrid.ColumnDefinitions.Clear();

                // إنشاء أعمدة التقويم (7 أيام)
                for (int i = 0; i < 7; i++)
                {
                    CalendarGrid.ColumnDefinitions.Add(new ColumnDefinition());
                }

                // الحصول على أول يوم في الشهر وآخر يوم
                var firstDayOfMonth = _currentMonth;
                var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);
                
                // الحصول على يوم الأسبوع لأول يوم في الشهر (الأحد = 0)
                var startDayOfWeek = (int)firstDayOfMonth.DayOfWeek;
                
                // حساب عدد الأسابيع المطلوبة
                var daysInMonth = DateTime.DaysInMonth(_currentMonth.Year, _currentMonth.Month);
                var totalCells = startDayOfWeek + daysInMonth;
                var weeksNeeded = (int)Math.Ceiling(totalCells / 7.0);

                // إنشاء صفوف التقويم
                for (int i = 0; i < weeksNeeded; i++)
                {
                    CalendarGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
                }

                // إضافة أيام الشهر السابق (إذا لزم الأمر)
                var previousMonth = _currentMonth.AddMonths(-1);
                var daysInPreviousMonth = DateTime.DaysInMonth(previousMonth.Year, previousMonth.Month);
                
                for (int i = startDayOfWeek - 1; i >= 0; i--)
                {
                    var day = daysInPreviousMonth - i;
                    var date = new DateTime(previousMonth.Year, previousMonth.Month, day);
                    CreateDayButton(date, false);
                }
                
                // إضافة أيام الشهر الحالي
                for (int day = 1; day <= DateTime.DaysInMonth(_currentMonth.Year, _currentMonth.Month); day++)
                {
                    var date = new DateTime(_currentMonth.Year, _currentMonth.Month, day);
                    CreateDayButton(date, true);
                }
                
                // إضافة أيام الشهر التالي (لملء الصفوف المتبقية)
                var nextMonth = _currentMonth.AddMonths(1);
                var cellsUsed = startDayOfWeek + daysInMonth;
                var remainingCells = (weeksNeeded * 7) - cellsUsed;
                
                for (int day = 1; day <= remainingCells; day++)
                {
                    var date = new DateTime(nextMonth.Year, nextMonth.Month, day);
                    CreateDayButton(date, false);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث التقويم: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CreateDayButton(DateTime date, bool isCurrentMonth)
        {
            try
            {
                var dayAppointments = GetAppointmentsForDay(date);
                var isToday = date.Date == DateTime.Today;
                var isSelected = _selectedDate?.Date == date.Date;

                // إنشاء الحاوية الرئيسية
                var dayBorder = new Border
                {
                    Style = isToday ? (Style)FindResource("TodayDayStyle") : (Style)FindResource("CalendarDayStyle"),
                    MinHeight = 100,
                    Opacity = isCurrentMonth ? 1.0 : 0.4
                };

                if (isSelected)
                {
                    dayBorder.Background = new SolidColorBrush(Color.FromRgb(238, 242, 255));
                    dayBorder.BorderBrush = new SolidColorBrush(Color.FromRgb(99, 102, 241));
                    dayBorder.BorderThickness = new Thickness(2);
                }

                // إنشاء المحتوى الداخلي
                var stackPanel = new StackPanel
                {
                    Margin = new Thickness(5)
                };

                // رقم اليوم
                var dayNumber = new TextBlock
                {
                    Text = date.Day.ToString(),
                    FontSize = 14,
                    FontWeight = FontWeights.Bold,
                    Foreground = isToday ? new SolidColorBrush(Color.FromRgb(99, 102, 241)) : 
                                isCurrentMonth ? new SolidColorBrush(Color.FromRgb(31, 41, 55)) : 
                                new SolidColorBrush(Color.FromRgb(156, 163, 175)),
                    HorizontalAlignment = HorizontalAlignment.Left
                };

                stackPanel.Children.Add(dayNumber);

                // إضافة المواعيد
                foreach (var appointment in dayAppointments.Take(3)) // عرض أول 3 مواعيد فقط
                {
                    var appointmentBorder = new Border
                    {
                        Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(appointment.StatusColor)),
                        CornerRadius = new CornerRadius(3),
                        Padding = new Thickness(4, 2, 4, 2),
                        Margin = new Thickness(0, 1, 0, 1),
                        Cursor = System.Windows.Input.Cursors.Hand
                    };

                    var appointmentText = new TextBlock
                    {
                        Text = appointment.Title.Length > 15 ? appointment.Title.Substring(0, 12) + "..." : appointment.Title,
                        FontSize = 10,
                        Foreground = Brushes.White,
                        TextWrapping = TextWrapping.NoWrap
                    };

                    appointmentBorder.Child = appointmentText;
                    
                    // إضافة حدث النقر على الموعد
                    appointmentBorder.MouseLeftButtonDown += (s, e) =>
                    {
                        ShowAppointmentDetails(appointment);
                        e.Handled = true;
                    };

                    stackPanel.Children.Add(appointmentBorder);
                }

                // إضافة مؤشر للمواعيد الإضافية
                if (dayAppointments.Count > 3)
                {
                    var moreText = new TextBlock
                    {
                        Text = $"+{dayAppointments.Count - 3} أخرى",
                        FontSize = 9,
                        Foreground = new SolidColorBrush(Color.FromRgb(107, 114, 128)),
                        Margin = new Thickness(0, 2, 0, 0)
                    };
                    stackPanel.Children.Add(moreText);
                }

                dayBorder.Child = stackPanel;

                // إضافة حدث النقر على اليوم
                dayBorder.MouseLeftButtonDown += (s, e) =>
                {
                    SelectDate(date);
                };

                // تحديد موقع اليوم في الشبكة
                var totalDays = (date - _currentMonth.AddDays(-((int)_currentMonth.DayOfWeek))).Days;
                var row = totalDays / 7;
                var column = totalDays % 7;

                Grid.SetRow(dayBorder, row);
                Grid.SetColumn(dayBorder, column);

                CalendarGrid.Children.Add(dayBorder);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء زر اليوم: {ex.Message}");
            }
        }

        #endregion

        #region Helper Methods

        private List<AdvancedAppointmentModel> GetAppointmentsForMonth(DateTime month)
        {
            return _appointments.Where(a => a.AppointmentDate.Year == month.Year && 
                                          a.AppointmentDate.Month == month.Month).ToList();
        }

        private List<AdvancedAppointmentModel> GetAppointmentsForDay(DateTime date)
        {
            return _appointments.Where(a => a.AppointmentDate.Date == date.Date)
                               .OrderBy(a => a.AppointmentTime)
                               .ToList();
        }

        private void SelectDate(DateTime date)
        {
            try
            {
                _selectedDate = date;
                UpdateCalendar();
                UpdateSelectedDateDisplay();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديد التاريخ: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateSelectedDateDisplay()
        {
            try
            {
                if (_selectedDate == null)
                {
                    SelectedDateText.Text = "اختر يوماً لعرض المواعيد";
                    DayAppointmentsPanel.Children.Clear();
                    return;
                }

                var dayAppointments = GetAppointmentsForDay(_selectedDate.Value);
                var culture = new CultureInfo("ar-SA");
                
                SelectedDateText.Text = $"مواعيد يوم {_selectedDate.Value.ToString("dddd، dd MMMM yyyy", culture)} ({dayAppointments.Count} موعد)";

                // مسح المواعيد السابقة
                DayAppointmentsPanel.Children.Clear();

                // إضافة مواعيد اليوم
                foreach (var appointment in dayAppointments)
                {
                    var appointmentCard = CreateAppointmentCard(appointment);
                    DayAppointmentsPanel.Children.Add(appointmentCard);
                }

                if (!dayAppointments.Any())
                {
                    var noAppointmentsText = new TextBlock
                    {
                        Text = "لا توجد مواعيد في هذا اليوم",
                        FontSize = 14,
                        Foreground = new SolidColorBrush(Color.FromRgb(107, 114, 128)),
                        Margin = new Thickness(20, 10, 20, 10)
                    };
                    DayAppointmentsPanel.Children.Add(noAppointmentsText);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث عرض التاريخ المحدد: {ex.Message}");
            }
        }

        private Border CreateAppointmentCard(AdvancedAppointmentModel appointment)
        {
            var card = new Border
            {
                Background = Brushes.White,
                BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString(appointment.StatusColor)),
                BorderThickness = new Thickness(0, 0, 0, 3),
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(15),
                Margin = new Thickness(10, 0, 10, 0),
                MinWidth = 250,
                Cursor = System.Windows.Input.Cursors.Hand
            };

            card.Effect = new System.Windows.Media.Effects.DropShadowEffect
            {
                Color = Color.FromRgb(229, 231, 235),
                Direction = 270,
                ShadowDepth = 2,
                BlurRadius = 8,
                Opacity = 0.3
            };

            var stackPanel = new StackPanel();

            // عنوان الموعد
            var titleText = new TextBlock
            {
                Text = appointment.Title,
                FontSize = 14,
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush(Color.FromRgb(31, 41, 55)),
                TextWrapping = TextWrapping.Wrap
            };
            stackPanel.Children.Add(titleText);

            // وقت الموعد
            var timeText = new TextBlock
            {
                Text = $"🕐 {appointment.AppointmentTimeText} - {appointment.EndTime.ToString("HH:mm")}",
                FontSize = 12,
                Foreground = new SolidColorBrush(Color.FromRgb(107, 114, 128)),
                Margin = new Thickness(0, 5, 0, 0)
            };
            stackPanel.Children.Add(timeText);

            // اسم الموكل
            if (!string.IsNullOrWhiteSpace(appointment.ClientName))
            {
                var clientText = new TextBlock
                {
                    Text = $"👤 {appointment.ClientName}",
                    FontSize = 12,
                    Foreground = new SolidColorBrush(Color.FromRgb(107, 114, 128)),
                    Margin = new Thickness(0, 2, 0, 0)
                };
                stackPanel.Children.Add(clientText);
            }

            // حالة الموعد
            var statusBorder = new Border
            {
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(appointment.StatusColor)),
                CornerRadius = new CornerRadius(12),
                Padding = new Thickness(8, 4, 8, 4),
                Margin = new Thickness(0, 8, 0, 0),
                HorizontalAlignment = HorizontalAlignment.Left
            };

            var statusText = new TextBlock
            {
                Text = appointment.Status,
                FontSize = 10,
                FontWeight = FontWeights.SemiBold,
                Foreground = Brushes.White
            };

            statusBorder.Child = statusText;
            stackPanel.Children.Add(statusBorder);

            card.Child = stackPanel;

            // إضافة حدث النقر
            card.MouseLeftButtonDown += (s, e) =>
            {
                ShowAppointmentDetails(appointment);
            };

            return card;
        }

        private void ShowAppointmentDetails(AdvancedAppointmentModel appointment)
        {
            try
            {
                var details = $"تفاصيل الموعد:\n\n" +
                             $"العنوان: {appointment.Title}\n" +
                             $"التاريخ: {appointment.AppointmentDate:dd/MM/yyyy}\n" +
                             $"الوقت: {appointment.AppointmentTimeText}\n" +
                             $"المدة: {appointment.DurationText}\n" +
                             $"النوع: {appointment.AppointmentType}\n" +
                             $"الموكل: {appointment.ClientName}\n" +
                             $"المحامي: {appointment.AssignedLawyer}\n" +
                             $"الحالة: {appointment.Status}\n" +
                             $"الأولوية: {appointment.Priority}";

                if (!string.IsNullOrWhiteSpace(appointment.Notes))
                {
                    details += $"\n\nملاحظات: {appointment.Notes}";
                }

                MessageBox.Show(details, "تفاصيل الموعد", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض تفاصيل الموعد: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region Event Handlers

        private void PreviousMonthButton_Click(object sender, RoutedEventArgs e)
        {
            _currentMonth = _currentMonth.AddMonths(-1);
            UpdateCalendar();
        }

        private void NextMonthButton_Click(object sender, RoutedEventArgs e)
        {
            _currentMonth = _currentMonth.AddMonths(1);
            UpdateCalendar();
        }

        private void TodayButton_Click(object sender, RoutedEventArgs e)
        {
            _currentMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            SelectDate(DateTime.Today);
        }

        private void AddAppointmentButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة إضافة موعد جديد", "إضافة موعد", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ViewModeButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        #endregion
    }
}
