<Page x:Class="AvocatPro.Views.Pages.BasicClientsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="إدارة الموكلين"
      Background="White">

    <Grid>
        <StackPanel Margin="20">
            <TextBlock Text="إدارة الموكلين" FontSize="24" FontWeight="Bold" 
                       HorizontalAlignment="Center" Margin="0,0,0,20"/>
            
            <Button Content="إضافة موكل جديد" Width="150" Height="40" 
                    Click="AddClient_Click" Margin="0,0,0,20"/>
            
            <ListBox x:Name="ClientsListBox" Height="300">
                <ListBox.ItemTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal" Margin="5">
                            <TextBlock Text="{Binding Name}" Width="150" FontWeight="Bold"/>
                            <TextBlock Text="{Binding ClientType}" Width="80"/>
                            <TextBlock Text="{Binding Phone}" Width="120"/>
                            <TextBlock Text="{Binding Status}" Width="80"/>
                        </StackPanel>
                    </DataTemplate>
                </ListBox.ItemTemplate>
            </ListBox>
            
            <TextBlock x:Name="StatusText" Text="جاري تحميل البيانات..." 
                       HorizontalAlignment="Center" Margin="0,10,0,0"/>
        </StackPanel>
    </Grid>
</Page>
