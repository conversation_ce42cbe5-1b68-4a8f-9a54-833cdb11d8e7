<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Google Services Configuration
    |--------------------------------------------------------------------------
    */
    'google' => [
        'client_id' => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
        'redirect' => env('GOOGLE_REDIRECT_URI'),
        'calendar_api_url' => 'https://www.googleapis.com/calendar/v3',
        'scopes' => [
            'https://www.googleapis.com/auth/calendar',
            'https://www.googleapis.com/auth/calendar.events',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Microsoft Services Configuration
    |--------------------------------------------------------------------------
    */
    'microsoft' => [
        'client_id' => env('MICROSOFT_CLIENT_ID'),
        'client_secret' => env('MICROSOFT_CLIENT_SECRET'),
        'redirect' => env('MICROSOFT_REDIRECT_URI'),
        'graph_api_url' => 'https://graph.microsoft.com/v1.0',
        'scopes' => [
            'https://graph.microsoft.com/calendars.readwrite',
            'https://graph.microsoft.com/user.read',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | WhatsApp Business API Configuration
    |--------------------------------------------------------------------------
    */
    'whatsapp' => [
        'token' => env('WHATSAPP_TOKEN'),
        'phone_number_id' => env('WHATSAPP_PHONE_NUMBER_ID'),
        'webhook_verify_token' => env('WHATSAPP_WEBHOOK_VERIFY_TOKEN'),
        'webhook_url' => env('WHATSAPP_WEBHOOK_URL'),
        'api_url' => 'https://graph.facebook.com/v18.0',
        'max_retries' => 3,
        'retry_delay' => 5, // seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Moroccan Government APIs Configuration
    |--------------------------------------------------------------------------
    */
    'morocco_justice' => [
        'api_key' => env('MOROCCO_JUSTICE_API_KEY'),
        'api_url' => env('MOROCCO_JUSTICE_API_URL', 'https://api.justice.gov.ma'),
        'timeout' => 30,
        'verify_ssl' => true,
    ],

    'morocco_interior' => [
        'api_key' => env('MOROCCO_INTERIOR_API_KEY'),
        'api_url' => env('MOROCCO_INTERIOR_API_URL', 'https://api.interior.gov.ma'),
        'timeout' => 30,
        'verify_ssl' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Moroccan Courts Integration (mahakim.ma)
    |--------------------------------------------------------------------------
    */
    'mahakim' => [
        'api_key' => env('MAHAKIM_API_KEY'),
        'api_url' => env('MAHAKIM_API_URL', 'https://api.mahakim.ma'),
        'certificate_path' => env('MAHAKIM_CERTIFICATE_PATH'),
        'private_key_path' => env('MAHAKIM_PRIVATE_KEY_PATH'),
        'timeout' => 60,
        'verify_ssl' => true,
        'cache_duration' => 3600, // 1 hour
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Gateways Configuration
    |--------------------------------------------------------------------------
    */
    'stripe' => [
        'key' => env('STRIPE_KEY'),
        'secret' => env('STRIPE_SECRET'),
        'webhook_secret' => env('STRIPE_WEBHOOK_SECRET'),
        'currency' => 'mad',
    ],

    /*
    |--------------------------------------------------------------------------
    | SMS Services Configuration
    |--------------------------------------------------------------------------
    */
    'twilio' => [
        'sid' => env('TWILIO_SID'),
        'token' => env('TWILIO_TOKEN'),
        'from' => env('TWILIO_FROM'),
    ],

    /*
    |--------------------------------------------------------------------------
    | File Storage Configuration
    |--------------------------------------------------------------------------
    */
    'aws' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
        'bucket' => env('AWS_BUCKET'),
        'url' => env('AWS_URL'),
        'endpoint' => env('AWS_ENDPOINT'),
        'use_path_style_endpoint' => env('AWS_USE_PATH_STYLE_ENDPOINT', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Moroccan Specific Services
    |--------------------------------------------------------------------------
    */
    'moroccan_holidays' => [
        'api_url' => env('MOROCCAN_HOLIDAYS_API', 'https://api.holidays.ma'),
        'cache_duration' => 86400, // 24 hours
    ],

    'court_fees' => [
        'api_url' => env('COURT_FEES_API', 'https://api.justice.gov.ma/fees'),
        'update_interval' => env('COURT_FEES_UPDATE_INTERVAL', 'daily'),
        'cache_duration' => 43200, // 12 hours
    ],

    /*
    |--------------------------------------------------------------------------
    | Document Processing Services
    |--------------------------------------------------------------------------
    */
    'pdf_generator' => [
        'engine' => env('PDF_ENGINE', 'dompdf'),
        'options' => [
            'enable_remote' => env('DOMPDF_ENABLE_REMOTE', true),
            'enable_css_float' => env('DOMPDF_ENABLE_CSS_FLOAT', true),
            'enable_html5_parser' => env('DOMPDF_ENABLE_HTML5_PARSER', true),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Services
    |--------------------------------------------------------------------------
    */
    'notifications' => [
        'email' => [
            'enabled' => env('ENABLE_EMAIL_NOTIFICATIONS', true),
            'queue' => env('NOTIFICATION_QUEUE', 'default'),
        ],
        'sms' => [
            'enabled' => env('ENABLE_SMS_NOTIFICATIONS', true),
            'provider' => env('SMS_PROVIDER', 'twilio'),
        ],
        'whatsapp' => [
            'enabled' => env('ENABLE_WHATSAPP_NOTIFICATIONS', true),
            'auto_reply' => env('WHATSAPP_AUTO_REPLY', true),
        ],
        'push' => [
            'enabled' => env('ENABLE_PUSH_NOTIFICATIONS', true),
            'provider' => env('PUSH_PROVIDER', 'pusher'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup Services
    |--------------------------------------------------------------------------
    */
    'backup' => [
        'disk' => env('BACKUP_DISK', 's3'),
        'notification_mail' => env('BACKUP_NOTIFICATION_MAIL'),
        'schedule' => env('BACKUP_SCHEDULE', 'daily'),
        'retention_days' => env('BACKUP_RETENTION_DAYS', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring and Analytics
    |--------------------------------------------------------------------------
    */
    'sentry' => [
        'dsn' => env('SENTRY_LARAVEL_DSN'),
        'traces_sample_rate' => env('SENTRY_TRACES_SAMPLE_RATE', 1.0),
    ],

    /*
    |--------------------------------------------------------------------------
    | Legal Document Templates
    |--------------------------------------------------------------------------
    */
    'templates' => [
        'storage_path' => env('TEMPLATE_STORAGE_PATH', 'storage/templates'),
        'contract_path' => env('CONTRACT_TEMPLATE_PATH', 'storage/templates/contracts'),
        'forms_path' => env('LEGAL_FORMS_PATH', 'storage/templates/forms'),
        'supported_formats' => ['docx', 'pdf', 'html'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Application Specific Settings
    |--------------------------------------------------------------------------
    */
    'app_settings' => [
        'default_currency' => env('DEFAULT_CURRENCY', 'MAD'),
        'default_language' => env('APP_LOCALE', 'ar'),
        'supported_languages' => explode(',', env('SUPPORTED_LANGUAGES', 'ar,fr,en')),
        'timezone_detection' => env('TIMEZONE_DETECTION', true),
        'max_file_upload_size' => env('MAX_FILE_UPLOAD_SIZE', 10240), // KB
        'enable_registration' => env('ENABLE_REGISTRATION', true),
        'enable_email_verification' => env('ENABLE_EMAIL_VERIFICATION', true),
        'enable_two_factor_auth' => env('ENABLE_TWO_FACTOR_AUTH', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance and Caching
    |--------------------------------------------------------------------------
    */
    'cache' => [
        'ttl' => env('CACHE_TTL', 3600),
        'session_ttl' => env('SESSION_CACHE_TTL', 7200),
        'api_ttl' => env('API_CACHE_TTL', 1800),
        'query_cache_enabled' => env('QUERY_CACHE_ENABLED', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    */
    'rate_limiting' => [
        'api_limit' => env('API_RATE_LIMIT', 60),
        'api_window' => env('API_RATE_LIMIT_WINDOW', 1),
        'webhook_limit' => 100,
        'webhook_window' => 1,
    ],

];
