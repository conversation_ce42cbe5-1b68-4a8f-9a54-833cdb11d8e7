﻿#pragma checksum "..\..\..\..\..\Views\Pages\ReportsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "71A3CB8574DC2B1177C233F31001CEE59D3AE906"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Pages {
    
    
    /// <summary>
    /// ReportsPage
    /// </summary>
    public partial class ReportsPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 138 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PeriodFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CustomReportButton;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintDashboardButton;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalRevenuesKPI;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RevenuesTrendIcon;
        
        #line default
        #line hidden
        
        
        #line 205 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RevenuesTrendText;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalExpensesKPI;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ExpensesTrendIcon;
        
        #line default
        #line hidden
        
        
        #line 220 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ExpensesTrendText;
        
        #line default
        #line hidden
        
        
        #line 232 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NetProfitKPI;
        
        #line default
        #line hidden
        
        
        #line 234 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProfitTrendIcon;
        
        #line default
        #line hidden
        
        
        #line 235 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProfitTrendText;
        
        #line default
        #line hidden
        
        
        #line 247 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalCasesKPI;
        
        #line default
        #line hidden
        
        
        #line 249 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CasesTrendIcon;
        
        #line default
        #line hidden
        
        
        #line 250 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CasesTrendText;
        
        #line default
        #line hidden
        
        
        #line 262 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalClientsKPI;
        
        #line default
        #line hidden
        
        
        #line 264 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ClientsTrendIcon;
        
        #line default
        #line hidden
        
        
        #line 265 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ClientsTrendText;
        
        #line default
        #line hidden
        
        
        #line 277 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SuccessRateKPI;
        
        #line default
        #line hidden
        
        
        #line 279 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SuccessTrendIcon;
        
        #line default
        #line hidden
        
        
        #line 280 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SuccessTrendText;
        
        #line default
        #line hidden
        
        
        #line 310 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer FinancialTrendsChart;
        
        #line default
        #line hidden
        
        
        #line 311 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel FinancialTrendsChartPanel;
        
        #line default
        #line hidden
        
        
        #line 320 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer CasesPerformanceChart;
        
        #line default
        #line hidden
        
        
        #line 321 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel CasesPerformanceChartPanel;
        
        #line default
        #line hidden
        
        
        #line 330 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer ClientsDistributionChart;
        
        #line default
        #line hidden
        
        
        #line 331 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ClientsDistributionChartPanel;
        
        #line default
        #line hidden
        
        
        #line 343 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer AlertsScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 344 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AlertsPanel;
        
        #line default
        #line hidden
        
        
        #line 353 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel QuickStatsPanel;
        
        #line default
        #line hidden
        
        
        #line 361 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PerformanceIndicatorsPanel;
        
        #line default
        #line hidden
        
        
        #line 379 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DetailedReportsPanel;
        
        #line default
        #line hidden
        
        
        #line 395 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ComparisonsPanel;
        
        #line default
        #line hidden
        
        
        #line 411 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ForecastsPanel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/pages/reportspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PeriodFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 139 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
            this.PeriodFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PeriodFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 150 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 158 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.CustomReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 166 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
            this.CustomReportButton.Click += new System.Windows.RoutedEventHandler(this.CustomReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.PrintDashboardButton = ((System.Windows.Controls.Button)(target));
            
            #line 174 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
            this.PrintDashboardButton.Click += new System.Windows.RoutedEventHandler(this.PrintDashboardButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.TotalRevenuesKPI = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.RevenuesTrendIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.RevenuesTrendText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.TotalExpensesKPI = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.ExpensesTrendIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.ExpensesTrendText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.NetProfitKPI = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.ProfitTrendIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.ProfitTrendText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.TotalCasesKPI = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.CasesTrendIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.CasesTrendText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.TotalClientsKPI = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.ClientsTrendIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.ClientsTrendText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.SuccessRateKPI = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.SuccessTrendIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.SuccessTrendText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.FinancialTrendsChart = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 25:
            this.FinancialTrendsChartPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 26:
            this.CasesPerformanceChart = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 27:
            this.CasesPerformanceChartPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 28:
            this.ClientsDistributionChart = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 29:
            this.ClientsDistributionChartPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 30:
            this.AlertsScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 31:
            this.AlertsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 32:
            this.QuickStatsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 33:
            this.PerformanceIndicatorsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 34:
            this.DetailedReportsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 35:
            this.ComparisonsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 36:
            this.ForecastsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

