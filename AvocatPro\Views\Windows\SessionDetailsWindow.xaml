<Window x:Class="AvocatPro.Views.Windows.SessionDetailsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تفاصيل الجلسة" Height="600" Width="800"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize"
        Background="#F8FAFC" FlowDirection="RightToLeft">

    <Window.Resources>
        <Style TargetType="TextBlock" x:Key="LabelStyle">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#374151"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>

        <Style TargetType="TextBlock" x:Key="ValueStyle">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#1F2937"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>

        <Style TargetType="Button" x:Key="ActionButtonStyle">
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="8" Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Background="White" CornerRadius="12" Padding="24" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock x:Name="SessionTitleText" Text="تفاصيل الجلسة" FontSize="24" FontWeight="Bold" Foreground="#1F2937"/>
                    <TextBlock x:Name="SessionSubtitleText" Text="معلومات شاملة عن الجلسة" FontSize="14" Foreground="#6B7280"/>
                </StackPanel>
                
                <Border x:Name="StatusBorder" Grid.Column="1" CornerRadius="20" Padding="16,8">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock x:Name="StatusIcon" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock x:Name="StatusText" FontSize="14" FontWeight="SemiBold" Foreground="White"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- العمود الأول -->
                <StackPanel Grid.Column="0">
                    
                    <!-- المعلومات الأساسية -->
                    <Border Background="White" CornerRadius="12" Padding="20" Margin="0,0,0,20">
                        <Border.Effect>
                            <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                        </Border.Effect>
                        
                        <StackPanel>
                            <TextBlock Text="📋 المعلومات الأساسية" FontSize="16" FontWeight="Bold" 
                                       Foreground="#1F2937" Margin="0,0,0,15"/>
                            
                            <TextBlock Text="رقم الجلسة:" Style="{StaticResource LabelStyle}"/>
                            <TextBlock x:Name="SessionNumberValue" Style="{StaticResource ValueStyle}"/>
                            
                            <TextBlock Text="رقم الملف:" Style="{StaticResource LabelStyle}"/>
                            <TextBlock x:Name="FileNumberValue" Style="{StaticResource ValueStyle}"/>
                            
                            <TextBlock Text="الموكل:" Style="{StaticResource LabelStyle}"/>
                            <TextBlock x:Name="ClientValue" Style="{StaticResource ValueStyle}"/>
                            
                            <TextBlock Text="نوع القضية:" Style="{StaticResource LabelStyle}"/>
                            <TextBlock x:Name="CaseTypeValue" Style="{StaticResource ValueStyle}"/>
                            
                            <TextBlock Text="المحكمة:" Style="{StaticResource LabelStyle}"/>
                            <TextBlock x:Name="CourtValue" Style="{StaticResource ValueStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- تفاصيل الجلسة -->
                    <Border Background="White" CornerRadius="12" Padding="20" Margin="0,0,0,20">
                        <Border.Effect>
                            <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                        </Border.Effect>
                        
                        <StackPanel>
                            <TextBlock Text="⚖️ تفاصيل الجلسة" FontSize="16" FontWeight="Bold" 
                                       Foreground="#1F2937" Margin="0,0,0,15"/>
                            
                            <TextBlock Text="تاريخ الجلسة:" Style="{StaticResource LabelStyle}"/>
                            <TextBlock x:Name="SessionDateValue" Style="{StaticResource ValueStyle}"/>
                            
                            <TextBlock Text="وقت الجلسة:" Style="{StaticResource LabelStyle}"/>
                            <TextBlock x:Name="SessionTimeValue" Style="{StaticResource ValueStyle}"/>
                            
                            <TextBlock Text="نوع الجلسة:" Style="{StaticResource LabelStyle}"/>
                            <TextBlock x:Name="SessionTypeValue" Style="{StaticResource ValueStyle}"/>
                            
                            <TextBlock Text="المحامي المكلف:" Style="{StaticResource LabelStyle}"/>
                            <TextBlock x:Name="AssignedLawyerValue" Style="{StaticResource ValueStyle}"/>
                            
                            <TextBlock Text="القاضي:" Style="{StaticResource LabelStyle}"/>
                            <TextBlock x:Name="JudgeValue" Style="{StaticResource ValueStyle}"/>
                            
                            <TextBlock Text="قاعة المحكمة:" Style="{StaticResource LabelStyle}"/>
                            <TextBlock x:Name="CourtRoomValue" Style="{StaticResource ValueStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- الأولوية والمصروفات -->
                    <Border Background="White" CornerRadius="12" Padding="20">
                        <Border.Effect>
                            <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                        </Border.Effect>
                        
                        <StackPanel>
                            <TextBlock Text="💰 الأولوية والمصروفات" FontSize="16" FontWeight="Bold" 
                                       Foreground="#1F2937" Margin="0,0,0,15"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="الأولوية:" Style="{StaticResource LabelStyle}"/>
                                    <Border x:Name="PriorityBorder" CornerRadius="12" Padding="8,4" HorizontalAlignment="Left" Margin="0,0,0,15">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock x:Name="PriorityIcon" FontSize="12" Margin="0,0,4,0"/>
                                            <TextBlock x:Name="PriorityValue" FontSize="12" FontWeight="SemiBold" Foreground="White"/>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="المصروفات:" Style="{StaticResource LabelStyle}"/>
                                    <TextBlock x:Name="ExpensesValue" Style="{StaticResource ValueStyle}"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>
                </StackPanel>

                <!-- العمود الثاني -->
                <StackPanel Grid.Column="2">
                    
                    <!-- الإجراءات والقرارات -->
                    <Border Background="White" CornerRadius="12" Padding="20" Margin="0,0,0,20">
                        <Border.Effect>
                            <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                        </Border.Effect>
                        
                        <StackPanel>
                            <TextBlock Text="📄 الإجراءات والقرارات" FontSize="16" FontWeight="Bold" 
                                       Foreground="#1F2937" Margin="0,0,0,15"/>
                            
                            <TextBlock Text="نوع الإجراء:" Style="{StaticResource LabelStyle}"/>
                            <TextBlock x:Name="ProcedureTypeValue" Style="{StaticResource ValueStyle}"/>
                            
                            <TextBlock Text="تاريخ الإجراء:" Style="{StaticResource LabelStyle}"/>
                            <TextBlock x:Name="ProcedureDateValue" Style="{StaticResource ValueStyle}"/>
                            
                            <TextBlock Text="القرار:" Style="{StaticResource LabelStyle}"/>
                            <TextBlock x:Name="DecisionValue" Style="{StaticResource ValueStyle}"/>
                            
                            <TextBlock Text="نتيجة الجلسة:" Style="{StaticResource LabelStyle}"/>
                            <TextBlock x:Name="OutcomeValue" Style="{StaticResource ValueStyle}"/>
                        </StackPanel>
                    </Border>

                    <!-- الجلسة المقبلة -->
                    <Border Background="White" CornerRadius="12" Padding="20" Margin="0,0,0,20">
                        <Border.Effect>
                            <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                        </Border.Effect>
                        
                        <StackPanel>
                            <TextBlock Text="📅 الجلسة المقبلة" FontSize="16" FontWeight="Bold" 
                                       Foreground="#1F2937" Margin="0,0,0,15"/>
                            
                            <TextBlock Text="تاريخ الجلسة المقبلة:" Style="{StaticResource LabelStyle}"/>
                            <TextBlock x:Name="NextSessionValue" Style="{StaticResource ValueStyle}"/>
                            
                            <TextBlock Text="حالة التذكير:" Style="{StaticResource LabelStyle}"/>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                <TextBlock x:Name="ReminderIcon" FontSize="14" Margin="0,0,8,0"/>
                                <TextBlock x:Name="ReminderStatusValue" FontSize="14"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- الملاحظات والوثائق -->
                    <Border Background="White" CornerRadius="12" Padding="20">
                        <Border.Effect>
                            <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                        </Border.Effect>
                        
                        <StackPanel>
                            <TextBlock Text="📝 الملاحظات والوثائق" FontSize="16" FontWeight="Bold" 
                                       Foreground="#1F2937" Margin="0,0,0,15"/>
                            
                            <TextBlock Text="الوثائق المطلوبة:" Style="{StaticResource LabelStyle}"/>
                            <TextBlock x:Name="DocumentsValue" Style="{StaticResource ValueStyle}"/>
                            
                            <TextBlock Text="الملاحظات:" Style="{StaticResource LabelStyle}"/>
                            <ScrollViewer MaxHeight="100" VerticalScrollBarVisibility="Auto">
                                <TextBlock x:Name="NotesValue" Style="{StaticResource ValueStyle}"/>
                            </ScrollViewer>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </ScrollViewer>

        <!-- أزرار الإجراءات -->
        <Border Grid.Row="2" Background="White" CornerRadius="12" Padding="20" Margin="0,20,0,0">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
            </Border.Effect>
            
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="EditButton" Content="✏️ تعديل الجلسة" Background="#6366F1" Foreground="White" 
                        Style="{StaticResource ActionButtonStyle}" Click="EditButton_Click"/>
                <Button x:Name="PrintButton" Content="🖨️ طباعة" Background="#10B981" Foreground="White" 
                        Style="{StaticResource ActionButtonStyle}" Click="PrintButton_Click"/>
                <Button x:Name="CloseButton" Content="❌ إغلاق" Background="#6B7280" Foreground="White" 
                        Style="{StaticResource ActionButtonStyle}" Click="CloseButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
