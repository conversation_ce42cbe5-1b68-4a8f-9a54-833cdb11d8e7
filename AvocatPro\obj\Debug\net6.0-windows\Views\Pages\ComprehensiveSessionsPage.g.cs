﻿#pragma checksum "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "D9B575A0307CED37E5815E83B4D76DE2591810BF"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Pages {
    
    
    /// <summary>
    /// ComprehensiveSessionsPage
    /// </summary>
    public partial class ComprehensiveSessionsPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 82 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddSessionButton;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CalendarViewButton;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalSessionsCount;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TodaySessionsCount;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ScheduledSessionsCount;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CompletedSessionsCount;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PostponedSessionsCount;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalExpensesAmount;
        
        #line default
        #line hidden
        
        
        #line 258 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 267 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 279 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SessionTypeFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 286 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CourtFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 294 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 299 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintButton;
        
        #line default
        #line hidden
        
        
        #line 317 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LawyerFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 324 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PriorityFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 334 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DateFromPicker;
        
        #line default
        #line hidden
        
        
        #line 339 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DateToPicker;
        
        #line default
        #line hidden
        
        
        #line 344 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox QuickViewComboBox;
        
        #line default
        #line hidden
        
        
        #line 364 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SessionsDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/pages/comprehensivesessionspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AddSessionButton = ((System.Windows.Controls.Button)(target));
            
            #line 84 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
            this.AddSessionButton.Click += new System.Windows.RoutedEventHandler(this.AddSessionButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.CalendarViewButton = ((System.Windows.Controls.Button)(target));
            
            #line 87 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
            this.CalendarViewButton.Click += new System.Windows.RoutedEventHandler(this.CalendarViewButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 90 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.TotalSessionsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.TodaySessionsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.ScheduledSessionsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.CompletedSessionsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.PostponedSessionsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.TotalExpensesAmount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 261 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
            this.SearchTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.SearchTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 262 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
            this.SearchTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.SearchTextBox_LostFocus);
            
            #line default
            #line hidden
            
            #line 262 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.StatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 269 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
            this.StatusFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.StatusFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 12:
            this.SessionTypeFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 281 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
            this.SessionTypeFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SessionTypeFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 13:
            this.CourtFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 288 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
            this.CourtFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CourtFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 14:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 296 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.PrintButton = ((System.Windows.Controls.Button)(target));
            
            #line 301 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
            this.PrintButton.Click += new System.Windows.RoutedEventHandler(this.PrintButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.LawyerFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 319 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
            this.LawyerFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.LawyerFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 17:
            this.PriorityFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 326 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
            this.PriorityFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PriorityFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 18:
            this.DateFromPicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 336 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
            this.DateFromPicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 19:
            this.DateToPicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 341 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
            this.DateToPicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 20:
            this.QuickViewComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 346 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
            this.QuickViewComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.QuickView_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 21:
            this.SessionsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 369 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
            this.SessionsDataGrid.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.SessionsDataGrid_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 22:
            
            #line 445 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditSession_Click);
            
            #line default
            #line hidden
            break;
            case 23:
            
            #line 448 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteSession_Click);
            
            #line default
            #line hidden
            break;
            case 24:
            
            #line 451 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewDetails_Click);
            
            #line default
            #line hidden
            break;
            case 25:
            
            #line 454 "..\..\..\..\..\Views\Pages\ComprehensiveSessionsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PrintSession_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

