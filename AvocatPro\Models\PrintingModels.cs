using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace AvocatPro.Models;

/// <summary>
/// نموذج بيانات الشيك
/// </summary>
public class CheckModel : INotifyPropertyChanged
{
    private string _checkNumber = string.Empty;
    private string _payeeName = string.Empty;
    private decimal _amount;
    private string _amountInWords = string.Empty;
    private DateTime _checkDate = DateTime.Now;
    private string _bankName = string.Empty;
    private string _accountNumber = string.Empty;
    private string _memo = string.Empty;
    private string _signature = string.Empty;
    private bool _isCrossed;
    private string _crossedText = "للمستفيد فقط";

    /// <summary>
    /// رقم الشيك
    /// </summary>
    public string CheckNumber
    {
        get => _checkNumber;
        set => SetProperty(ref _checkNumber, value);
    }

    /// <summary>
    /// اسم المستفيد
    /// </summary>
    public string PayeeName
    {
        get => _payeeName;
        set => SetProperty(ref _payeeName, value);
    }

    /// <summary>
    /// المبلغ بالأرقام
    /// </summary>
    public decimal Amount
    {
        get => _amount;
        set
        {
            SetProperty(ref _amount, value);
            AmountInWords = ConvertAmountToWords(value);
        }
    }

    /// <summary>
    /// المبلغ بالكلمات
    /// </summary>
    public string AmountInWords
    {
        get => _amountInWords;
        set => SetProperty(ref _amountInWords, value);
    }

    /// <summary>
    /// تاريخ الشيك
    /// </summary>
    public DateTime CheckDate
    {
        get => _checkDate;
        set => SetProperty(ref _checkDate, value);
    }

    /// <summary>
    /// اسم البنك
    /// </summary>
    public string BankName
    {
        get => _bankName;
        set => SetProperty(ref _bankName, value);
    }

    /// <summary>
    /// رقم الحساب
    /// </summary>
    public string AccountNumber
    {
        get => _accountNumber;
        set => SetProperty(ref _accountNumber, value);
    }

    /// <summary>
    /// ملاحظة
    /// </summary>
    public string Memo
    {
        get => _memo;
        set => SetProperty(ref _memo, value);
    }

    /// <summary>
    /// التوقيع
    /// </summary>
    public string Signature
    {
        get => _signature;
        set => SetProperty(ref _signature, value);
    }

    /// <summary>
    /// هل الشيك مشطوب
    /// </summary>
    public bool IsCrossed
    {
        get => _isCrossed;
        set => SetProperty(ref _isCrossed, value);
    }

    /// <summary>
    /// نص الشطب
    /// </summary>
    public string CrossedText
    {
        get => _crossedText;
        set => SetProperty(ref _crossedText, value);
    }

    /// <summary>
    /// تحويل المبلغ إلى كلمات
    /// </summary>
    private string ConvertAmountToWords(decimal amount)
    {
        if (amount == 0) return "صفر درهم";
        
        var integerPart = (long)Math.Floor(amount);
        var decimalPart = (int)Math.Round((amount - integerPart) * 100);
        
        var result = ConvertIntegerToWords(integerPart) + " درهم";
        
        if (decimalPart > 0)
        {
            result += " و " + ConvertIntegerToWords(decimalPart) + " سنتيم";
        }
        
        return result + " لا غير";
    }

    /// <summary>
    /// تحويل الرقم الصحيح إلى كلمات
    /// </summary>
    private string ConvertIntegerToWords(long number)
    {
        if (number == 0) return "صفر";
        
        var ones = new[] { "", "واحد", "اثنان", "ثلاثة", "أربعة", "خمسة", "ستة", "سبعة", "ثمانية", "تسعة" };
        var tens = new[] { "", "", "عشرون", "ثلاثون", "أربعون", "خمسون", "ستون", "سبعون", "ثمانون", "تسعون" };
        var teens = new[] { "عشرة", "أحد عشر", "اثنا عشر", "ثلاثة عشر", "أربعة عشر", "خمسة عشر", "ستة عشر", "سبعة عشر", "ثمانية عشر", "تسعة عشر" };
        
        if (number < 10) return ones[number];
        if (number < 20) return teens[number - 10];
        if (number < 100)
        {
            var ten = (int)(number / 10);
            var one = (int)(number % 10);
            return tens[ten] + (one > 0 ? " " + ones[one] : "");
        }
        if (number < 1000)
        {
            var hundred = (int)(number / 100);
            var remainder = (int)(number % 100);
            var result = ones[hundred] + " مائة";
            if (remainder > 0) result += " " + ConvertIntegerToWords(remainder);
            return result;
        }
        
        // للأرقام الأكبر من 1000
        return number.ToString();
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (Equals(field, value)) return false;
        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }
}

/// <summary>
/// نموذج بيانات الظرف
/// </summary>
public class EnvelopeModel : INotifyPropertyChanged
{
    private string _senderName = string.Empty;
    private string _senderAddress = string.Empty;
    private string _senderCity = string.Empty;
    private string _senderPostalCode = string.Empty;
    private string _recipientName = string.Empty;
    private string _recipientAddress = string.Empty;
    private string _recipientCity = string.Empty;
    private string _recipientPostalCode = string.Empty;
    private EnvelopeSize _envelopeSize = EnvelopeSize.Standard;
    private bool _isRegistered;
    private bool _isUrgent;
    private string _specialInstructions = string.Empty;

    /// <summary>
    /// اسم المرسل
    /// </summary>
    public string SenderName
    {
        get => _senderName;
        set => SetProperty(ref _senderName, value);
    }

    /// <summary>
    /// عنوان المرسل
    /// </summary>
    public string SenderAddress
    {
        get => _senderAddress;
        set => SetProperty(ref _senderAddress, value);
    }

    /// <summary>
    /// مدينة المرسل
    /// </summary>
    public string SenderCity
    {
        get => _senderCity;
        set => SetProperty(ref _senderCity, value);
    }

    /// <summary>
    /// الرمز البريدي للمرسل
    /// </summary>
    public string SenderPostalCode
    {
        get => _senderPostalCode;
        set => SetProperty(ref _senderPostalCode, value);
    }

    /// <summary>
    /// اسم المستقبل
    /// </summary>
    public string RecipientName
    {
        get => _recipientName;
        set => SetProperty(ref _recipientName, value);
    }

    /// <summary>
    /// عنوان المستقبل
    /// </summary>
    public string RecipientAddress
    {
        get => _recipientAddress;
        set => SetProperty(ref _recipientAddress, value);
    }

    /// <summary>
    /// مدينة المستقبل
    /// </summary>
    public string RecipientCity
    {
        get => _recipientCity;
        set => SetProperty(ref _recipientCity, value);
    }

    /// <summary>
    /// الرمز البريدي للمستقبل
    /// </summary>
    public string RecipientPostalCode
    {
        get => _recipientPostalCode;
        set => SetProperty(ref _recipientPostalCode, value);
    }

    /// <summary>
    /// حجم الظرف
    /// </summary>
    public EnvelopeSize EnvelopeSize
    {
        get => _envelopeSize;
        set => SetProperty(ref _envelopeSize, value);
    }

    /// <summary>
    /// هل البريد مسجل
    /// </summary>
    public bool IsRegistered
    {
        get => _isRegistered;
        set => SetProperty(ref _isRegistered, value);
    }

    /// <summary>
    /// هل البريد عاجل
    /// </summary>
    public bool IsUrgent
    {
        get => _isUrgent;
        set => SetProperty(ref _isUrgent, value);
    }

    /// <summary>
    /// تعليمات خاصة
    /// </summary>
    public string SpecialInstructions
    {
        get => _specialInstructions;
        set => SetProperty(ref _specialInstructions, value);
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (Equals(field, value)) return false;
        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }
}

/// <summary>
/// أحجام الأظرفة
/// </summary>
public enum EnvelopeSize
{
    /// <summary>
    /// حجم قياسي
    /// </summary>
    Standard,
    
    /// <summary>
    /// حجم كبير
    /// </summary>
    Large,
    
    /// <summary>
    /// حجم A4
    /// </summary>
    A4,
    
    /// <summary>
    /// حجم مخصص
    /// </summary>
    Custom
}

/// <summary>
/// قالب الشيك
/// </summary>
public class CheckTemplate
{
    public string Name { get; set; } = string.Empty;
    public string BankName { get; set; } = string.Empty;
    public double Width { get; set; } = 200; // بالمليمتر
    public double Height { get; set; } = 85; // بالمليمتر
    public CheckFieldPosition PayeePosition { get; set; } = new();
    public CheckFieldPosition AmountPosition { get; set; } = new();
    public CheckFieldPosition AmountWordsPosition { get; set; } = new();
    public CheckFieldPosition DatePosition { get; set; } = new();
    public CheckFieldPosition MemoPosition { get; set; } = new();
    public CheckFieldPosition SignaturePosition { get; set; } = new();
}

/// <summary>
/// موضع حقل في الشيك
/// </summary>
public class CheckFieldPosition
{
    public double X { get; set; }
    public double Y { get; set; }
    public double Width { get; set; }
    public double Height { get; set; }
    public string FontFamily { get; set; } = "Arial";
    public double FontSize { get; set; } = 12;
    public bool IsBold { get; set; }
}
