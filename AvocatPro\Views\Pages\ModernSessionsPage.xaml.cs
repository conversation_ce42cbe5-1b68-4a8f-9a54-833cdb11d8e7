using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace AvocatPro.Views.Pages
{
    /// <summary>
    /// صفحة إدارة الجلسات الحديثة
    /// </summary>
    public partial class ModernSessionsPage : Page
    {
        private ObservableCollection<SessionViewModel> _allSessions = new ObservableCollection<SessionViewModel>();
        private ObservableCollection<SessionViewModel> _filteredSessions = new ObservableCollection<SessionViewModel>();

        public ModernSessionsPage()
        {
            try
            {
                InitializeComponent();
                InitializeData();
                LoadSessions();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة صفحة الجلسات: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تهيئة البيانات الوهمية
        /// </summary>
        private void InitializeData()
        {
            _allSessions = new ObservableCollection<SessionViewModel>();
            _filteredSessions = new ObservableCollection<SessionViewModel>();

            // إضافة بيانات وهمية للجلسات
            var sampleSessions = new List<SessionViewModel>
            {
                new SessionViewModel
                {
                    Id = 1,
                    SessionNumber = "S2024/001",
                    CaseTitle = "قضية نزاع تجاري - شركة التجارة المغربية",
                    Court = "المحكمة التجارية بالرباط",
                    SessionDate = "2024-04-15",
                    SessionTime = "10:00 ص",
                    Status = "مجدولة",
                    StatusColor = "#3B82F6",
                    SessionType = "مرافعة",
                    TypeColor = "#10B981"
                },
                new SessionViewModel
                {
                    Id = 2,
                    SessionNumber = "S2024/002",
                    CaseTitle = "قضية طلاق وحضانة",
                    Court = "محكمة الأسرة بالدار البيضاء",
                    SessionDate = "2024-04-18",
                    SessionTime = "02:00 م",
                    Status = "مجدولة",
                    StatusColor = "#3B82F6",
                    SessionType = "استماع",
                    TypeColor = "#F59E0B"
                },
                new SessionViewModel
                {
                    Id = 3,
                    SessionNumber = "S2024/003",
                    CaseTitle = "قضية اعتداء وضرب",
                    Court = "المحكمة الابتدائية بفاس",
                    SessionDate = "2024-04-10",
                    SessionTime = "11:30 ص",
                    Status = "مكتملة",
                    StatusColor = "#10B981",
                    SessionType = "حكم",
                    TypeColor = "#EF4444"
                },
                new SessionViewModel
                {
                    Id = 4,
                    SessionNumber = "S2024/004",
                    CaseTitle = "نزاع عمالي - فصل تعسفي",
                    Court = "محكمة الاستئناف بطنجة",
                    SessionDate = "2024-04-22",
                    SessionTime = "09:00 ص",
                    Status = "مؤجلة",
                    StatusColor = "#F59E0B",
                    SessionType = "استئناف",
                    TypeColor = "#8B5CF6"
                },
                new SessionViewModel
                {
                    Id = 5,
                    SessionNumber = "S2024/005",
                    CaseTitle = "قضية ميراث وتركة",
                    Court = "المحكمة الابتدائية بأكادير",
                    SessionDate = "2024-04-12",
                    SessionTime = "03:30 م",
                    Status = "مكتملة",
                    StatusColor = "#10B981",
                    SessionType = "تسوية",
                    TypeColor = "#06B6D4"
                },
                new SessionViewModel
                {
                    Id = 6,
                    SessionNumber = "S2024/006",
                    CaseTitle = "عقد شراكة تجارية",
                    Court = "المحكمة التجارية بمراكش",
                    SessionDate = "2024-04-25",
                    SessionTime = "01:00 م",
                    Status = "مجدولة",
                    StatusColor = "#3B82F6",
                    SessionType = "مرافعة",
                    TypeColor = "#10B981"
                },
                new SessionViewModel
                {
                    Id = 7,
                    SessionNumber = "S2024/007",
                    CaseTitle = "قضية احتيال مالي",
                    Court = "محكمة الجنايات بالرباط",
                    SessionDate = "2024-04-20",
                    SessionTime = "10:30 ص",
                    Status = "ملغية",
                    StatusColor = "#EF4444",
                    SessionType = "تحقيق",
                    TypeColor = "#DC2626"
                },
                new SessionViewModel
                {
                    Id = 8,
                    SessionNumber = "S2024/008",
                    CaseTitle = "نزاع حول ملكية عقارية",
                    Court = "المحكمة الابتدائية بالدار البيضاء",
                    SessionDate = "2024-04-28",
                    SessionTime = "11:00 ص",
                    Status = "مجدولة",
                    StatusColor = "#3B82F6",
                    SessionType = "خبرة",
                    TypeColor = "#7C3AED"
                }
            };

            foreach (var session in sampleSessions)
            {
                _allSessions.Add(session);
                _filteredSessions.Add(session);
            }
        }

        /// <summary>
        /// تحميل قائمة الجلسات
        /// </summary>
        private void LoadSessions()
        {
            SessionsItemsControl.ItemsSource = _filteredSessions;
            UpdateSessionsCount();
        }

        /// <summary>
        /// تحديث عدد الجلسات
        /// </summary>
        private void UpdateSessionsCount()
        {
            SessionsCountLabel.Text = $"إجمالي الجلسات: {_filteredSessions.Count}";
        }

        /// <summary>
        /// معالج البحث في النص
        /// </summary>
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterSessions();
        }

        /// <summary>
        /// معالج تصفية حسب الحالة
        /// </summary>
        private void StatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            FilterSessions();
        }

        /// <summary>
        /// معالج تصفية حسب المحكمة
        /// </summary>
        private void CourtFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            FilterSessions();
        }

        /// <summary>
        /// تصفية الجلسات
        /// </summary>
        private void FilterSessions()
        {
            var searchText = SearchTextBox?.Text?.ToLower() ?? "";
            var selectedStatus = (StatusFilterComboBox?.SelectedItem as ComboBoxItem)?.Content?.ToString();
            var selectedCourt = (CourtFilterComboBox?.SelectedItem as ComboBoxItem)?.Content?.ToString();

            _filteredSessions.Clear();

            var filtered = _allSessions.Where(session =>
            {
                // تصفية النص
                var matchesSearch = string.IsNullOrEmpty(searchText) ||
                                   session.CaseTitle.ToLower().Contains(searchText) ||
                                   session.SessionNumber.ToLower().Contains(searchText) ||
                                   session.Court.ToLower().Contains(searchText);

                // تصفية الحالة
                var matchesStatus = selectedStatus == "جميع الحالات" ||
                                   selectedStatus == session.Status;

                // تصفية المحكمة
                var matchesCourt = selectedCourt == "جميع المحاكم" ||
                                  selectedCourt == session.Court;

                return matchesSearch && matchesStatus && matchesCourt;
            });

            foreach (var session in filtered)
            {
                _filteredSessions.Add(session);
            }

            UpdateSessionsCount();
        }

        /// <summary>
        /// إضافة جلسة جديدة
        /// </summary>
        private void AddSession_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة إضافة جلسة جديدة", "إضافة جلسة", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// عرض تفاصيل الجلسة
        /// </summary>
        private void ViewSession_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var sessionId = (int)button.Tag;
            MessageBox.Show($"عرض تفاصيل الجلسة رقم: {sessionId}", "تفاصيل الجلسة", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// تعديل الجلسة
        /// </summary>
        private void EditSession_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var sessionId = (int)button.Tag;
            MessageBox.Show($"تعديل الجلسة رقم: {sessionId}", "تعديل الجلسة", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// تأجيل الجلسة
        /// </summary>
        private void PostponeSession_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var sessionId = (int)button.Tag;
            
            var result = MessageBox.Show($"هل أنت متأكد من تأجيل الجلسة رقم {sessionId}؟", 
                                        "تأكيد التأجيل", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                var sessionToPostpone = _allSessions.FirstOrDefault(s => s.Id == sessionId);
                if (sessionToPostpone != null)
                {
                    sessionToPostpone.Status = "مؤجلة";
                    sessionToPostpone.StatusColor = "#F59E0B";
                    FilterSessions();
                    MessageBox.Show("تم تأجيل الجلسة بنجاح", "تأجيل الجلسة", 
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        /// <summary>
        /// تصدير البيانات
        /// </summary>
        private void Export_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تصدير قائمة الجلسات إلى ملف Excel", "تصدير البيانات", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// الصفحة السابقة
        /// </summary>
        private void PreviousPage_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("الانتقال إلى الصفحة السابقة", "التنقل", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// الصفحة التالية
        /// </summary>
        private void NextPage_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("الانتقال إلى الصفحة التالية", "التنقل", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    /// <summary>
    /// نموذج عرض الجلسة
    /// </summary>
    public class SessionViewModel
    {
        public int Id { get; set; }
        public string SessionNumber { get; set; } = "";
        public string CaseTitle { get; set; } = "";
        public string Court { get; set; } = "";
        public string SessionDate { get; set; } = "";
        public string SessionTime { get; set; } = "";
        public string Status { get; set; } = "";
        public string StatusColor { get; set; } = "";
        public string SessionType { get; set; } = "";
        public string TypeColor { get; set; } = "";
    }
}
