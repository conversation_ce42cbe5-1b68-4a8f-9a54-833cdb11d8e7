﻿#pragma checksum "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "2CCC0158143D2B707652C55105FD5BFDF33F5723"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Windows {
    
    
    /// <summary>
    /// ReportExportWindow
    /// </summary>
    public partial class ReportExportWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 164 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel FormatOptionsPanel;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton PdfFormatRadio;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton ExcelFormatRadio;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton WordFormatRadio;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton PowerPointFormatRadio;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton HtmlFormatRadio;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ContentOptionsPanel;
        
        #line default
        #line hidden
        
        
        #line 190 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IncludeKPIsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IncludeFinancialCheckBox;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IncludeCasesCheckBox;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IncludeClientsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IncludeAppointmentsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 205 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IncludePerformanceCheckBox;
        
        #line default
        #line hidden
        
        
        #line 208 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IncludeChartsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IncludeTrendsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IncludeComparisonsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IncludeRecommendationsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AdditionalOptionsPanel;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IncludeCoverPageCheckBox;
        
        #line default
        #line hidden
        
        
        #line 228 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IncludeTableOfContentsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 231 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IncludeWatermarkCheckBox;
        
        #line default
        #line hidden
        
        
        #line 234 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IncludeFooterCheckBox;
        
        #line default
        #line hidden
        
        
        #line 237 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox HighQualityChartsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 240 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CompressFileCheckBox;
        
        #line default
        #line hidden
        
        
        #line 250 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer PreviewScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 251 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PreviewPanel;
        
        #line default
        #line hidden
        
        
        #line 256 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PreviewPeriodText;
        
        #line default
        #line hidden
        
        
        #line 259 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PreviewContentPanel;
        
        #line default
        #line hidden
        
        
        #line 284 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileNameText;
        
        #line default
        #line hidden
        
        
        #line 287 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileSizeText;
        
        #line default
        #line hidden
        
        
        #line 290 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PageCountText;
        
        #line default
        #line hidden
        
        
        #line 293 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock GenerationTimeText;
        
        #line default
        #line hidden
        
        
        #line 304 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 311 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PreviewButton;
        
        #line default
        #line hidden
        
        
        #line 319 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveTemplateButton;
        
        #line default
        #line hidden
        
        
        #line 327 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/windows/reportexportwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.FormatOptionsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 2:
            this.PdfFormatRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 3:
            this.ExcelFormatRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 4:
            this.WordFormatRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 5:
            this.PowerPointFormatRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 6:
            this.HtmlFormatRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 7:
            this.ContentOptionsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 8:
            this.IncludeKPIsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 9:
            this.IncludeFinancialCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 10:
            this.IncludeCasesCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 11:
            this.IncludeClientsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 12:
            this.IncludeAppointmentsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 13:
            this.IncludePerformanceCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 14:
            this.IncludeChartsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 15:
            this.IncludeTrendsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 16:
            this.IncludeComparisonsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 17:
            this.IncludeRecommendationsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 18:
            this.AdditionalOptionsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 19:
            this.IncludeCoverPageCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 20:
            this.IncludeTableOfContentsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 21:
            this.IncludeWatermarkCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 22:
            this.IncludeFooterCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 23:
            this.HighQualityChartsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 24:
            this.CompressFileCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 25:
            this.PreviewScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 26:
            this.PreviewPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 27:
            this.PreviewPeriodText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.PreviewContentPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 29:
            this.FileNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            this.FileSizeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 31:
            this.PageCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 32:
            this.GenerationTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 33:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 304 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 34:
            this.PreviewButton = ((System.Windows.Controls.Button)(target));
            
            #line 312 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
            this.PreviewButton.Click += new System.Windows.RoutedEventHandler(this.PreviewButton_Click);
            
            #line default
            #line hidden
            return;
            case 35:
            this.SaveTemplateButton = ((System.Windows.Controls.Button)(target));
            
            #line 320 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
            this.SaveTemplateButton.Click += new System.Windows.RoutedEventHandler(this.SaveTemplateButton_Click);
            
            #line default
            #line hidden
            return;
            case 36:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 327 "..\..\..\..\..\Views\Windows\ReportExportWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

