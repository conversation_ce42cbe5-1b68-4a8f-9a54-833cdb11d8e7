﻿#pragma checksum "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "2D76985373B78C4FE13915830110E55188972FE7"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Windows {
    
    
    /// <summary>
    /// AttachmentsViewerWindow
    /// </summary>
    public partial class AttachmentsViewerWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 78 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DocumentInfoTextBlock;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AttachmentsListPanel;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddAttachmentButton;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ScanDocumentButton;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DownloadAllButton;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileTypeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileSizeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DateAddedTextBlock;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer PreviewScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid PreviewGrid;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel NoPreviewPanel;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image ImagePreview;
        
        #line default
        #line hidden
        
        
        #line 183 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TextPreview;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PdfPreview;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel OtherFilePreview;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileIconTextBlock;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileTypeDisplayTextBlock;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenFileButton;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DownloadFileButton;
        
        #line default
        #line hidden
        
        
        #line 223 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintFileButton;
        
        #line default
        #line hidden
        
        
        #line 231 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ShareFileButton;
        
        #line default
        #line hidden
        
        
        #line 239 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteFileButton;
        
        #line default
        #line hidden
        
        
        #line 260 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 264 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/windows/attachmentsviewerwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DocumentInfoTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.AttachmentsListPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 3:
            this.AddAttachmentButton = ((System.Windows.Controls.Button)(target));
            
            #line 104 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
            this.AddAttachmentButton.Click += new System.Windows.RoutedEventHandler(this.AddAttachmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ScanDocumentButton = ((System.Windows.Controls.Button)(target));
            
            #line 112 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
            this.ScanDocumentButton.Click += new System.Windows.RoutedEventHandler(this.ScanDocumentButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.DownloadAllButton = ((System.Windows.Controls.Button)(target));
            
            #line 120 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
            this.DownloadAllButton.Click += new System.Windows.RoutedEventHandler(this.DownloadAllButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.FileNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.FileTypeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.FileSizeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.DateAddedTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.PreviewScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 11:
            this.PreviewGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 12:
            this.NoPreviewPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 13:
            this.ImagePreview = ((System.Windows.Controls.Image)(target));
            return;
            case 14:
            this.TextPreview = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.PdfPreview = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 16:
            this.OtherFilePreview = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 17:
            this.FileIconTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.FileTypeDisplayTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.OpenFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 208 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
            this.OpenFileButton.Click += new System.Windows.RoutedEventHandler(this.OpenFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.DownloadFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 216 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
            this.DownloadFileButton.Click += new System.Windows.RoutedEventHandler(this.DownloadFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.PrintFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 224 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
            this.PrintFileButton.Click += new System.Windows.RoutedEventHandler(this.PrintFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.ShareFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 232 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
            this.ShareFileButton.Click += new System.Windows.RoutedEventHandler(this.ShareFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.DeleteFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 240 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
            this.DeleteFileButton.Click += new System.Windows.RoutedEventHandler(this.DeleteFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 264 "..\..\..\..\..\Views\Windows\AttachmentsViewerWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

