using System;
using System.Windows;
using AvocatPro.Models;

namespace AvocatPro.Views.Windows
{
    public partial class EditUserWindow : Window
    {
        private readonly User _currentUser;
        private readonly int _userId;

        public EditUserWindow(User currentUser, int userId)
        {
            // محاكاة نافذة تعديل المستخدم
            _currentUser = currentUser;
            _userId = userId;
            
            // إنشاء نافذة بسيطة
            Title = "تعديل المستخدم";
            Width = 400;
            Height = 300;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;
            
            var button = new System.Windows.Controls.Button
            {
                Content = "إغلاق",
                Width = 100,
                Height = 30,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };
            button.Click += (s, e) => Close();
            
            Content = button;
        }
    }
}
