using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AvocatPro.Models;

/// <summary>
/// نموذج الموعد
/// </summary>
public class Appointment : BaseEntity
{
    /// <summary>
    /// عنوان الموعد
    /// </summary>
    [Required]
    [StringLength(200)]
    public string Title { get; set; } = string.Empty;
    
    /// <summary>
    /// وصف الموعد
    /// </summary>
    [StringLength(1000)]
    public string? Description { get; set; }
    
    /// <summary>
    /// تاريخ الموعد
    /// </summary>
    [Required]
    public DateTime AppointmentDate { get; set; }
    
    /// <summary>
    /// وقت البداية
    /// </summary>
    [Required]
    public TimeSpan StartTime { get; set; }
    
    /// <summary>
    /// وقت النهاية
    /// </summary>
    [Required]
    public TimeSpan EndTime { get; set; }
    
    /// <summary>
    /// نوع الموعد
    /// </summary>
    public AppointmentType Type { get; set; }
    
    /// <summary>
    /// حالة الموعد
    /// </summary>
    public AppointmentStatus Status { get; set; } = AppointmentStatus.Scheduled;
    
    /// <summary>
    /// أولوية الموعد
    /// </summary>
    public AppointmentPriority Priority { get; set; } = AppointmentPriority.Medium;
    
    /// <summary>
    /// معرف الموكل (اختياري)
    /// </summary>
    public int? ClientId { get; set; }
    
    /// <summary>
    /// معرف القضية (اختياري)
    /// </summary>
    public int? CaseId { get; set; }
    
    /// <summary>
    /// المكان
    /// </summary>
    [StringLength(200)]
    public string? Location { get; set; }
    
    /// <summary>
    /// الحضور المطلوب
    /// </summary>
    [StringLength(500)]
    public string? RequiredAttendees { get; set; }
    
    /// <summary>
    /// التكلفة
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? Cost { get; set; }
    
    /// <summary>
    /// تم الدفع
    /// </summary>
    public bool IsPaid { get; set; } = false;
    
    /// <summary>
    /// تم التنبيه
    /// </summary>
    public bool IsNotified { get; set; } = false;
    
    /// <summary>
    /// وقت التنبيه (بالدقائق قبل الموعد)
    /// </summary>
    public int NotificationMinutes { get; set; } = 30;
    
    /// <summary>
    /// تاريخ التنبيه
    /// </summary>
    public DateTime? NotificationDate { get; set; }
    
    /// <summary>
    /// ملاحظات الموعد
    /// </summary>
    [StringLength(1000)]
    public string? AppointmentNotes { get; set; }
    
    /// <summary>
    /// النتيجة
    /// </summary>
    [StringLength(1000)]
    public string? Result { get; set; }
    
    /// <summary>
    /// المتابعة المطلوبة
    /// </summary>
    [StringLength(500)]
    public string? FollowUp { get; set; }
    
    /// <summary>
    /// رقم الهاتف للتواصل
    /// </summary>
    [StringLength(20)]
    public string? ContactPhone { get; set; }
    
    /// <summary>
    /// البريد الإلكتروني للتواصل
    /// </summary>
    [StringLength(100)]
    public string? ContactEmail { get; set; }
    
    /// <summary>
    /// موعد متكرر
    /// </summary>
    public bool IsRecurring { get; set; } = false;
    
    /// <summary>
    /// نمط التكرار (JSON)
    /// </summary>
    public string? RecurrencePattern { get; set; }
    
    /// <summary>
    /// معرف المستخدم المنشئ
    /// </summary>
    [Required]
    public int CreatedByUserId { get; set; }
    
    // Navigation Properties
    [ForeignKey("ClientId")]
    public virtual Client? Client { get; set; }
    
    [ForeignKey("CaseId")]
    public virtual Case? Case { get; set; }
    
    [ForeignKey("CreatedByUserId")]
    public virtual User CreatedByUser { get; set; } = null!;
    
    public virtual ICollection<AppointmentReminder> Reminders { get; set; } = new List<AppointmentReminder>();
}

/// <summary>
/// نوع الموعد
/// </summary>
public enum AppointmentType
{
    /// <summary>
    /// استشارة
    /// </summary>
    Consultation = 1,
    
    /// <summary>
    /// اجتماع
    /// </summary>
    Meeting = 2,
    
    /// <summary>
    /// مكالمة هاتفية
    /// </summary>
    PhoneCall = 3,
    
    /// <summary>
    /// زيارة ميدانية
    /// </summary>
    FieldVisit = 4,
    
    /// <summary>
    /// توقيع عقد
    /// </summary>
    ContractSigning = 5,
    
    /// <summary>
    /// متابعة قضية
    /// </summary>
    CaseFollowUp = 6,
    
    /// <summary>
    /// موعد شخصي
    /// </summary>
    Personal = 7,
    
    /// <summary>
    /// تدريب
    /// </summary>
    Training = 8
}

/// <summary>
/// حالة الموعد
/// </summary>
public enum AppointmentStatus
{
    /// <summary>
    /// مجدول
    /// </summary>
    Scheduled = 1,
    
    /// <summary>
    /// مؤكد
    /// </summary>
    Confirmed = 2,
    
    /// <summary>
    /// جاري
    /// </summary>
    InProgress = 3,
    
    /// <summary>
    /// مكتمل
    /// </summary>
    Completed = 4,
    
    /// <summary>
    /// مؤجل
    /// </summary>
    Postponed = 5,
    
    /// <summary>
    /// ملغي
    /// </summary>
    Cancelled = 6,
    
    /// <summary>
    /// لم يحضر
    /// </summary>
    NoShow = 7
}

/// <summary>
/// أولوية الموعد
/// </summary>
public enum AppointmentPriority
{
    /// <summary>
    /// منخفضة
    /// </summary>
    Low = 1,
    
    /// <summary>
    /// متوسطة
    /// </summary>
    Medium = 2,
    
    /// <summary>
    /// عالية
    /// </summary>
    High = 3,
    
    /// <summary>
    /// عاجلة
    /// </summary>
    Urgent = 4
}
