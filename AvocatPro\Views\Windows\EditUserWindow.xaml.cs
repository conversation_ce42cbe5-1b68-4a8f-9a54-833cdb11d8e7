using System;
using System.Windows;
using AvocatPro.Models;
using AvocatPro.Models.UserManagement;

namespace AvocatPro.Views.Windows
{

public partial class EditUserWindow : Window
{
    private readonly int _userId;

    public EditUserWindow(int userId)
    {
        InitializeComponent();
        _userId = userId;
        InitializeControls();
    }

    private void InitializeControls()
    {
        RoleComboBox.ItemsSource = new[]
        {
            new { Value = UserRole.Admin, Display = "مدير النظام" },
            new { Value = UserRole.SeniorLawyer, Display = "محامي رئيسي" },
            new { Value = UserRole.Lawyer, Display = "محامي" },
            new { Value = UserRole.Secretary, Display = "سكرتير" },
            new { Value = UserRole.User, Display = "مستخدم عادي" }
        };
        RoleComboBox.DisplayMemberPath = "Display";
        RoleComboBox.SelectedValuePath = "Value";

        StatusComboBox.ItemsSource = new[]
        {
            new { Value = UserStatus.Active, Display = "نشط" },
            new { Value = UserStatus.Inactive, Display = "غير نشط" },
            new { Value = UserStatus.Suspended, Display = "معلق" },
            new { Value = UserStatus.Banned, Display = "محظور" }
        };
        StatusComboBox.DisplayMemberPath = "Display";
        StatusComboBox.SelectedValuePath = "Value";

        GenderComboBox.ItemsSource = new[]
        {
            new { Value = (Models.UserManagement.Gender?)null, Display = "غير محدد" },
            new { Value = (Models.UserManagement.Gender?)Models.UserManagement.Gender.Male, Display = "ذكر" },
            new { Value = (Models.UserManagement.Gender?)Models.UserManagement.Gender.Female, Display = "أنثى" }
        };
        GenderComboBox.DisplayMemberPath = "Display";
        GenderComboBox.SelectedValuePath = "Value";
    }

    private void SaveButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("تم حفظ التغييرات بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
        DialogResult = true;
        Close();
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = false;
        Close();
    }

    private void ResetPasswordButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("هل أنت متأكد من إعادة تعيين كلمة المرور؟", "تأكيد", 
            MessageBoxButton.YesNo, MessageBoxImage.Question);
        
        if (result == MessageBoxResult.Yes)
        {
            MessageBox.Show("تم إعادة تعيين كلمة المرور بنجاح!", "نجح", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void UnlockAccountButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("تم إلغاء قفل الحساب بنجاح!", "نجح",
            MessageBoxButton.OK, MessageBoxImage.Information);
    }
}
}
