using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using AvocatPro.Models;
using Microsoft.Win32;

namespace AvocatPro.Views.Windows;

public partial class ReportExportWindow : Window
{
    private readonly ComprehensiveReportModel _report;
    private string _selectedFormat = "PDF";

    public ReportExportWindow(ComprehensiveReportModel report)
    {
        InitializeComponent();
        _report = report;
        
        InitializeWindow();
        UpdatePreview();
        UpdateFileInfo();
    }

    private void InitializeWindow()
    {
        // تعيين النص للفترة
        PreviewPeriodText.Text = $"الفترة: {_report.PeriodDisplay}";
        
        // ربط أحداث تغيير التنسيق
        PdfFormatRadio.Checked += FormatRadio_Checked;
        ExcelFormatRadio.Checked += FormatRadio_Checked;
        WordFormatRadio.Checked += FormatRadio_Checked;
        PowerPointFormatRadio.Checked += FormatRadio_Checked;
        HtmlFormatRadio.Checked += FormatRadio_Checked;
        
        // ربط أحداث تغيير المحتوى
        foreach (CheckBox checkBox in ContentOptionsPanel.Children)
        {
            checkBox.Checked += ContentOption_Changed;
            checkBox.Unchecked += ContentOption_Changed;
        }
        
        foreach (CheckBox checkBox in AdditionalOptionsPanel.Children)
        {
            checkBox.Checked += ContentOption_Changed;
            checkBox.Unchecked += ContentOption_Changed;
        }
    }

    private void FormatRadio_Checked(object sender, RoutedEventArgs e)
    {
        if (sender is RadioButton radio && radio.IsChecked == true)
        {
            _selectedFormat = radio.Content.ToString() ?? "PDF";
            UpdateFileInfo();
        }
    }

    private void ContentOption_Changed(object sender, RoutedEventArgs e)
    {
        UpdatePreview();
        UpdateFileInfo();
    }

    private void UpdatePreview()
    {
        PreviewContentPanel.Children.Clear();

        if (IncludeKPIsCheckBox.IsChecked == true)
        {
            AddPreviewSection("📊 المؤشرات الرئيسية", new[]
            {
                $"إجمالي الإيرادات: {_report.Financial.TotalRevenuesDisplay}",
                $"إجمالي المصاريف: {_report.Financial.TotalExpensesDisplay}",
                $"صافي الربح: {_report.Financial.NetProfitDisplay}",
                $"عدد القضايا: {_report.Cases.TotalCases}",
                $"عدد الموكلين: {_report.Clients.TotalClients}",
                $"معدل النجاح: {_report.Cases.SuccessRateDisplay}"
            });
        }

        if (IncludeFinancialCheckBox.IsChecked == true)
        {
            AddPreviewSection("💰 التقارير المالية", new[]
            {
                $"هامش الربح: {_report.Financial.ProfitMarginDisplay}",
                $"الإيرادات المعلقة: {_report.Financial.PendingRevenuesDisplay}",
                $"الإيرادات المتأخرة: {_report.Financial.OverdueRevenuesDisplay}",
                $"المصاريف القابلة للاسترداد: {_report.Financial.ReimbursableExpensesDisplay}",
                $"متوسط قيمة المعاملة: {_report.Financial.AverageTransactionDisplay}"
            });
        }

        if (IncludeCasesCheckBox.IsChecked == true)
        {
            AddPreviewSection("⚖️ إحصائيات القضايا", new[]
            {
                $"القضايا النشطة: {_report.Cases.ActiveCases}",
                $"القضايا المغلقة: {_report.Cases.ClosedCases}",
                $"القضايا المكسوبة: {_report.Cases.WonCases}",
                $"القضايا المخسورة: {_report.Cases.LostCases}",
                $"متوسط مدة القضية: {_report.Cases.AverageCaseDurationDisplay}"
            });
        }

        if (IncludeClientsCheckBox.IsChecked == true)
        {
            AddPreviewSection("👥 تحليل الموكلين", new[]
            {
                $"الموكلين النشطين: {_report.Clients.ActiveClients}",
                $"الموكلين الجدد: {_report.Clients.NewClients}",
                $"الأفراد: {_report.Clients.IndividualClients}",
                $"الشركات: {_report.Clients.CorporateClients}",
                $"معدل الاحتفاظ: {_report.Clients.ClientRetentionRateDisplay}"
            });
        }

        if (IncludeAppointmentsCheckBox.IsChecked == true)
        {
            AddPreviewSection("📅 إحصائيات المواعيد", new[]
            {
                $"إجمالي المواعيد: {_report.Appointments.TotalAppointments}",
                $"المواعيد المكتملة: {_report.Appointments.CompletedAppointments}",
                $"المواعيد الملغية: {_report.Appointments.CancelledAppointments}",
                $"معدل الحضور: {_report.Appointments.AttendanceRateDisplay}",
                $"متوسط مدة الموعد: {_report.Appointments.AverageAppointmentDurationDisplay}"
            });
        }

        if (IncludePerformanceCheckBox.IsChecked == true)
        {
            AddPreviewSection("🎯 مؤشرات الأداء", new[]
            {
                $"مؤشر الإنتاجية: {_report.Performance.ProductivityIndexDisplay}",
                $"تقييم الكفاءة: {_report.Performance.EfficiencyRatingDisplay}",
                $"نقاط الجودة: {_report.Performance.QualityScoreDisplay}",
                $"النتيجة الإجمالية: {_report.Performance.OverallPerformanceScoreDisplay}",
                $"التقييم: {_report.Performance.PerformanceGrade} {_report.Performance.PerformanceIcon}"
            });
        }

        if (IncludeChartsCheckBox.IsChecked == true)
        {
            AddPreviewSection("📈 الرسوم البيانية", new[]
            {
                "• رسم بياني للاتجاهات المالية",
                "• رسم بياني لأداء القضايا",
                "• رسم بياني لتوزيع الموكلين",
                "• رسم بياني للمواعيد الشهرية",
                "• رسم بياني لمؤشرات الأداء"
            });
        }

        if (IncludeTrendsCheckBox.IsChecked == true)
        {
            AddPreviewSection("📊 تحليل الاتجاهات", new[]
            {
                "• تحليل الاتجاهات المالية الشهرية",
                "• اتجاهات أداء القضايا",
                "• نمو قاعدة الموكلين",
                "• تطور مؤشرات الأداء",
                "• توقعات الفترة القادمة"
            });
        }

        if (IncludeComparisonsCheckBox.IsChecked == true)
        {
            AddPreviewSection("⚖️ المقارنات الزمنية", new[]
            {
                "• مقارنة مع الفترة السابقة",
                "• مقارنة مع نفس الفترة من العام الماضي",
                "• تحليل معدلات النمو",
                "• مقارنة الأداء بالأهداف",
                "• تحليل الانحرافات"
            });
        }

        if (IncludeRecommendationsCheckBox.IsChecked == true)
        {
            AddPreviewSection("💡 التوصيات والتحليلات", new[]
            {
                "• نقاط القوة المحددة",
                "• مجالات التحسين المطلوبة",
                "• توصيات استراتيجية",
                "• خطة العمل المقترحة",
                "• مؤشرات المتابعة"
            });
        }
    }

    private void AddPreviewSection(string title, string[] items)
    {
        var sectionPanel = new StackPanel { Margin = new Thickness(0, 0, 0, 15) };
        
        var titleBlock = new TextBlock
        {
            Text = title,
            FontSize = 14,
            FontWeight = FontWeights.Bold,
            Margin = new Thickness(0, 0, 0, 8)
        };
        
        sectionPanel.Children.Add(titleBlock);
        
        foreach (var item in items)
        {
            var itemBlock = new TextBlock
            {
                Text = item,
                FontSize = 11,
                Margin = new Thickness(10, 2, 0, 2),
                Foreground = System.Windows.Media.Brushes.Gray
            };
            
            sectionPanel.Children.Add(itemBlock);
        }
        
        PreviewContentPanel.Children.Add(sectionPanel);
    }

    private void UpdateFileInfo()
    {
        var fileName = $"تقرير_شامل_{DateTime.Now:yyyy_MM_dd}";
        var extension = _selectedFormat.ToLower() switch
        {
            "pdf" => ".pdf",
            "excel" => ".xlsx",
            "word" => ".docx",
            "powerpoint" => ".pptx",
            "html" => ".html",
            _ => ".pdf"
        };
        
        FileNameText.Text = fileName + extension;
        
        // تقدير حجم الملف بناءً على المحتوى المحدد
        var estimatedSize = CalculateEstimatedFileSize();
        FileSizeText.Text = $"~{estimatedSize:F1} MB";
        
        // تقدير عدد الصفحات
        var estimatedPages = CalculateEstimatedPages();
        PageCountText.Text = $"~{estimatedPages} صفحة";
        
        // تقدير وقت الإنشاء
        var estimatedTime = CalculateEstimatedTime();
        GenerationTimeText.Text = $"~{estimatedTime} ثانية";
    }

    private double CalculateEstimatedFileSize()
    {
        double baseSize = 0.5; // حجم أساسي
        
        if (IncludeKPIsCheckBox.IsChecked == true) baseSize += 0.2;
        if (IncludeFinancialCheckBox.IsChecked == true) baseSize += 0.3;
        if (IncludeCasesCheckBox.IsChecked == true) baseSize += 0.3;
        if (IncludeClientsCheckBox.IsChecked == true) baseSize += 0.3;
        if (IncludeAppointmentsCheckBox.IsChecked == true) baseSize += 0.2;
        if (IncludePerformanceCheckBox.IsChecked == true) baseSize += 0.2;
        if (IncludeChartsCheckBox.IsChecked == true) baseSize += 1.0; // الرسوم البيانية تأخذ مساحة أكبر
        if (IncludeTrendsCheckBox.IsChecked == true) baseSize += 0.4;
        if (IncludeComparisonsCheckBox.IsChecked == true) baseSize += 0.4;
        if (IncludeRecommendationsCheckBox.IsChecked == true) baseSize += 0.3;
        
        if (HighQualityChartsCheckBox.IsChecked == true) baseSize += 0.5;
        
        // تعديل بناءً على التنسيق
        return _selectedFormat.ToLower() switch
        {
            "excel" => baseSize * 0.7,
            "word" => baseSize * 0.8,
            "powerpoint" => baseSize * 1.2,
            "html" => baseSize * 0.6,
            _ => baseSize // PDF
        };
    }

    private int CalculateEstimatedPages()
    {
        int pages = 1; // صفحة الغلاف
        
        if (IncludeCoverPageCheckBox.IsChecked == true) pages += 1;
        if (IncludeTableOfContentsCheckBox.IsChecked == true) pages += 1;
        if (IncludeKPIsCheckBox.IsChecked == true) pages += 2;
        if (IncludeFinancialCheckBox.IsChecked == true) pages += 3;
        if (IncludeCasesCheckBox.IsChecked == true) pages += 2;
        if (IncludeClientsCheckBox.IsChecked == true) pages += 2;
        if (IncludeAppointmentsCheckBox.IsChecked == true) pages += 2;
        if (IncludePerformanceCheckBox.IsChecked == true) pages += 2;
        if (IncludeChartsCheckBox.IsChecked == true) pages += 4;
        if (IncludeTrendsCheckBox.IsChecked == true) pages += 3;
        if (IncludeComparisonsCheckBox.IsChecked == true) pages += 3;
        if (IncludeRecommendationsCheckBox.IsChecked == true) pages += 2;
        
        return Math.Max(1, pages);
    }

    private int CalculateEstimatedTime()
    {
        int baseTime = 10; // وقت أساسي
        
        if (IncludeChartsCheckBox.IsChecked == true) baseTime += 15;
        if (HighQualityChartsCheckBox.IsChecked == true) baseTime += 10;
        if (IncludeTrendsCheckBox.IsChecked == true) baseTime += 8;
        if (IncludeComparisonsCheckBox.IsChecked == true) baseTime += 8;
        
        // تعديل بناءً على التنسيق
        return _selectedFormat.ToLower() switch
        {
            "excel" => baseTime + 5,
            "word" => baseTime + 3,
            "powerpoint" => baseTime + 10,
            "html" => baseTime + 2,
            _ => baseTime // PDF
        };
    }

    // معالجات الأحداث
    private void ExportButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var saveDialog = new SaveFileDialog
            {
                Title = "حفظ التقرير",
                FileName = FileNameText.Text,
                Filter = GetFileFilter()
            };

            if (saveDialog.ShowDialog() == true)
            {
                var exportOptions = CreateExportOptions();

                // إظهار نافذة التقدم
                var progressWindow = new ExportProgressWindow();
                progressWindow.Show();

                // تنفيذ التصدير في خيط منفصل
                System.Threading.Tasks.Task.Run(async () =>
                {
                    try
                    {
                        await ExportReport(saveDialog.FileName, exportOptions);

                        Dispatcher.Invoke(() =>
                        {
                            progressWindow.Close();
                            MessageBox.Show($"تم تصدير التقرير بنجاح!\n\nالملف: {saveDialog.FileName}",
                                           "نجح التصدير", MessageBoxButton.OK, MessageBoxImage.Information);
                            DialogResult = true;
                            Close();
                        });
                    }
                    catch (Exception ex)
                    {
                        Dispatcher.Invoke(() =>
                        {
                            progressWindow.Close();
                            MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ",
                                           MessageBoxButton.OK, MessageBoxImage.Error);
                        });
                    }
                });
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في بدء عملية التصدير: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void PreviewButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var exportOptions = CreateExportOptions();
            // var previewWindow = new ReportPreviewWindow(_report, exportOptions);
            // previewWindow.ShowDialog();
            MessageBox.Show("ميزة المعاينة الكاملة قيد التطوير", "معلومات",
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح المعاينة: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void SaveTemplateButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var templateName = $"قالب_تقرير_{DateTime.Now:yyyy_MM_dd}";

            // يمكن إضافة نافذة إدخال مخصصة لاحقاً
            var exportOptions = CreateExportOptions();
            SaveReportTemplate(templateName, exportOptions);

            MessageBox.Show($"تم حفظ القالب '{templateName}' بنجاح!", "حفظ القالب",
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في حفظ القالب: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = false;
        Close();
    }

    // وظائف مساعدة
    private string GetFileFilter()
    {
        return _selectedFormat.ToLower() switch
        {
            "pdf" => "PDF Files (*.pdf)|*.pdf",
            "excel" => "Excel Files (*.xlsx)|*.xlsx",
            "word" => "Word Documents (*.docx)|*.docx",
            "powerpoint" => "PowerPoint Presentations (*.pptx)|*.pptx",
            "html" => "HTML Files (*.html)|*.html",
            _ => "All Files (*.*)|*.*"
        };
    }

    private ExportOptions CreateExportOptions()
    {
        return new ExportOptions
        {
            Format = _selectedFormat,
            IncludeKPIs = IncludeKPIsCheckBox.IsChecked == true,
            IncludeFinancial = IncludeFinancialCheckBox.IsChecked == true,
            IncludeCases = IncludeCasesCheckBox.IsChecked == true,
            IncludeClients = IncludeClientsCheckBox.IsChecked == true,
            IncludeAppointments = IncludeAppointmentsCheckBox.IsChecked == true,
            IncludePerformance = IncludePerformanceCheckBox.IsChecked == true,
            IncludeCharts = IncludeChartsCheckBox.IsChecked == true,
            IncludeTrends = IncludeTrendsCheckBox.IsChecked == true,
            IncludeComparisons = IncludeComparisonsCheckBox.IsChecked == true,
            IncludeRecommendations = IncludeRecommendationsCheckBox.IsChecked == true,
            IncludeCoverPage = IncludeCoverPageCheckBox.IsChecked == true,
            IncludeTableOfContents = IncludeTableOfContentsCheckBox.IsChecked == true,
            IncludeWatermark = IncludeWatermarkCheckBox.IsChecked == true,
            IncludeFooter = IncludeFooterCheckBox.IsChecked == true,
            HighQualityCharts = HighQualityChartsCheckBox.IsChecked == true,
            CompressFile = CompressFileCheckBox.IsChecked == true
        };
    }

    private async System.Threading.Tasks.Task ExportReport(string fileName, ExportOptions options)
    {
        // محاكاة عملية التصدير
        await System.Threading.Tasks.Task.Delay(2000);

        // هنا يمكن إضافة منطق التصدير الفعلي
        switch (options.Format.ToLower())
        {
            case "pdf":
                await ExportToPdf(fileName, options);
                break;
            case "excel":
                await ExportToExcel(fileName, options);
                break;
            case "word":
                await ExportToWord(fileName, options);
                break;
            case "powerpoint":
                await ExportToPowerPoint(fileName, options);
                break;
            case "html":
                await ExportToHtml(fileName, options);
                break;
        }
    }

    private async System.Threading.Tasks.Task ExportToPdf(string fileName, ExportOptions options)
    {
        // منطق تصدير PDF
        await System.Threading.Tasks.Task.Delay(1000);

        // إنشاء ملف PDF بسيط للاختبار
        var content = GenerateReportContent(options);
        await File.WriteAllTextAsync(fileName.Replace(".pdf", ".txt"), content);
    }

    private async System.Threading.Tasks.Task ExportToExcel(string fileName, ExportOptions options)
    {
        // منطق تصدير Excel
        await System.Threading.Tasks.Task.Delay(800);

        var content = GenerateReportContent(options);
        await File.WriteAllTextAsync(fileName.Replace(".xlsx", ".txt"), content);
    }

    private async System.Threading.Tasks.Task ExportToWord(string fileName, ExportOptions options)
    {
        // منطق تصدير Word
        await System.Threading.Tasks.Task.Delay(600);

        var content = GenerateReportContent(options);
        await File.WriteAllTextAsync(fileName.Replace(".docx", ".txt"), content);
    }

    private async System.Threading.Tasks.Task ExportToPowerPoint(string fileName, ExportOptions options)
    {
        // منطق تصدير PowerPoint
        await System.Threading.Tasks.Task.Delay(1200);

        var content = GenerateReportContent(options);
        await File.WriteAllTextAsync(fileName.Replace(".pptx", ".txt"), content);
    }

    private async System.Threading.Tasks.Task ExportToHtml(string fileName, ExportOptions options)
    {
        // منطق تصدير HTML
        await System.Threading.Tasks.Task.Delay(400);

        var htmlContent = GenerateHtmlContent(options);
        await File.WriteAllTextAsync(fileName, htmlContent);
    }

    private string GenerateReportContent(ExportOptions options)
    {
        var content = $"تقرير شامل - AvocatPro\n";
        content += $"الفترة: {_report.PeriodDisplay}\n";
        content += $"تاريخ الإنشاء: {_report.GeneratedAt}\n\n";

        if (options.IncludeKPIs)
        {
            content += "المؤشرات الرئيسية:\n";
            content += $"إجمالي الإيرادات: {_report.Financial.TotalRevenuesDisplay}\n";
            content += $"إجمالي المصاريف: {_report.Financial.TotalExpensesDisplay}\n";
            content += $"صافي الربح: {_report.Financial.NetProfitDisplay}\n\n";
        }

        // إضافة باقي المحتوى بناءً على الخيارات المحددة

        return content;
    }

    private string GenerateHtmlContent(ExportOptions options)
    {
        var html = $@"
<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <title>تقرير شامل - AvocatPro</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; }}
        .header {{ text-align: center; margin-bottom: 30px; }}
        .section {{ margin-bottom: 20px; }}
        .kpi {{ background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }}
    </style>
</head>
<body>
    <div class='header'>
        <h1>تقرير شامل - AvocatPro</h1>
        <p>الفترة: {_report.PeriodDisplay}</p>
        <p>تاريخ الإنشاء: {_report.GeneratedAt}</p>
    </div>";

        if (options.IncludeKPIs)
        {
            html += @"
    <div class='section'>
        <h2>المؤشرات الرئيسية</h2>";
            html += $"<div class='kpi'>إجمالي الإيرادات: {_report.Financial.TotalRevenuesDisplay}</div>";
            html += $"<div class='kpi'>إجمالي المصاريف: {_report.Financial.TotalExpensesDisplay}</div>";
            html += $"<div class='kpi'>صافي الربح: {_report.Financial.NetProfitDisplay}</div>";
            html += "</div>";
        }

        html += @"
</body>
</html>";

        return html;
    }

    private void SaveReportTemplate(string templateName, ExportOptions options)
    {
        // حفظ القالب في ملف JSON أو قاعدة البيانات
        var templateData = System.Text.Json.JsonSerializer.Serialize(options);
        var templatesDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "AvocatPro", "Templates");

        if (!Directory.Exists(templatesDir))
            Directory.CreateDirectory(templatesDir);

        var templateFile = Path.Combine(templatesDir, $"{templateName}.json");
        File.WriteAllText(templateFile, templateData);
    }
}

// فئة خيارات التصدير
public class ExportOptions
{
    public string Format { get; set; } = "PDF";
    public bool IncludeKPIs { get; set; } = true;
    public bool IncludeFinancial { get; set; } = true;
    public bool IncludeCases { get; set; } = true;
    public bool IncludeClients { get; set; } = true;
    public bool IncludeAppointments { get; set; } = true;
    public bool IncludePerformance { get; set; } = true;
    public bool IncludeCharts { get; set; } = true;
    public bool IncludeTrends { get; set; } = false;
    public bool IncludeComparisons { get; set; } = false;
    public bool IncludeRecommendations { get; set; } = false;
    public bool IncludeCoverPage { get; set; } = true;
    public bool IncludeTableOfContents { get; set; } = true;
    public bool IncludeWatermark { get; set; } = false;
    public bool IncludeFooter { get; set; } = true;
    public bool HighQualityCharts { get; set; } = true;
    public bool CompressFile { get; set; } = false;
}
