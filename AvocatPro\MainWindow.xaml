﻿<Window x:Class="AvocatPro.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:AvocatPro"
        mc:Ignorable="d"
        Title="AvocatPro - نظام إدارة المكاتب القانونية المتطور"
        Height="900" Width="1600" MinHeight="700" MinWidth="1200"
        WindowStartupLocation="CenterScreen" WindowState="Maximized"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- تدرجات الألوان القانونية الأنيقة -->
        <LinearGradientBrush x:Key="PrimaryGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#1e3a8a" Offset="0"/>
            <GradientStop Color="#1e40af" Offset="0.3"/>
            <GradientStop Color="#3b82f6" Offset="0.7"/>
            <GradientStop Color="#60a5fa" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="GoldAccent" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#b45309" Offset="0"/>
            <GradientStop Color="#d97706" Offset="0.3"/>
            <GradientStop Color="#f59e0b" Offset="0.7"/>
            <GradientStop Color="#fbbf24" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="SidebarGradient" StartPoint="0,0" EndPoint="0,1">
            <GradientStop Color="#0f172a" Offset="0"/>
            <GradientStop Color="#1e293b" Offset="0.3"/>
            <GradientStop Color="#334155" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="HeaderGradient" StartPoint="0,0" EndPoint="1,0">
            <GradientStop Color="#1e293b" Offset="0"/>
            <GradientStop Color="#334155" Offset="0.5"/>
            <GradientStop Color="#475569" Offset="1"/>
        </LinearGradientBrush>

        <!-- أنماط الأزرار المتطورة -->
        <Style x:Key="ModernMenuButton" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#e2e8f0"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,15"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
            <Setter Property="Margin" Value="0,2"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="12"
                                Margin="10,2">
                            <ContentPresenter Margin="{TemplateBinding Padding}"
                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                            <GradientStop Color="#3b82f6" Offset="0"/>
                                            <GradientStop Color="#60a5fa" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                            <GradientStop Color="#1e40af" Offset="0"/>
                                            <GradientStop Color="#3b82f6" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- أنماط النصوص المحسنة -->
        <Style x:Key="HeaderText" TargetType="TextBlock">
            <Setter Property="FontSize" Value="28"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
        </Style>

        <Style x:Key="SubHeaderText" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#94a3b8"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
        </Style>

        <Style x:Key="StatusText" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#64748b"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
        </Style>

        <!-- أنماط البطاقات -->
        <Style x:Key="ModernCard" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="16"/>
            <Setter Property="Padding" Value="25"/>
            <Setter Property="Margin" Value="15"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Opacity="0.08" BlurRadius="20" ShadowDepth="4"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Effect">
                        <Setter.Value>
                            <DropShadowEffect Color="Black" Opacity="0.15" BlurRadius="30" ShadowDepth="8"/>
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <!-- الخلفية الرئيسية -->
        <Grid.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#f8fafc" Offset="0"/>
                <GradientStop Color="#f1f5f9" Offset="0.5"/>
                <GradientStop Color="#e2e8f0" Offset="1"/>
            </LinearGradientBrush>
        </Grid.Background>

        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="320"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- الشريط الجانبي الأنيق -->
        <Border Grid.Column="0" Background="{StaticResource SidebarGradient}">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.2" BlurRadius="15" ShadowDepth="5"/>
            </Border.Effect>

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- شعار التطبيق المتطور -->
                <Border Grid.Row="0" Padding="25,30">
                    <StackPanel HorizontalAlignment="Center">
                        <!-- الشعار الرئيسي -->
                        <Border Width="80" Height="80" CornerRadius="40" Margin="0,0,0,20">
                            <Border.Background>
                                <RadialGradientBrush>
                                    <GradientStop Color="#fbbf24" Offset="0"/>
                                    <GradientStop Color="#f59e0b" Offset="0.7"/>
                                    <GradientStop Color="#d97706" Offset="1"/>
                                </RadialGradientBrush>
                            </Border.Background>
                            <Border.Effect>
                                <DropShadowEffect Color="#f59e0b" Opacity="0.4" BlurRadius="20" ShadowDepth="0"/>
                            </Border.Effect>
                            <TextBlock Text="⚖️" FontSize="40" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>

                        <!-- اسم التطبيق -->
                        <TextBlock Text="AvocatPro" Style="{StaticResource HeaderText}"
                                   HorizontalAlignment="Center" Margin="0,0,0,5"/>
                        <TextBlock Text="نظام إدارة المكاتب القانونية المتطور"
                                   Style="{StaticResource SubHeaderText}"
                                   HorizontalAlignment="Center" TextAlignment="Center"/>

                        <!-- خط فاصل أنيق -->
                        <Rectangle Height="2" Margin="0,20,0,0" HorizontalAlignment="Stretch">
                            <Rectangle.Fill>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                    <GradientStop Color="Transparent" Offset="0"/>
                                    <GradientStop Color="#60a5fa" Offset="0.5"/>
                                    <GradientStop Color="Transparent" Offset="1"/>
                                </LinearGradientBrush>
                            </Rectangle.Fill>
                        </Rectangle>
                    </StackPanel>
                </Border>

                <!-- القائمة الرئيسية المتطورة -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto"
                              HorizontalScrollBarVisibility="Disabled" Padding="0,20">
                    <StackPanel>
                        <!-- قسم الإدارة الرئيسية -->
                        <TextBlock Text="الإدارة الرئيسية" FontSize="14" FontWeight="SemiBold"
                                   Foreground="#94a3b8" Margin="20,0,20,15"/>

                        <Button x:Name="DashboardBtn" Style="{StaticResource ModernMenuButton}" Click="Dashboard_Click">
                            <StackPanel Orientation="Horizontal">
                                <Border Width="35" Height="35" CornerRadius="8" Margin="0,0,15,0">
                                    <Border.Background>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                            <GradientStop Color="#3b82f6" Offset="0"/>
                                            <GradientStop Color="#1e40af" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Border.Background>
                                    <TextBlock Text="🏠" FontSize="18" Foreground="White"
                                               HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <StackPanel VerticalAlignment="Center">
                                    <TextBlock Text="الصفحة الرئيسية" FontSize="16" FontWeight="SemiBold"/>
                                    <TextBlock Text="لوحة التحكم العامة" FontSize="12" Opacity="0.7"/>
                                </StackPanel>
                            </StackPanel>
                        </Button>

                        <Button x:Name="InteractiveDashboardBtn" Style="{StaticResource ModernMenuButton}" Click="InteractiveDashboard_Click">
                            <StackPanel Orientation="Horizontal">
                                <Border Width="35" Height="35" CornerRadius="8" Margin="0,0,15,0">
                                    <Border.Background>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                            <GradientStop Color="#10b981" Offset="0"/>
                                            <GradientStop Color="#047857" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Border.Background>
                                    <TextBlock Text="📊" FontSize="18" Foreground="White"
                                               HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <StackPanel VerticalAlignment="Center">
                                    <TextBlock Text="لوحة التحكم التفاعلية" FontSize="16" FontWeight="SemiBold"/>
                                    <TextBlock Text="إحصائيات ومخططات" FontSize="12" Opacity="0.7"/>
                                </StackPanel>
                            </StackPanel>
                        </Button>

                        <!-- قسم الأمان والحماية -->
                        <TextBlock Text="الأمان والحماية" FontSize="14" FontWeight="SemiBold"
                                   Foreground="#94a3b8" Margin="20,30,20,15"/>

                        <Button x:Name="SecurityBtn" Style="{StaticResource ModernMenuButton}" Click="Security_Click">
                            <StackPanel Orientation="Horizontal">
                                <Border Width="35" Height="35" CornerRadius="8" Margin="0,0,15,0">
                                    <Border.Background>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                            <GradientStop Color="#ef4444" Offset="0"/>
                                            <GradientStop Color="#dc2626" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Border.Background>
                                    <TextBlock Text="🔒" FontSize="18" Foreground="White"
                                               HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <StackPanel VerticalAlignment="Center">
                                    <TextBlock Text="إدارة الأمان" FontSize="16" FontWeight="SemiBold"/>
                                    <TextBlock Text="حماية وتشفير البيانات" FontSize="12" Opacity="0.7"/>
                                </StackPanel>
                            </StackPanel>
                        </Button>

                        <!-- قسم إدارة الوثائق -->
                        <TextBlock Text="إدارة الوثائق" FontSize="14" FontWeight="SemiBold"
                                   Foreground="#94a3b8" Margin="20,30,20,15"/>

                        <Button x:Name="DocumentsBtn" Style="{StaticResource ModernMenuButton}" Click="Documents_Click">
                            <StackPanel Orientation="Horizontal">
                                <Border Width="35" Height="35" CornerRadius="8" Margin="0,0,15,0">
                                    <Border.Background>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                            <GradientStop Color="#8b5cf6" Offset="0"/>
                                            <GradientStop Color="#7c3aed" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Border.Background>
                                    <TextBlock Text="📄" FontSize="18" Foreground="White"
                                               HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <StackPanel VerticalAlignment="Center">
                                    <TextBlock Text="الوثائق المتطورة" FontSize="16" FontWeight="SemiBold"/>
                                    <TextBlock Text="مسح ضوئي وتوقيع رقمي" FontSize="12" Opacity="0.7"/>
                                </StackPanel>
                            </StackPanel>
                        </Button>

                        <!-- قسم الذكاء الاصطناعي -->
                        <TextBlock Text="الذكاء الاصطناعي" FontSize="14" FontWeight="SemiBold"
                                   Foreground="#94a3b8" Margin="20,30,20,15"/>

                        <Button x:Name="SmartAssistantBtn" Style="{StaticResource ModernMenuButton}" Click="SmartAssistant_Click">
                            <StackPanel Orientation="Horizontal">
                                <Border Width="35" Height="35" CornerRadius="8" Margin="0,0,15,0">
                                    <Border.Background>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                            <GradientStop Color="#f59e0b" Offset="0"/>
                                            <GradientStop Color="#d97706" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Border.Background>
                                    <TextBlock Text="🤖" FontSize="18" Foreground="White"
                                               HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <StackPanel VerticalAlignment="Center">
                                    <TextBlock Text="المساعد الذكي" FontSize="16" FontWeight="SemiBold"/>
                                    <TextBlock Text="مساعدة قانونية ذكية" FontSize="12" Opacity="0.7"/>
                                </StackPanel>
                            </StackPanel>
                        </Button>

                        <!-- قسم الإعدادات -->
                        <TextBlock Text="الإعدادات والتخصيص" FontSize="14" FontWeight="SemiBold"
                                   Foreground="#94a3b8" Margin="20,30,20,15"/>

                        <Button x:Name="UISettingsBtn" Style="{StaticResource ModernMenuButton}" Click="UISettings_Click">
                            <StackPanel Orientation="Horizontal">
                                <Border Width="35" Height="35" CornerRadius="8" Margin="0,0,15,0">
                                    <Border.Background>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                            <GradientStop Color="#ec4899" Offset="0"/>
                                            <GradientStop Color="#db2777" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Border.Background>
                                    <TextBlock Text="🎨" FontSize="18" Foreground="White"
                                               HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <StackPanel VerticalAlignment="Center">
                                    <TextBlock Text="إعدادات الواجهة" FontSize="16" FontWeight="SemiBold"/>
                                    <TextBlock Text="ثيمات وألوان متقدمة" FontSize="12" Opacity="0.7"/>
                                </StackPanel>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </ScrollViewer>

                <!-- معلومات المستخدم والحالة -->
                <Border Grid.Row="2" Padding="20" Background="#1e293b">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- معلومات المستخدم -->
                        <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                            <Border Width="45" Height="45" CornerRadius="22" Margin="0,0,15,0">
                                <Border.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#10b981" Offset="0"/>
                                        <GradientStop Color="#047857" Offset="1"/>
                                    </LinearGradientBrush>
                                </Border.Background>
                                <Border.Effect>
                                    <DropShadowEffect Color="#10b981" Opacity="0.3" BlurRadius="10" ShadowDepth="0"/>
                                </Border.Effect>
                                <TextBlock Text="👤" FontSize="22" Foreground="White"
                                           HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>

                            <StackPanel VerticalAlignment="Center">
                                <TextBlock Text="المحامي الرئيسي" FontSize="16" FontWeight="SemiBold" Foreground="White"/>
                                <StackPanel Orientation="Horizontal">
                                    <Ellipse Width="8" Height="8" Fill="#10b981" Margin="0,0,8,0"/>
                                    <TextBlock Text="متصل الآن" FontSize="12" Foreground="#94a3b8"/>
                                </StackPanel>
                            </StackPanel>
                        </StackPanel>

                        <!-- أزرار الإجراءات السريعة -->
                        <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                            <Button x:Name="NotificationsBtn" Background="Transparent" BorderThickness="0"
                                    Padding="12" Margin="0,0,10,0" ToolTip="الإشعارات">
                                <Grid>
                                    <TextBlock Text="🔔" FontSize="20" Foreground="#94a3b8"/>
                                    <Border Background="#ef4444" CornerRadius="8" Width="16" Height="16"
                                            HorizontalAlignment="Right" VerticalAlignment="Top" Margin="0,-8,-8,0">
                                        <TextBlock Text="3" FontSize="10" Foreground="White" FontWeight="Bold"
                                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                </Grid>
                            </Button>

                            <Button x:Name="SettingsBtn" Background="Transparent" BorderThickness="0"
                                    Padding="12" Margin="0,0,10,0" ToolTip="الإعدادات">
                                <TextBlock Text="⚙️" FontSize="20" Foreground="#94a3b8"/>
                            </Button>

                            <Button x:Name="LogoutBtn" Background="Transparent" BorderThickness="0"
                                    Padding="12" ToolTip="تسجيل الخروج">
                                <TextBlock Text="🚪" FontSize="20" Foreground="#94a3b8"/>
                            </Button>
                        </StackPanel>
                    </Grid>
                </Border>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي المتطور -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- شريط العنوان العلوي -->
            <Border Grid.Row="0" Background="{StaticResource HeaderGradient}" Padding="30,20">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="3"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- عنوان الصفحة الحالية -->
                    <StackPanel Grid.Column="0" VerticalAlignment="Center">
                        <TextBlock x:Name="PageTitleText" Text="لوحة التحكم الرئيسية"
                                   Style="{StaticResource HeaderText}" Margin="0,0,0,5"/>
                        <TextBlock x:Name="PageDescriptionText" Text="مرحباً بك في نظام إدارة المكاتب القانونية المتطور"
                                   Style="{StaticResource SubHeaderText}"/>
                    </StackPanel>

                    <!-- أزرار التحكم السريع -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                        <Button Background="Transparent" BorderThickness="0" Padding="15,10"
                                Margin="0,0,10,0" ToolTip="البحث السريع">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🔍" FontSize="18" Foreground="White" Margin="0,0,8,0"/>
                                <TextBlock Text="بحث" FontSize="14" Foreground="White" FontWeight="SemiBold"/>
                            </StackPanel>
                        </Button>

                        <Button Background="Transparent" BorderThickness="0" Padding="15,10"
                                ToolTip="إضافة جديد">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="➕" FontSize="18" Foreground="White" Margin="0,0,8,0"/>
                                <TextBlock Text="جديد" FontSize="14" Foreground="White" FontWeight="SemiBold"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- المحتوى الرئيسي -->
            <Border Grid.Row="1" Background="Transparent" Margin="20">
                <Frame x:Name="MainFrame" NavigationUIVisibility="Hidden"
                       Background="Transparent" BorderThickness="0"/>
            </Border>

            <!-- شريط الحالة المتطور -->
            <Border Grid.Row="2" Background="White" Padding="30,15">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.05" BlurRadius="10" ShadowDepth="-3"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- حالة النظام -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        <Ellipse Width="12" Height="12" Fill="#10b981" Margin="0,0,10,0"/>
                        <TextBlock x:Name="StatusText" Text="النظام جاهز - جميع الخدمات تعمل بكفاءة"
                                   Style="{StaticResource StatusText}"/>
                    </StackPanel>

                    <!-- معلومات الاتصال -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                        <Border Background="#f1f5f9" CornerRadius="15" Padding="12,6" Margin="0,0,10,0">
                            <StackPanel Orientation="Horizontal">
                                <Ellipse Width="8" Height="8" Fill="#10b981" Margin="0,0,6,0"/>
                                <TextBlock Text="متصل" FontSize="12" Foreground="#64748b" FontWeight="SemiBold"/>
                            </StackPanel>
                        </Border>

                        <Border Background="#f1f5f9" CornerRadius="15" Padding="12,6">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🌐" FontSize="12" Margin="0,0,6,0"/>
                                <TextBlock Text="خادم محلي" FontSize="12" Foreground="#64748b" FontWeight="SemiBold"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>

                    <!-- الوقت ومعلومات النسخة -->
                    <StackPanel Grid.Column="2" VerticalAlignment="Center" HorizontalAlignment="Left">
                        <TextBlock x:Name="TimeText" FontSize="14" Foreground="#475569"
                                   FontWeight="SemiBold" HorizontalAlignment="Left"/>
                        <TextBlock Text="AvocatPro v2.0 - GenerationFive" FontSize="11"
                                   Foreground="#94a3b8" HorizontalAlignment="Left"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</Window>
