<Window x:Class="AvocatPro.Views.Windows.EditUserWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تعديل بيانات المستخدم"
        Width="600" Height="700"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">

    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,30">
            <TextBlock Text="✏️" FontSize="24" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <TextBlock Text="تعديل بيانات المستخدم" FontSize="20" FontWeight="Bold" VerticalAlignment="Center"/>
        </StackPanel>

        <!-- المحتوى -->
        <TabControl Grid.Row="1">
            <TabItem Header="البيانات الأساسية">
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
                    <StackPanel>
                        <TextBlock Text="الاسم الأول *" FontWeight="Medium" Margin="0,0,0,5"/>
                        <TextBox Name="FirstNameTextBox" Padding="10,8" Margin="0,5,0,15" FontSize="14"/>

                        <TextBlock Text="الاسم الأخير *" FontWeight="Medium" Margin="0,0,0,5"/>
                        <TextBox Name="LastNameTextBox" Padding="10,8" Margin="0,5,0,15" FontSize="14"/>

                        <TextBlock Text="البريد الإلكتروني *" FontWeight="Medium" Margin="0,0,0,5"/>
                        <TextBox Name="EmailTextBox" Padding="10,8" Margin="0,5,0,15" FontSize="14"/>

                        <TextBlock Text="رقم الهاتف" FontWeight="Medium" Margin="0,0,0,5"/>
                        <TextBox Name="PhoneTextBox" Padding="10,8" Margin="0,5,0,15" FontSize="14"/>

                        <TextBlock Text="العنوان" FontWeight="Medium" Margin="0,0,0,5"/>
                        <TextBox Name="AddressTextBox" Padding="10,8" Margin="0,5,0,15" FontSize="14"/>

                        <TextBlock Text="الدور *" FontWeight="Medium" Margin="0,0,0,5"/>
                        <ComboBox Name="RoleComboBox" Padding="10,8" Margin="0,5,0,15" FontSize="14"/>

                        <TextBlock Text="الحالة *" FontWeight="Medium" Margin="0,0,0,5"/>
                        <ComboBox Name="StatusComboBox" Padding="10,8" Margin="0,5,0,15" FontSize="14"/>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <TabItem Header="إعدادات كلمة المرور">
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
                    <StackPanel>
                        <CheckBox Name="MustChangePasswordCheckBox" Content="يجب تغيير كلمة المرور في التسجيل التالي" 
                                  Margin="0,0,0,15"/>

                        <TextBlock Text="مدة انتهاء كلمة المرور (بالأيام)" FontWeight="Medium" Margin="0,0,0,5"/>
                        <TextBox Name="PasswordExpiryDaysTextBox" Padding="10,8" Margin="0,5,0,15" FontSize="14"/>

                        <Button Name="ResetPasswordButton" Content="🔄 إعادة تعيين كلمة المرور" 
                                Padding="15,10" Margin="0,10" Background="#FF9800" Foreground="White" 
                                BorderThickness="0" Click="ResetPasswordButton_Click"/>

                        <Button Name="UnlockAccountButton" Content="🔓 إلغاء قفل الحساب" 
                                Padding="15,10" Margin="0,10" Background="#4CAF50" Foreground="White" 
                                BorderThickness="0" Click="UnlockAccountButton_Click"/>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <TabItem Header="معلومات إضافية">
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
                    <StackPanel>
                        <TextBlock Text="تاريخ الميلاد" FontWeight="Medium" Margin="0,0,0,5"/>
                        <DatePicker Name="DateOfBirthPicker" Padding="10,8" Margin="0,5,0,15" FontSize="14"/>

                        <TextBlock Text="الجنس" FontWeight="Medium" Margin="0,0,0,5"/>
                        <ComboBox Name="GenderComboBox" Padding="10,8" Margin="0,5,0,15" FontSize="14"/>

                        <TextBlock Text="الجنسية" FontWeight="Medium" Margin="0,0,0,5"/>
                        <TextBox Name="NationalityTextBox" Padding="10,8" Margin="0,5,0,15" FontSize="14"/>

                        <TextBlock Text="رقم الهوية" FontWeight="Medium" Margin="0,0,0,5"/>
                        <TextBox Name="IdentityNumberTextBox" Padding="10,8" Margin="0,5,0,15" FontSize="14"/>

                        <TextBlock Text="ملاحظات" FontWeight="Medium" Margin="0,0,0,5"/>
                        <TextBox Name="NotesTextBox" Padding="10,8" Margin="0,5,0,15" FontSize="14" 
                                 Height="100" TextWrapping="Wrap" AcceptsReturn="True"/>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>

        <!-- الأزرار -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Name="SaveButton" Content="💾 حفظ التغييرات" Padding="20,10" Margin="5" 
                    Background="#4CAF50" Foreground="White" BorderThickness="0" FontSize="14" 
                    Click="SaveButton_Click"/>
            <Button Name="CancelButton" Content="❌ إلغاء" Padding="20,10" Margin="5" 
                    Background="#F44336" Foreground="White" BorderThickness="0" FontSize="14" 
                    Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
