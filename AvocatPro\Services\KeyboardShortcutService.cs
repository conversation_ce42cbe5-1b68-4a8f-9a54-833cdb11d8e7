using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using AvocatPro.Models;

namespace AvocatPro.Services
{
    public class KeyboardShortcutService
    {
        private readonly Dictionary<string, KeyboardShortcut> _shortcuts;
        private readonly Dictionary<string, Action> _actions;
        private readonly List<Window> _registeredWindows;

        public event EventHandler<ShortcutExecutedEventArgs>? ShortcutExecuted;

        public KeyboardShortcutService()
        {
            _shortcuts = new Dictionary<string, KeyboardShortcut>();
            _actions = new Dictionary<string, Action>();
            _registeredWindows = new List<Window>();
            
            InitializeDefaultShortcuts();
        }

        // 🔧 تسجيل الاختصارات
        public void RegisterShortcut(string id, Key key, ModifierKeys modifiers, Action action, string description = "")
        {
            var shortcut = new KeyboardShortcut
            {
                Id = id,
                Key = key,
                Modifiers = modifiers,
                Description = description,
                IsEnabled = true
            };

            _shortcuts[id] = shortcut;
            _actions[id] = action;
        }

        public void RegisterShortcut(string id, string keyCombo, Action action, string description = "")
        {
            if (TryParseKeyCombo(keyCombo, out var key, out var modifiers))
            {
                RegisterShortcut(id, key, modifiers, action, description);
            }
        }

        // 🪟 تسجيل النوافذ
        public void RegisterWindow(Window window)
        {
            if (_registeredWindows.Contains(window)) return;

            _registeredWindows.Add(window);
            window.KeyDown += Window_KeyDown;
            window.Closed += (s, e) => UnregisterWindow(window);
        }

        public void UnregisterWindow(Window window)
        {
            if (_registeredWindows.Contains(window))
            {
                window.KeyDown -= Window_KeyDown;
                _registeredWindows.Remove(window);
            }
        }

        // ⚡ تنفيذ الاختصارات
        private void Window_KeyDown(object sender, KeyEventArgs e)
        {
            var modifiers = Keyboard.Modifiers;
            var key = e.Key;

            // التعامل مع مفاتيح النظام
            if (key == Key.System)
                key = e.SystemKey;

            var matchingShortcut = _shortcuts.Values.FirstOrDefault(s => 
                s.Key == key && s.Modifiers == modifiers && s.IsEnabled);

            if (matchingShortcut != null && _actions.TryGetValue(matchingShortcut.Id, out var action))
            {
                try
                {
                    action.Invoke();
                    OnShortcutExecuted(matchingShortcut);
                    e.Handled = true;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في تنفيذ الاختصار {matchingShortcut.Id}: {ex.Message}");
                }
            }
        }

        // 🎯 الاختصارات الافتراضية
        private void InitializeDefaultShortcuts()
        {
            // اختصارات التنقل
            RegisterShortcut("navigate_home", Key.H, ModifierKeys.Control, 
                () => NavigateToPage("Home"), "الانتقال للصفحة الرئيسية");
            
            RegisterShortcut("navigate_clients", Key.C, ModifierKeys.Control, 
                () => NavigateToPage("Clients"), "الانتقال لصفحة العملاء");
            
            RegisterShortcut("navigate_cases", Key.K, ModifierKeys.Control, 
                () => NavigateToPage("Cases"), "الانتقال لصفحة القضايا");
            
            RegisterShortcut("navigate_appointments", Key.A, ModifierKeys.Control, 
                () => NavigateToPage("Appointments"), "الانتقال لصفحة المواعيد");
            
            RegisterShortcut("navigate_documents", Key.D, ModifierKeys.Control, 
                () => NavigateToPage("Documents"), "الانتقال لصفحة الوثائق");
            
            RegisterShortcut("navigate_financial", Key.F, ModifierKeys.Control, 
                () => NavigateToPage("Financial"), "الانتقال لصفحة المالية");

            // اختصارات الإجراءات
            RegisterShortcut("new_client", Key.N, ModifierKeys.Control | ModifierKeys.Shift, 
                () => ExecuteAction("NewClient"), "إضافة عميل جديد");
            
            RegisterShortcut("new_case", Key.N, ModifierKeys.Control | ModifierKeys.Alt, 
                () => ExecuteAction("NewCase"), "إضافة قضية جديدة");
            
            RegisterShortcut("new_appointment", Key.T, ModifierKeys.Control, 
                () => ExecuteAction("NewAppointment"), "إضافة موعد جديد");

            // اختصارات البحث
            RegisterShortcut("global_search", Key.F, ModifierKeys.Control | ModifierKeys.Shift, 
                () => ExecuteAction("GlobalSearch"), "البحث الشامل");
            
            RegisterShortcut("quick_search", Key.Q, ModifierKeys.Control, 
                () => ExecuteAction("QuickSearch"), "البحث السريع");

            // اختصارات النظام
            RegisterShortcut("save", Key.S, ModifierKeys.Control, 
                () => ExecuteAction("Save"), "حفظ");
            
            RegisterShortcut("refresh", Key.F5, ModifierKeys.None, 
                () => ExecuteAction("Refresh"), "تحديث");
            
            RegisterShortcut("print", Key.P, ModifierKeys.Control, 
                () => ExecuteAction("Print"), "طباعة");

            // اختصارات الثيمات
            RegisterShortcut("toggle_theme", Key.T, ModifierKeys.Control | ModifierKeys.Shift, 
                () => ExecuteAction("ToggleTheme"), "تبديل الثيم");
            
            RegisterShortcut("dark_mode", Key.D, ModifierKeys.Control | ModifierKeys.Shift, 
                () => ExecuteAction("DarkMode"), "الوضع الليلي");

            // اختصارات المساعدة
            RegisterShortcut("help", Key.F1, ModifierKeys.None, 
                () => ExecuteAction("Help"), "المساعدة");
            
            RegisterShortcut("shortcuts_help", Key.OemQuestion, ModifierKeys.Control, 
                () => ExecuteAction("ShortcutsHelp"), "عرض الاختصارات");

            // اختصارات النوافذ
            RegisterShortcut("minimize", Key.M, ModifierKeys.Control | ModifierKeys.Shift, 
                () => ExecuteAction("Minimize"), "تصغير النافذة");
            
            RegisterShortcut("maximize", Key.M, ModifierKeys.Control | ModifierKeys.Alt, 
                () => ExecuteAction("Maximize"), "تكبير النافذة");
            
            RegisterShortcut("close", Key.W, ModifierKeys.Control, 
                () => ExecuteAction("Close"), "إغلاق النافذة");

            // اختصارات التحرير
            RegisterShortcut("undo", Key.Z, ModifierKeys.Control, 
                () => ExecuteAction("Undo"), "تراجع");
            
            RegisterShortcut("redo", Key.Y, ModifierKeys.Control, 
                () => ExecuteAction("Redo"), "إعادة");
            
            RegisterShortcut("copy", Key.C, ModifierKeys.Control, 
                () => ExecuteAction("Copy"), "نسخ");
            
            RegisterShortcut("paste", Key.V, ModifierKeys.Control, 
                () => ExecuteAction("Paste"), "لصق");
            
            RegisterShortcut("cut", Key.X, ModifierKeys.Control, 
                () => ExecuteAction("Cut"), "قص");

            // اختصارات التصدير
            RegisterShortcut("export_pdf", Key.E, ModifierKeys.Control | ModifierKeys.Shift, 
                () => ExecuteAction("ExportPDF"), "تصدير PDF");
            
            RegisterShortcut("export_excel", Key.E, ModifierKeys.Control | ModifierKeys.Alt, 
                () => ExecuteAction("ExportExcel"), "تصدير Excel");
        }

        // 🔍 إدارة الاختصارات
        public List<KeyboardShortcut> GetAllShortcuts()
        {
            return _shortcuts.Values.ToList();
        }

        public List<KeyboardShortcut> GetShortcutsByCategory(string category)
        {
            return _shortcuts.Values.Where(s => s.Category == category).ToList();
        }

        public void EnableShortcut(string id)
        {
            if (_shortcuts.TryGetValue(id, out var shortcut))
            {
                shortcut.IsEnabled = true;
            }
        }

        public void DisableShortcut(string id)
        {
            if (_shortcuts.TryGetValue(id, out var shortcut))
            {
                shortcut.IsEnabled = false;
            }
        }

        public void RemoveShortcut(string id)
        {
            _shortcuts.Remove(id);
            _actions.Remove(id);
        }

        // 🔧 دوال مساعدة
        private bool TryParseKeyCombo(string keyCombo, out Key key, out ModifierKeys modifiers)
        {
            key = Key.None;
            modifiers = ModifierKeys.None;

            try
            {
                var parts = keyCombo.Split('+');
                var keyPart = parts.Last();
                
                if (Enum.TryParse<Key>(keyPart, true, out key))
                {
                    foreach (var part in parts.Take(parts.Length - 1))
                    {
                        switch (part.ToLower().Trim())
                        {
                            case "ctrl":
                            case "control":
                                modifiers |= ModifierKeys.Control;
                                break;
                            case "alt":
                                modifiers |= ModifierKeys.Alt;
                                break;
                            case "shift":
                                modifiers |= ModifierKeys.Shift;
                                break;
                            case "win":
                            case "windows":
                                modifiers |= ModifierKeys.Windows;
                                break;
                        }
                    }
                    return true;
                }
            }
            catch
            {
                // تجاهل الأخطاء
            }

            return false;
        }

        private void NavigateToPage(string pageName)
        {
            // سيتم تنفيذ التنقل من خلال الحدث
            OnShortcutExecuted(new KeyboardShortcut { Id = $"navigate_{pageName.ToLower()}" });
        }

        private void ExecuteAction(string actionName)
        {
            // سيتم تنفيذ الإجراء من خلال الحدث
            OnShortcutExecuted(new KeyboardShortcut { Id = actionName.ToLower() });
        }

        private void OnShortcutExecuted(KeyboardShortcut shortcut)
        {
            ShortcutExecuted?.Invoke(this, new ShortcutExecutedEventArgs(shortcut));
        }

        // 📋 تصدير الاختصارات
        public string GetShortcutsHelpText()
        {
            var categories = _shortcuts.Values.GroupBy(s => s.Category ?? "عام");
            var helpText = "🔥 اختصارات لوحة المفاتيح:\n\n";

            foreach (var category in categories)
            {
                helpText += $"📂 {category.Key}:\n";
                foreach (var shortcut in category.OrderBy(s => s.Description))
                {
                    var keyCombo = GetKeyComboString(shortcut);
                    helpText += $"  • {keyCombo} - {shortcut.Description}\n";
                }
                helpText += "\n";
            }

            return helpText;
        }

        private string GetKeyComboString(KeyboardShortcut shortcut)
        {
            var combo = "";
            
            if (shortcut.Modifiers.HasFlag(ModifierKeys.Control))
                combo += "Ctrl + ";
            if (shortcut.Modifiers.HasFlag(ModifierKeys.Alt))
                combo += "Alt + ";
            if (shortcut.Modifiers.HasFlag(ModifierKeys.Shift))
                combo += "Shift + ";
            if (shortcut.Modifiers.HasFlag(ModifierKeys.Windows))
                combo += "Win + ";
                
            combo += shortcut.Key.ToString();
            
            return combo;
        }
    }

    // 📊 نماذج البيانات للاختصارات
    public class KeyboardShortcut
    {
        public string Id { get; set; } = string.Empty;
        public Key Key { get; set; }
        public ModifierKeys Modifiers { get; set; }
        public string Description { get; set; } = string.Empty;
        public string? Category { get; set; }
        public bool IsEnabled { get; set; } = true;
        public bool IsGlobal { get; set; } = false;
    }

    public class ShortcutExecutedEventArgs : EventArgs
    {
        public KeyboardShortcut Shortcut { get; }

        public ShortcutExecutedEventArgs(KeyboardShortcut shortcut)
        {
            Shortcut = shortcut;
        }
    }
}
