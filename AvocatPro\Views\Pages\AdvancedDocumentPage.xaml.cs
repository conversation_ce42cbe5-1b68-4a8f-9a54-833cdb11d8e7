using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Microsoft.Win32;
using AvocatPro.Services;
using AvocatPro.Models;

namespace AvocatPro.Views.Pages
{
    public partial class AdvancedDocumentPage : Page
    {
        private readonly DocumentManagementService _documentService;
        private List<Document> _allDocuments;
        private List<Document> _filteredDocuments;
        private bool _isLoading = false;

        public AdvancedDocumentPage()
        {
            InitializeComponent();
            _documentService = new DocumentManagementService();
            _allDocuments = new List<Document>();
            _filteredDocuments = new List<Document>();
            
            Loaded += AdvancedDocumentPage_Loaded;
        }

        private async void AdvancedDocumentPage_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadDocumentsAsync();
            await LoadQuickStatsAsync();
        }

        private async Task LoadDocumentsAsync()
        {
            if (_isLoading) return;

            _isLoading = true;
            ShowLoading(true);

            try
            {
                // محاكاة تحميل الوثائق
                await Task.Delay(1000);
                
                _allDocuments = GenerateSampleDocuments();
                _filteredDocuments = new List<Document>(_allDocuments);
                
                DisplayDocuments();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الوثائق: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                ShowLoading(false);
                _isLoading = false;
            }
        }

        private async Task LoadQuickStatsAsync()
        {
            QuickStatsPanel.Children.Clear();

            var stats = new[]
            {
                new { Label = "إجمالي الوثائق", Value = _allDocuments.Count.ToString("N0"), Icon = "📄" },
                new { Label = "وثائق موقعة", Value = _allDocuments.Count(d => d.IsSigned).ToString("N0"), Icon = "✍️" },
                new { Label = "وثائق مشفرة", Value = _allDocuments.Count(d => d.IsEncrypted).ToString("N0"), Icon = "🔒" },
                new { Label = "وثائق مشاركة", Value = _allDocuments.Count(d => d.Shares.Any()).ToString("N0"), Icon = "🔗" }
            };

            foreach (var stat in stats)
            {
                var statPanel = CreateStatPanel(stat.Icon, stat.Label, stat.Value);
                QuickStatsPanel.Children.Add(statPanel);
            }
        }

        private void DisplayDocuments()
        {
            DocumentsPanel.Children.Clear();

            if (!_filteredDocuments.Any())
            {
                var noDocumentsPanel = CreateNoDocumentsPanel();
                DocumentsPanel.Children.Add(noDocumentsPanel);
                return;
            }

            foreach (var document in _filteredDocuments.Take(20)) // عرض أول 20 وثيقة
            {
                var documentCard = CreateDocumentCard(document);
                DocumentsPanel.Children.Add(documentCard);
            }
        }

        private Border CreateDocumentCard(Document document)
        {
            var card = new Border
            {
                Background = new SolidColorBrush(Colors.White),
                CornerRadius = new CornerRadius(10),
                Padding = new Thickness(15),
                Margin = new Thickness(0, 0, 0, 10),
                BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E5E7EB")),
                BorderThickness = new Thickness(1)
            };

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            // معلومات الوثيقة
            var infoPanel = new StackPanel();
            Grid.SetColumn(infoPanel, 0);

            var headerPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 5) };
            
            var fileIcon = new TextBlock
            {
                Text = GetFileIcon(document.FileName),
                FontSize = 20,
                Margin = new Thickness(0, 0, 10, 0),
                VerticalAlignment = VerticalAlignment.Center
            };

            var fileName = new TextBlock
            {
                Text = document.FileName,
                FontSize = 16,
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#1F2937")),
                VerticalAlignment = VerticalAlignment.Center
            };

            headerPanel.Children.Add(fileIcon);
            headerPanel.Children.Add(fileName);

            var detailsPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 5) };
            
            var sizeText = new TextBlock
            {
                Text = $"{document.FileSize / 1024:N0} KB",
                FontSize = 12,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#6B7280")),
                Margin = new Thickness(0, 0, 15, 0)
            };

            var dateText = new TextBlock
            {
                Text = document.UploadedAt.ToString("yyyy-MM-dd HH:mm"),
                FontSize = 12,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#6B7280")),
                Margin = new Thickness(0, 0, 15, 0)
            };

            var categoryText = new TextBlock
            {
                Text = GetCategoryDisplayName(document.Category),
                FontSize = 12,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#3B82F6")),
                FontWeight = FontWeights.SemiBold
            };

            detailsPanel.Children.Add(sizeText);
            detailsPanel.Children.Add(dateText);
            detailsPanel.Children.Add(categoryText);

            // شارات الحالة
            var statusPanel = new StackPanel { Orientation = Orientation.Horizontal };
            
            if (document.IsSigned)
            {
                var signedBadge = CreateStatusBadge("✍️ موقع", "#10B981");
                statusPanel.Children.Add(signedBadge);
            }

            if (document.IsEncrypted)
            {
                var encryptedBadge = CreateStatusBadge("🔒 مشفر", "#F59E0B");
                statusPanel.Children.Add(encryptedBadge);
            }

            if (document.Shares.Any())
            {
                var sharedBadge = CreateStatusBadge("🔗 مشارك", "#8B5CF6");
                statusPanel.Children.Add(sharedBadge);
            }

            infoPanel.Children.Add(headerPanel);
            infoPanel.Children.Add(detailsPanel);
            infoPanel.Children.Add(statusPanel);

            // أزرار الإجراءات
            var actionsPanel = new StackPanel { Orientation = Orientation.Horizontal };
            Grid.SetColumn(actionsPanel, 1);

            var viewBtn = new Button
            {
                Content = "👁️",
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#3B82F6")),
                Foreground = new SolidColorBrush(Colors.White),
                Padding = new Thickness(8),
                Margin = new Thickness(0, 0, 5, 0),
                BorderThickness = new Thickness(0),
                ToolTip = "عرض"
            };
            viewBtn.Click += (s, e) => ViewDocument(document);

            var shareBtn = new Button
            {
                Content = "🔗",
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#10B981")),
                Foreground = new SolidColorBrush(Colors.White),
                Padding = new Thickness(8),
                Margin = new Thickness(0, 0, 5, 0),
                BorderThickness = new Thickness(0),
                ToolTip = "مشاركة"
            };
            shareBtn.Click += (s, e) => ShareDocument(document);

            var signBtn = new Button
            {
                Content = "✍️",
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F59E0B")),
                Foreground = new SolidColorBrush(Colors.White),
                Padding = new Thickness(8),
                Margin = new Thickness(0, 0, 5, 0),
                BorderThickness = new Thickness(0),
                ToolTip = "توقيع"
            };
            signBtn.Click += (s, e) => SignDocument(document);

            var moreBtn = new Button
            {
                Content = "⋮",
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#6B7280")),
                Foreground = new SolidColorBrush(Colors.White),
                Padding = new Thickness(8),
                BorderThickness = new Thickness(0),
                ToolTip = "المزيد"
            };
            moreBtn.Click += (s, e) => ShowMoreOptions(document);

            actionsPanel.Children.Add(viewBtn);
            actionsPanel.Children.Add(shareBtn);
            actionsPanel.Children.Add(signBtn);
            actionsPanel.Children.Add(moreBtn);

            grid.Children.Add(infoPanel);
            grid.Children.Add(actionsPanel);
            card.Child = grid;

            return card;
        }

        private Border CreateStatusBadge(string text, string color)
        {
            return new Border
            {
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
                CornerRadius = new CornerRadius(12),
                Padding = new Thickness(8, 4, 8, 4),
                Margin = new Thickness(0, 0, 5, 0),
                Child = new TextBlock
                {
                    Text = text,
                    FontSize = 11,
                    Foreground = new SolidColorBrush(Colors.White),
                    FontWeight = FontWeights.SemiBold
                }
            };
        }

        private StackPanel CreateStatPanel(string icon, string label, string value)
        {
            var panel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 10)
            };

            var iconText = new TextBlock
            {
                Text = icon,
                FontSize = 16,
                Margin = new Thickness(0, 0, 10, 0),
                VerticalAlignment = VerticalAlignment.Center
            };

            var labelText = new TextBlock
            {
                Text = label,
                FontSize = 12,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#6B7280")),
                VerticalAlignment = VerticalAlignment.Center,
                Width = 80
            };

            var valueText = new TextBlock
            {
                Text = value,
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#1F2937")),
                VerticalAlignment = VerticalAlignment.Center
            };

            panel.Children.Add(iconText);
            panel.Children.Add(labelText);
            panel.Children.Add(valueText);

            return panel;
        }

        private StackPanel CreateNoDocumentsPanel()
        {
            var panel = new StackPanel
            {
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 50, 0, 0)
            };

            var icon = new TextBlock
            {
                Text = "📄",
                FontSize = 48,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };

            var message = new TextBlock
            {
                Text = "لا توجد وثائق مطابقة للبحث",
                FontSize = 18,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#6B7280")),
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 10)
            };

            var suggestion = new TextBlock
            {
                Text = "جرب تغيير معايير البحث أو رفع وثيقة جديدة",
                FontSize = 14,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#9CA3AF")),
                HorizontalAlignment = HorizontalAlignment.Center
            };

            panel.Children.Add(icon);
            panel.Children.Add(message);
            panel.Children.Add(suggestion);

            return panel;
        }

        private List<Document> GenerateSampleDocuments()
        {
            var documents = new List<Document>();
            var random = new Random();
            var categories = Enum.GetValues<DocumentCategory>();
            var fileExtensions = new[] { ".pdf", ".docx", ".jpg", ".png", ".txt" };

            for (int i = 1; i <= 15; i++)
            {
                var extension = fileExtensions[random.Next(fileExtensions.Length)];
                var document = new Document
                {
                    Id = Guid.NewGuid().ToString(),
                    FileName = $"وثيقة_قانونية_{i:D3}{extension}",
                    FileSize = random.Next(50000, 5000000),
                    Category = categories[random.Next(categories.Length)],
                    UploadedAt = DateTime.Now.AddDays(-random.Next(1, 30)),
                    IsSigned = random.Next(0, 3) == 0,
                    IsEncrypted = random.Next(0, 4) == 0,
                    Tags = new List<string> { "مهم", "عاجل", "سري" }.Take(random.Next(0, 3)).ToList()
                };

                if (random.Next(0, 5) == 0)
                {
                    document.Shares.Add(new DocumentShare
                    {
                        Id = Guid.NewGuid().ToString(),
                        SharedWith = "<EMAIL>",
                        ShareType = ShareType.Email,
                        IsActive = true
                    });
                }

                documents.Add(document);
            }

            return documents;
        }

        private string GetFileIcon(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLower();
            return extension switch
            {
                ".pdf" => "📕",
                ".doc" or ".docx" => "📘",
                ".jpg" or ".jpeg" or ".png" => "🖼️",
                ".txt" => "📄",
                _ => "📄"
            };
        }

        private string GetCategoryDisplayName(DocumentCategory category)
        {
            return category switch
            {
                DocumentCategory.Contract => "عقد",
                DocumentCategory.PowerOfAttorney => "توكيل",
                DocumentCategory.LegalNotice => "إنذار قانوني",
                DocumentCategory.CourtDocument => "وثيقة محكمة",
                DocumentCategory.Evidence => "دليل",
                DocumentCategory.ClientDocument => "وثيقة عميل",
                DocumentCategory.InternalDocument => "وثيقة داخلية",
                DocumentCategory.Template => "قالب",
                _ => "أخرى"
            };
        }

        private void ShowLoading(bool show)
        {
            LoadingOverlay.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
        }

        // معالجات الأحداث
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterDocuments();
        }

        private void CategoryFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            FilterDocuments();
        }

        private void DocumentFilter_Changed(object sender, RoutedEventArgs e)
        {
            FilterDocuments();
        }

        private void FilterDocuments()
        {
            var searchText = SearchTextBox?.Text?.ToLower() ?? "";
            var selectedCategory = CategoryFilterComboBox?.SelectedIndex ?? 0;

            _filteredDocuments = _allDocuments.Where(doc =>
            {
                // تصفية النص
                var matchesText = string.IsNullOrEmpty(searchText) ||
                                 doc.FileName.ToLower().Contains(searchText) ||
                                 doc.Tags.Any(tag => tag.ToLower().Contains(searchText));

                // تصفية الفئة
                var matchesCategory = selectedCategory == 0 || (int)doc.Category == selectedCategory - 1;

                // تصفية الحالة
                var matchesSigned = SignedDocumentsCheckBox?.IsChecked != true || doc.IsSigned;
                var matchesEncrypted = EncryptedDocumentsCheckBox?.IsChecked != true || doc.IsEncrypted;
                var matchesShared = SharedDocumentsCheckBox?.IsChecked != true || doc.Shares.Any();

                return matchesText && matchesCategory && matchesSigned && matchesEncrypted && matchesShared;
            }).ToList();

            DisplayDocuments();
        }

        private void Sort_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_filteredDocuments == null) return;

            var selectedIndex = SortComboBox.SelectedIndex;
            _filteredDocuments = selectedIndex switch
            {
                0 => _filteredDocuments.OrderByDescending(d => d.UploadedAt).ToList(),
                1 => _filteredDocuments.OrderBy(d => d.UploadedAt).ToList(),
                2 => _filteredDocuments.OrderBy(d => d.FileName).ToList(),
                3 => _filteredDocuments.OrderByDescending(d => d.FileSize).ToList(),
                _ => _filteredDocuments
            };

            DisplayDocuments();
        }

        private void ViewMode_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // تغيير وضع العرض (شبكي/قائمة/تفصيلي)
            DisplayDocuments();
        }

        private async void RefreshDocuments_Click(object sender, RoutedEventArgs e)
        {
            await LoadDocumentsAsync();
            await LoadQuickStatsAsync();
        }

        private async void UploadDocument_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "اختر الوثائق للرفع",
                Filter = "جميع الملفات (*.*)|*.*|PDF (*.pdf)|*.pdf|Word (*.docx)|*.docx|صور (*.jpg;*.png)|*.jpg;*.png",
                Multiselect = true
            };

            if (openFileDialog.ShowDialog() == true)
            {
                ShowLoading(true);
                try
                {
                    foreach (var fileName in openFileDialog.FileNames)
                    {
                        var fileData = await File.ReadAllBytesAsync(fileName);
                        var request = new DocumentUploadRequest
                        {
                            FileName = Path.GetFileName(fileName),
                            FileData = fileData,
                            UserId = 1,
                            Category = DocumentCategory.Other,
                            EncryptDocument = false
                        };

                        var result = await _documentService.UploadDocumentAsync(request);
                        if (!result.Success)
                        {
                            MessageBox.Show($"خطأ في رفع {Path.GetFileName(fileName)}: {result.Message}", 
                                           "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }

                    MessageBox.Show("تم رفع الوثائق بنجاح", "نجح الرفع", 
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                    await LoadDocumentsAsync();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في رفع الوثائق: {ex.Message}", "خطأ", 
                                   MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    ShowLoading(false);
                }
            }
        }

        private void ScanDocument_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم فتح واجهة المسح الضوئي مع تقنية OCR", "مسح ضوئي", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private async void CreateFromTemplate_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var templates = await _documentService.GetDocumentTemplatesAsync();
                var templateNames = string.Join("\n", templates.Select(t => $"• {t.Name}"));
                
                MessageBox.Show($"القوالب المتاحة:\n{templateNames}", "القوالب الجاهزة", 
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل القوالب: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ViewDocument(Document document)
        {
            MessageBox.Show($"عرض الوثيقة: {document.FileName}", "عرض الوثيقة", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private async void ShareDocument(Document document)
        {
            try
            {
                var request = new DocumentShareRequest
                {
                    DocumentId = document.Id,
                    UserId = 1,
                    SharedWith = "<EMAIL>",
                    ShareType = ShareType.Email,
                    AccessLevel = AccessLevel.View,
                    ExpiryDate = DateTime.Now.AddDays(7)
                };

                var result = await _documentService.ShareDocumentAsync(request);
                if (result.Success)
                {
                    MessageBox.Show($"تم مشاركة الوثيقة بنجاح\nرابط المشاركة: {result.ShareUrl}", 
                                   "مشاركة الوثيقة", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show(result.Message, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في مشاركة الوثيقة: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SignDocument(Document document)
        {
            try
            {
                var request = new DocumentSignRequest
                {
                    DocumentId = document.Id,
                    SignerId = 1,
                    SignerName = "المحامي الرئيسي",
                    SignerEmail = "<EMAIL>",
                    SignatureData = new byte[] { 1, 2, 3 }, // محاكاة بيانات التوقيع
                    SignatureType = SignatureType.Digital
                };

                var result = await _documentService.SignDocumentAsync(request);
                if (result.Success)
                {
                    MessageBox.Show("تم توقيع الوثيقة بنجاح", "توقيع الوثيقة", 
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                    await LoadDocumentsAsync();
                }
                else
                {
                    MessageBox.Show(result.Message, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في توقيع الوثيقة: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ShowMoreOptions(Document document)
        {
            var options = $"خيارات إضافية للوثيقة: {document.FileName}\n\n";
            options += "• تحميل\n• حذف\n• تعديل المعلومات\n• عرض التاريخ\n• إدارة الأذونات";
            
            MessageBox.Show(options, "خيارات إضافية", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
