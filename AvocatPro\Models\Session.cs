using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AvocatPro.Models;

/// <summary>
/// نموذج الجلسة
/// </summary>
public class Session : BaseEntity
{
    /// <summary>
    /// معرف القضية
    /// </summary>
    [Required]
    public int CaseId { get; set; }
    
    /// <summary>
    /// تاريخ الجلسة
    /// </summary>
    [Required]
    public DateTime SessionDate { get; set; }
    
    /// <summary>
    /// وقت الجلسة
    /// </summary>
    public TimeSpan SessionTime { get; set; }
    
    /// <summary>
    /// نوع الجلسة
    /// </summary>
    public SessionType Type { get; set; }
    
    /// <summary>
    /// نوع الإجراء
    /// </summary>
    [StringLength(100)]
    public string? ProcedureType { get; set; }
    
    /// <summary>
    /// القرار المتخذ
    /// </summary>
    [StringLength(1000)]
    public string? Decision { get; set; }
    
    /// <summary>
    /// تاريخ الجلسة القادمة
    /// </summary>
    public DateTime? NextSessionDate { get; set; }
    
    /// <summary>
    /// وقت الجلسة القادمة
    /// </summary>
    public TimeSpan? NextSessionTime { get; set; }
    
    /// <summary>
    /// حالة الجلسة
    /// </summary>
    public SessionStatus Status { get; set; } = SessionStatus.Scheduled;
    
    /// <summary>
    /// المحامي المكلف
    /// </summary>
    public int? AssignedLawyerId { get; set; }
    
    /// <summary>
    /// القاضي
    /// </summary>
    [StringLength(100)]
    public string? Judge { get; set; }
    
    /// <summary>
    /// رقم القاعة
    /// </summary>
    [StringLength(20)]
    public string? CourtRoom { get; set; }
    
    /// <summary>
    /// ملاحظات الجلسة
    /// </summary>
    [StringLength(2000)]
    public string? SessionNotes { get; set; }
    
    /// <summary>
    /// الحضور
    /// </summary>
    [StringLength(500)]
    public string? Attendance { get; set; }
    
    /// <summary>
    /// المرافعات
    /// </summary>
    [StringLength(2000)]
    public string? Arguments { get; set; }
    
    /// <summary>
    /// الأدلة المقدمة
    /// </summary>
    [StringLength(1000)]
    public string? Evidence { get; set; }
    
    /// <summary>
    /// المصاريف
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? Expenses { get; set; }
    
    /// <summary>
    /// مدة الجلسة (بالدقائق)
    /// </summary>
    public int? Duration { get; set; }
    
    /// <summary>
    /// تم التنبيه
    /// </summary>
    public bool IsNotified { get; set; } = false;
    
    /// <summary>
    /// تاريخ التنبيه
    /// </summary>
    public DateTime? NotificationDate { get; set; }
    
    /// <summary>
    /// المرفقات (JSON)
    /// </summary>
    public string? Attachments { get; set; }
    
    // Navigation Properties
    [ForeignKey("CaseId")]
    public virtual Case Case { get; set; } = null!;
    
    [ForeignKey("AssignedLawyerId")]
    public virtual User? AssignedLawyer { get; set; }
    
    public virtual ICollection<SessionDocument> Documents { get; set; } = new List<SessionDocument>();
}

/// <summary>
/// نوع الجلسة
/// </summary>
public enum SessionType
{
    /// <summary>
    /// جلسة عادية
    /// </summary>
    Regular = 1,
    
    /// <summary>
    /// جلسة استعجالية
    /// </summary>
    Urgent = 2,
    
    /// <summary>
    /// جلسة مرافعة
    /// </summary>
    Pleading = 3,
    
    /// <summary>
    /// جلسة حكم
    /// </summary>
    Judgment = 4,
    
    /// <summary>
    /// جلسة تأجيل
    /// </summary>
    Postponement = 5,
    
    /// <summary>
    /// جلسة تحضيرية
    /// </summary>
    Preparatory = 6,
    
    /// <summary>
    /// جلسة صلح
    /// </summary>
    Settlement = 7
}

/// <summary>
/// حالة الجلسة
/// </summary>
public enum SessionStatus
{
    /// <summary>
    /// مجدولة
    /// </summary>
    Scheduled = 1,
    
    /// <summary>
    /// جارية
    /// </summary>
    InProgress = 2,
    
    /// <summary>
    /// مكتملة
    /// </summary>
    Completed = 3,
    
    /// <summary>
    /// مؤجلة
    /// </summary>
    Postponed = 4,
    
    /// <summary>
    /// ملغاة
    /// </summary>
    Cancelled = 5,
    
    /// <summary>
    /// لم يحضر
    /// </summary>
    NoShow = 6
}
