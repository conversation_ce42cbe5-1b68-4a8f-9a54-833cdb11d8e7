using System;
using System.Windows;
using AvocatPro.Views.Windows;

namespace AvocatPro
{
    /// <summary>
    /// تشغيل الواجهة الحديثة مباشرة
    /// </summary>
    public class RunModernUI
    {
        [STAThread]
        public static void Main()
        {
            try
            {
                // إنشاء تطبيق WPF
                var app = new Application();
                
                // تطبيق الإعدادات الأساسية
                app.ShutdownMode = ShutdownMode.OnMainWindowClose;

                // إنشاء النافذة الرئيسية الحديثة
                var modernMainWindow = new ModernMainWindow();
                
                // تعيين النافذة الرئيسية
                app.MainWindow = modernMainWindow;
                
                // إظهار النافذة
                modernMainWindow.Show();
                
                // تشغيل التطبيق
                app.Run();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل الواجهة الحديثة: {ex.Message}\n\nتفاصيل الخطأ:\n{ex}", 
                               "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
