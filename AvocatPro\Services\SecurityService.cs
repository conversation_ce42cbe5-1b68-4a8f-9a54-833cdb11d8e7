using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using AvocatPro.Models;

namespace AvocatPro.Services
{
    public class SecurityService
    {
        private readonly string _encryptionKey;
        private readonly AuditService _auditService;

        public SecurityService()
        {
            _encryptionKey = GenerateEncryptionKey();
            _auditService = new AuditService();
        }

        // 🔐 المصادقة الثنائية (2FA)
        public async Task<TwoFactorResult> GenerateTwoFactorCodeAsync(User user)
        {
            try
            {
                var code = GenerateRandomCode(6);
                var expiryTime = DateTime.Now.AddMinutes(5);

                // حفظ الكود في قاعدة البيانات مؤقتاً
                var twoFactorCode = new TwoFactorCode
                {
                    UserId = user.Id,
                    Code = code,
                    ExpiryTime = expiryTime,
                    IsUsed = false,
                    CreatedAt = DateTime.Now
                };

                // إرسال الكود عبر SMS أو Email
                await SendTwoFactorCodeAsync(user, code);

                await _auditService.LogActivityAsync(user.Id, "تم إنشاء رمز المصادقة الثنائية", 
                    $"تم إنشاء رمز مصادقة ثنائية للمستخدم {user.Username}");

                return new TwoFactorResult
                {
                    Success = true,
                    Message = "تم إرسال رمز التحقق بنجاح",
                    ExpiryTime = expiryTime
                };
            }
            catch (Exception ex)
            {
                return new TwoFactorResult
                {
                    Success = false,
                    Message = $"خطأ في إنشاء رمز التحقق: {ex.Message}"
                };
            }
        }

        public async Task<bool> VerifyTwoFactorCodeAsync(User user, string code)
        {
            try
            {
                // التحقق من الكود في قاعدة البيانات
                // هنا يجب البحث في قاعدة البيانات عن الكود
                // للتبسيط، سنقوم بمحاكاة التحقق

                if (string.IsNullOrEmpty(code) || code.Length != 6)
                    return false;

                // محاكاة التحقق من الكود
                var isValid = await ValidateCodeInDatabaseAsync(user.Id, code);

                if (isValid)
                {
                    await _auditService.LogActivityAsync(user.Id, "تم التحقق من المصادقة الثنائية بنجاح", 
                        $"تم التحقق من رمز المصادقة الثنائية للمستخدم {user.Username}");
                }
                else
                {
                    await _auditService.LogActivityAsync(user.Id, "فشل في التحقق من المصادقة الثنائية", 
                        $"محاولة فاشلة للتحقق من رمز المصادقة الثنائية للمستخدم {user.Username}");
                }

                return isValid;
            }
            catch (Exception ex)
            {
                await _auditService.LogActivityAsync(user.Id, "خطأ في التحقق من المصادقة الثنائية", 
                    $"خطأ: {ex.Message}");
                return false;
            }
        }

        // 🔒 التشفير المتقدم
        public string EncryptSensitiveData(string data)
        {
            try
            {
                using (var aes = Aes.Create())
                {
                    aes.Key = Encoding.UTF8.GetBytes(_encryptionKey.Substring(0, 32));
                    aes.IV = new byte[16]; // للتبسيط، يجب استخدام IV عشوائي في الإنتاج

                    using (var encryptor = aes.CreateEncryptor())
                    {
                        var dataBytes = Encoding.UTF8.GetBytes(data);
                        var encryptedBytes = encryptor.TransformFinalBlock(dataBytes, 0, dataBytes.Length);
                        return Convert.ToBase64String(encryptedBytes);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new SecurityException($"خطأ في تشفير البيانات: {ex.Message}");
            }
        }

        public string DecryptSensitiveData(string encryptedData)
        {
            try
            {
                using (var aes = Aes.Create())
                {
                    aes.Key = Encoding.UTF8.GetBytes(_encryptionKey.Substring(0, 32));
                    aes.IV = new byte[16];

                    using (var decryptor = aes.CreateDecryptor())
                    {
                        var encryptedBytes = Convert.FromBase64String(encryptedData);
                        var decryptedBytes = decryptor.TransformFinalBlock(encryptedBytes, 0, encryptedBytes.Length);
                        return Encoding.UTF8.GetString(decryptedBytes);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new SecurityException($"خطأ في فك تشفير البيانات: {ex.Message}");
            }
        }

        // 🛡️ حماية كلمات المرور
        public string HashPassword(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var salt = GenerateRandomCode(16);
                var saltedPassword = password + salt;
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));
                return Convert.ToBase64String(hashedBytes) + ":" + salt;
            }
        }

        public bool VerifyPassword(string password, string hashedPassword)
        {
            try
            {
                var parts = hashedPassword.Split(':');
                if (parts.Length != 2) return false;

                var hash = parts[0];
                var salt = parts[1];

                using (var sha256 = SHA256.Create())
                {
                    var saltedPassword = password + salt;
                    var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));
                    var newHash = Convert.ToBase64String(hashedBytes);
                    return hash == newHash;
                }
            }
            catch
            {
                return false;
            }
        }

        // 📋 سجل المراجعة
        public async Task<List<AuditLog>> GetAuditLogsAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            return await _auditService.GetUserAuditLogsAsync(userId, fromDate, toDate);
        }

        // 🔧 دوال مساعدة
        private string GenerateRandomCode(int length)
        {
            const string chars = "0123456789";
            var random = new Random();
            var result = new StringBuilder();

            for (int i = 0; i < length; i++)
            {
                result.Append(chars[random.Next(chars.Length)]);
            }

            return result.ToString();
        }

        private string GenerateEncryptionKey()
        {
            // في الإنتاج، يجب تخزين هذا المفتاح بشكل آمن
            return "MySecureEncryptionKey123456789012345678901234567890";
        }

        private async Task SendTwoFactorCodeAsync(User user, string code)
        {
            // محاكاة إرسال الكود
            await Task.Delay(100);
            
            // هنا يمكن إضافة تكامل مع خدمات SMS أو Email
            System.Diagnostics.Debug.WriteLine($"رمز التحقق للمستخدم {user.Username}: {code}");
        }

        private async Task<bool> ValidateCodeInDatabaseAsync(int userId, string code)
        {
            // محاكاة التحقق من قاعدة البيانات
            await Task.Delay(100);
            
            // للتبسيط، سنقبل أي كود مكون من 6 أرقام
            return code.Length == 6 && int.TryParse(code, out _);
        }
    }

    // 📊 نماذج البيانات للأمان
    public class TwoFactorResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public DateTime? ExpiryTime { get; set; }
    }

    public class TwoFactorCode
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string Code { get; set; } = string.Empty;
        public DateTime ExpiryTime { get; set; }
        public bool IsUsed { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class AuditLog
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string Action { get; set; } = string.Empty;
        public string Details { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string IpAddress { get; set; } = string.Empty;
        public string UserAgent { get; set; } = string.Empty;
    }

    public class SecurityException : Exception
    {
        public SecurityException(string message) : base(message) { }
        public SecurityException(string message, Exception innerException) : base(message, innerException) { }
    }
}
