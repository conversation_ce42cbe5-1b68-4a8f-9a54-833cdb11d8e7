using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace AvocatPro.Models
{
    /// <summary>
    /// نموذج الملف المتقدم مع جميع التفاصيل المطلوبة
    /// </summary>
    public class AdvancedFileModel : INotifyPropertyChanged
    {
        #region Private Fields

        private int _id;
        private string _fileNumber = string.Empty;
        private string _officeReference = string.Empty;
        private string _courtReference = string.Empty;
        private string _fileType = string.Empty;
        private string _court = string.Empty;
        private string _caseType = string.Empty;
        private string _opponent = string.Empty;
        private string _subject = string.Empty;
        private string _client = string.Empty;
        private string _procedureType = string.Empty;
        private string _decision = string.Empty;
        private DateTime? _nextSessionDate;
        private DateTime _createdDate = DateTime.Now;
        private DateTime? _lastUpdated;
        private string _status = "نشط";
        private string _notes = string.Empty;
        private bool _isElectronicTracking = false;
        private string _electronicFileNumber = string.Empty;
        private string _electronicFileCode = string.Empty;
        private int _electronicYear = DateTime.Now.Year;
        private string _appealCourt = string.Empty;
        private bool _searchInPrimaryCourts = true;
        private DateTime? _lastSyncDate;
        private string _syncStatus = "غير متزامن";
        private string _lawyer = string.Empty;
        private decimal _fileValue = 0;
        private string _priority = "عادي";
        private string _category = string.Empty;

        #endregion

        #region Properties

        public int Id
        {
            get => _id;
            set { _id = value; OnPropertyChanged(); }
        }

        public string FileNumber
        {
            get => _fileNumber;
            set { _fileNumber = value; OnPropertyChanged(); }
        }

        public string OfficeReference
        {
            get => _officeReference;
            set { _officeReference = value; OnPropertyChanged(); }
        }

        public string CourtReference
        {
            get => _courtReference;
            set { _courtReference = value; OnPropertyChanged(); }
        }

        public string FileType
        {
            get => _fileType;
            set { _fileType = value; OnPropertyChanged(); }
        }

        public string Court
        {
            get => _court;
            set { _court = value; OnPropertyChanged(); }
        }

        public string CaseType
        {
            get => _caseType;
            set { _caseType = value; OnPropertyChanged(); }
        }

        public string Opponent
        {
            get => _opponent;
            set { _opponent = value; OnPropertyChanged(); }
        }

        public string Subject
        {
            get => _subject;
            set { _subject = value; OnPropertyChanged(); }
        }

        public string Client
        {
            get => _client;
            set { _client = value; OnPropertyChanged(); }
        }

        public string ProcedureType
        {
            get => _procedureType;
            set { _procedureType = value; OnPropertyChanged(); }
        }

        public string Decision
        {
            get => _decision;
            set { _decision = value; OnPropertyChanged(); }
        }

        public DateTime? NextSessionDate
        {
            get => _nextSessionDate;
            set { _nextSessionDate = value; OnPropertyChanged(); }
        }

        public DateTime CreatedDate
        {
            get => _createdDate;
            set { _createdDate = value; OnPropertyChanged(); }
        }

        public DateTime? LastUpdated
        {
            get => _lastUpdated;
            set { _lastUpdated = value; OnPropertyChanged(); }
        }

        public string Status
        {
            get => _status;
            set { _status = value; OnPropertyChanged(); }
        }

        public string Notes
        {
            get => _notes;
            set { _notes = value; OnPropertyChanged(); }
        }

        public bool IsElectronicTracking
        {
            get => _isElectronicTracking;
            set { _isElectronicTracking = value; OnPropertyChanged(); }
        }

        public string ElectronicFileNumber
        {
            get => _electronicFileNumber;
            set { _electronicFileNumber = value; OnPropertyChanged(); }
        }

        public string ElectronicFileCode
        {
            get => _electronicFileCode;
            set { _electronicFileCode = value; OnPropertyChanged(); }
        }

        public int ElectronicYear
        {
            get => _electronicYear;
            set { _electronicYear = value; OnPropertyChanged(); }
        }

        public string AppealCourt
        {
            get => _appealCourt;
            set { _appealCourt = value; OnPropertyChanged(); }
        }

        public bool SearchInPrimaryCourts
        {
            get => _searchInPrimaryCourts;
            set { _searchInPrimaryCourts = value; OnPropertyChanged(); }
        }

        public DateTime? LastSyncDate
        {
            get => _lastSyncDate;
            set { _lastSyncDate = value; OnPropertyChanged(); }
        }

        public string SyncStatus
        {
            get => _syncStatus;
            set { _syncStatus = value; OnPropertyChanged(); }
        }

        public string Lawyer
        {
            get => _lawyer;
            set { _lawyer = value; OnPropertyChanged(); }
        }

        public decimal FileValue
        {
            get => _fileValue;
            set { _fileValue = value; OnPropertyChanged(); }
        }

        public string Priority
        {
            get => _priority;
            set { _priority = value; OnPropertyChanged(); }
        }

        public string Category
        {
            get => _category;
            set { _category = value; OnPropertyChanged(); }
        }

        #endregion

        #region Computed Properties

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    "نشط" => "#10B981",
                    "في الجلسات" => "#F59E0B",
                    "مؤرشف" => "#6B7280",
                    "مغلق" => "#EF4444",
                    "معلق" => "#8B5CF6",
                    _ => "#6B7280"
                };
            }
        }

        public string PriorityColor
        {
            get
            {
                return Priority switch
                {
                    "عاجل" => "#EF4444",
                    "مهم" => "#F59E0B",
                    "عادي" => "#10B981",
                    _ => "#6B7280"
                };
            }
        }

        public string SyncStatusIcon
        {
            get
            {
                return SyncStatus switch
                {
                    "متزامن" => "✅",
                    "قيد المزامنة" => "🔄",
                    "خطأ في المزامنة" => "❌",
                    _ => "⏸️"
                };
            }
        }

        public string NextSessionText
        {
            get
            {
                if (NextSessionDate.HasValue)
                {
                    var days = (NextSessionDate.Value - DateTime.Now).Days;
                    if (days < 0)
                        return "متأخرة";
                    else if (days == 0)
                        return "اليوم";
                    else if (days == 1)
                        return "غداً";
                    else
                        return $"خلال {days} يوم";
                }
                return "غير محدد";
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
