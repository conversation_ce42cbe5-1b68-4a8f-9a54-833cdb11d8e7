using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using AvocatPro.Models;

namespace AvocatPro.Services
{
    public class DocumentManagementService
    {
        private readonly SecurityService _securityService;
        private readonly AuditService _auditService;
        private readonly string _documentsDirectory;
        private readonly List<Document> _documents;

        public DocumentManagementService()
        {
            _securityService = new SecurityService();
            _auditService = new AuditService();
            _documentsDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "AvocatPro", "Documents");
            _documents = new List<Document>();
            
            Directory.CreateDirectory(_documentsDirectory);
        }

        // 📄 رفع وثيقة جديدة
        public async Task<DocumentResult> UploadDocumentAsync(DocumentUploadRequest request)
        {
            try
            {
                var documentId = Guid.NewGuid().ToString();
                var fileName = $"{documentId}_{request.FileName}";
                var filePath = Path.Combine(_documentsDirectory, fileName);

                // حفظ الملف
                await File.WriteAllBytesAsync(filePath, request.FileData);

                // إنشاء سجل الوثيقة
                var document = new Document
                {
                    Id = documentId,
                    FileName = request.FileName,
                    FilePath = filePath,
                    FileSize = request.FileData.Length,
                    MimeType = GetMimeType(request.FileName),
                    UploadedBy = request.UserId,
                    UploadedAt = DateTime.Now,
                    Category = request.Category,
                    Tags = request.Tags,
                    IsEncrypted = request.EncryptDocument,
                    ClientId = request.ClientId,
                    CaseId = request.CaseId
                };

                // تشفير الوثيقة إذا كان مطلوباً
                if (request.EncryptDocument)
                {
                    await EncryptDocumentAsync(document);
                }

                // استخراج النص باستخدام OCR إذا كانت صورة
                if (IsImageFile(request.FileName))
                {
                    document.ExtractedText = await ExtractTextFromImageAsync(filePath);
                }

                // إنشاء فهرس للبحث
                await IndexDocumentAsync(document);

                _documents.Add(document);

                // تسجيل النشاط
                await _auditService.LogActivityAsync(request.UserId, "رفع وثيقة", 
                    $"تم رفع الوثيقة: {request.FileName}");

                return new DocumentResult
                {
                    Success = true,
                    DocumentId = documentId,
                    Message = "تم رفع الوثيقة بنجاح"
                };
            }
            catch (Exception ex)
            {
                return new DocumentResult
                {
                    Success = false,
                    Message = $"خطأ في رفع الوثيقة: {ex.Message}"
                };
            }
        }

        // 🔍 البحث في الوثائق
        public async Task<List<Document>> SearchDocumentsAsync(DocumentSearchRequest request)
        {
            await Task.CompletedTask;

            var query = _documents.AsQueryable();

            // البحث بالنص
            if (!string.IsNullOrEmpty(request.SearchText))
            {
                query = query.Where(d => 
                    d.FileName.Contains(request.SearchText, StringComparison.OrdinalIgnoreCase) ||
                    d.Tags.Any(tag => tag.Contains(request.SearchText, StringComparison.OrdinalIgnoreCase)) ||
                    (!string.IsNullOrEmpty(d.ExtractedText) && d.ExtractedText.Contains(request.SearchText, StringComparison.OrdinalIgnoreCase)));
            }

            // تصفية حسب الفئة
            if (request.Category.HasValue)
            {
                query = query.Where(d => d.Category == request.Category.Value);
            }

            // تصفية حسب العميل
            if (request.ClientId.HasValue)
            {
                query = query.Where(d => d.ClientId == request.ClientId.Value);
            }

            // تصفية حسب القضية
            if (request.CaseId.HasValue)
            {
                query = query.Where(d => d.CaseId == request.CaseId.Value);
            }

            // تصفية حسب التاريخ
            if (request.FromDate.HasValue)
            {
                query = query.Where(d => d.UploadedAt >= request.FromDate.Value);
            }

            if (request.ToDate.HasValue)
            {
                query = query.Where(d => d.UploadedAt <= request.ToDate.Value);
            }

            return query.OrderByDescending(d => d.UploadedAt).ToList();
        }

        // ✍️ التوقيع الإلكتروني
        public async Task<SignatureResult> SignDocumentAsync(DocumentSignRequest request)
        {
            try
            {
                var document = _documents.FirstOrDefault(d => d.Id == request.DocumentId);
                if (document == null)
                {
                    return new SignatureResult
                    {
                        Success = false,
                        Message = "الوثيقة غير موجودة"
                    };
                }

                // إنشاء التوقيع الإلكتروني
                var signature = new DigitalSignature
                {
                    Id = Guid.NewGuid().ToString(),
                    DocumentId = request.DocumentId,
                    SignerId = request.SignerId,
                    SignerName = request.SignerName,
                    SignerEmail = request.SignerEmail,
                    SignatureData = request.SignatureData,
                    SignedAt = DateTime.Now,
                    SignatureType = request.SignatureType,
                    CertificateInfo = GenerateSignatureCertificate(request)
                };

                // إضافة التوقيع للوثيقة
                document.Signatures.Add(signature);
                document.IsSigned = true;
                document.LastModifiedAt = DateTime.Now;

                // تسجيل النشاط
                await _auditService.LogActivityAsync(request.SignerId, "توقيع وثيقة", 
                    $"تم توقيع الوثيقة: {document.FileName} بواسطة {request.SignerName}");

                return new SignatureResult
                {
                    Success = true,
                    SignatureId = signature.Id,
                    Message = "تم توقيع الوثيقة بنجاح"
                };
            }
            catch (Exception ex)
            {
                return new SignatureResult
                {
                    Success = false,
                    Message = $"خطأ في توقيع الوثيقة: {ex.Message}"
                };
            }
        }

        // 📤 مشاركة الوثائق
        public async Task<ShareResult> ShareDocumentAsync(DocumentShareRequest request)
        {
            try
            {
                var document = _documents.FirstOrDefault(d => d.Id == request.DocumentId);
                if (document == null)
                {
                    return new ShareResult
                    {
                        Success = false,
                        Message = "الوثيقة غير موجودة"
                    };
                }

                // إنشاء رابط مشاركة آمن
                var shareLink = new DocumentShare
                {
                    Id = Guid.NewGuid().ToString(),
                    DocumentId = request.DocumentId,
                    SharedBy = request.UserId,
                    SharedWith = request.SharedWith,
                    ShareType = request.ShareType,
                    ExpiryDate = request.ExpiryDate,
                    AccessLevel = request.AccessLevel,
                    ShareUrl = GenerateSecureShareUrl(),
                    CreatedAt = DateTime.Now,
                    IsActive = true
                };

                document.Shares.Add(shareLink);

                // إرسال إشعار للمستلم
                await SendShareNotificationAsync(shareLink, document);

                // تسجيل النشاط
                await _auditService.LogActivityAsync(request.UserId, "مشاركة وثيقة", 
                    $"تم مشاركة الوثيقة: {document.FileName} مع {request.SharedWith}");

                return new ShareResult
                {
                    Success = true,
                    ShareId = shareLink.Id,
                    ShareUrl = shareLink.ShareUrl,
                    Message = "تم مشاركة الوثيقة بنجاح"
                };
            }
            catch (Exception ex)
            {
                return new ShareResult
                {
                    Success = false,
                    Message = $"خطأ في مشاركة الوثيقة: {ex.Message}"
                };
            }
        }

        // 📋 قوالب الوثائق
        public async Task<List<DocumentTemplate>> GetDocumentTemplatesAsync(DocumentCategory? category = null)
        {
            await Task.CompletedTask;

            var templates = new List<DocumentTemplate>
            {
                new DocumentTemplate
                {
                    Id = "contract_template_1",
                    Name = "عقد عمل",
                    Category = DocumentCategory.Contract,
                    Description = "قالب عقد عمل قياسي",
                    TemplateContent = GetContractTemplate(),
                    Language = "ar"
                },
                new DocumentTemplate
                {
                    Id = "power_of_attorney_1",
                    Name = "توكيل عام",
                    Category = DocumentCategory.PowerOfAttorney,
                    Description = "قالب توكيل عام",
                    TemplateContent = GetPowerOfAttorneyTemplate(),
                    Language = "ar"
                },
                new DocumentTemplate
                {
                    Id = "legal_notice_1",
                    Name = "إنذار قانوني",
                    Category = DocumentCategory.LegalNotice,
                    Description = "قالب إنذار قانوني",
                    TemplateContent = GetLegalNoticeTemplate(),
                    Language = "ar"
                }
            };

            if (category.HasValue)
            {
                templates = templates.Where(t => t.Category == category.Value).ToList();
            }

            return templates;
        }

        // 🔧 دوال مساعدة
        private async Task<string> ExtractTextFromImageAsync(string imagePath)
        {
            // محاكاة استخراج النص من الصورة باستخدام OCR
            await Task.Delay(1000);
            
            // في التطبيق الحقيقي، يمكن استخدام مكتبات مثل Tesseract
            return "نص مستخرج من الصورة باستخدام تقنية OCR";
        }

        private async Task EncryptDocumentAsync(Document document)
        {
            await Task.Delay(500);
            
            var fileData = await File.ReadAllBytesAsync(document.FilePath);
            var encryptedData = _securityService.EncryptSensitiveData(Convert.ToBase64String(fileData));
            
            await File.WriteAllTextAsync(document.FilePath + ".encrypted", encryptedData);
            File.Delete(document.FilePath);
            File.Move(document.FilePath + ".encrypted", document.FilePath);
        }

        private async Task IndexDocumentAsync(Document document)
        {
            // محاكاة فهرسة الوثيقة للبحث السريع
            await Task.Delay(200);
            System.Diagnostics.Debug.WriteLine($"تم فهرسة الوثيقة: {document.FileName}");
        }

        private string GetMimeType(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLower();
            return extension switch
            {
                ".pdf" => "application/pdf",
                ".doc" => "application/msword",
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".txt" => "text/plain",
                _ => "application/octet-stream"
            };
        }

        private bool IsImageFile(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLower();
            return new[] { ".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".gif" }.Contains(extension);
        }

        private string GenerateSignatureCertificate(DocumentSignRequest request)
        {
            // محاكاة إنشاء شهادة التوقيع الإلكتروني
            return $"CERT_{DateTime.Now:yyyyMMddHHmmss}_{request.SignerId}";
        }

        private string GenerateSecureShareUrl()
        {
            return $"https://avocatpro.com/share/{Guid.NewGuid()}";
        }

        private async Task SendShareNotificationAsync(DocumentShare shareLink, Document document)
        {
            // محاكاة إرسال إشعار المشاركة
            await Task.Delay(100);
            System.Diagnostics.Debug.WriteLine($"تم إرسال إشعار مشاركة الوثيقة {document.FileName} إلى {shareLink.SharedWith}");
        }

        private string GetContractTemplate()
        {
            return @"
عقد عمل

الطرف الأول: [اسم صاحب العمل]
الطرف الثاني: [اسم الموظف]

بنود العقد:
1. مدة العقد: [المدة]
2. الراتب: [المبلغ] درهم شهرياً
3. ساعات العمل: [عدد الساعات] ساعة أسبوعياً

التوقيع:
الطرف الأول: ________________
الطرف الثاني: ________________
التاريخ: ________________
";
        }

        private string GetPowerOfAttorneyTemplate()
        {
            return @"
توكيل عام

أنا الموقع أدناه [الاسم الكامل]
أوكل السيد/السيدة [اسم الوكيل]
في [نوع التوكيل]

التوقيع: ________________
التاريخ: ________________
";
        }

        private string GetLegalNoticeTemplate()
        {
            return @"
إنذار قانوني

إلى السيد/السيدة: [الاسم]
الموضوع: [موضوع الإنذار]

نحيطكم علماً بأنه...

المطلوب: [المطلوب]
المهلة: [المهلة المحددة]

المحامي: ________________
التوقيع: ________________
التاريخ: ________________
";
        }
    }

    // 📊 نماذج البيانات لإدارة الوثائق
    public class Document
    {
        public string Id { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public string MimeType { get; set; } = string.Empty;
        public int UploadedBy { get; set; }
        public DateTime UploadedAt { get; set; }
        public DateTime? LastModifiedAt { get; set; }
        public DocumentCategory Category { get; set; }
        public List<string> Tags { get; set; } = new();
        public bool IsEncrypted { get; set; }
        public bool IsSigned { get; set; }
        public string? ExtractedText { get; set; }
        public int? ClientId { get; set; }
        public int? CaseId { get; set; }
        public List<DigitalSignature> Signatures { get; set; } = new();
        public List<DocumentShare> Shares { get; set; } = new();
    }

    public class DocumentUploadRequest
    {
        public string FileName { get; set; } = string.Empty;
        public byte[] FileData { get; set; } = Array.Empty<byte>();
        public int UserId { get; set; }
        public DocumentCategory Category { get; set; }
        public List<string> Tags { get; set; } = new();
        public bool EncryptDocument { get; set; } = false;
        public int? ClientId { get; set; }
        public int? CaseId { get; set; }
    }

    public class DocumentSearchRequest
    {
        public string? SearchText { get; set; }
        public DocumentCategory? Category { get; set; }
        public int? ClientId { get; set; }
        public int? CaseId { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
    }

    public class DocumentSignRequest
    {
        public string DocumentId { get; set; } = string.Empty;
        public int SignerId { get; set; }
        public string SignerName { get; set; } = string.Empty;
        public string SignerEmail { get; set; } = string.Empty;
        public byte[] SignatureData { get; set; } = Array.Empty<byte>();
        public SignatureType SignatureType { get; set; }
    }

    public class DocumentShareRequest
    {
        public string DocumentId { get; set; } = string.Empty;
        public int UserId { get; set; }
        public string SharedWith { get; set; } = string.Empty;
        public ShareType ShareType { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public AccessLevel AccessLevel { get; set; }
    }

    public class DigitalSignature
    {
        public string Id { get; set; } = string.Empty;
        public string DocumentId { get; set; } = string.Empty;
        public int SignerId { get; set; }
        public string SignerName { get; set; } = string.Empty;
        public string SignerEmail { get; set; } = string.Empty;
        public byte[] SignatureData { get; set; } = Array.Empty<byte>();
        public DateTime SignedAt { get; set; }
        public SignatureType SignatureType { get; set; }
        public string CertificateInfo { get; set; } = string.Empty;
    }

    public class DocumentShare
    {
        public string Id { get; set; } = string.Empty;
        public string DocumentId { get; set; } = string.Empty;
        public int SharedBy { get; set; }
        public string SharedWith { get; set; } = string.Empty;
        public ShareType ShareType { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public AccessLevel AccessLevel { get; set; }
        public string ShareUrl { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public bool IsActive { get; set; }
    }

    public class DocumentTemplate
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public DocumentCategory Category { get; set; }
        public string Description { get; set; } = string.Empty;
        public string TemplateContent { get; set; } = string.Empty;
        public string Language { get; set; } = "ar";
    }

    public class DocumentResult
    {
        public bool Success { get; set; }
        public string DocumentId { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
    }

    public class SignatureResult
    {
        public bool Success { get; set; }
        public string SignatureId { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
    }

    public class ShareResult
    {
        public bool Success { get; set; }
        public string ShareId { get; set; } = string.Empty;
        public string ShareUrl { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
    }

    public enum DocumentCategory
    {
        Contract,
        PowerOfAttorney,
        LegalNotice,
        CourtDocument,
        Evidence,
        ClientDocument,
        InternalDocument,
        Template,
        Other
    }

    public enum SignatureType
    {
        Digital,
        Electronic,
        Handwritten
    }

    public enum ShareType
    {
        Email,
        Link,
        Internal
    }

    public enum AccessLevel
    {
        View,
        Download,
        Edit,
        Full
    }
}
