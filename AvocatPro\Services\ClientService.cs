using Microsoft.EntityFrameworkCore;
using AvocatPro.Data;
using AvocatPro.Models;

namespace AvocatPro.Services;

/// <summary>
/// خدمة إدارة الموكلين
/// </summary>
public class ClientService : IClientService
{
    private readonly AvocatProDbContext _context;

    public ClientService(AvocatProDbContext context)
    {
        _context = context;
    }

    public async Task<List<Client>> GetAllClientsAsync()
    {
        return await _context.Clients
            .Where(c => !c.IsDeleted)
            .OrderBy(c => c.FullName)
            .ToListAsync();
    }

    public async Task<PagedResult<Client>> GetClientsPagedAsync(int pageNumber, int pageSize, 
        string? searchTerm = null, ClientType? clientType = null, ClientStatus? status = null)
    {
        var query = _context.Clients.Where(c => !c.IsDeleted);

        // تطبيق البحث
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            searchTerm = searchTerm.ToLower();
            query = query.Where(c => 
                c.FullName.ToLower().Contains(searchTerm) ||
                c.Phone.Contains(searchTerm) ||
                c.Email != null && c.Email.ToLower().Contains(searchTerm) ||
                c.OfficeReference.ToLower().Contains(searchTerm) ||
                c.IdentityNumber != null && c.IdentityNumber.Contains(searchTerm));
        }

        // تطبيق تصفية النوع
        if (clientType.HasValue)
        {
            query = query.Where(c => c.Type == clientType.Value);
        }

        // تطبيق تصفية الحالة
        if (status.HasValue)
        {
            query = query.Where(c => c.Status == status.Value);
        }

        var totalCount = await query.CountAsync();

        var items = await query
            .OrderBy(c => c.FullName)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return new PagedResult<Client>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize
        };
    }

    public async Task<Client?> GetClientByIdAsync(int id)
    {
        return await _context.Clients
            .Include(c => c.Contacts)
            .Include(c => c.Documents)
            .FirstOrDefaultAsync(c => c.Id == id && !c.IsDeleted);
    }

    public async Task<Client?> GetClientByOfficeReferenceAsync(string officeReference)
    {
        return await _context.Clients
            .FirstOrDefaultAsync(c => c.OfficeReference == officeReference && !c.IsDeleted);
    }

    public async Task<Client> CreateClientAsync(Client client)
    {
        // التحقق من توفر مرجع المكتب
        if (!await IsOfficeReferenceAvailableAsync(client.OfficeReference))
        {
            throw new ArgumentException("مرجع المكتب غير متاح");
        }

        client.CreatedAt = DateTime.Now;
        _context.Clients.Add(client);
        await _context.SaveChangesAsync();

        return client;
    }

    public async Task<Client> UpdateClientAsync(Client client)
    {
        var existingClient = await GetClientByIdAsync(client.Id);
        if (existingClient == null)
        {
            throw new ArgumentException("الموكل غير موجود");
        }

        // التحقق من توفر مرجع المكتب
        if (!await IsOfficeReferenceAvailableAsync(client.OfficeReference, client.Id))
        {
            throw new ArgumentException("مرجع المكتب غير متاح");
        }

        // تحديث البيانات
        existingClient.OfficeReference = client.OfficeReference;
        existingClient.FullName = client.FullName;
        existingClient.Type = client.Type;
        existingClient.IdentityNumber = client.IdentityNumber;
        existingClient.Phone = client.Phone;
        existingClient.Phone2 = client.Phone2;
        existingClient.Email = client.Email;
        existingClient.Address = client.Address;
        existingClient.City = client.City;
        existingClient.Region = client.Region;
        existingClient.PostalCode = client.PostalCode;
        existingClient.Nationality = client.Nationality;
        existingClient.DateOfBirth = client.DateOfBirth;
        existingClient.Gender = client.Gender;
        existingClient.Profession = client.Profession;
        existingClient.MaritalStatus = client.MaritalStatus;
        existingClient.CompanyName = client.CompanyName;
        existingClient.CommercialRegister = client.CommercialRegister;
        existingClient.TaxNumber = client.TaxNumber;
        existingClient.LegalRepresentative = client.LegalRepresentative;
        existingClient.Status = client.Status;
        existingClient.Source = client.Source;
        existingClient.Rating = client.Rating;
        existingClient.Notes = client.Notes;
        existingClient.UpdatedAt = DateTime.Now;
        existingClient.UpdatedBy = client.UpdatedBy;

        await _context.SaveChangesAsync();
        return existingClient;
    }

    public async Task<bool> DeleteClientAsync(int id, string deletedBy)
    {
        var client = await GetClientByIdAsync(id);
        if (client == null)
        {
            return false;
        }

        // التحقق من وجود قضايا مرتبطة
        var hasCases = await _context.Cases.AnyAsync(c => c.ClientId == id && !c.IsDeleted);
        if (hasCases)
        {
            throw new InvalidOperationException("لا يمكن حذف الموكل لوجود قضايا مرتبطة به");
        }

        client.IsDeleted = true;
        client.DeletedAt = DateTime.Now;
        client.DeletedBy = deletedBy;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<List<Client>> SearchClientsAsync(string searchTerm)
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
        {
            return await GetAllClientsAsync();
        }

        searchTerm = searchTerm.ToLower();

        return await _context.Clients
            .Where(c => !c.IsDeleted && 
                       (c.FullName.ToLower().Contains(searchTerm) ||
                        c.Phone.Contains(searchTerm) ||
                        c.Email != null && c.Email.ToLower().Contains(searchTerm) ||
                        c.OfficeReference.ToLower().Contains(searchTerm)))
            .OrderBy(c => c.FullName)
            .ToListAsync();
    }

    public async Task<List<Client>> GetClientsByTypeAsync(ClientType type)
    {
        return await _context.Clients
            .Where(c => !c.IsDeleted && c.Type == type)
            .OrderBy(c => c.FullName)
            .ToListAsync();
    }

    public async Task<List<Client>> GetClientsByStatusAsync(ClientStatus status)
    {
        return await _context.Clients
            .Where(c => !c.IsDeleted && c.Status == status)
            .OrderBy(c => c.FullName)
            .ToListAsync();
    }

    public async Task<List<Client>> GetClientsByCityAsync(string city)
    {
        return await _context.Clients
            .Where(c => !c.IsDeleted && c.City == city)
            .OrderBy(c => c.FullName)
            .ToListAsync();
    }

    public async Task<bool> IsOfficeReferenceAvailableAsync(string officeReference, int? excludeClientId = null)
    {
        var query = _context.Clients.Where(c => c.OfficeReference == officeReference && !c.IsDeleted);
        
        if (excludeClientId.HasValue)
        {
            query = query.Where(c => c.Id != excludeClientId.Value);
        }

        return !await query.AnyAsync();
    }

    public async Task<string> GenerateOfficeReferenceAsync()
    {
        var year = DateTime.Now.Year.ToString();
        var prefix = $"CL{year}";

        // البحث عن آخر رقم مستخدم
        var lastReference = await _context.Clients
            .Where(c => c.OfficeReference.StartsWith(prefix))
            .OrderByDescending(c => c.OfficeReference)
            .Select(c => c.OfficeReference)
            .FirstOrDefaultAsync();

        int nextNumber = 1;
        if (lastReference != null)
        {
            var numberPart = lastReference.Substring(prefix.Length);
            if (int.TryParse(numberPart, out int lastNumber))
            {
                nextNumber = lastNumber + 1;
            }
        }

        return $"{prefix}{nextNumber:D4}";
    }

    public async Task<ClientStatistics> GetClientStatisticsAsync()
    {
        var currentMonth = DateTime.Now.Month;
        var currentYear = DateTime.Now.Year;

        var statistics = new ClientStatistics
        {
            TotalClients = await _context.Clients.CountAsync(c => !c.IsDeleted),
            ActiveClients = await _context.Clients.CountAsync(c => !c.IsDeleted && c.Status == ClientStatus.Active),
            InactiveClients = await _context.Clients.CountAsync(c => !c.IsDeleted && c.Status == ClientStatus.Inactive),
            BlockedClients = await _context.Clients.CountAsync(c => !c.IsDeleted && c.Status == ClientStatus.Blocked),
            NewClientsThisMonth = await _context.Clients.CountAsync(c => !c.IsDeleted && 
                c.CreatedAt.Month == currentMonth && c.CreatedAt.Year == currentYear),
            NewClientsThisYear = await _context.Clients.CountAsync(c => !c.IsDeleted && 
                c.CreatedAt.Year == currentYear)
        };

        // إحصائيات حسب النوع
        var typeStats = await _context.Clients
            .Where(c => !c.IsDeleted)
            .GroupBy(c => c.Type)
            .Select(g => new { Type = g.Key, Count = g.Count() })
            .ToListAsync();

        foreach (var typeStat in typeStats)
        {
            statistics.ClientsByType[typeStat.Type] = typeStat.Count;
        }

        // إحصائيات حسب المدينة
        var cityStats = await _context.Clients
            .Where(c => !c.IsDeleted && c.City != null)
            .GroupBy(c => c.City)
            .Select(g => new { City = g.Key, Count = g.Count() })
            .OrderByDescending(g => g.Count)
            .Take(10)
            .ToListAsync();

        foreach (var cityStat in cityStats)
        {
            statistics.ClientsByCity[cityStat.City!] = cityStat.Count;
        }

        // متوسط التقييم
        var ratings = await _context.Clients
            .Where(c => !c.IsDeleted && c.Rating.HasValue)
            .Select(c => c.Rating!.Value)
            .ToListAsync();

        if (ratings.Any())
        {
            statistics.AverageRating = ratings.Average();
        }

        return statistics;
    }

    public async Task<bool> ExportClientsToExcelAsync(List<Client> clients, string filePath)
    {
        // TODO: تنفيذ تصدير Excel
        await Task.CompletedTask;
        return true;
    }

    public async Task<bool> ExportClientsToPdfAsync(List<Client> clients, string filePath)
    {
        // TODO: تنفيذ تصدير PDF
        await Task.CompletedTask;
        return true;
    }

    public async Task<List<Client>> ImportClientsFromExcelAsync(string filePath)
    {
        // TODO: تنفيذ استيراد Excel
        await Task.CompletedTask;
        return new List<Client>();
    }

    public async Task<ClientContact> AddClientContactAsync(int clientId, ClientContact contact)
    {
        contact.ClientId = clientId;
        contact.CreatedAt = DateTime.Now;
        
        _context.ClientContacts.Add(contact);
        await _context.SaveChangesAsync();
        
        return contact;
    }

    public async Task<ClientDocument> AddClientDocumentAsync(int clientId, ClientDocument document)
    {
        document.ClientId = clientId;
        document.CreatedAt = DateTime.Now;
        
        _context.ClientDocuments.Add(document);
        await _context.SaveChangesAsync();
        
        return document;
    }

    public async Task<List<ClientContact>> GetClientContactsAsync(int clientId)
    {
        return await _context.ClientContacts
            .Where(cc => cc.ClientId == clientId && !cc.IsDeleted)
            .OrderBy(cc => cc.Name)
            .ToListAsync();
    }

    public async Task<List<ClientDocument>> GetClientDocumentsAsync(int clientId)
    {
        return await _context.ClientDocuments
            .Where(cd => cd.ClientId == clientId && !cd.IsDeleted)
            .OrderByDescending(cd => cd.CreatedAt)
            .ToListAsync();
    }
}
