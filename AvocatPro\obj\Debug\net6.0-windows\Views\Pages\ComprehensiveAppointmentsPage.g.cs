﻿#pragma checksum "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "0A17FF7DBFF4AC34C736CD23D0BD6131820847E3"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using FontAwesome.WPF;
using FontAwesome.WPF.Converters;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Pages {
    
    
    /// <summary>
    /// ComprehensiveAppointmentsPage
    /// </summary>
    public partial class ComprehensiveAppointmentsPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 119 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddAppointmentButton;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CalendarViewButton;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SendRemindersButton;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAppointmentsCount;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TodayAppointmentsCount;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ScheduledAppointmentsCount;
        
        #line default
        #line hidden
        
        
        #line 184 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CompletedAppointmentsCount;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OverdueAppointmentsCount;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PendingRemindersCount;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 234 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 240 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TypeFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 246 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LawyerFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 252 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PriorityFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 259 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 262 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 283 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker FromDatePicker;
        
        #line default
        #line hidden
        
        
        #line 286 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ToDatePicker;
        
        #line default
        #line hidden
        
        
        #line 289 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox QuickViewComboBox;
        
        #line default
        #line hidden
        
        
        #line 322 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditAppointmentButton;
        
        #line default
        #line hidden
        
        
        #line 328 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteAppointmentButton;
        
        #line default
        #line hidden
        
        
        #line 334 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewDetailsButton;
        
        #line default
        #line hidden
        
        
        #line 340 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintAppointmentButton;
        
        #line default
        #line hidden
        
        
        #line 350 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid AppointmentsDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/pages/comprehensiveappointmentspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AddAppointmentButton = ((System.Windows.Controls.Button)(target));
            
            #line 119 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
            this.AddAppointmentButton.Click += new System.Windows.RoutedEventHandler(this.AddAppointmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.CalendarViewButton = ((System.Windows.Controls.Button)(target));
            
            #line 125 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
            this.CalendarViewButton.Click += new System.Windows.RoutedEventHandler(this.CalendarViewButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.SendRemindersButton = ((System.Windows.Controls.Button)(target));
            
            #line 131 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
            this.SendRemindersButton.Click += new System.Windows.RoutedEventHandler(this.SendRemindersButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.TotalAppointmentsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.TodayAppointmentsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.ScheduledAppointmentsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.CompletedAppointmentsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.OverdueAppointmentsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.PendingRemindersCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 231 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
            this.SearchTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.SearchTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 231 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
            this.SearchTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.SearchTextBox_LostFocus);
            
            #line default
            #line hidden
            
            #line 231 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.StatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 235 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
            this.StatusFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 12:
            this.TypeFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 241 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
            this.TypeFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 13:
            this.LawyerFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 247 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
            this.LawyerFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 14:
            this.PriorityFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 253 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
            this.PriorityFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 15:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 259 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 262 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.FromDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 283 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
            this.FromDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DatePicker_SelectedDateChanged);
            
            #line default
            #line hidden
            return;
            case 18:
            this.ToDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 286 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
            this.ToDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DatePicker_SelectedDateChanged);
            
            #line default
            #line hidden
            return;
            case 19:
            this.QuickViewComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 290 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
            this.QuickViewComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.QuickViewComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 20:
            this.EditAppointmentButton = ((System.Windows.Controls.Button)(target));
            
            #line 322 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
            this.EditAppointmentButton.Click += new System.Windows.RoutedEventHandler(this.EditAppointmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.DeleteAppointmentButton = ((System.Windows.Controls.Button)(target));
            
            #line 328 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
            this.DeleteAppointmentButton.Click += new System.Windows.RoutedEventHandler(this.DeleteAppointmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.ViewDetailsButton = ((System.Windows.Controls.Button)(target));
            
            #line 334 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
            this.ViewDetailsButton.Click += new System.Windows.RoutedEventHandler(this.ViewDetailsButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.PrintAppointmentButton = ((System.Windows.Controls.Button)(target));
            
            #line 340 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
            this.PrintAppointmentButton.Click += new System.Windows.RoutedEventHandler(this.PrintAppointmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.AppointmentsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 362 "..\..\..\..\..\Views\Pages\ComprehensiveAppointmentsPage.xaml"
            this.AppointmentsDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.AppointmentsDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

