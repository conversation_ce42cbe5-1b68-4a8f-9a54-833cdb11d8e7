using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AvocatPro.Models.UserManagement;

/// <summary>
/// نموذج الصلاحيات
/// </summary>
public class Permission
{
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// اسم الصلاحية
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// رمز الصلاحية الفريد
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// وصف الصلاحية
    /// </summary>
    [MaxLength(500)]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// القسم الذي تنتمي إليه الصلاحية
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string Module { get; set; } = string.Empty;

    /// <summary>
    /// نوع العملية
    /// </summary>
    public PermissionType Type { get; set; }

    /// <summary>
    /// مستوى الأهمية
    /// </summary>
    public PermissionLevel Level { get; set; }

    /// <summary>
    /// هل الصلاحية نشطة
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// تاريخ الإنشاء
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// تاريخ آخر تحديث
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.Now;
}

/// <summary>
/// أنواع الصلاحيات
/// </summary>
public enum PermissionType
{
    /// <summary>
    /// عرض/قراءة
    /// </summary>
    View = 1,

    /// <summary>
    /// إضافة/إنشاء
    /// </summary>
    Create = 2,

    /// <summary>
    /// تعديل/تحديث
    /// </summary>
    Update = 3,

    /// <summary>
    /// حذف
    /// </summary>
    Delete = 4,

    /// <summary>
    /// طباعة
    /// </summary>
    Print = 5,

    /// <summary>
    /// تصدير
    /// </summary>
    Export = 6,

    /// <summary>
    /// استيراد
    /// </summary>
    Import = 7,

    /// <summary>
    /// إدارة
    /// </summary>
    Manage = 8,

    /// <summary>
    /// موافقة
    /// </summary>
    Approve = 9,

    /// <summary>
    /// رفض
    /// </summary>
    Reject = 10
}

/// <summary>
/// مستويات الصلاحيات
/// </summary>
public enum PermissionLevel
{
    /// <summary>
    /// أساسي
    /// </summary>
    Basic = 1,

    /// <summary>
    /// متوسط
    /// </summary>
    Intermediate = 2,

    /// <summary>
    /// متقدم
    /// </summary>
    Advanced = 3,

    /// <summary>
    /// إداري
    /// </summary>
    Administrative = 4,

    /// <summary>
    /// مدير النظام
    /// </summary>
    SystemAdmin = 5
}

/// <summary>
/// صلاحيات المستخدم
/// </summary>
public class UserPermission
{
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// معرف المستخدم
    /// </summary>
    [Required]
    public int UserId { get; set; }

    /// <summary>
    /// معرف الصلاحية
    /// </summary>
    [Required]
    public int PermissionId { get; set; }

    /// <summary>
    /// هل الصلاحية ممنوحة
    /// </summary>
    public bool IsGranted { get; set; } = true;

    /// <summary>
    /// تاريخ منح الصلاحية
    /// </summary>
    public DateTime GrantedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// من منح الصلاحية
    /// </summary>
    public int? GrantedBy { get; set; }

    /// <summary>
    /// تاريخ انتهاء الصلاحية (اختياري)
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// ملاحظات
    /// </summary>
    [MaxLength(500)]
    public string Notes { get; set; } = string.Empty;

    // Navigation Properties
    [ForeignKey("UserId")]
    public virtual User User { get; set; } = null!;

    [ForeignKey("PermissionId")]
    public virtual Permission Permission { get; set; } = null!;

    [ForeignKey("GrantedBy")]
    public virtual User? GrantedByUser { get; set; }
}

/// <summary>
/// مجموعة الصلاحيات
/// </summary>
public class PermissionGroup
{
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// اسم المجموعة
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// وصف المجموعة
    /// </summary>
    [MaxLength(500)]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// لون المجموعة
    /// </summary>
    [MaxLength(7)]
    public string Color { get; set; } = "#007ACC";

    /// <summary>
    /// أيقونة المجموعة
    /// </summary>
    [MaxLength(50)]
    public string Icon { get; set; } = "👥";

    /// <summary>
    /// ترتيب العرض
    /// </summary>
    public int DisplayOrder { get; set; }

    /// <summary>
    /// هل المجموعة نشطة
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// تاريخ الإنشاء
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// الصلاحيات في هذه المجموعة
    /// </summary>
    public virtual ICollection<Permission> Permissions { get; set; } = new List<Permission>();
}

/// <summary>
/// صلاحيات المجموعة للمستخدم
/// </summary>
public class UserPermissionGroup
{
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// معرف المستخدم
    /// </summary>
    [Required]
    public int UserId { get; set; }

    /// <summary>
    /// معرف مجموعة الصلاحيات
    /// </summary>
    [Required]
    public int PermissionGroupId { get; set; }

    /// <summary>
    /// تاريخ الإضافة
    /// </summary>
    public DateTime AssignedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// من أضاف المستخدم للمجموعة
    /// </summary>
    public int? AssignedBy { get; set; }

    // Navigation Properties
    [ForeignKey("UserId")]
    public virtual User User { get; set; } = null!;

    [ForeignKey("PermissionGroupId")]
    public virtual PermissionGroup PermissionGroup { get; set; } = null!;

    [ForeignKey("AssignedBy")]
    public virtual User? AssignedByUser { get; set; }
}
