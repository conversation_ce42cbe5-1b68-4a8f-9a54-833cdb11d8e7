using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;
using AvocatPro.Models;
using AvocatPro.Views.Windows;

namespace AvocatPro.Views.Pages
{
    public partial class UserManagementPage : Page
    {
        private readonly User _currentUser;
        private List<UserCard> _allUsers;
        private List<UserCard> _filteredUsers;
        private bool _isCardView = true;

        public UserManagementPage(User currentUser)
        {
            try
            {
                InitializeComponent();
                _currentUser = currentUser ?? new User
                {
                    Id = 1,
                    Username = "admin",
                    FullName = "مدير النظام",
                    Email = "<EMAIL>",
                    Role = UserRole.Admin
                };
                _allUsers = new List<UserCard>();
                _filteredUsers = new List<UserCard>();

                // تأخير تحميل البيانات حتى يتم تحميل جميع العناصر
                this.Loaded += UserManagementPage_Loaded;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة الصفحة: {ex.Message}\n\nStack Trace: {ex.StackTrace}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UserManagementPage_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من وجود العناصر الأساسية
                if (UsersCardsPanel == null)
                {
                    MessageBox.Show("لم يتم العثور على UsersCardsPanel", "خطأ");
                    return;
                }

                LoadSampleData();
                LoadRoles();

                // تحديد القيم الافتراضية للفلاتر
                SetDefaultFilterValues();

                // تحديد وضع العرض الافتراضي
                if (CardViewBtn != null)
                {
                    CardViewBtn.IsChecked = true;
                    _isCardView = true;
                }

                ApplyFilters();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}\n\nStack Trace: {ex.StackTrace}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SetDefaultFilterValues()
        {
            try
            {
                if (StatusFilterCombo != null && StatusFilterCombo.Items.Count > 0)
                    StatusFilterCombo.SelectedIndex = 0;
                if (RoleFilterCombo != null && RoleFilterCombo.Items.Count > 0)
                    RoleFilterCombo.SelectedIndex = 0;
                if (DateFilterCombo != null && DateFilterCombo.Items.Count > 0)
                    DateFilterCombo.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديد القيم الافتراضية: {ex.Message}");
            }
        }

        private void LoadSampleData()
        {
            try
            {
                // بيانات تجريبية للمستخدمين
                _allUsers = new List<UserCard>
            {
                new UserCard
                {
                    UserId = 1,
                    UserName = "admin",
                    FullName = "أحمد محمد الإدارة",
                    Email = "<EMAIL>",
                    IsOnline = true,
                    CurrentSessionStart = DateTime.Now.AddHours(-2),
                    CurrentSessionDuration = TimeSpan.FromHours(2),
                    Location = "الرياض، السعودية",
                    DeviceInfo = "Windows 11 - Chrome",
                    Roles = new List<string> { "مدير النظام", "محامي أول" },
                    UnreadMessages = 0,
                    CanMessage = false // لا يمكن مراسلة النفس
                },
                new UserCard
                {
                    UserId = 2,
                    UserName = "lawyer1",
                    FullName = "سارة أحمد القانونية",
                    Email = "<EMAIL>",
                    IsOnline = true,
                    CurrentSessionStart = DateTime.Now.AddMinutes(-45),
                    CurrentSessionDuration = TimeSpan.FromMinutes(45),
                    Location = "جدة، السعودية",
                    DeviceInfo = "Windows 10 - Edge",
                    Roles = new List<string> { "محامي", "مستشار قانوني" },
                    UnreadMessages = 3,
                    CanMessage = true
                },
                new UserCard
                {
                    UserId = 3,
                    UserName = "secretary1",
                    FullName = "فاطمة علي السكرتارية",
                    Email = "<EMAIL>",
                    IsOnline = false,
                    LastSeen = DateTime.Now.AddHours(-1),
                    Location = "الدمام، السعودية",
                    DeviceInfo = "Windows 11 - Firefox",
                    Roles = new List<string> { "سكرتير", "مساعد إداري" },
                    UnreadMessages = 1,
                    CanMessage = true
                },
                new UserCard
                {
                    UserId = 4,
                    UserName = "accountant1",
                    FullName = "محمد خالد المحاسب",
                    Email = "<EMAIL>",
                    IsOnline = true,
                    CurrentSessionStart = DateTime.Now.AddHours(-4),
                    CurrentSessionDuration = TimeSpan.FromHours(4),
                    Location = "الرياض، السعودية",
                    DeviceInfo = "Windows 11 - Chrome",
                    Roles = new List<string> { "محاسب", "مدير مالي" },
                    UnreadMessages = 0,
                    CanMessage = true
                },
                new UserCard
                {
                    UserId = 5,
                    UserName = "intern1",
                    FullName = "عبدالله سعد المتدرب",
                    Email = "<EMAIL>",
                    IsOnline = false,
                    LastSeen = DateTime.Now.AddDays(-2),
                    Location = "الرياض، السعودية",
                    DeviceInfo = "Windows 10 - Chrome",
                    Roles = new List<string> { "متدرب" },
                    UnreadMessages = 5,
                    CanMessage = true
                }
            };

                if (UsersCountText != null)
                    UsersCountText.Text = $"({_allUsers.Count} مستخدم)";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات التجريبية: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
                _allUsers = new List<UserCard>(); // قائمة فارغة في حالة الخطأ
            }
        }

        private void LoadRoles()
        {
            if (RoleFilterCombo == null) return;

            // تحميل الأدوار في فلتر الأدوار
            var roles = _allUsers.SelectMany(u => u.Roles).Distinct().ToList();

            foreach (var role in roles)
            {
                RoleFilterCombo.Items.Add(new ComboBoxItem { Content = role });
            }
        }

        private void ApplyFilters()
        {
            if (SearchTextBox == null || StatusFilterCombo == null || RoleFilterCombo == null)
                return;

            var searchText = SearchTextBox.Text?.ToLower() ?? "";
            var statusFilter = ((ComboBoxItem)StatusFilterCombo.SelectedItem)?.Content?.ToString() ?? "جميع الحالات";
            var roleFilter = ((ComboBoxItem)RoleFilterCombo.SelectedItem)?.Content?.ToString() ?? "جميع الأدوار";

            _filteredUsers = _allUsers.Where(user =>
            {
                // فلتر البحث
                if (!string.IsNullOrEmpty(searchText) &&
                    !user.UserName.ToLower().Contains(searchText) &&
                    !user.FullName.ToLower().Contains(searchText) &&
                    !user.Email.ToLower().Contains(searchText))
                    return false;

                // فلتر الحالة
                if (statusFilter != "جميع الحالات")
                {
                    switch (statusFilter)
                    {
                        case "متصل":
                            if (!user.IsOnline) return false;
                            break;
                        case "غير متصل":
                            if (user.IsOnline) return false;
                            break;
                        case "نشط":
                            if (!user.IsOnline && !user.LastSeen.HasValue) return false;
                            break;
                        case "معطل":
                            if (user.IsOnline || user.LastSeen.HasValue) return false;
                            break;
                    }
                }

                // فلتر الدور
                if (roleFilter != "جميع الأدوار" && !user.Roles.Contains(roleFilter))
                    return false;

                return true;
            }).ToList();

            UpdateDisplay();
        }

        private void UpdateDisplay()
        {
            try
            {
                if (_isCardView)
                {
                    UpdateCardView();
                }
                else
                {
                    UpdateListView();
                }

                // إظهار رسالة عدم وجود بيانات
                if (NoDataPanel != null)
                    NoDataPanel.Visibility = _filteredUsers.Count == 0 ? Visibility.Visible : Visibility.Collapsed;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث العرض: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateCardView()
        {
            if (UsersCardsPanel == null) return;

            UsersCardsPanel.Children.Clear();

            foreach (var user in _filteredUsers)
            {
                try
                {
                    var card = CreateUserCard(user);
                    if (card != null)
                        UsersCardsPanel.Children.Add(card);
                }
                catch (Exception ex)
                {
                    // تسجيل الخطأ وتجاهله لتجنب توقف التطبيق
                    System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء بطاقة المستخدم: {ex.Message}");
                }
            }
        }

        private Border CreateUserCard(UserCard user)
        {
            var card = new Border
            {
                Style = (Style)FindResource("UserCard"),
                Width = 280,
                Height = 200,
                Cursor = System.Windows.Input.Cursors.Hand
            };

            var grid = new Grid();
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // الرأس
            var headerPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 10) };
            
            // صورة المستخدم
            var avatar = new Border
            {
                Width = 50,
                Height = 50,
                CornerRadius = new CornerRadius(25),
                Background = new SolidColorBrush(Color.FromRgb(0, 123, 255)),
                Margin = new Thickness(0, 0, 10, 0)
            };
            
            var avatarText = new TextBlock
            {
                Text = user.FullName.Split(' ').FirstOrDefault()?.Substring(0, 1) ?? "؟",
                Foreground = Brushes.White,
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };
            avatar.Child = avatarText;

            // معلومات المستخدم
            var userInfo = new StackPanel { VerticalAlignment = VerticalAlignment.Center };
            
            var nameText = new TextBlock
            {
                Text = user.FullName,
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(44, 62, 80))
            };
            
            var usernameText = new TextBlock
            {
                Text = $"@{user.UserName}",
                FontSize = 12,
                Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125))
            };

            userInfo.Children.Add(nameText);
            userInfo.Children.Add(usernameText);

            // مؤشر الحالة
            var statusIndicator = new Ellipse
            {
                Style = (Style)FindResource("StatusIndicator"),
                Fill = user.IsOnline ? Brushes.Green : Brushes.Gray,
                HorizontalAlignment = HorizontalAlignment.Right,
                VerticalAlignment = VerticalAlignment.Top
            };

            headerPanel.Children.Add(avatar);
            headerPanel.Children.Add(userInfo);
            
            Grid.SetRow(headerPanel, 0);
            grid.Children.Add(headerPanel);
            grid.Children.Add(statusIndicator);

            // المحتوى
            var contentPanel = new StackPanel { Margin = new Thickness(0, 10, 0, 10) };
            
            // الحالة
            var statusPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 5) };
            var statusIcon = new TextBlock { Text = "🟢", FontSize = 12, Margin = new Thickness(0, 0, 5, 0) };
            var statusText = new TextBlock
            {
                Text = user.StatusDisplay,
                FontSize = 12,
                Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125))
            };
            statusPanel.Children.Add(statusIcon);
            statusPanel.Children.Add(statusText);

            // مدة الجلسة
            if (user.IsOnline && user.CurrentSessionDuration.HasValue)
            {
                var sessionPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 5) };
                var sessionIcon = new TextBlock { Text = "⏱️", FontSize = 12, Margin = new Thickness(0, 0, 5, 0) };
                var sessionText = new TextBlock
                {
                    Text = $"مدة الجلسة: {user.SessionDurationDisplay}",
                    FontSize = 12,
                    Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125))
                };
                sessionPanel.Children.Add(sessionIcon);
                sessionPanel.Children.Add(sessionText);
                contentPanel.Children.Add(sessionPanel);
            }

            // الأدوار
            var rolesText = new TextBlock
            {
                Text = string.Join("، ", user.Roles),
                FontSize = 11,
                Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 5, 0, 0)
            };

            contentPanel.Children.Add(statusPanel);
            contentPanel.Children.Add(rolesText);

            Grid.SetRow(contentPanel, 1);
            grid.Children.Add(contentPanel);

            // الأزرار
            var buttonsPanel = new StackPanel { Orientation = Orientation.Horizontal, HorizontalAlignment = HorizontalAlignment.Center };
            
            if (user.CanMessage)
            {
                var chatBtn = new Button
                {
                    Content = "💬",
                    Width = 35,
                    Height = 35,
                    Margin = new Thickness(5, 0, 5, 0),
                    Background = new SolidColorBrush(Color.FromRgb(40, 167, 69)),
                    Foreground = Brushes.White,
                    BorderThickness = new Thickness(0),
                    FontSize = 16,
                    Tag = user.UserId
                };
                chatBtn.Click += ChatUser_Click;
                buttonsPanel.Children.Add(chatBtn);
            }

            var editBtn = new Button
            {
                Content = "⚙️",
                Width = 35,
                Height = 35,
                Margin = new Thickness(5, 0, 5, 0),
                Background = new SolidColorBrush(Color.FromRgb(0, 123, 255)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                FontSize = 16,
                Tag = user.UserId
            };
            editBtn.Click += EditUser_Click;
            buttonsPanel.Children.Add(editBtn);

            var reportBtn = new Button
            {
                Content = "📊",
                Width = 35,
                Height = 35,
                Margin = new Thickness(5, 0, 5, 0),
                Background = new SolidColorBrush(Color.FromRgb(23, 162, 184)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                FontSize = 16,
                Tag = user.UserId
            };
            reportBtn.Click += UserReport_Click;
            buttonsPanel.Children.Add(reportBtn);

            Grid.SetRow(buttonsPanel, 2);
            grid.Children.Add(buttonsPanel);

            card.Child = grid;
            return card;
        }

        private void UpdateListView()
        {
            if (UsersDataGrid == null) return;

            try
            {
                // تحويل البيانات لعرض الجدول
                var listData = _filteredUsers.Select(u => new
                {
                    UserId = u.UserId,
                    UserName = u.UserName,
                    FullName = u.FullName,
                    Email = u.Email,
                    RolesDisplay = string.Join("، ", u.Roles),
                    LastLoginDisplay = u.LastSeen?.ToString("yyyy/MM/dd HH:mm") ?? "لم يسجل دخول",
                    SessionDurationDisplay = u.SessionDurationDisplay,
                    StatusColor = u.IsOnline ? Brushes.Green : Brushes.Gray
                }).ToList();

                UsersDataGrid.ItemsSource = listData;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث عرض القائمة: {ex.Message}");
            }
        }

        // معالجات الأحداث
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void StatusFilterCombo_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void RoleFilterCombo_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void DateFilterCombo_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void ViewModeChanged(object sender, RoutedEventArgs e)
        {
            try
            {
                if (CardViewBtn == null) return;

                _isCardView = CardViewBtn.IsChecked == true;

                if (CardViewScroller != null)
                    CardViewScroller.Visibility = _isCardView ? Visibility.Visible : Visibility.Collapsed;
                if (UsersDataGrid != null)
                    UsersDataGrid.Visibility = _isCardView ? Visibility.Collapsed : Visibility.Visible;

                // تحديث ألوان الأزرار
                if (CardViewBtn != null)
                    CardViewBtn.Background = _isCardView ? new SolidColorBrush(Color.FromRgb(0, 123, 255)) : new SolidColorBrush(Color.FromRgb(108, 117, 125));
                if (ListViewBtn != null)
                    ListViewBtn.Background = !_isCardView ? new SolidColorBrush(Color.FromRgb(0, 123, 255)) : new SolidColorBrush(Color.FromRgb(108, 117, 125));

                UpdateDisplay();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تغيير وضع العرض: {ex.Message}");
            }
        }

        private void AddUserBtn_Click(object sender, RoutedEventArgs e)
        {
            var addUserWindow = new AddUserWindow(_currentUser);
            if (addUserWindow.ShowDialog() == true)
            {
                LoadSampleData(); // إعادة تحميل البيانات
                ApplyFilters();
            }
        }

        private void ManageRolesBtn_Click(object sender, RoutedEventArgs e)
        {
            var rolesWindow = new RoleManagementWindow(_currentUser);
            rolesWindow.ShowDialog();
        }

        private void UserReportsBtn_Click(object sender, RoutedEventArgs e)
        {
            var reportsWindow = new UserReportsWindow(_currentUser);
            reportsWindow.ShowDialog();
        }

        private void RefreshBtn_Click(object sender, RoutedEventArgs e)
        {
            LoadSampleData();
            ApplyFilters();
        }

        private void EditUser_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button btn && btn.Tag is int userId)
            {
                var editWindow = new EditUserWindow(_currentUser, userId);
                if (editWindow.ShowDialog() == true)
                {
                    LoadSampleData();
                    ApplyFilters();
                }
            }
        }

        private void ChatUser_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button btn && btn.Tag is int userId)
            {
                var chatWindow = new UserChatWindow(_currentUser, userId);
                chatWindow.Show();
            }
        }

        private void UserReport_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button btn && btn.Tag is int userId)
            {
                var reportWindow = new UserDetailReportWindow(_currentUser, userId);
                reportWindow.ShowDialog();
            }
        }
    }
}
