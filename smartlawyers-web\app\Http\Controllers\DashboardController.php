<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Display the main dashboard
     */
    public function index()
    {
        $stats = $this->getDashboardStats();
        $recentActivities = $this->getRecentActivities();
        $upcomingAppointments = $this->getUpcomingAppointments();
        $caseStatistics = $this->getCaseStatistics();
        $monthlyRevenue = $this->getMonthlyRevenue();
        
        return view('dashboard.index', compact(
            'stats',
            'recentActivities', 
            'upcomingAppointments',
            'caseStatistics',
            'monthlyRevenue'
        ));
    }

    /**
     * Get dashboard statistics
     */
    private function getDashboardStats()
    {
        return [
            'total_clients' => DB::table('clients')->count(),
            'active_cases' => DB::table('cases')->where('status', 'active')->count(),
            'pending_appointments' => DB::table('appointments')
                ->where('status', 'pending')
                ->where('appointment_date', '>=', now())
                ->count(),
            'monthly_revenue' => DB::table('invoices')
                ->where('created_at', '>=', now()->startOfMonth())
                ->where('status', 'paid')
                ->sum('amount'),
            'total_documents' => DB::table('documents')->count(),
            'court_sessions' => DB::table('court_sessions')
                ->where('session_date', '>=', now())
                ->count(),
        ];
    }

    /**
     * Get recent activities
     */
    private function getRecentActivities()
    {
        return DB::table('activity_log')
            ->join('users', 'activity_log.causer_id', '=', 'users.id')
            ->select('activity_log.*', 'users.name as user_name')
            ->orderBy('activity_log.created_at', 'desc')
            ->limit(10)
            ->get();
    }

    /**
     * Get upcoming appointments
     */
    private function getUpcomingAppointments()
    {
        return DB::table('appointments')
            ->join('clients', 'appointments.client_id', '=', 'clients.id')
            ->select('appointments.*', 'clients.name as client_name')
            ->where('appointments.appointment_date', '>=', now())
            ->orderBy('appointments.appointment_date', 'asc')
            ->limit(5)
            ->get();
    }

    /**
     * Get case statistics for charts
     */
    private function getCaseStatistics()
    {
        $casesByType = DB::table('cases')
            ->select('case_type', DB::raw('count(*) as count'))
            ->groupBy('case_type')
            ->get();

        $casesByStatus = DB::table('cases')
            ->select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get();

        return [
            'by_type' => $casesByType,
            'by_status' => $casesByStatus,
        ];
    }

    /**
     * Get monthly revenue data
     */
    private function getMonthlyRevenue()
    {
        $months = [];
        $revenue = [];

        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $months[] = $date->format('M Y');
            
            $monthRevenue = DB::table('invoices')
                ->whereYear('created_at', $date->year)
                ->whereMonth('created_at', $date->month)
                ->where('status', 'paid')
                ->sum('amount');
                
            $revenue[] = $monthRevenue;
        }

        return [
            'months' => $months,
            'revenue' => $revenue,
        ];
    }

    /**
     * Get calendar events for dashboard
     */
    public function getCalendarEvents(Request $request)
    {
        $start = $request->get('start');
        $end = $request->get('end');

        $appointments = DB::table('appointments')
            ->join('clients', 'appointments.client_id', '=', 'clients.id')
            ->select(
                'appointments.id',
                'appointments.title',
                'appointments.appointment_date as start',
                'appointments.end_time as end',
                'clients.name as client_name',
                DB::raw("'appointment' as type")
            )
            ->whereBetween('appointments.appointment_date', [$start, $end])
            ->get();

        $courtSessions = DB::table('court_sessions')
            ->join('cases', 'court_sessions.case_id', '=', 'cases.id')
            ->select(
                'court_sessions.id',
                'court_sessions.session_type as title',
                'court_sessions.session_date as start',
                'court_sessions.session_date as end',
                'cases.case_number',
                DB::raw("'court_session' as type")
            )
            ->whereBetween('court_sessions.session_date', [$start, $end])
            ->get();

        $events = $appointments->merge($courtSessions)->map(function ($event) {
            return [
                'id' => $event->id,
                'title' => $event->title,
                'start' => $event->start,
                'end' => $event->end,
                'className' => $event->type === 'appointment' ? 'event-appointment' : 'event-court',
                'extendedProps' => [
                    'type' => $event->type,
                    'client_name' => $event->client_name ?? null,
                    'case_number' => $event->case_number ?? null,
                ]
            ];
        });

        return response()->json($events);
    }

    /**
     * Get Moroccan holidays
     */
    public function getMoroccanHolidays()
    {
        // Static holidays for now - can be enhanced with API integration
        $holidays = [
            [
                'title' => 'رأس السنة الميلادية',
                'date' => Carbon::create(date('Y'), 1, 1)->format('Y-m-d'),
                'type' => 'national'
            ],
            [
                'title' => 'عيد الاستقلال',
                'date' => Carbon::create(date('Y'), 1, 11)->format('Y-m-d'),
                'type' => 'national'
            ],
            [
                'title' => 'عيد العمال',
                'date' => Carbon::create(date('Y'), 5, 1)->format('Y-m-d'),
                'type' => 'national'
            ],
            [
                'title' => 'عيد العرش',
                'date' => Carbon::create(date('Y'), 7, 30)->format('Y-m-d'),
                'type' => 'national'
            ],
            [
                'title' => 'ثورة الملك والشعب',
                'date' => Carbon::create(date('Y'), 8, 20)->format('Y-m-d'),
                'type' => 'national'
            ],
            [
                'title' => 'عيد الشباب',
                'date' => Carbon::create(date('Y'), 8, 21)->format('Y-m-d'),
                'type' => 'national'
            ],
            [
                'title' => 'المسيرة الخضراء',
                'date' => Carbon::create(date('Y'), 11, 6)->format('Y-m-d'),
                'type' => 'national'
            ],
            [
                'title' => 'عيد الاستقلال',
                'date' => Carbon::create(date('Y'), 11, 18)->format('Y-m-d'),
                'type' => 'national'
            ],
        ];

        return response()->json($holidays);
    }

    /**
     * Get quick stats for widgets
     */
    public function getQuickStats()
    {
        $today = Carbon::today();
        $thisWeek = Carbon::now()->startOfWeek();
        $thisMonth = Carbon::now()->startOfMonth();

        return response()->json([
            'today' => [
                'appointments' => DB::table('appointments')->whereDate('appointment_date', $today)->count(),
                'court_sessions' => DB::table('court_sessions')->whereDate('session_date', $today)->count(),
                'new_clients' => DB::table('clients')->whereDate('created_at', $today)->count(),
            ],
            'this_week' => [
                'new_cases' => DB::table('cases')->where('created_at', '>=', $thisWeek)->count(),
                'completed_tasks' => DB::table('tasks')->where('status', 'completed')->where('updated_at', '>=', $thisWeek)->count(),
                'revenue' => DB::table('invoices')->where('status', 'paid')->where('created_at', '>=', $thisWeek)->sum('amount'),
            ],
            'this_month' => [
                'total_revenue' => DB::table('invoices')->where('status', 'paid')->where('created_at', '>=', $thisMonth)->sum('amount'),
                'new_clients' => DB::table('clients')->where('created_at', '>=', $thisMonth)->count(),
                'closed_cases' => DB::table('cases')->where('status', 'closed')->where('updated_at', '>=', $thisMonth)->count(),
            ]
        ]);
    }
}
