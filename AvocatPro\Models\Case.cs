using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AvocatPro.Models;

/// <summary>
/// نموذج الملف/القضية
/// </summary>
public class Case : BaseEntity
{
    /// <summary>
    /// مرجع المكتب للملف
    /// </summary>
    [Required]
    [StringLength(20)]
    public string OfficeReference { get; set; } = string.Empty;
    
    /// <summary>
    /// مرجع المحكمة
    /// </summary>
    [StringLength(50)]
    public string? CourtReference { get; set; }
    
    /// <summary>
    /// عنوان القضية
    /// </summary>
    [Required]
    [StringLength(200)]
    public string Title { get; set; } = string.Empty;
    
    /// <summary>
    /// وصف القضية
    /// </summary>
    [StringLength(2000)]
    public string? Description { get; set; }
    
    /// <summary>
    /// نوع القضية
    /// </summary>
    public CaseType Type { get; set; }
    
    /// <summary>
    /// فئة القضية
    /// </summary>
    public CaseCategory Category { get; set; }
    
    /// <summary>
    /// المحكمة المختصة
    /// </summary>
    [Required]
    [StringLength(100)]
    public string Court { get; set; } = string.Empty;
    
    /// <summary>
    /// نوع المحكمة
    /// </summary>
    public CourtType CourtType { get; set; }
    
    /// <summary>
    /// الخصم
    /// </summary>
    [StringLength(200)]
    public string? Opponent { get; set; }
    
    /// <summary>
    /// محامي الخصم
    /// </summary>
    [StringLength(100)]
    public string? OpponentLawyer { get; set; }
    
    /// <summary>
    /// موضوع القضية
    /// </summary>
    [StringLength(500)]
    public string? Subject { get; set; }
    
    /// <summary>
    /// تاريخ بداية القضية
    /// </summary>
    public DateTime StartDate { get; set; } = DateTime.Now;
    
    /// <summary>
    /// تاريخ انتهاء القضية
    /// </summary>
    public DateTime? EndDate { get; set; }
    
    /// <summary>
    /// حالة القضية
    /// </summary>
    public CaseStatus Status { get; set; } = CaseStatus.Active;
    
    /// <summary>
    /// أولوية القضية
    /// </summary>
    public CasePriority Priority { get; set; } = CasePriority.Medium;
    
    /// <summary>
    /// القيمة المالية للقضية
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? FinancialValue { get; set; }
    
    /// <summary>
    /// أتعاب المحاماة
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? LawyerFees { get; set; }
    
    /// <summary>
    /// المصاريف الإضافية
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? AdditionalCosts { get; set; }
    
    /// <summary>
    /// معرف الموكل
    /// </summary>
    [Required]
    public int ClientId { get; set; }
    
    /// <summary>
    /// معرف المحامي المكلف
    /// </summary>
    public int? AssignedLawyerId { get; set; }
    
    /// <summary>
    /// النتيجة النهائية
    /// </summary>
    [StringLength(1000)]
    public string? FinalResult { get; set; }
    
    /// <summary>
    /// تاريخ آخر جلسة
    /// </summary>
    public DateTime? LastSessionDate { get; set; }
    
    /// <summary>
    /// تاريخ الجلسة القادمة
    /// </summary>
    public DateTime? NextSessionDate { get; set; }
    
    /// <summary>
    /// مرحلة القضية
    /// </summary>
    [StringLength(100)]
    public string? Stage { get; set; }
    
    /// <summary>
    /// درجة التقاضي
    /// </summary>
    public LitigationLevel LitigationLevel { get; set; } = LitigationLevel.FirstInstance;
    
    // Navigation Properties
    [ForeignKey("ClientId")]
    public virtual Client Client { get; set; } = null!;
    
    [ForeignKey("AssignedLawyerId")]
    public virtual User? AssignedLawyer { get; set; }
    
    public virtual ICollection<Session> Sessions { get; set; } = new List<Session>();
    public virtual ICollection<CaseDocument> Documents { get; set; } = new List<CaseDocument>();
    // public virtual ICollection<CaseExpense> Expenses { get; set; } = new List<CaseExpense>();
    public virtual ICollection<CaseNote> CaseNotes { get; set; } = new List<CaseNote>();
}

/// <summary>
/// نوع القضية
/// </summary>
public enum CaseType
{
    /// <summary>
    /// مدنية
    /// </summary>
    Civil = 1,
    
    /// <summary>
    /// جنائية
    /// </summary>
    Criminal = 2,
    
    /// <summary>
    /// تجارية
    /// </summary>
    Commercial = 3,
    
    /// <summary>
    /// إدارية
    /// </summary>
    Administrative = 4,
    
    /// <summary>
    /// عمالية
    /// </summary>
    Labor = 5,
    
    /// <summary>
    /// أسرة
    /// </summary>
    Family = 6,
    
    /// <summary>
    /// عقارية
    /// </summary>
    RealEstate = 7,
    
    /// <summary>
    /// ضريبية
    /// </summary>
    Tax = 8
}

/// <summary>
/// فئة القضية
/// </summary>
public enum CaseCategory
{
    /// <summary>
    /// دعوى
    /// </summary>
    Lawsuit = 1,
    
    /// <summary>
    /// استشارة
    /// </summary>
    Consultation = 2,
    
    /// <summary>
    /// عقد
    /// </summary>
    Contract = 3,
    
    /// <summary>
    /// تحكيم
    /// </summary>
    Arbitration = 4,
    
    /// <summary>
    /// وساطة
    /// </summary>
    Mediation = 5
}

/// <summary>
/// نوع المحكمة
/// </summary>
public enum CourtType
{
    /// <summary>
    /// ابتدائية
    /// </summary>
    FirstInstance = 1,
    
    /// <summary>
    /// استئناف
    /// </summary>
    Appeal = 2,
    
    /// <summary>
    /// نقض
    /// </summary>
    Cassation = 3,
    
    /// <summary>
    /// تجارية
    /// </summary>
    Commercial = 4,
    
    /// <summary>
    /// إدارية
    /// </summary>
    Administrative = 5
}

/// <summary>
/// حالة القضية
/// </summary>
public enum CaseStatus
{
    /// <summary>
    /// نشطة
    /// </summary>
    Active = 1,
    
    /// <summary>
    /// مؤجلة
    /// </summary>
    Postponed = 2,
    
    /// <summary>
    /// مغلقة
    /// </summary>
    Closed = 3,
    
    /// <summary>
    /// مؤرشفة
    /// </summary>
    Archived = 4,
    
    /// <summary>
    /// ملغاة
    /// </summary>
    Cancelled = 5
}

/// <summary>
/// أولوية القضية
/// </summary>
public enum CasePriority
{
    /// <summary>
    /// منخفضة
    /// </summary>
    Low = 1,
    
    /// <summary>
    /// متوسطة
    /// </summary>
    Medium = 2,
    
    /// <summary>
    /// عالية
    /// </summary>
    High = 3,
    
    /// <summary>
    /// عاجلة
    /// </summary>
    Urgent = 4
}

/// <summary>
/// درجة التقاضي
/// </summary>
public enum LitigationLevel
{
    /// <summary>
    /// ابتدائية
    /// </summary>
    FirstInstance = 1,
    
    /// <summary>
    /// استئناف
    /// </summary>
    Appeal = 2,
    
    /// <summary>
    /// نقض
    /// </summary>
    Cassation = 3
}
