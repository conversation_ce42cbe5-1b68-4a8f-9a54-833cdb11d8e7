using System;

namespace AvocatPro.Models;

/// <summary>
/// نموذج عرض الملفات في الجدول
/// </summary>
public class CaseDisplayModel
{
    public int Id { get; set; }
    public string OfficeReference { get; set; } = "";
    public string Title { get; set; } = "";
    public CaseType Type { get; set; }
    public CaseStatus Status { get; set; }
    public CasePriority Priority { get; set; }
    public string Court { get; set; } = "";
    public string ClientName { get; set; } = "";
    public string LawyerName { get; set; } = "";
    public string? Opponent { get; set; }
    public string? Subject { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime? NextSessionDate { get; set; }
    public decimal? FinancialValue { get; set; }
    public decimal? LawyerFees { get; set; }
    public string? Stage { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    // خصائص العرض
    public string TypeDisplay => Type switch
    {
        CaseType.Civil => "مدنية",
        CaseType.Criminal => "جنائية",
        CaseType.Commercial => "تجارية",
        CaseType.Administrative => "إدارية",
        CaseType.Labor => "عمالية",
        CaseType.Family => "أسرة",
        CaseType.RealEstate => "عقارية",
        CaseType.Tax => "ضريبية",
        _ => "غير محدد"
    };

    public string StatusDisplay => Status switch
    {
        CaseStatus.Active => "نشطة",
        CaseStatus.Postponed => "مؤجلة",
        CaseStatus.Closed => "مغلقة",
        CaseStatus.Archived => "مؤرشفة",
        CaseStatus.Cancelled => "ملغاة",
        _ => "غير محدد"
    };

    public string PriorityDisplay => Priority switch
    {
        CasePriority.Low => "منخفضة",
        CasePriority.Medium => "متوسطة",
        CasePriority.High => "عالية",
        CasePriority.Urgent => "عاجلة",
        _ => "غير محدد"
    };

    public string StartDateDisplay => StartDate.ToString("dd/MM/yyyy");
    public string NextSessionDateDisplay => NextSessionDate?.ToString("dd/MM/yyyy") ?? "غير محدد";
    public string FinancialValueDisplay => FinancialValue?.ToString("N0") + " ريال" ?? "غير محدد";
    public string LawyerFeesDisplay => LawyerFees?.ToString("N0") + " ريال" ?? "غير محدد";
    public string CreatedAtDisplay => CreatedAt.ToString("dd/MM/yyyy");
    public string StageDisplay => Stage ?? "غير محدد";
    public string OpponentDisplay => Opponent ?? "غير محدد";
    public string SubjectDisplay => Subject ?? "غير محدد";
}
