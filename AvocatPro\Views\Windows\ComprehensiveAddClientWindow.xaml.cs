using System;
using System.Windows;
using System.Windows.Controls;
using AvocatPro.Views.Pages;

namespace AvocatPro.Views.Windows
{
    /// <summary>
    /// نافذة إضافة موكل جديد شاملة مع جميع البيانات المطلوبة
    /// </summary>
    public partial class ComprehensiveAddClientWindow : Window
    {
        #region Properties

        /// <summary>
        /// الموكل الجديد المُنشأ
        /// </summary>
        public ComprehensiveClientModel NewClient { get; private set; }

        /// <summary>
        /// هل تم الحفظ بنجاح
        /// </summary>
        public bool IsSaved { get; private set; } = false;

        /// <summary>
        /// الموكل المراد تعديله (في حالة التعديل)
        /// </summary>
        private ComprehensiveClientModel _existingClient;

        /// <summary>
        /// هل النافذة في وضع التعديل
        /// </summary>
        public bool IsEditMode => _existingClient != null;

        #endregion

        #region Constructor

        /// <summary>
        /// منشئ لإضافة موكل جديد
        /// </summary>
        public ComprehensiveAddClientWindow()
        {
            InitializeComponent();
            InitializeNewClient();
            SetupWindow();
        }

        /// <summary>
        /// منشئ لتعديل موكل موجود
        /// </summary>
        /// <param name="existingClient">الموكل المراد تعديله</param>
        public ComprehensiveAddClientWindow(ComprehensiveClientModel existingClient)
        {
            InitializeComponent();
            _existingClient = existingClient;
            LoadExistingClientData();
            SetupWindow();
        }

        #endregion

        #region Initialization

        /// <summary>
        /// تهيئة موكل جديد
        /// </summary>
        private void InitializeNewClient()
        {
            NewClient = new ComprehensiveClientModel
            {
                CreatedDate = DateTime.Now,
                LastContactDate = DateTime.Now,
                Status = "نشط",
                ClientType = "فرد",
                CasesCount = 0,
                TotalFees = 0
            };

            // توليد مرجع المكتب تلقائياً
            GenerateOfficeReference();
        }

        /// <summary>
        /// تحميل بيانات الموكل الموجود للتعديل
        /// </summary>
        private void LoadExistingClientData()
        {
            if (_existingClient == null) return;

            NewClient = new ComprehensiveClientModel
            {
                Id = _existingClient.Id,
                Name = _existingClient.Name,
                ClientType = _existingClient.ClientType,
                OfficeRef = _existingClient.OfficeRef,
                Phone = _existingClient.Phone,
                Email = _existingClient.Email,
                Address = _existingClient.Address,
                Status = _existingClient.Status,
                CreatedDate = _existingClient.CreatedDate,
                LastContactDate = _existingClient.LastContactDate,
                CasesCount = _existingClient.CasesCount,
                TotalFees = _existingClient.TotalFees,
                Notes = _existingClient.Notes
            };

            // تحميل البيانات في الحقول
            ClientNameTextBox.Text = NewClient.Name;
            PhoneTextBox.Text = NewClient.Phone;
            EmailTextBox.Text = NewClient.Email;
            OfficeRefTextBox.Text = NewClient.OfficeRef;
            FullAddressTextBox.Text = NewClient.Address;
            NotesTextBox.Text = NewClient.Notes;

            // تحديد نوع الموكل
            foreach (ComboBoxItem item in ClientTypeComboBox.Items)
            {
                if (item.Content.ToString() == NewClient.ClientType)
                {
                    ClientTypeComboBox.SelectedItem = item;
                    break;
                }
            }

            // تحديد الحالة
            foreach (ComboBoxItem item in StatusComboBox.Items)
            {
                if (item.Content.ToString() == NewClient.Status)
                {
                    StatusComboBox.SelectedItem = item;
                    break;
                }
            }
        }

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupWindow()
        {
            if (IsEditMode)
            {
                Title = $"تعديل بيانات الموكل: {_existingClient?.Name}";
            }
            else
            {
                Title = "إضافة موكل جديد - شامل";
            }

            // تركيز على حقل الاسم
            ClientNameTextBox.Focus();
        }

        /// <summary>
        /// توليد مرجع المكتب
        /// </summary>
        private void GenerateOfficeReference()
        {
            if (!IsEditMode)
            {
                var timestamp = DateTime.Now.ToString("yyyyMMdd");
                var random = new Random().Next(100, 999);
                OfficeRefTextBox.Text = $"CLT-{timestamp}-{random}";
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void Close_Click(object sender, RoutedEventArgs e)
        {
            if (HasUnsavedChanges())
            {
                var result = MessageBox.Show("هناك تغييرات غير محفوظة. هل تريد الخروج بدون حفظ؟", 
                                           "تأكيد الخروج", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.No)
                    return;
            }

            DialogResult = false;
            Close();
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            Close_Click(sender, e);
        }

        /// <summary>
        /// حفظ ومتابعة (لإضافة موكل آخر)
        /// </summary>
        private void SaveAndContinue_Click(object sender, RoutedEventArgs e)
        {
            if (SaveClient())
            {
                MessageBox.Show("تم حفظ بيانات الموكل بنجاح!\n\nيمكنك الآن إضافة موكل آخر.", 
                               "نجح الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);
                
                // إعادة تعيين النموذج لموكل جديد
                ClearForm();
                InitializeNewClient();
                ClientNameTextBox.Focus();
            }
        }

        /// <summary>
        /// حفظ وإنهاء
        /// </summary>
        private void SaveAndFinish_Click(object sender, RoutedEventArgs e)
        {
            if (SaveClient())
            {
                IsSaved = true;
                DialogResult = true;
                Close();
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// حفظ بيانات الموكل
        /// </summary>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        private bool SaveClient()
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateData())
                    return false;

                // تحديث بيانات الموكل
                UpdateClientFromForm();

                // محاكاة حفظ في قاعدة البيانات
                // هنا سيتم إضافة كود الحفظ الفعلي لاحقاً
                
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات:\n{ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات المدخلة
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        private bool ValidateData()
        {
            // التحقق من الاسم
            if (string.IsNullOrWhiteSpace(ClientNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الموكل", "بيانات ناقصة", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                ClientNameTextBox.Focus();
                return false;
            }

            // التحقق من رقم الهاتف
            if (string.IsNullOrWhiteSpace(PhoneTextBox.Text) || PhoneTextBox.Text.Length < 10)
            {
                MessageBox.Show("يرجى إدخال رقم هاتف صحيح (10 أرقام على الأقل)", "بيانات ناقصة", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                PhoneTextBox.Focus();
                return false;
            }

            // التحقق من البريد الإلكتروني (إذا تم إدخاله)
            if (!string.IsNullOrWhiteSpace(EmailTextBox.Text) && !IsValidEmail(EmailTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال بريد إلكتروني صحيح", "بيانات غير صحيحة", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                EmailTextBox.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// تحديث بيانات الموكل من النموذج
        /// </summary>
        private void UpdateClientFromForm()
        {
            NewClient.Name = ClientNameTextBox.Text.Trim();
            NewClient.ClientType = (ClientTypeComboBox.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "فرد";
            NewClient.Phone = PhoneTextBox.Text.Trim();
            NewClient.Email = EmailTextBox.Text.Trim();
            NewClient.OfficeRef = OfficeRefTextBox.Text.Trim();
            NewClient.Status = (StatusComboBox.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "نشط";
            NewClient.Notes = NotesTextBox.Text.Trim();

            // تجميع العنوان
            var addressParts = new[]
            {
                StreetTextBox.Text.Trim(),
                DistrictTextBox.Text.Trim(),
                CityComboBox.Text.Trim(),
                BuildingNumberTextBox.Text.Trim()
            };

            var address = string.Join(", ", Array.FindAll(addressParts, s => !string.IsNullOrEmpty(s)));
            
            if (!string.IsNullOrWhiteSpace(FullAddressTextBox.Text))
            {
                NewClient.Address = FullAddressTextBox.Text.Trim();
            }
            else if (!string.IsNullOrEmpty(address))
            {
                NewClient.Address = address;
            }

            // تحديث الحرف الأول
            NewClient.InitialLetter = !string.IsNullOrEmpty(NewClient.Name) ? 
                                     NewClient.Name.Substring(0, 1) : "؟";

            // تحديث تاريخ آخر تعديل
            NewClient.LastContactDate = DateTime.Now;
        }

        /// <summary>
        /// مسح النموذج
        /// </summary>
        private void ClearForm()
        {
            ClientNameTextBox.Clear();
            PhoneTextBox.Text = "+966";
            EmailTextBox.Clear();
            OfficeRefTextBox.Clear();
            FileRefTextBox.Clear();
            CityComboBox.SelectedIndex = -1;
            DistrictTextBox.Clear();
            StreetTextBox.Clear();
            BuildingNumberTextBox.Clear();
            FullAddressTextBox.Clear();
            NotesTextBox.Text = "أدخل أي ملاحظات أو معلومات إضافية حول الموكل...";
            ClientTypeComboBox.SelectedIndex = 0;
            StatusComboBox.SelectedIndex = 0;
        }

        /// <summary>
        /// التحقق من وجود تغييرات غير محفوظة
        /// </summary>
        /// <returns>true إذا كانت هناك تغييرات غير محفوظة</returns>
        private bool HasUnsavedChanges()
        {
            if (IsEditMode)
            {
                return ClientNameTextBox.Text != _existingClient.Name ||
                       PhoneTextBox.Text != _existingClient.Phone ||
                       EmailTextBox.Text != _existingClient.Email ||
                       FullAddressTextBox.Text != _existingClient.Address ||
                       NotesTextBox.Text != _existingClient.Notes;
            }
            else
            {
                return !string.IsNullOrWhiteSpace(ClientNameTextBox.Text) ||
                       PhoneTextBox.Text != "+966" ||
                       !string.IsNullOrWhiteSpace(EmailTextBox.Text) ||
                       !string.IsNullOrWhiteSpace(FullAddressTextBox.Text);
            }
        }

        /// <summary>
        /// التحقق من صحة البريد الإلكتروني
        /// </summary>
        /// <param name="email">البريد الإلكتروني</param>
        /// <returns>true إذا كان البريد صحيحاً</returns>
        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        #endregion
    }
}
