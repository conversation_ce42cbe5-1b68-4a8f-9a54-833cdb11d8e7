using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace AvocatPro.Models.UserManagement;

/// <summary>
/// ملف المستخدم الشخصي المتقدم
/// </summary>
public class UserProfile : INotifyPropertyChanged
{
    private string _firstName = string.Empty;
    private string _lastName = string.Empty;
    private string _email = string.Empty;
    private string _phone = string.Empty;
    private string _address = string.Empty;
    private string _profilePicture = string.Empty;
    private UserStatus _status = UserStatus.Active;
    private bool _isEmailVerified;
    private bool _isPhoneVerified;

    [Key]
    public int Id { get; set; }

    /// <summary>
    /// معرف المستخدم
    /// </summary>
    [Required]
    public int UserId { get; set; }

    /// <summary>
    /// الاسم الأول
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string FirstName
    {
        get => _firstName;
        set => SetProperty(ref _firstName, value);
    }

    /// <summary>
    /// الاسم الأخير
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string LastName
    {
        get => _lastName;
        set => SetProperty(ref _lastName, value);
    }

    /// <summary>
    /// الاسم الكامل
    /// </summary>
    [NotMapped]
    public string FullName => $"{FirstName} {LastName}";

    /// <summary>
    /// البريد الإلكتروني
    /// </summary>
    [Required]
    [EmailAddress]
    [MaxLength(100)]
    public string Email
    {
        get => _email;
        set => SetProperty(ref _email, value);
    }

    /// <summary>
    /// رقم الهاتف
    /// </summary>
    [Phone]
    [MaxLength(20)]
    public string Phone
    {
        get => _phone;
        set => SetProperty(ref _phone, value);
    }

    /// <summary>
    /// العنوان
    /// </summary>
    [MaxLength(200)]
    public string Address
    {
        get => _address;
        set => SetProperty(ref _address, value);
    }

    /// <summary>
    /// تاريخ الميلاد
    /// </summary>
    public DateTime? DateOfBirth { get; set; }

    /// <summary>
    /// الجنس
    /// </summary>
    public Gender? Gender { get; set; }

    /// <summary>
    /// الجنسية
    /// </summary>
    [MaxLength(50)]
    public string Nationality { get; set; } = string.Empty;

    /// <summary>
    /// رقم الهوية
    /// </summary>
    [MaxLength(20)]
    public string IdentityNumber { get; set; } = string.Empty;

    /// <summary>
    /// صورة الملف الشخصي
    /// </summary>
    [MaxLength(500)]
    public string ProfilePicture
    {
        get => _profilePicture;
        set => SetProperty(ref _profilePicture, value);
    }

    /// <summary>
    /// حالة المستخدم
    /// </summary>
    public UserStatus Status
    {
        get => _status;
        set => SetProperty(ref _status, value);
    }

    /// <summary>
    /// هل البريد الإلكتروني مؤكد
    /// </summary>
    public bool IsEmailVerified
    {
        get => _isEmailVerified;
        set => SetProperty(ref _isEmailVerified, value);
    }

    /// <summary>
    /// هل رقم الهاتف مؤكد
    /// </summary>
    public bool IsPhoneVerified
    {
        get => _isPhoneVerified;
        set => SetProperty(ref _isPhoneVerified, value);
    }

    /// <summary>
    /// تاريخ آخر تسجيل دخول
    /// </summary>
    public DateTime? LastLoginAt { get; set; }

    /// <summary>
    /// عنوان IP لآخر تسجيل دخول
    /// </summary>
    [MaxLength(45)]
    public string LastLoginIP { get; set; } = string.Empty;

    /// <summary>
    /// عدد مرات تسجيل الدخول
    /// </summary>
    public int LoginCount { get; set; }

    /// <summary>
    /// تاريخ الإنشاء
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// تاريخ آخر تحديث
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// ملاحظات إضافية
    /// </summary>
    [MaxLength(1000)]
    public string Notes { get; set; } = string.Empty;

    // Navigation Properties
    [ForeignKey("UserId")]
    public virtual User User { get; set; } = null!;

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (Equals(field, value)) return false;
        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }
}

/// <summary>
/// حالة المستخدم
/// </summary>
public enum UserStatus
{
    /// <summary>
    /// نشط
    /// </summary>
    Active = 1,

    /// <summary>
    /// غير نشط
    /// </summary>
    Inactive = 2,

    /// <summary>
    /// معلق
    /// </summary>
    Suspended = 3,

    /// <summary>
    /// محظور
    /// </summary>
    Banned = 4,

    /// <summary>
    /// في انتظار التفعيل
    /// </summary>
    PendingActivation = 5,

    /// <summary>
    /// منتهي الصلاحية
    /// </summary>
    Expired = 6
}

/// <summary>
/// الجنس
/// </summary>
public enum Gender
{
    /// <summary>
    /// ذكر
    /// </summary>
    Male = 1,

    /// <summary>
    /// أنثى
    /// </summary>
    Female = 2
}

/// <summary>
/// إعدادات كلمة المرور
/// </summary>
public class PasswordSettings
{
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// معرف المستخدم
    /// </summary>
    [Required]
    public int UserId { get; set; }

    /// <summary>
    /// هل يجب تغيير كلمة المرور في التسجيل التالي
    /// </summary>
    public bool MustChangePassword { get; set; }

    /// <summary>
    /// تاريخ آخر تغيير لكلمة المرور
    /// </summary>
    public DateTime? LastPasswordChange { get; set; }

    /// <summary>
    /// عدد أيام انتهاء صلاحية كلمة المرور
    /// </summary>
    public int? PasswordExpiryDays { get; set; }

    /// <summary>
    /// عدد محاولات تسجيل الدخول الفاشلة
    /// </summary>
    public int FailedLoginAttempts { get; set; }

    /// <summary>
    /// تاريخ قفل الحساب
    /// </summary>
    public DateTime? LockedUntil { get; set; }

    /// <summary>
    /// هل الحساب مقفل
    /// </summary>
    [NotMapped]
    public bool IsLocked => LockedUntil.HasValue && LockedUntil > DateTime.Now;

    /// <summary>
    /// تاريخ الإنشاء
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// تاريخ آخر تحديث
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    // Navigation Properties
    [ForeignKey("UserId")]
    public virtual User User { get; set; } = null!;
}

/// <summary>
/// سجل تسجيل الدخول
/// </summary>
public class LoginLog
{
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// معرف المستخدم
    /// </summary>
    [Required]
    public int UserId { get; set; }

    /// <summary>
    /// تاريخ ووقت تسجيل الدخول
    /// </summary>
    public DateTime LoginTime { get; set; } = DateTime.Now;

    /// <summary>
    /// تاريخ ووقت تسجيل الخروج
    /// </summary>
    public DateTime? LogoutTime { get; set; }

    /// <summary>
    /// عنوان IP
    /// </summary>
    [MaxLength(45)]
    public string IPAddress { get; set; } = string.Empty;

    /// <summary>
    /// معلومات المتصفح
    /// </summary>
    [MaxLength(500)]
    public string UserAgent { get; set; } = string.Empty;

    /// <summary>
    /// هل تسجيل الدخول ناجح
    /// </summary>
    public bool IsSuccessful { get; set; }

    /// <summary>
    /// سبب فشل تسجيل الدخول
    /// </summary>
    [MaxLength(200)]
    public string FailureReason { get; set; } = string.Empty;

    /// <summary>
    /// مدة الجلسة بالدقائق
    /// </summary>
    [NotMapped]
    public int? SessionDurationMinutes
    {
        get
        {
            if (LogoutTime.HasValue)
                return (int)(LogoutTime.Value - LoginTime).TotalMinutes;
            return null;
        }
    }

    // Navigation Properties
    [ForeignKey("UserId")]
    public virtual User User { get; set; } = null!;
}
