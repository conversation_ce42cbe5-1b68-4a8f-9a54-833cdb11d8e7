using System.Windows;
using System.Windows.Controls;
using AvocatPro.Models;

namespace AvocatPro.Views.Pages;

public partial class DashboardPage : Page
{
    private readonly User _currentUser;

    public DashboardPage(User currentUser)
    {
        InitializeComponent();
        _currentUser = currentUser;
    }

    private void AddClientButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("سيتم فتح نافذة إضافة موكل جديد", "إضافة موكل", 
                       MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void AddCaseButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("سيتم فتح نافذة إنشاء ملف جديد", "إنشاء ملف", 
                       MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void AddAppointmentButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("سيتم فتح نافذة جدولة موعد جديد", "جدولة موعد", 
                       MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void ViewReportsButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("سيتم فتح صفحة التقارير والإحصائيات", "التقارير", 
                       MessageBoxButton.OK, MessageBoxImage.Information);
    }
}
