﻿#pragma checksum "..\..\..\..\..\Views\Windows\ExportProgressWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "B2690DDBA5659DCDC15350A7175E0D738FCA5022"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Windows {
    
    
    /// <summary>
    /// ExportProgressWindow
    /// </summary>
    public partial class ExportProgressWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 40 "..\..\..\..\..\Views\Windows\ExportProgressWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MainStatusText;
        
        #line default
        #line hidden
        
        
        #line 43 "..\..\..\..\..\Views\Windows\ExportProgressWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar MainProgressBar;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\..\..\Views\Windows\ExportProgressWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MainProgressText;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\..\Views\Windows\ExportProgressWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PrepareIcon;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\..\Views\Windows\ExportProgressWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PrepareStatus;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\..\Views\Windows\ExportProgressWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ContentIcon;
        
        #line default
        #line hidden
        
        
        #line 79 "..\..\..\..\..\Views\Windows\ExportProgressWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ContentStatus;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\..\Views\Windows\ExportProgressWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ChartsIcon;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\..\..\Views\Windows\ExportProgressWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ChartsStatus;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\..\Views\Windows\ExportProgressWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FormatIcon;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\..\Views\Windows\ExportProgressWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FormatStatus;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\..\Views\Windows\ExportProgressWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SaveIcon;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\..\Views\Windows\ExportProgressWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SaveStatus;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\..\Views\Windows\ExportProgressWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TimeRemainingText;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\..\Views\Windows\ExportProgressWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\..\Views\Windows\ExportProgressWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MinimizeButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/windows/exportprogresswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\ExportProgressWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.MainStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.MainProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 3:
            this.MainProgressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.PrepareIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.PrepareStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.ContentIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.ContentStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.ChartsIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.ChartsStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.FormatIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.FormatStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.SaveIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.SaveStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.TimeRemainingText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 115 "..\..\..\..\..\Views\Windows\ExportProgressWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.MinimizeButton = ((System.Windows.Controls.Button)(target));
            
            #line 133 "..\..\..\..\..\Views\Windows\ExportProgressWindow.xaml"
            this.MinimizeButton.Click += new System.Windows.RoutedEventHandler(this.MinimizeButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

