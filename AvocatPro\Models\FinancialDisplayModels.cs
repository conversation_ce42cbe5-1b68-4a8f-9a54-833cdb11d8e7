using System;

namespace AvocatPro.Models;

/// <summary>
/// نموذج عرض المصاريف
/// </summary>
public class ExpenseDisplayModel
{
    public int Id { get; set; }
    public string Reference { get; set; } = "";
    public string Description { get; set; } = "";
    public decimal Amount { get; set; }
    public DateTime ExpenseDate { get; set; }
    public ExpenseCategory Category { get; set; }
    public ExpenseType Type { get; set; }
    public int? CaseId { get; set; }
    public string? CaseTitle { get; set; }
    public int? ClientId { get; set; }
    public string? ClientName { get; set; }
    public string? Vendor { get; set; }
    public string? InvoiceNumber { get; set; }
    public PaymentMethod PaymentMethod { get; set; }
    public PaymentStatus PaymentStatus { get; set; }
    public DateTime? PaymentDate { get; set; }
    public bool IsReimbursable { get; set; }
    public bool IsReimbursed { get; set; }
    public DateTime? ReimbursementDate { get; set; }
    public string? Attachments { get; set; }
    public DateTime CreatedAt { get; set; }

    // خصائص العرض
    public string CategoryDisplay => Category switch
    {
        ExpenseCategory.Administrative => "مصاريف إدارية",
        ExpenseCategory.Legal => "مصاريف قانونية",
        ExpenseCategory.Operational => "مصاريف تشغيلية",
        ExpenseCategory.Technology => "مصاريف تقنية",
        ExpenseCategory.Marketing => "مصاريف تسويق",
        ExpenseCategory.Travel => "مصاريف سفر",
        _ => "غير محدد"
    };

    public string TypeDisplay => Type switch
    {
        ExpenseType.CourtFees => "رسوم محكمة",
        ExpenseType.ExpertFees => "رسوم خبرة",
        ExpenseType.Transportation => "مصاريف انتقال",
        ExpenseType.Communication => "مصاريف اتصالات",
        ExpenseType.Printing => "مصاريف طباعة",
        ExpenseType.Translation => "مصاريف ترجمة",
        ExpenseType.Other => "أخرى",
        _ => "غير محدد"
    };

    public string PaymentMethodDisplay => PaymentMethod switch
    {
        PaymentMethod.Cash => "نقدي",
        PaymentMethod.Check => "شيك",
        PaymentMethod.BankTransfer => "تحويل بنكي",
        PaymentMethod.CreditCard => "بطاقة ائتمان",
        PaymentMethod.EWallet => "محفظة إلكترونية",
        _ => "غير محدد"
    };

    public string PaymentStatusDisplay => PaymentStatus switch
    {
        PaymentStatus.Pending => "في الانتظار",
        PaymentStatus.Paid => "مدفوع",
        PaymentStatus.PartiallyPaid => "مدفوع جزئياً",
        PaymentStatus.Overdue => "متأخر",
        PaymentStatus.Cancelled => "ملغي",
        _ => "غير محدد"
    };

    public string AmountDisplay => Amount.ToString("N2") + " ريال";
    public string ExpenseDateDisplay => ExpenseDate.ToString("dd/MM/yyyy");
    public string PaymentDateDisplay => PaymentDate?.ToString("dd/MM/yyyy") ?? "غير محدد";
    public string ReimbursementDateDisplay => ReimbursementDate?.ToString("dd/MM/yyyy") ?? "غير محدد";
    public string CreatedAtDisplay => CreatedAt.ToString("dd/MM/yyyy");
    
    public string CaseDisplay => CaseTitle ?? "غير مرتبط بقضية";
    public string ClientDisplay => ClientName ?? "غير محدد";
    public string VendorDisplay => Vendor ?? "غير محدد";
    public string InvoiceNumberDisplay => InvoiceNumber ?? "غير محدد";
    
    public string ReimbursableStatusDisplay => IsReimbursable ? "قابل للاسترداد" : "غير قابل للاسترداد";
    public string ReimbursedStatusDisplay => IsReimbursed ? "تم الاسترداد" : "لم يتم الاسترداد";

    // خصائص الألوان
    public string PaymentStatusColor => PaymentStatus switch
    {
        PaymentStatus.Pending => "#FF9800",
        PaymentStatus.Paid => "#4CAF50",
        PaymentStatus.PartiallyPaid => "#2196F3",
        PaymentStatus.Overdue => "#F44336",
        PaymentStatus.Cancelled => "#9E9E9E",
        _ => "#607D8B"
    };

    public string CategoryColor => Category switch
    {
        ExpenseCategory.Administrative => "#2196F3",
        ExpenseCategory.Legal => "#9C27B0",
        ExpenseCategory.Operational => "#FF9800",
        ExpenseCategory.Technology => "#4CAF50",
        ExpenseCategory.Marketing => "#E91E63",
        ExpenseCategory.Travel => "#00BCD4",
        _ => "#607D8B"
    };

    // أيقونات
    public string CategoryIcon => Category switch
    {
        ExpenseCategory.Administrative => "🏢",
        ExpenseCategory.Legal => "⚖️",
        ExpenseCategory.Operational => "⚙️",
        ExpenseCategory.Technology => "💻",
        ExpenseCategory.Marketing => "📢",
        ExpenseCategory.Travel => "✈️",
        _ => "💰"
    };

    public string PaymentStatusIcon => PaymentStatus switch
    {
        PaymentStatus.Pending => "⏳",
        PaymentStatus.Paid => "✅",
        PaymentStatus.PartiallyPaid => "🔵",
        PaymentStatus.Overdue => "❌",
        PaymentStatus.Cancelled => "🚫",
        _ => "❓"
    };

    // خصائص للإحصائيات
    public string MonthYear => ExpenseDate.ToString("yyyy-MM");
    public string Year => ExpenseDate.ToString("yyyy");
    public string Month => ExpenseDate.ToString("MM");
    public int WeekOfYear => System.Globalization.CultureInfo.CurrentCulture.Calendar.GetWeekOfYear(
        ExpenseDate, System.Globalization.CalendarWeekRule.FirstDay, System.DayOfWeek.Saturday);

    // خصائص للبحث
    public string SearchableText => $"{Reference} {Description} {Vendor} {InvoiceNumber} {CaseTitle} {ClientName}".ToLower();

    // حالة التأخير
    public bool IsOverdue => PaymentStatus == PaymentStatus.Overdue;
    public bool IsPending => PaymentStatus == PaymentStatus.Pending;
    public bool IsPaid => PaymentStatus == PaymentStatus.Paid;

    // هل لديه مرفقات
    public bool HasAttachments => !string.IsNullOrEmpty(Attachments);
    public string AttachmentsDisplay => HasAttachments ? "يوجد مرفقات" : "لا توجد مرفقات";
}

/// <summary>
/// نموذج عرض الإيرادات
/// </summary>
public class RevenueDisplayModel
{
    public int Id { get; set; }
    public string Reference { get; set; } = "";
    public string Description { get; set; } = "";
    public decimal Amount { get; set; }
    public DateTime RevenueDate { get; set; }
    public RevenueType Type { get; set; }
    public int? CaseId { get; set; }
    public string? CaseTitle { get; set; }
    public int ClientId { get; set; }
    public string ClientName { get; set; } = "";
    public string? InvoiceNumber { get; set; }
    public PaymentMethod PaymentMethod { get; set; }
    public PaymentStatus PaymentStatus { get; set; }
    public DateTime? PaymentDate { get; set; }
    public DateTime? DueDate { get; set; }
    public string? Attachments { get; set; }
    public DateTime CreatedAt { get; set; }

    // خصائص العرض
    public string TypeDisplay => Type switch
    {
        RevenueType.LawyerFees => "أتعاب محاماة",
        RevenueType.ConsultationFees => "أتعاب استشارة",
        RevenueType.ContractFees => "أتعاب عقود",
        RevenueType.ArbitrationFees => "أتعاب تحكيم",
        RevenueType.Other => "أخرى",
        _ => "غير محدد"
    };

    public string PaymentMethodDisplay => PaymentMethod switch
    {
        PaymentMethod.Cash => "نقدي",
        PaymentMethod.Check => "شيك",
        PaymentMethod.BankTransfer => "تحويل بنكي",
        PaymentMethod.CreditCard => "بطاقة ائتمان",
        PaymentMethod.EWallet => "محفظة إلكترونية",
        _ => "غير محدد"
    };

    public string PaymentStatusDisplay => PaymentStatus switch
    {
        PaymentStatus.Pending => "في الانتظار",
        PaymentStatus.Paid => "مدفوع",
        PaymentStatus.PartiallyPaid => "مدفوع جزئياً",
        PaymentStatus.Overdue => "متأخر",
        PaymentStatus.Cancelled => "ملغي",
        _ => "غير محدد"
    };

    public string AmountDisplay => Amount.ToString("N2") + " ريال";
    public string RevenueDateDisplay => RevenueDate.ToString("dd/MM/yyyy");
    public string PaymentDateDisplay => PaymentDate?.ToString("dd/MM/yyyy") ?? "غير محدد";
    public string DueDateDisplay => DueDate?.ToString("dd/MM/yyyy") ?? "غير محدد";
    public string CreatedAtDisplay => CreatedAt.ToString("dd/MM/yyyy");
    
    public string CaseDisplay => CaseTitle ?? "غير مرتبط بقضية";
    public string InvoiceNumberDisplay => InvoiceNumber ?? "غير محدد";

    // خصائص الألوان
    public string PaymentStatusColor => PaymentStatus switch
    {
        PaymentStatus.Pending => "#FF9800",
        PaymentStatus.Paid => "#4CAF50",
        PaymentStatus.PartiallyPaid => "#2196F3",
        PaymentStatus.Overdue => "#F44336",
        PaymentStatus.Cancelled => "#9E9E9E",
        _ => "#607D8B"
    };

    public string TypeColor => Type switch
    {
        RevenueType.LawyerFees => "#2196F3",
        RevenueType.ConsultationFees => "#4CAF50",
        RevenueType.ContractFees => "#FF9800",
        RevenueType.ArbitrationFees => "#9C27B0",
        RevenueType.Other => "#607D8B",
        _ => "#607D8B"
    };

    // أيقونات
    public string TypeIcon => Type switch
    {
        RevenueType.LawyerFees => "⚖️",
        RevenueType.ConsultationFees => "💼",
        RevenueType.ContractFees => "📝",
        RevenueType.ArbitrationFees => "🤝",
        RevenueType.Other => "💰",
        _ => "💰"
    };

    public string PaymentStatusIcon => PaymentStatus switch
    {
        PaymentStatus.Pending => "⏳",
        PaymentStatus.Paid => "✅",
        PaymentStatus.PartiallyPaid => "🔵",
        PaymentStatus.Overdue => "❌",
        PaymentStatus.Cancelled => "🚫",
        _ => "❓"
    };

    // خصائص للإحصائيات
    public string MonthYear => RevenueDate.ToString("yyyy-MM");
    public string Year => RevenueDate.ToString("yyyy");
    public string Month => RevenueDate.ToString("MM");
    public int WeekOfYear => System.Globalization.CultureInfo.CurrentCulture.Calendar.GetWeekOfYear(
        RevenueDate, System.Globalization.CalendarWeekRule.FirstDay, System.DayOfWeek.Saturday);

    // خصائص للبحث
    public string SearchableText => $"{Reference} {Description} {InvoiceNumber} {CaseTitle} {ClientName}".ToLower();

    // حالة التأخير
    public bool IsOverdue => PaymentStatus == PaymentStatus.Overdue || (DueDate.HasValue && DueDate < DateTime.Today && PaymentStatus != PaymentStatus.Paid);
    public bool IsPending => PaymentStatus == PaymentStatus.Pending;
    public bool IsPaid => PaymentStatus == PaymentStatus.Paid;

    // هل لديه مرفقات
    public bool HasAttachments => !string.IsNullOrEmpty(Attachments);
    public string AttachmentsDisplay => HasAttachments ? "يوجد مرفقات" : "لا توجد مرفقات";

    // أيام التأخير
    public int DaysOverdue => IsOverdue && DueDate.HasValue ? (DateTime.Today - DueDate.Value).Days : 0;
    public string OverdueDisplay => IsOverdue ? $"متأخر {DaysOverdue} يوم" : "";
}

/// <summary>
/// نموذج الملخص المالي
/// </summary>
public class FinancialSummaryModel
{
    public decimal TotalRevenues { get; set; }
    public decimal TotalExpenses { get; set; }
    public decimal NetProfit { get; set; }
    public decimal PendingRevenues { get; set; }
    public decimal PendingExpenses { get; set; }
    public decimal OverdueRevenues { get; set; }
    public decimal ReimbursableExpenses { get; set; }
    public int TotalTransactions { get; set; }
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }

    // خصائص العرض
    public string TotalRevenuesDisplay => TotalRevenues.ToString("N2") + " ريال";
    public string TotalExpensesDisplay => TotalExpenses.ToString("N2") + " ريال";
    public string NetProfitDisplay => NetProfit.ToString("N2") + " ريال";
    public string PendingRevenuesDisplay => PendingRevenues.ToString("N2") + " ريال";
    public string PendingExpensesDisplay => PendingExpenses.ToString("N2") + " ريال";
    public string OverdueRevenuesDisplay => OverdueRevenues.ToString("N2") + " ريال";
    public string ReimbursableExpensesDisplay => ReimbursableExpenses.ToString("N2") + " ريال";
    
    public string PeriodDisplay => $"{PeriodStart:dd/MM/yyyy} - {PeriodEnd:dd/MM/yyyy}";
    
    public bool IsProfitable => NetProfit > 0;
    public string ProfitabilityStatus => IsProfitable ? "مربح" : "خسارة";
    public string ProfitabilityColor => IsProfitable ? "#4CAF50" : "#F44336";
    public string ProfitabilityIcon => IsProfitable ? "📈" : "📉";
}
