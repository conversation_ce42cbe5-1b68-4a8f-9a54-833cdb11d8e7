<Window x:Class="AvocatPro.Views.Windows.UserStatisticsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إحصائيات وأداء المستخدم"
        Width="900" Height="700"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
            <TextBlock Text="📊" FontSize="24" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <StackPanel>
                <TextBlock Text="إحصائيات وأداء المستخدم" FontSize="20" FontWeight="Bold"/>
                <TextBlock Name="UserNameText" Text="اسم المستخدم" FontSize="14" Foreground="Gray"/>
            </StackPanel>
        </StackPanel>

        <!-- المحتوى -->
        <TabControl Grid.Row="1">
            <TabItem Header="الإحصائيات العامة">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <Grid Margin="20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- إحصائيات سريعة -->
                        <Grid Grid.Row="0" Margin="0,0,0,30">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Border Grid.Column="0" Background="#E8F5E8" CornerRadius="8" Padding="20" Margin="0,0,10,0">
                                <StackPanel>
                                    <TextBlock Text="🔐" FontSize="24" HorizontalAlignment="Center"/>
                                    <TextBlock Name="LoginCountText" Text="0" FontSize="28" FontWeight="Bold" 
                                               HorizontalAlignment="Center" Foreground="#2E7D32"/>
                                    <TextBlock Text="مرات تسجيل الدخول" FontSize="12" HorizontalAlignment="Center" 
                                               Foreground="#4CAF50"/>
                                </StackPanel>
                            </Border>

                            <Border Grid.Column="1" Background="#E3F2FD" CornerRadius="8" Padding="20" Margin="10,0">
                                <StackPanel>
                                    <TextBlock Text="⏱️" FontSize="24" HorizontalAlignment="Center"/>
                                    <TextBlock Name="SessionTimeText" Text="0" FontSize="28" FontWeight="Bold" 
                                               HorizontalAlignment="Center" Foreground="#1976D2"/>
                                    <TextBlock Text="ساعات العمل" FontSize="12" HorizontalAlignment="Center" 
                                               Foreground="#2196F3"/>
                                </StackPanel>
                            </Border>

                            <Border Grid.Column="2" Background="#FFF3E0" CornerRadius="8" Padding="20" Margin="10,0">
                                <StackPanel>
                                    <TextBlock Text="📋" FontSize="24" HorizontalAlignment="Center"/>
                                    <TextBlock Name="ActionsCountText" Text="0" FontSize="28" FontWeight="Bold" 
                                               HorizontalAlignment="Center" Foreground="#F57C00"/>
                                    <TextBlock Text="العمليات المنجزة" FontSize="12" HorizontalAlignment="Center" 
                                               Foreground="#FF9800"/>
                                </StackPanel>
                            </Border>

                            <Border Grid.Column="3" Background="#FFEBEE" CornerRadius="8" Padding="20" Margin="10,0,0,0">
                                <StackPanel>
                                    <TextBlock Text="⚠️" FontSize="24" HorizontalAlignment="Center"/>
                                    <TextBlock Name="ErrorCountText" Text="0" FontSize="28" FontWeight="Bold" 
                                               HorizontalAlignment="Center" Foreground="#D32F2F"/>
                                    <TextBlock Text="الأخطاء" FontSize="12" HorizontalAlignment="Center" 
                                               Foreground="#F44336"/>
                                </StackPanel>
                            </Border>
                        </Grid>

                        <!-- فلاتر التاريخ -->
                        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,20">
                            <TextBlock Text="الفترة:" VerticalAlignment="Center" Margin="0,0,10,0" FontWeight="Medium"/>
                            <DatePicker Name="FromDatePicker" Margin="0,0,10,0"/>
                            <TextBlock Text="إلى" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <DatePicker Name="ToDatePicker" Margin="0,0,10,0"/>
                            <Button Name="RefreshButton" Content="🔄 تحديث" Padding="10,5" 
                                    Background="#2196F3" Foreground="White" BorderThickness="0" 
                                    Click="RefreshButton_Click"/>
                        </StackPanel>

                        <!-- الرسوم البيانية -->
                        <Grid Grid.Row="2">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Border Grid.Column="0" Background="White" CornerRadius="8" Padding="15" Margin="0,0,10,0"
                                    BorderBrush="#E0E0E0" BorderThickness="1">
                                <StackPanel>
                                    <TextBlock Text="نشاط تسجيل الدخول" FontWeight="Bold" Margin="0,0,0,10"/>
                                    <Canvas Name="LoginActivityChart" Height="200" Background="#F5F5F5"/>
                                </StackPanel>
                            </Border>

                            <Border Grid.Column="1" Background="White" CornerRadius="8" Padding="15" Margin="10,0,0,0"
                                    BorderBrush="#E0E0E0" BorderThickness="1">
                                <StackPanel>
                                    <TextBlock Text="توزيع العمليات" FontWeight="Bold" Margin="0,0,0,10"/>
                                    <Canvas Name="OperationsChart" Height="200" Background="#F5F5F5"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </Grid>
                </ScrollViewer>
            </TabItem>

            <TabItem Header="سجل الأنشطة">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- فلاتر سجل الأنشطة -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                        <TextBox Name="ActivitySearchBox" Width="200" Padding="8" 
                                 Text="🔍 البحث في الأنشطة..." Margin="0,0,10,0"/>
                        <ComboBox Name="ActivityTypeFilter" Width="150" Padding="8" Margin="0,0,10,0"/>
                        <ComboBox Name="ActivityLevelFilter" Width="120" Padding="8"/>
                    </StackPanel>

                    <!-- جدول الأنشطة -->
                    <DataGrid Name="ActivitiesDataGrid" Grid.Row="1" 
                              AutoGenerateColumns="False" 
                              IsReadOnly="True"
                              GridLinesVisibility="None"
                              HeadersVisibility="Column"
                              AlternatingRowBackground="#F8F9FA">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="التاريخ والوقت" Binding="{Binding DateTime}" Width="140"/>
                            <DataGridTextColumn Header="النوع" Binding="{Binding Type}" Width="100"/>
                            <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="250"/>
                            <DataGridTextColumn Header="الوحدة" Binding="{Binding Module}" Width="100"/>
                            <DataGridTextColumn Header="المستوى" Binding="{Binding Level}" Width="80"/>
                            <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="80"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>

            <TabItem Header="تقييم الأداء">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <Grid Margin="20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- التقييم الحالي -->
                        <Border Grid.Row="0" Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,20"
                                BorderBrush="#E0E0E0" BorderThickness="1">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="الإنتاجية" FontWeight="Bold" HorizontalAlignment="Center"/>
                                    <TextBlock Name="ProductivityRatingText" Text="0/10" FontSize="24" FontWeight="Bold" 
                                               HorizontalAlignment="Center" Foreground="#4CAF50"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="الجودة" FontWeight="Bold" HorizontalAlignment="Center"/>
                                    <TextBlock Name="QualityRatingText" Text="0/10" FontSize="24" FontWeight="Bold" 
                                               HorizontalAlignment="Center" Foreground="#2196F3"/>
                                </StackPanel>

                                <StackPanel Grid.Column="2">
                                    <TextBlock Text="الالتزام" FontWeight="Bold" HorizontalAlignment="Center"/>
                                    <TextBlock Name="PunctualityRatingText" Text="0/10" FontSize="24" FontWeight="Bold" 
                                               HorizontalAlignment="Center" Foreground="#FF9800"/>
                                </StackPanel>

                                <StackPanel Grid.Column="3">
                                    <TextBlock Text="التعاون" FontWeight="Bold" HorizontalAlignment="Center"/>
                                    <TextBlock Name="CollaborationRatingText" Text="0/10" FontSize="24" FontWeight="Bold" 
                                               HorizontalAlignment="Center" Foreground="#9C27B0"/>
                                </StackPanel>
                            </Grid>
                        </Border>

                        <!-- إضافة تقييم جديد -->
                        <Button Grid.Row="1" Name="AddRatingButton" Content="➕ إضافة تقييم جديد" 
                                Padding="15,10" Margin="0,0,0,20" Background="#4CAF50" Foreground="White" 
                                BorderThickness="0" HorizontalAlignment="Center" 
                                Click="AddRatingButton_Click"/>

                        <!-- تاريخ التقييمات -->
                        <DataGrid Name="RatingsHistoryDataGrid" Grid.Row="2" 
                                  AutoGenerateColumns="False" 
                                  IsReadOnly="True"
                                  GridLinesVisibility="None"
                                  HeadersVisibility="Column">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="الفترة" Binding="{Binding Period}" Width="100"/>
                                <DataGridTextColumn Header="الإنتاجية" Binding="{Binding Productivity}" Width="80"/>
                                <DataGridTextColumn Header="الجودة" Binding="{Binding Quality}" Width="80"/>
                                <DataGridTextColumn Header="الالتزام" Binding="{Binding Punctuality}" Width="80"/>
                                <DataGridTextColumn Header="التعاون" Binding="{Binding Collaboration}" Width="80"/>
                                <DataGridTextColumn Header="الإجمالي" Binding="{Binding Overall}" Width="80"/>
                                <DataGridTextColumn Header="المقيم" Binding="{Binding RatedBy}" Width="120"/>
                                <DataGridTextColumn Header="التاريخ" Binding="{Binding Date}" Width="100"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </ScrollViewer>
            </TabItem>
        </TabControl>
    </Grid>
</Window>
