<?php
echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>اختبار PHP</title>";
echo "<style>body{font-family:Arial;padding:20px;direction:rtl;}</style>";
echo "</head>";
echo "<body>";

echo "<h1>🎉 PHP يعمل بنجاح!</h1>";
echo "<p><strong>الإصدار:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>الوقت:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>المجلد:</strong> " . __DIR__ . "</p>";

echo "<h2>الإضافات المطلوبة:</h2>";
$extensions = ['pdo', 'mbstring', 'openssl', 'tokenizer', 'xml', 'ctype', 'json', 'curl'];
echo "<ul>";
foreach ($extensions as $ext) {
    $status = extension_loaded($ext) ? '✅ متوفر' : '❌ غير متوفر';
    echo "<li>$ext: $status</li>";
}
echo "</ul>";

echo "<h2>الملفات:</h2>";
echo "<ul>";
echo "<li>public/index.php: " . (file_exists('public/index.php') ? '✅ موجود' : '❌ غير موجود') . "</li>";
echo "<li>install.php: " . (file_exists('install.php') ? '✅ موجود' : '❌ غير موجود') . "</li>";
echo "<li>.env: " . (file_exists('.env') ? '✅ موجود' : '❌ غير موجود') . "</li>";
echo "<li>composer.json: " . (file_exists('composer.json') ? '✅ موجود' : '❌ غير موجود') . "</li>";
echo "</ul>";

echo "<h2>الروابط:</h2>";
echo "<ul>";
echo "<li><a href='public/'>الصفحة الرئيسية (public/)</a></li>";
echo "<li><a href='install.php'>صفحة التثبيت</a></li>";
echo "<li><a href='public/test.php'>اختبار في public</a></li>";
echo "</ul>";

echo "<h2>معلومات الخادم:</h2>";
echo "<ul>";
echo "<li><strong>نظام التشغيل:</strong> " . PHP_OS . "</li>";
echo "<li><strong>الخادم:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد') . "</li>";
echo "<li><strong>المنفذ:</strong> " . ($_SERVER['SERVER_PORT'] ?? 'غير محدد') . "</li>";
echo "</ul>";

if (isset($_GET['phpinfo'])) {
    echo "<hr>";
    phpinfo();
}

echo "<p><a href='?phpinfo=1'>عرض معلومات PHP كاملة</a></p>";

echo "</body>";
echo "</html>";
?>
