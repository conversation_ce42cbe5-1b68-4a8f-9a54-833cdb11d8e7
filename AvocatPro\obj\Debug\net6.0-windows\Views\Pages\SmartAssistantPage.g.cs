﻿#pragma checksum "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "008E42CF47C92EE39A6850EE25324B88EC62FFD2"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using FontAwesome.WPF;
using FontAwesome.WPF.Converters;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Pages {
    
    
    /// <summary>
    /// SmartAssistantPage
    /// </summary>
    public partial class SmartAssistantPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 49 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button VoiceInputBtn;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearChatBtn;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportChatBtn;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer ChatScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 79 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ChatPanel;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border TypingIndicator;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse Dot1;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse Dot2;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse Dot3;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox QueryTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox QueryTextBox;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SendBtn;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AttachFileBtn;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel QuickSuggestionsPanel;
        
        #line default
        #line hidden
        
        
        #line 241 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AIStatsPanel;
        
        #line default
        #line hidden
        
        
        #line 257 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView RecentAnalysisListView;
        
        #line default
        #line hidden
        
        
        #line 286 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PredictCaseBtn;
        
        #line default
        #line hidden
        
        
        #line 292 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AnalyzeDocumentBtn;
        
        #line default
        #line hidden
        
        
        #line 298 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OptimizeScheduleBtn;
        
        #line default
        #line hidden
        
        
        #line 304 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AssessRiskBtn;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/pages/smartassistantpage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.VoiceInputBtn = ((System.Windows.Controls.Button)(target));
            
            #line 49 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
            this.VoiceInputBtn.Click += new System.Windows.RoutedEventHandler(this.VoiceInput_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ClearChatBtn = ((System.Windows.Controls.Button)(target));
            
            #line 55 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
            this.ClearChatBtn.Click += new System.Windows.RoutedEventHandler(this.ClearChat_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ExportChatBtn = ((System.Windows.Controls.Button)(target));
            
            #line 61 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
            this.ExportChatBtn.Click += new System.Windows.RoutedEventHandler(this.ExportChat_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ChatScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 5:
            this.ChatPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 6:
            this.TypingIndicator = ((System.Windows.Controls.Border)(target));
            return;
            case 7:
            this.Dot1 = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 8:
            this.Dot2 = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 9:
            this.Dot3 = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 10:
            this.QueryTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 155 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
            this.QueryTypeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.QueryType_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.QueryTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 179 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
            this.QueryTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.QueryTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 179 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
            this.QueryTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.QueryTextBox_LostFocus);
            
            #line default
            #line hidden
            
            #line 180 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
            this.QueryTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.QueryTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 12:
            this.SendBtn = ((System.Windows.Controls.Button)(target));
            
            #line 185 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
            this.SendBtn.Click += new System.Windows.RoutedEventHandler(this.SendBtn_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.AttachFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 198 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
            this.AttachFileBtn.Click += new System.Windows.RoutedEventHandler(this.AttachFile_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.QuickSuggestionsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 15:
            this.AIStatsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 16:
            this.RecentAnalysisListView = ((System.Windows.Controls.ListView)(target));
            return;
            case 17:
            this.PredictCaseBtn = ((System.Windows.Controls.Button)(target));
            
            #line 286 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
            this.PredictCaseBtn.Click += new System.Windows.RoutedEventHandler(this.PredictCase_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.AnalyzeDocumentBtn = ((System.Windows.Controls.Button)(target));
            
            #line 292 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
            this.AnalyzeDocumentBtn.Click += new System.Windows.RoutedEventHandler(this.AnalyzeDocument_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.OptimizeScheduleBtn = ((System.Windows.Controls.Button)(target));
            
            #line 298 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
            this.OptimizeScheduleBtn.Click += new System.Windows.RoutedEventHandler(this.OptimizeSchedule_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.AssessRiskBtn = ((System.Windows.Controls.Button)(target));
            
            #line 304 "..\..\..\..\..\Views\Pages\SmartAssistantPage.xaml"
            this.AssessRiskBtn.Click += new System.Windows.RoutedEventHandler(this.AssessRisk_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

