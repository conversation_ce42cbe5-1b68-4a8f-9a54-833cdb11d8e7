<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whatsapp_messages', function (Blueprint $table) {
            $table->id();
            $table->string('message_id')->unique();
            $table->string('from');
            $table->string('to');
            $table->enum('direction', ['incoming', 'outgoing']);
            $table->enum('type', ['text', 'image', 'document', 'audio', 'video', 'template']);
            $table->text('content');
            $table->json('metadata')->nullable();
            $table->enum('status', [
                'sent', 'delivered', 'read', 'failed', 
                'received', 'pending'
            ])->default('pending');
            $table->timestamp('status_timestamp')->nullable();
            $table->foreignId('client_id')->nullable()->constrained('clients')->onDelete('set null');
            $table->foreignId('case_id')->nullable()->constrained('legal_cases')->onDelete('set null');
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->boolean('is_automated')->default(false);
            $table->string('template_name')->nullable();
            $table->json('template_parameters')->nullable();
            $table->text('error_message')->nullable();
            $table->integer('retry_count')->default(0);
            $table->timestamp('scheduled_at')->nullable();
            $table->timestamps();

            // Indexes
            $table->index('message_id');
            $table->index(['from', 'to']);
            $table->index('direction');
            $table->index('status');
            $table->index('client_id');
            $table->index('case_id');
            $table->index('created_at');
            $table->index('scheduled_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whatsapp_messages');
    }
};
