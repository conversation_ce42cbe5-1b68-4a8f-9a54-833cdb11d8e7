<Window x:Class="AvocatPro.Views.Windows.PaymentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تسديد المدفوعات" 
        Height="500" Width="700"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">

    <Window.Resources>
        <!-- أنماط النموذج -->
        <Style x:Key="FormLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#333"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>

        <Style x:Key="FormTextBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Style.Triggers>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="BorderBrush" Value="#4CAF50"/>
                    <Setter Property="BorderThickness" Value="2"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="FormComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="FormDatePickerStyle" TargetType="DatePicker">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#4CAF50"/>
            <Setter Property="Margin" Value="0,20,0,15"/>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#388E3C"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#F5F5F5"/>
            <Setter Property="Foreground" Value="#333"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#E0E0E0"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="InfoCardStyle" TargetType="Border">
            <Setter Property="Background" Value="#E8F5E8"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#4CAF50"/>
        </Style>
    </Window.Resources>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- رأس النافذة -->
        <Border Grid.Row="0" Background="#4CAF50" Padding="20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="💳" FontSize="24" Foreground="White" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="تسديد المدفوعات" FontSize="20" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="تسجيل وإدارة عمليات الدفع والتحصيل" FontSize="12" Foreground="#E8F5E8"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- محتوى النموذج -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="30">
            <StackPanel>
                <!-- معلومات المعاملة -->
                <Border Style="{StaticResource InfoCardStyle}">
                    <StackPanel>
                        <TextBlock Text="📋 معلومات المعاملة" Style="{StaticResource SectionHeaderStyle}" Foreground="#2E7D32"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="رقم المرجع:" FontWeight="Bold" Margin="0,0,15,5"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Name="ReferenceTextBlock" Text="-" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="الوصف:" FontWeight="Bold" Margin="0,0,15,5"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Name="DescriptionTextBlock" Text="-" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="المبلغ الإجمالي:" FontWeight="Bold" Margin="0,0,15,5"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Name="TotalAmountTextBlock" Text="-" FontWeight="Bold" 
                                      Foreground="#4CAF50" FontSize="16" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="المبلغ المدفوع:" FontWeight="Bold" Margin="0,0,15,5"/>
                            <TextBlock Grid.Row="3" Grid.Column="1" Name="PaidAmountTextBlock" Text="-" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="4" Grid.Column="0" Text="المبلغ المتبقي:" FontWeight="Bold" Margin="0,0,15,5"/>
                            <TextBlock Grid.Row="4" Grid.Column="1" Name="RemainingAmountTextBlock" Text="-" FontWeight="Bold" 
                                      Foreground="#F44336" FontSize="14" Margin="0,0,0,5"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- تفاصيل الدفع -->
                <TextBlock Text="💳 تفاصيل الدفع" Style="{StaticResource SectionHeaderStyle}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="المبلغ المراد دفعه *" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="PaymentAmountTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="أدخل المبلغ المراد دفعه"/>

                        <TextBlock Text="طريقة الدفع *" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="PaymentMethodComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="💵 نقدي" Tag="Cash" IsSelected="True"/>
                            <ComboBoxItem Content="📄 شيك" Tag="Check"/>
                            <ComboBoxItem Content="🏦 تحويل بنكي" Tag="BankTransfer"/>
                            <ComboBoxItem Content="💳 بطاقة ائتمان" Tag="CreditCard"/>
                            <ComboBoxItem Content="📱 محفظة إلكترونية" Tag="EWallet"/>
                        </ComboBox>

                        <TextBlock Text="تاريخ الدفع *" Style="{StaticResource FormLabelStyle}"/>
                        <DatePicker Name="PaymentDatePicker" Style="{StaticResource FormDatePickerStyle}"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="رقم المرجع/الشيك" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="ReferenceNumberTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="رقم الشيك أو رقم المرجع البنكي"/>

                        <TextBlock Text="البنك/المؤسسة المالية" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="BankComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="البنك الأهلي السعودي" Tag="NCB"/>
                            <ComboBoxItem Content="بنك الراجحي" Tag="Rajhi"/>
                            <ComboBoxItem Content="بنك الرياض" Tag="Riyadh"/>
                            <ComboBoxItem Content="البنك السعودي للاستثمار" Tag="SAIB"/>
                            <ComboBoxItem Content="البنك السعودي الفرنسي" Tag="BSF"/>
                            <ComboBoxItem Content="بنك سامبا" Tag="Samba"/>
                            <ComboBoxItem Content="البنك العربي الوطني" Tag="ANB"/>
                            <ComboBoxItem Content="أخرى" Tag="Other"/>
                        </ComboBox>

                        <TextBlock Text="رسوم التحويل" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="TransferFeesTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="0" ToolTip="رسوم التحويل أو العمولة"/>
                    </StackPanel>
                </Grid>

                <!-- ملاحظات -->
                <TextBlock Text="📝 ملاحظات إضافية" Style="{StaticResource SectionHeaderStyle}"/>
                <TextBox Name="NotesTextBox" Style="{StaticResource FormTextBoxStyle}" 
                        Height="80" TextWrapping="Wrap" AcceptsReturn="True"
                        Text="" ToolTip="ملاحظات حول عملية الدفع"/>

                <!-- خيارات إضافية -->
                <TextBlock Text="⚙️ خيارات إضافية" Style="{StaticResource SectionHeaderStyle}"/>
                <StackPanel>
                    <CheckBox Name="SendReceiptCheckBox" Content="إرسال إيصال الدفع للموكل" 
                             FontSize="14" Margin="0,5,0,5" IsChecked="True"/>
                    <CheckBox Name="UpdateStatusCheckBox" Content="تحديث حالة المعاملة تلقائياً" 
                             FontSize="14" Margin="0,5,0,5" IsChecked="True"/>
                    <CheckBox Name="PrintReceiptCheckBox" Content="طباعة إيصال الدفع" 
                             FontSize="14" Margin="0,5,0,5" IsChecked="False"/>
                    <CheckBox Name="AddToAccountingCheckBox" Content="إضافة للسجلات المحاسبية" 
                             FontSize="14" Margin="0,5,0,5" IsChecked="True"/>
                </StackPanel>

                <!-- ملخص العملية -->
                <Border Style="{StaticResource InfoCardStyle}" Margin="0,20,0,0">
                    <StackPanel>
                        <TextBlock Text="📊 ملخص العملية" Style="{StaticResource SectionHeaderStyle}" Foreground="#2E7D32"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="المبلغ المدفوع:" FontWeight="Bold" Margin="0,0,15,5"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Name="SummaryPaymentAmountTextBlock" Text="0 ريال" 
                                      FontWeight="Bold" Foreground="#4CAF50" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="رسوم التحويل:" FontWeight="Bold" Margin="0,0,15,5"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Name="SummaryFeesTextBlock" Text="0 ريال" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="إجمالي المبلغ:" FontWeight="Bold" Margin="0,0,15,5"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Name="SummaryTotalTextBlock" Text="0 ريال" 
                                      FontWeight="Bold" FontSize="16" Foreground="#2E7D32" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="المبلغ المتبقي بعد الدفع:" FontWeight="Bold" Margin="0,0,15,5"/>
                            <TextBlock Grid.Row="3" Grid.Column="1" Name="SummaryRemainingTextBlock" Text="0 ريال" 
                                      FontWeight="Bold" Foreground="#F44336" Margin="0,0,0,5"/>
                        </Grid>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- أزرار الإجراءات -->
        <Border Grid.Row="2" Background="White" Padding="20" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Name="ProcessPaymentButton" Style="{StaticResource PrimaryButtonStyle}" Click="ProcessPaymentButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="💳" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="تنفيذ الدفع"/>
                    </StackPanel>
                </Button>
                
                <Button Name="PartialPaymentButton" Style="{StaticResource PrimaryButtonStyle}" 
                       Background="#FF9800" Click="PartialPaymentButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🔵" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="دفع جزئي"/>
                    </StackPanel>
                </Button>
                
                <Button Name="SchedulePaymentButton" Style="{StaticResource PrimaryButtonStyle}" 
                       Background="#9C27B0" Click="SchedulePaymentButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📅" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="جدولة الدفع"/>
                    </StackPanel>
                </Button>
                
                <Button Name="CancelButton" Style="{StaticResource SecondaryButtonStyle}" Click="CancelButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="❌" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="إلغاء"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
