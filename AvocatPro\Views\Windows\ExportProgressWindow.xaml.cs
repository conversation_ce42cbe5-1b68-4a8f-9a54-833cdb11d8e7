using System;
using System.Windows;
using System.Windows.Threading;

namespace AvocatPro.Views.Windows;

public partial class ExportProgressWindow : Window
{
    private readonly DispatcherTimer _progressTimer;
    private int _currentStep = 0;
    private readonly string[] _steps = { "تحضير البيانات", "إنشاء المحتوى", "إنشاء الرسوم البيانية", "تنسيق التقرير", "حفظ الملف" };
    private int _totalTime = 30; // إجمالي الوقت المتوقع بالثواني
    private int _elapsedTime = 0;

    public ExportProgressWindow()
    {
        InitializeComponent();
        
        _progressTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromMilliseconds(500)
        };
        _progressTimer.Tick += ProgressTimer_Tick;
        
        StartProgress();
    }

    private void StartProgress()
    {
        _progressTimer.Start();
        UpdateCurrentStep();
    }

    private void ProgressTimer_Tick(object? sender, EventArgs e)
    {
        _elapsedTime++;
        
        // تحديث شريط التقدم الرئيسي
        var progress = Math.Min(100, (_elapsedTime * 100.0) / (_totalTime * 2)); // ضرب في 2 لأن التايمر يعمل كل 0.5 ثانية
        MainProgressBar.Value = progress;
        MainProgressText.Text = $"{progress:F0}%";
        
        // تحديث الوقت المتبقي
        var remainingTime = Math.Max(0, _totalTime - (_elapsedTime / 2));
        TimeRemainingText.Text = remainingTime > 0 ? $"~{remainingTime} ثانية" : "اكتمل";
        
        // تحديث الخطوة الحالية
        var stepProgress = (_elapsedTime / 2.0) / _totalTime * _steps.Length;
        var newStep = Math.Min(_steps.Length - 1, (int)stepProgress);
        
        if (newStep != _currentStep)
        {
            _currentStep = newStep;
            UpdateCurrentStep();
        }
        
        // إنهاء العملية عند الوصول للنهاية
        if (_elapsedTime >= _totalTime * 2)
        {
            _progressTimer.Stop();
            CompleteProgress();
        }
    }

    private void UpdateCurrentStep()
    {
        if (_currentStep < _steps.Length)
        {
            MainStatusText.Text = _steps[_currentStep] + "...";
            
            // تحديث حالة الخطوات
            UpdateStepStatus(0, _currentStep >= 0);
            UpdateStepStatus(1, _currentStep >= 1);
            UpdateStepStatus(2, _currentStep >= 2);
            UpdateStepStatus(3, _currentStep >= 3);
            UpdateStepStatus(4, _currentStep >= 4);
        }
    }

    private void UpdateStepStatus(int stepIndex, bool isCompleted)
    {
        var icon = stepIndex switch
        {
            0 => PrepareIcon,
            1 => ContentIcon,
            2 => ChartsIcon,
            3 => FormatIcon,
            4 => SaveIcon,
            _ => null
        };
        
        var status = stepIndex switch
        {
            0 => PrepareStatus,
            1 => ContentStatus,
            2 => ChartsStatus,
            3 => FormatStatus,
            4 => SaveStatus,
            _ => null
        };
        
        if (icon != null && status != null)
        {
            if (isCompleted)
            {
                icon.Text = "✅";
                status.Text = "مكتمل";
                status.Foreground = System.Windows.Media.Brushes.Green;
            }
            else if (stepIndex == _currentStep)
            {
                icon.Text = "🔄";
                status.Text = "جاري التنفيذ";
                status.Foreground = System.Windows.Media.Brushes.Blue;
            }
            else
            {
                icon.Text = "⏳";
                status.Text = "في الانتظار";
                status.Foreground = System.Windows.Media.Brushes.Gray;
            }
        }
    }

    private void CompleteProgress()
    {
        MainStatusText.Text = "تم إكمال التصدير بنجاح!";
        MainProgressBar.Value = 100;
        MainProgressText.Text = "100%";
        TimeRemainingText.Text = "مكتمل";
        
        // تحديث جميع الخطوات كمكتملة
        for (int i = 0; i < _steps.Length; i++)
        {
            UpdateStepStatus(i, true);
        }
        
        // إخفاء أزرار الإلغاء والتصغير
        CancelButton.Visibility = Visibility.Collapsed;
        MinimizeButton.Visibility = Visibility.Collapsed;
        
        // إغلاق النافذة تلقائياً بعد ثانيتين
        var closeTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(2)
        };
        closeTimer.Tick += (s, e) =>
        {
            closeTimer.Stop();
            Close();
        };
        closeTimer.Start();
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("هل تريد إلغاء عملية التصدير؟", "تأكيد الإلغاء", 
                                   MessageBoxButton.YesNo, MessageBoxImage.Question);
        
        if (result == MessageBoxResult.Yes)
        {
            _progressTimer.Stop();
            DialogResult = false;
            Close();
        }
    }

    private void MinimizeButton_Click(object sender, RoutedEventArgs e)
    {
        WindowState = WindowState.Minimized;
    }

    protected override void OnClosed(EventArgs e)
    {
        _progressTimer?.Stop();
        base.OnClosed(e);
    }
}
