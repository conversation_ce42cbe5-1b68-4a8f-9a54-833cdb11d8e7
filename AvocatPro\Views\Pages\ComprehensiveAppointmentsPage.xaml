<Page x:Class="AvocatPro.Views.Pages.ComprehensiveAppointmentsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:fa="http://schemas.fontawesome.io/icons/"
      Title="إدارة المواعيد الشاملة"
      Background="#F8FAFC">

    <Page.Resources>
        <!-- أنماط مخصصة -->
        <Style x:Key="ModernCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#E5E7EB" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="StatCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#E5E7EB" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#6366F1"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="8" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#5B5BD6"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#4F46E5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SearchTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#D1D5DB"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}" 
                                CornerRadius="8">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                          Margin="{TemplateBinding Padding}"
                                          VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#D1D5DB"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>
    </Page.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان الرئيسي -->
        <Border Grid.Row="0" Style="{StaticResource ModernCardStyle}" Margin="10,10,10,5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <fa:ImageAwesome Icon="Calendar" Foreground="#6366F1" Width="32" Height="32" Margin="0,0,15,0"/>
                    <StackPanel>
                        <TextBlock Text="إدارة المواعيد الشاملة" FontSize="24" FontWeight="Bold" Foreground="#1F2937"/>
                        <TextBlock Text="إدارة وتنظيم جميع المواعيد مع التذكيرات والإشعارات" FontSize="14" Foreground="#6B7280"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="AddAppointmentButton" Style="{StaticResource ModernButtonStyle}" Click="AddAppointmentButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <fa:ImageAwesome Icon="Plus" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="إضافة موعد"/>
                        </StackPanel>
                    </Button>
                    <Button x:Name="CalendarViewButton" Style="{StaticResource ModernButtonStyle}" Background="#10B981" Click="CalendarViewButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <fa:ImageAwesome Icon="Calendar" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="التقويم"/>
                        </StackPanel>
                    </Button>
                    <Button x:Name="SendRemindersButton" Style="{StaticResource ModernButtonStyle}" Background="#F59E0B" Click="SendRemindersButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <fa:ImageAwesome Icon="Bell" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="إرسال تذكيرات"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- الإحصائيات -->
        <Border Grid.Row="1" Style="{StaticResource ModernCardStyle}" Margin="10,5,10,5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- إجمالي المواعيد -->
                <Border Grid.Column="0" Style="{StaticResource StatCardStyle}" Background="#EEF2FF">
                    <StackPanel HorizontalAlignment="Center">
                        <fa:ImageAwesome Icon="Calendar" Foreground="#6366F1" Width="24" Height="24" HorizontalAlignment="Center"/>
                        <TextBlock x:Name="TotalAppointmentsCount" Text="0" FontSize="28" FontWeight="Bold" Foreground="#6366F1" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                        <TextBlock Text="إجمالي المواعيد" FontSize="12" Foreground="#6B7280" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- مواعيد اليوم -->
                <Border Grid.Column="1" Style="{StaticResource StatCardStyle}" Background="#ECFDF5">
                    <StackPanel HorizontalAlignment="Center">
                        <fa:ImageAwesome Icon="ClockOutline" Foreground="#10B981" Width="24" Height="24" HorizontalAlignment="Center"/>
                        <TextBlock x:Name="TodayAppointmentsCount" Text="0" FontSize="28" FontWeight="Bold" Foreground="#10B981" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                        <TextBlock Text="مواعيد اليوم" FontSize="12" Foreground="#6B7280" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- مواعيد مجدولة -->
                <Border Grid.Column="2" Style="{StaticResource StatCardStyle}" Background="#FEF3C7">
                    <StackPanel HorizontalAlignment="Center">
                        <fa:ImageAwesome Icon="CheckCircle" Foreground="#F59E0B" Width="24" Height="24" HorizontalAlignment="Center"/>
                        <TextBlock x:Name="ScheduledAppointmentsCount" Text="0" FontSize="28" FontWeight="Bold" Foreground="#F59E0B" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                        <TextBlock Text="مجدولة" FontSize="12" Foreground="#6B7280" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- مواعيد مكتملة -->
                <Border Grid.Column="3" Style="{StaticResource StatCardStyle}" Background="#D1FAE5">
                    <StackPanel HorizontalAlignment="Center">
                        <fa:ImageAwesome Icon="Check" Foreground="#059669" Width="24" Height="24" HorizontalAlignment="Center"/>
                        <TextBlock x:Name="CompletedAppointmentsCount" Text="0" FontSize="28" FontWeight="Bold" Foreground="#059669" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                        <TextBlock Text="مكتملة" FontSize="12" Foreground="#6B7280" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- مواعيد متأخرة -->
                <Border Grid.Column="4" Style="{StaticResource StatCardStyle}" Background="#FEE2E2">
                    <StackPanel HorizontalAlignment="Center">
                        <fa:ImageAwesome Icon="ExclamationTriangle" Foreground="#DC2626" Width="24" Height="24" HorizontalAlignment="Center"/>
                        <TextBlock x:Name="OverdueAppointmentsCount" Text="0" FontSize="28" FontWeight="Bold" Foreground="#DC2626" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                        <TextBlock Text="متأخرة" FontSize="12" Foreground="#6B7280" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- تذكيرات معلقة -->
                <Border Grid.Column="5" Style="{StaticResource StatCardStyle}" Background="#F3E8FF">
                    <StackPanel HorizontalAlignment="Center">
                        <fa:ImageAwesome Icon="Bell" Foreground="#8B5CF6" Width="24" Height="24" HorizontalAlignment="Center"/>
                        <TextBlock x:Name="PendingRemindersCount" Text="0" FontSize="28" FontWeight="Bold" Foreground="#8B5CF6" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                        <TextBlock Text="تذكيرات معلقة" FontSize="12" Foreground="#6B7280" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>

        <!-- شريط البحث والفلاتر -->
        <Border Grid.Row="2" Style="{StaticResource ModernCardStyle}" Margin="10,5,10,5">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- الصف الأول: البحث والفلاتر الأساسية -->
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- البحث -->
                    <TextBox x:Name="SearchTextBox" Grid.Column="0" Style="{StaticResource SearchTextBoxStyle}" 
                             Text="البحث في المواعيد..." Foreground="#9CA3AF" Margin="0,0,10,0"
                             GotFocus="SearchTextBox_GotFocus" LostFocus="SearchTextBox_LostFocus" TextChanged="SearchTextBox_TextChanged"/>

                    <!-- فلتر الحالة -->
                    <ComboBox x:Name="StatusFilterComboBox" Grid.Column="1" Style="{StaticResource ModernComboBoxStyle}" 
                              Margin="5,0" SelectionChanged="FilterComboBox_SelectionChanged">
                        <ComboBoxItem Content="جميع الحالات" IsSelected="True"/>
                    </ComboBox>

                    <!-- فلتر نوع الموعد -->
                    <ComboBox x:Name="TypeFilterComboBox" Grid.Column="2" Style="{StaticResource ModernComboBoxStyle}" 
                              Margin="5,0" SelectionChanged="FilterComboBox_SelectionChanged">
                        <ComboBoxItem Content="جميع الأنواع" IsSelected="True"/>
                    </ComboBox>

                    <!-- فلتر المحامي -->
                    <ComboBox x:Name="LawyerFilterComboBox" Grid.Column="3" Style="{StaticResource ModernComboBoxStyle}" 
                              Margin="5,0" SelectionChanged="FilterComboBox_SelectionChanged">
                        <ComboBoxItem Content="جميع المحامين" IsSelected="True"/>
                    </ComboBox>

                    <!-- فلتر الأولوية -->
                    <ComboBox x:Name="PriorityFilterComboBox" Grid.Column="4" Style="{StaticResource ModernComboBoxStyle}" 
                              Margin="5,0" SelectionChanged="FilterComboBox_SelectionChanged">
                        <ComboBoxItem Content="جميع الأولويات" IsSelected="True"/>
                    </ComboBox>

                    <!-- أزرار الإجراءات -->
                    <StackPanel Grid.Column="5" Orientation="Horizontal" Margin="10,0,0,0">
                        <Button x:Name="RefreshButton" Style="{StaticResource ModernButtonStyle}" Background="#6B7280" Click="RefreshButton_Click">
                            <fa:ImageAwesome Icon="Refresh" Width="16" Height="16"/>
                        </Button>
                        <Button x:Name="ExportButton" Style="{StaticResource ModernButtonStyle}" Background="#059669" Click="ExportButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <fa:ImageAwesome Icon="Download" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="تصدير"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>

                <!-- الصف الثاني: فلاتر التاريخ والعرض السريع -->
                <Grid Grid.Row="1" Margin="0,10,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="من تاريخ:" VerticalAlignment="Center" Margin="0,0,10,0" Foreground="#374151"/>
                    <DatePicker x:Name="FromDatePicker" Grid.Column="1" Margin="0,0,20,0" SelectedDateChanged="DatePicker_SelectedDateChanged"/>

                    <TextBlock Grid.Column="2" Text="إلى تاريخ:" VerticalAlignment="Center" Margin="0,0,10,0" Foreground="#374151"/>
                    <DatePicker x:Name="ToDatePicker" Grid.Column="3" Margin="0,0,20,0" SelectedDateChanged="DatePicker_SelectedDateChanged"/>

                    <TextBlock Grid.Column="4" Text="عرض سريع:" VerticalAlignment="Center" Margin="0,0,10,0" Foreground="#374151"/>
                    <ComboBox x:Name="QuickViewComboBox" Grid.Column="5" Style="{StaticResource ModernComboBoxStyle}" 
                              SelectionChanged="QuickViewComboBox_SelectionChanged">
                        <ComboBoxItem Content="جميع المواعيد" IsSelected="True"/>
                        <ComboBoxItem Content="مواعيد اليوم"/>
                        <ComboBoxItem Content="مواعيد غداً"/>
                        <ComboBoxItem Content="مواعيد الأسبوع"/>
                        <ComboBoxItem Content="مواعيد الشهر"/>
                        <ComboBoxItem Content="المواعيد القادمة"/>
                        <ComboBoxItem Content="المواعيد المتأخرة"/>
                        <ComboBoxItem Content="تحتاج تذكير"/>
                    </ComboBox>
                </Grid>
            </Grid>
        </Border>

        <!-- جدول المواعيد -->
        <Border Grid.Row="3" Style="{StaticResource ModernCardStyle}" Margin="10,5,10,10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- عنوان الجدول -->
                <Grid Grid.Row="0" Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="قائمة المواعيد" FontSize="18" FontWeight="SemiBold" Foreground="#1F2937"/>
                    
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button x:Name="EditAppointmentButton" Style="{StaticResource ModernButtonStyle}" Background="#F59E0B" Click="EditAppointmentButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <fa:ImageAwesome Icon="Edit" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="تعديل"/>
                            </StackPanel>
                        </Button>
                        <Button x:Name="DeleteAppointmentButton" Style="{StaticResource ModernButtonStyle}" Background="#EF4444" Click="DeleteAppointmentButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <fa:ImageAwesome Icon="Trash" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="حذف"/>
                            </StackPanel>
                        </Button>
                        <Button x:Name="ViewDetailsButton" Style="{StaticResource ModernButtonStyle}" Background="#8B5CF6" Click="ViewDetailsButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <fa:ImageAwesome Icon="Eye" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="تفاصيل"/>
                            </StackPanel>
                        </Button>
                        <Button x:Name="PrintAppointmentButton" Style="{StaticResource ModernButtonStyle}" Background="#059669" Click="PrintAppointmentButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <fa:ImageAwesome Icon="Print" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="طباعة"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>

                <!-- الجدول -->
                <DataGrid x:Name="AppointmentsDataGrid" Grid.Row="1" 
                          AutoGenerateColumns="False" 
                          CanUserAddRows="False" 
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="None"
                          HeadersVisibility="Column"
                          Background="Transparent"
                          BorderThickness="0"
                          RowBackground="White"
                          AlternatingRowBackground="#F9FAFB"
                          SelectionMode="Single"
                          SelectionChanged="AppointmentsDataGrid_SelectionChanged">
                    
                    <DataGrid.ColumnHeaderStyle>
                        <Style TargetType="DataGridColumnHeader">
                            <Setter Property="Background" Value="#F3F4F6"/>
                            <Setter Property="Foreground" Value="#374151"/>
                            <Setter Property="FontWeight" Value="SemiBold"/>
                            <Setter Property="Padding" Value="12,8"/>
                            <Setter Property="BorderThickness" Value="0,0,1,1"/>
                            <Setter Property="BorderBrush" Value="#E5E7EB"/>
                        </Style>
                    </DataGrid.ColumnHeaderStyle>

                    <DataGrid.CellStyle>
                        <Style TargetType="DataGridCell">
                            <Setter Property="BorderThickness" Value="0,0,1,1"/>
                            <Setter Property="BorderBrush" Value="#F3F4F6"/>
                            <Setter Property="Padding" Value="12,8"/>
                            <Style.Triggers>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Background" Value="#EEF2FF"/>
                                    <Setter Property="Foreground" Value="#1F2937"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </DataGrid.CellStyle>

                    <DataGrid.Columns>
                        <DataGridTextColumn Header="رقم الموعد" Binding="{Binding AppointmentNumber}" Width="120"/>
                        <DataGridTextColumn Header="العنوان" Binding="{Binding Title}" Width="200"/>
                        <DataGridTextColumn Header="التاريخ" Binding="{Binding AppointmentDate, StringFormat=dd/MM/yyyy}" Width="100"/>
                        <DataGridTextColumn Header="الوقت" Binding="{Binding AppointmentTimeText}" Width="80"/>
                        <DataGridTextColumn Header="المدة" Binding="{Binding DurationText}" Width="80"/>
                        <DataGridTextColumn Header="نوع الموعد" Binding="{Binding AppointmentType}" Width="150"/>
                        <DataGridTextColumn Header="الموكل" Binding="{Binding ClientName}" Width="150"/>
                        <DataGridTextColumn Header="المحامي" Binding="{Binding AssignedLawyer}" Width="150"/>
                        
                        <DataGridTemplateColumn Header="الحالة" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border Background="{Binding StatusColor}" CornerRadius="12" Padding="8,4">
                                        <TextBlock Text="{Binding Status}" Foreground="White" FontSize="11" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="الأولوية" Width="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border Background="{Binding PriorityColor}" CornerRadius="8" Padding="6,3">
                                        <TextBlock Text="{Binding Priority}" Foreground="White" FontSize="10" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTextColumn Header="الرسوم" Binding="{Binding FeesText}" Width="100"/>
                        <DataGridTextColumn Header="التذكير" Binding="{Binding ReminderStatusText}" Width="100"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
    </Grid>
</Page>
