<Page x:Class="AvocatPro.Views.Pages.SimpleClientsPageTest"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="إدارة الموكلين - اختبار"
      Background="LightGray"
      FlowDirection="RightToLeft">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <StackPanel Grid.Row="0" Background="White" Margin="0,0,0,20">
            <TextBlock Text="إدارة الموكلين" FontSize="24" FontWeight="Bold"
                       Foreground="Black" HorizontalAlignment="Center" Margin="10"/>
            <TextBlock Text="صفحة اختبار بسيطة" FontSize="14"
                       Foreground="Gray" HorizontalAlignment="Center" Margin="5"/>
        </StackPanel>

        <!-- المحتوى -->
        <StackPanel Grid.Row="1" Background="White">
            <TextBlock Text="قائمة الموكلين" FontSize="18" FontWeight="Bold"
                       Foreground="Black" Margin="10"/>

            <Button Content="إضافة موكل جديد" Background="Blue" Foreground="White"
                    BorderThickness="0" Padding="15,10" FontSize="14"
                    Click="AddClient_Click" Margin="10"/>

            <DataGrid x:Name="ClientsDataGrid" AutoGenerateColumns="False"
                      CanUserAddRows="False" CanUserDeleteRows="False"
                      GridLinesVisibility="All" HeadersVisibility="Column"
                      Background="White" BorderThickness="1" BorderBrush="Gray"
                      FontSize="12" RowHeight="30" Margin="10">

                <DataGrid.Columns>
                    <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="150"/>
                    <DataGridTextColumn Header="النوع" Binding="{Binding ClientType}" Width="100"/>
                    <DataGridTextColumn Header="الهاتف" Binding="{Binding Phone}" Width="120"/>
                    <DataGridTextColumn Header="البريد" Binding="{Binding Email}" Width="180"/>
                    <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="80"/>
                </DataGrid.Columns>
            </DataGrid>

            <TextBlock x:Name="StatusText" Text="جاري تحميل البيانات..."
                       FontSize="12" Foreground="Gray" Margin="10"/>
        </StackPanel>
    </Grid>
</Page>
