using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace AvocatPro.Views.Pages
{
    /// <summary>
    /// صفحة إدارة الموكلين الشاملة مع جميع الميزات المتقدمة
    /// </summary>
    public partial class ComprehensiveClientsPage : Page, INotifyPropertyChanged
    {
        #region Properties
        
        private ObservableCollection<ComprehensiveClientModel> _clients;
        public ObservableCollection<ComprehensiveClientModel> Clients
        {
            get => _clients;
            set
            {
                _clients = value;
                OnPropertyChanged(nameof(Clients));
            }
        }

        private ObservableCollection<ComprehensiveClientModel> _filteredClients;
        public ObservableCollection<ComprehensiveClientModel> FilteredClients
        {
            get => _filteredClients;
            set
            {
                _filteredClients = value;
                OnPropertyChanged(nameof(FilteredClients));
            }
        }

        #endregion

        #region Constructor

        public ComprehensiveClientsPage()
        {
            InitializeComponent();
            DataContext = this;
            LoadComprehensiveData();
            InitializeFilters();
        }

        #endregion

        #region Data Loading

        /// <summary>
        /// تحميل البيانات الشاملة للموكلين
        /// </summary>
        private void LoadComprehensiveData()
        {
            try
            {
                Clients = new ObservableCollection<ComprehensiveClientModel>
                {
                    new ComprehensiveClientModel
                    {
                        Id = 1,
                        Name = "أحمد محمد علي السعدي",
                        ClientType = "فرد",
                        OfficeRef = "CLT-001",
                        Phone = "+966501234567",
                        Email = "<EMAIL>",
                        Address = "الرياض، حي الملك فهد، شارع الأمير محمد بن عبدالعزيز",
                        CasesCount = 5,
                        Status = "نشط",
                        InitialLetter = "أ",
                        CreatedDate = DateTime.Now.AddDays(-45),
                        LastContactDate = DateTime.Now.AddDays(-5),
                        TotalFees = 25000,
                        Notes = "موكل مهم - قضايا تجارية"
                    },
                    new ComprehensiveClientModel
                    {
                        Id = 2,
                        Name = "شركة النور للتجارة والاستثمار المحدودة",
                        ClientType = "شركة",
                        OfficeRef = "CLT-002",
                        Phone = "+966502345678",
                        Email = "<EMAIL>",
                        Address = "جدة، حي الروضة، طريق الملك عبدالعزيز",
                        CasesCount = 12,
                        Status = "نشط",
                        InitialLetter = "ش",
                        CreatedDate = DateTime.Now.AddDays(-120),
                        LastContactDate = DateTime.Now.AddDays(-2),
                        TotalFees = 85000,
                        Notes = "شركة كبيرة - عقود متعددة"
                    },
                    new ComprehensiveClientModel
                    {
                        Id = 3,
                        Name = "فاطمة عبدالله الزهراني",
                        ClientType = "فرد",
                        OfficeRef = "CLT-003",
                        Phone = "+966503456789",
                        Email = "<EMAIL>",
                        Address = "الدمام، حي الشاطئ، شارع الكورنيش",
                        CasesCount = 3,
                        Status = "نشط",
                        InitialLetter = "ف",
                        CreatedDate = DateTime.Now.AddDays(-30),
                        LastContactDate = DateTime.Now.AddDays(-1),
                        TotalFees = 15000,
                        Notes = "قضايا أسرية"
                    },
                    new ComprehensiveClientModel
                    {
                        Id = 4,
                        Name = "مؤسسة البناء الحديث للمقاولات",
                        ClientType = "شركة",
                        OfficeRef = "CLT-004",
                        Phone = "+966504567890",
                        Email = "<EMAIL>",
                        Address = "مكة المكرمة، حي العزيزية، شارع إبراهيم الخليل",
                        CasesCount = 8,
                        Status = "غير نشط",
                        InitialLetter = "م",
                        CreatedDate = DateTime.Now.AddDays(-180),
                        LastContactDate = DateTime.Now.AddDays(-60),
                        TotalFees = 45000,
                        Notes = "مؤسسة مقاولات - قضايا عمالية"
                    },
                    new ComprehensiveClientModel
                    {
                        Id = 5,
                        Name = "خالد سعد الدين المطيري",
                        ClientType = "فرد",
                        OfficeRef = "CLT-005",
                        Phone = "+966505678901",
                        Email = "<EMAIL>",
                        Address = "المدينة المنورة، حي قباء، شارع سيد الشهداء",
                        CasesCount = 2,
                        Status = "نشط",
                        InitialLetter = "خ",
                        CreatedDate = DateTime.Now.AddDays(-15),
                        LastContactDate = DateTime.Now.AddDays(-3),
                        TotalFees = 8000,
                        Notes = "قضايا عقارية"
                    },
                    new ComprehensiveClientModel
                    {
                        Id = 6,
                        Name = "شركة التقنية المتقدمة للحلول الرقمية",
                        ClientType = "شركة",
                        OfficeRef = "CLT-006",
                        Phone = "+966506789012",
                        Email = "<EMAIL>",
                        Address = "الخبر، حي الراكة الشمالية، طريق الملك فهد",
                        CasesCount = 7,
                        Status = "نشط",
                        InitialLetter = "ش",
                        CreatedDate = DateTime.Now.AddDays(-75),
                        LastContactDate = DateTime.Now.AddDays(-7),
                        TotalFees = 35000,
                        Notes = "شركة تقنية - ملكية فكرية"
                    },
                    new ComprehensiveClientModel
                    {
                        Id = 7,
                        Name = "نورا أحمد الزهراني القحطاني",
                        ClientType = "فرد",
                        OfficeRef = "CLT-007",
                        Phone = "+966507890123",
                        Email = "<EMAIL>",
                        Address = "أبها، حي المنهل، شارع الملك خالد",
                        CasesCount = 4,
                        Status = "نشط",
                        InitialLetter = "ن",
                        CreatedDate = DateTime.Now.AddDays(-10),
                        LastContactDate = DateTime.Now,
                        TotalFees = 12000,
                        Notes = "قضايا تجارية صغيرة"
                    },
                    new ComprehensiveClientModel
                    {
                        Id = 8,
                        Name = "مجموعة الرياض للاستثمار والتطوير",
                        ClientType = "شركة",
                        OfficeRef = "CLT-008",
                        Phone = "+966508901234",
                        Email = "<EMAIL>",
                        Address = "الرياض، حي العليا، برج المملكة",
                        CasesCount = 15,
                        Status = "نشط",
                        InitialLetter = "م",
                        CreatedDate = DateTime.Now.AddDays(-200),
                        LastContactDate = DateTime.Now.AddDays(-1),
                        TotalFees = 150000,
                        Notes = "مجموعة استثمارية كبيرة - عقود معقدة"
                    }
                };

                FilteredClients = new ObservableCollection<ComprehensiveClientModel>(Clients);
                ClientsDataGrid.ItemsSource = FilteredClients;
                UpdateStatistics();
                UpdateResultsCount();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region Statistics

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        private void UpdateStatistics()
        {
            if (Clients == null) return;

            var totalClients = Clients.Count;
            var activeClients = Clients.Count(c => c.Status == "نشط");
            var companies = Clients.Count(c => c.ClientType == "شركة");
            var totalCases = Clients.Sum(c => c.CasesCount);

            TotalClientsText.Text = totalClients.ToString();
            ActiveClientsText.Text = activeClients.ToString();
            CompaniesText.Text = companies.ToString();
            ActiveCasesText.Text = totalCases.ToString();

            // حساب النسب المئوية
            if (totalClients > 0)
            {
                var activePercent = (activeClients * 100.0 / totalClients).ToString("F1");
                var companiesPercent = (companies * 100.0 / totalClients).ToString("F1");
                
                ActiveClientsPercentText.Text = $"{activePercent}% من إجمالي الموكلين";
                CompaniesPercentText.Text = $"{companiesPercent}% من إجمالي الموكلين";
            }

            // رسائل التغيير (يمكن ربطها بقاعدة البيانات لاحقاً)
            TotalClientsChangeText.Text = "↗️ +12% من الشهر الماضي";
            ActiveCasesChangeText.Text = $"↗️ +{Math.Max(1, totalCases / 10)} قضايا جديدة";
        }

        #endregion

        #region Initialization

        /// <summary>
        /// تهيئة عناصر التصفية
        /// </summary>
        private void InitializeFilters()
        {
            ClientTypeFilter.SelectedIndex = 0; // جميع الأنواع
            StatusFilter.SelectedIndex = 0; // جميع الحالات
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// إضافة موكل جديد
        /// </summary>
        private void AddClient_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addClientWindow = new Windows.ComprehensiveAddClientWindow();
                addClientWindow.Owner = Window.GetWindow(this);

                if (addClientWindow.ShowDialog() == true && addClientWindow.IsSaved)
                {
                    var newClient = addClientWindow.NewClient;
                    newClient.Id = Clients.Count + 1;

                    // إضافة الموكل الجديد للقائمة
                    Clients.Add(newClient);
                    ApplyFilters();
                    UpdateStatistics();

                    MessageBox.Show($"تم إضافة الموكل '{newClient.Name}' بنجاح!\n\nمرجع المكتب: {newClient.OfficeRef}",
                                   "نجح الإضافة", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الموكل: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تصدير البيانات
        /// </summary>
        private void ExportClients_Click(object sender, RoutedEventArgs e)
        {
            var contextMenu = new ContextMenu();

            var exportPdfItem = new MenuItem { Header = "تصدير PDF" };
            exportPdfItem.Click += (s, args) => ExportToPdf();

            var exportExcelItem = new MenuItem { Header = "تصدير Excel" };
            exportExcelItem.Click += (s, args) => ExportToExcel();

            var exportCsvItem = new MenuItem { Header = "تصدير CSV" };
            exportCsvItem.Click += (s, args) => ExportToCsv();

            contextMenu.Items.Add(exportPdfItem);
            contextMenu.Items.Add(exportExcelItem);
            contextMenu.Items.Add(exportCsvItem);

            var button = sender as Button;
            contextMenu.PlacementTarget = button;
            contextMenu.IsOpen = true;
        }

        /// <summary>
        /// طباعة قائمة الموكلين
        /// </summary>
        private void PrintClients_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم تطوير ميزة الطباعة المتقدمة قريباً\n\nستشمل:\n• طباعة قائمة مفصلة\n• تقارير إحصائية\n• تخصيص التقارير",
                               "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// البحث في النص
        /// </summary>
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        /// <summary>
        /// تصفية نوع الموكل
        /// </summary>
        private void ClientTypeFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        /// <summary>
        /// تصفية الحالة
        /// </summary>
        private void StatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void RefreshClients_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoadComprehensiveData();
                SearchTextBox.Text = "";
                ClientTypeFilter.SelectedIndex = 0;
                StatusFilter.SelectedIndex = 0;

                MessageBox.Show("تم تحديث البيانات بنجاح!", "تحديث",
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التحديث: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تصدير إلى Excel
        /// </summary>
        private void ExportExcel_Click(object sender, RoutedEventArgs e)
        {
            ExportToExcel();
        }

        /// <summary>
        /// تصدير إلى PDF
        /// </summary>
        private void ExportPdf_Click(object sender, RoutedEventArgs e)
        {
            ExportToPdf();
        }

        /// <summary>
        /// عرض تفاصيل الموكل
        /// </summary>
        private void ViewClient_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var client = button?.Tag as ComprehensiveClientModel;

                if (client != null)
                {
                    MessageBox.Show($"تفاصيل الموكل: {client.Name}\n\nالنوع: {client.ClientType}\nالمرجع: {client.OfficeRef}\nالهاتف: {client.Phone}\nالبريد: {client.Email}\nالعنوان: {client.Address}\nعدد القضايا: {client.CasesCount}\nالحالة: {client.Status}\nإجمالي الأتعاب: {client.TotalFees:N0} ريال\nالملاحظات: {client.Notes}",
                                   "تفاصيل الموكل", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض التفاصيل: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تعديل الموكل
        /// </summary>
        private void EditClient_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var client = button?.Tag as ComprehensiveClientModel;

                if (client != null)
                {
                    var editClientWindow = new Windows.ComprehensiveAddClientWindow(client);
                    editClientWindow.Owner = Window.GetWindow(this);

                    if (editClientWindow.ShowDialog() == true && editClientWindow.IsSaved)
                    {
                        var updatedClient = editClientWindow.NewClient;
                        var index = Clients.IndexOf(client);

                        if (index >= 0)
                        {
                            Clients[index] = updatedClient;
                            ApplyFilters();
                            UpdateStatistics();

                            MessageBox.Show($"تم تحديث بيانات الموكل '{updatedClient.Name}' بنجاح!",
                                           "نجح التحديث", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التعديل: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// عرض قضايا الموكل
        /// </summary>
        private void ViewCases_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var client = button?.Tag as ComprehensiveClientModel;

                if (client != null)
                {
                    MessageBox.Show($"عرض قضايا الموكل: {client.Name}\n\nعدد القضايا: {client.CasesCount}\nإجمالي الأتعاب: {client.TotalFees:N0} ريال\n\nسيتم تطوير صفحة إدارة القضايا قريباً",
                                   "قضايا الموكل", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض القضايا: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حذف الموكل
        /// </summary>
        private void DeleteClient_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var client = button?.Tag as ComprehensiveClientModel;

                if (client != null)
                {
                    var result = MessageBox.Show($"هل أنت متأكد من حذف الموكل '{client.Name}'؟\n\nسيتم حذف:\n• جميع بيانات الموكل\n• {client.CasesCount} قضية مرتبطة\n• جميع المستندات\n• إجمالي أتعاب: {client.TotalFees:N0} ريال\n\nهذا الإجراء لا يمكن التراجع عنه.",
                                               "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        Clients.Remove(client);
                        ApplyFilters();
                        UpdateStatistics();

                        MessageBox.Show("تم حذف الموكل وجميع بياناته بنجاح!", "نجح",
                                       MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الحذف: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// الصفحة السابقة
        /// </summary>
        private void PreviousPage_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير ميزة التنقل بين الصفحات مع:\n• عرض 50 موكل في كل صفحة\n• تنقل سريع\n• حفظ موضع التصفح",
                           "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// الصفحة التالية
        /// </summary>
        private void NextPage_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير ميزة التنقل بين الصفحات مع:\n• عرض 50 موكل في كل صفحة\n• تنقل سريع\n• حفظ موضع التصفح",
                           "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// تطبيق المرشحات على البيانات
        /// </summary>
        private void ApplyFilters()
        {
            if (Clients == null) return;

            var filtered = Clients.AsEnumerable();

            // تصفية النص
            if (!string.IsNullOrWhiteSpace(SearchTextBox?.Text))
            {
                var searchText = SearchTextBox.Text.ToLower();
                filtered = filtered.Where(c =>
                    c.Name.ToLower().Contains(searchText) ||
                    c.OfficeRef.ToLower().Contains(searchText) ||
                    c.Phone.Contains(searchText) ||
                    c.Email.ToLower().Contains(searchText) ||
                    c.Address.ToLower().Contains(searchText) ||
                    c.Notes.ToLower().Contains(searchText));
            }

            // تصفية نوع الموكل
            if (ClientTypeFilter?.SelectedIndex > 0)
            {
                var selectedType = (ClientTypeFilter.SelectedItem as ComboBoxItem)?.Content.ToString();
                if (selectedType == "أفراد")
                    filtered = filtered.Where(c => c.ClientType == "فرد");
                else if (selectedType == "شركات")
                    filtered = filtered.Where(c => c.ClientType == "شركة");
            }

            // تصفية الحالة
            if (StatusFilter?.SelectedIndex > 0)
            {
                var selectedStatus = (StatusFilter.SelectedItem as ComboBoxItem)?.Content.ToString();
                if (!string.IsNullOrEmpty(selectedStatus))
                    filtered = filtered.Where(c => c.Status == selectedStatus);
            }

            FilteredClients = new ObservableCollection<ComprehensiveClientModel>(filtered);
            ClientsDataGrid.ItemsSource = FilteredClients;
            UpdateResultsCount();
        }

        /// <summary>
        /// تحديث عداد النتائج
        /// </summary>
        private void UpdateResultsCount()
        {
            if (FilteredClients != null && ResultsCountText != null)
            {
                var total = FilteredClients.Count;
                var displayed = Math.Min(50, total); // افتراض عرض 50 عنصر كحد أقصى
                ResultsCountText.Text = $"عرض 1-{displayed} من {total} موكل";
            }
        }

        /// <summary>
        /// تصدير إلى PDF
        /// </summary>
        private void ExportToPdf()
        {
            try
            {
                MessageBox.Show("سيتم تطوير ميزة تصدير PDF المتقدمة قريباً\n\nستشمل:\n• تقرير مفصل بجميع البيانات\n• إحصائيات شاملة\n• تصميم احترافي\n• إمكانية التخصيص",
                               "تصدير PDF", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير PDF: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تصدير إلى Excel
        /// </summary>
        private void ExportToExcel()
        {
            try
            {
                MessageBox.Show("سيتم تطوير ميزة تصدير Excel المتقدمة قريباً\n\nستشمل:\n• جميع بيانات الموكلين\n• أوراق عمل متعددة\n• تنسيق احترافي\n• إحصائيات تفاعلية",
                               "تصدير Excel", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير Excel: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تصدير إلى CSV
        /// </summary>
        private void ExportToCsv()
        {
            try
            {
                MessageBox.Show("سيتم تطوير ميزة تصدير CSV قريباً\n\nستشمل:\n• تصدير سريع\n• تنسيق قابل للقراءة\n• دعم UTF-8 للعربية",
                               "تصدير CSV", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير CSV: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Comprehensive Client Model

    /// <summary>
    /// نموذج بيانات الموكل الشامل مع جميع التفاصيل
    /// </summary>
    public class ComprehensiveClientModel : INotifyPropertyChanged
    {
        private int _id;
        private string _name = "";
        private string _clientType = "";
        private string _officeRef = "";
        private string _phone = "";
        private string _email = "";
        private string _address = "";
        private int _casesCount;
        private string _status = "";
        private string _initialLetter = "";
        private DateTime _createdDate;
        private DateTime _lastContactDate;
        private decimal _totalFees;
        private string _notes = "";

        public int Id
        {
            get => _id;
            set { _id = value; OnPropertyChanged(nameof(Id)); }
        }

        public string Name
        {
            get => _name;
            set
            {
                _name = value;
                OnPropertyChanged(nameof(Name));
                InitialLetter = !string.IsNullOrEmpty(value) ? value.Substring(0, 1) : "؟";
            }
        }

        public string ClientType
        {
            get => _clientType;
            set { _clientType = value; OnPropertyChanged(nameof(ClientType)); }
        }

        public string OfficeRef
        {
            get => _officeRef;
            set { _officeRef = value; OnPropertyChanged(nameof(OfficeRef)); }
        }

        public string Phone
        {
            get => _phone;
            set { _phone = value; OnPropertyChanged(nameof(Phone)); }
        }

        public string Email
        {
            get => _email;
            set { _email = value; OnPropertyChanged(nameof(Email)); }
        }

        public string Address
        {
            get => _address;
            set { _address = value; OnPropertyChanged(nameof(Address)); }
        }

        public int CasesCount
        {
            get => _casesCount;
            set { _casesCount = value; OnPropertyChanged(nameof(CasesCount)); }
        }

        public string Status
        {
            get => _status;
            set { _status = value; OnPropertyChanged(nameof(Status)); }
        }

        public string InitialLetter
        {
            get => _initialLetter;
            set { _initialLetter = value; OnPropertyChanged(nameof(InitialLetter)); }
        }

        public DateTime CreatedDate
        {
            get => _createdDate;
            set { _createdDate = value; OnPropertyChanged(nameof(CreatedDate)); }
        }

        public DateTime LastContactDate
        {
            get => _lastContactDate;
            set { _lastContactDate = value; OnPropertyChanged(nameof(LastContactDate)); }
        }

        public decimal TotalFees
        {
            get => _totalFees;
            set { _totalFees = value; OnPropertyChanged(nameof(TotalFees)); }
        }

        public string Notes
        {
            get => _notes;
            set { _notes = value; OnPropertyChanged(nameof(Notes)); }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    #endregion

    #region Dialog Classes (Placeholder)

    /// <summary>
    /// نافذة إضافة/تعديل موكل شاملة (مؤقتة)
    /// </summary>
    public class ComprehensiveAddClientDialog : Window
    {
        public ComprehensiveClientModel NewClient { get; set; }

        public ComprehensiveAddClientDialog(ComprehensiveClientModel existingClient = null)
        {
            Title = existingClient == null ? "إضافة موكل جديد" : "تعديل بيانات الموكل";
            Width = 600;
            Height = 500;
            WindowStartupLocation = WindowStartupLocation.CenterOwner;

            NewClient = existingClient ?? new ComprehensiveClientModel
            {
                Name = "موكل جديد",
                ClientType = "فرد",
                Phone = "+966500000000",
                Email = "<EMAIL>",
                Address = "العنوان التفصيلي",
                Status = "نشط",
                CasesCount = 0,
                TotalFees = 0,
                Notes = "ملاحظات"
            };
        }

        protected override void OnSourceInitialized(EventArgs e)
        {
            base.OnSourceInitialized(e);
            DialogResult = true; // مؤقت - سيتم استبداله بنافذة حقيقية
        }
    }

    /// <summary>
    /// نافذة عرض تفاصيل الموكل الشاملة (مؤقتة)
    /// </summary>
    public class ComprehensiveViewClientDialog : Window
    {
        public ComprehensiveViewClientDialog(ComprehensiveClientModel client)
        {
            Title = $"تفاصيل الموكل: {client.Name}";
            Width = 700;
            Height = 600;
            WindowStartupLocation = WindowStartupLocation.CenterOwner;

            var content = new TextBlock
            {
                Text = $"الاسم: {client.Name}\nالنوع: {client.ClientType}\nمرجع المكتب: {client.OfficeRef}\nالهاتف: {client.Phone}\nالبريد: {client.Email}\nالعنوان: {client.Address}\nعدد القضايا: {client.CasesCount}\nالحالة: {client.Status}\nتاريخ الإضافة: {client.CreatedDate:dd/MM/yyyy}\nآخر اتصال: {client.LastContactDate:dd/MM/yyyy}\nإجمالي الأتعاب: {client.TotalFees:N0} ريال\nالملاحظات: {client.Notes}",
                Margin = new Thickness(20),
                FontSize = 14,
                TextWrapping = TextWrapping.Wrap,
                LineHeight = 25
            };

            Content = content;
        }
    }

    #endregion
}
