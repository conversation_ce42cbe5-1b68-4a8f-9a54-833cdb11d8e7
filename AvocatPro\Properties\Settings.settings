<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="AvocatPro.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <!-- إعدادات تسجيل الدخول -->
    <Setting Name="RememberLogin" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="SavedUsername" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>

    <!-- إعدادات النافذة الرئيسية -->
    <Setting Name="WindowLeft" Type="System.Double" Scope="User">
      <Value Profile="(Default)">100</Value>
    </Setting>
    <Setting Name="WindowTop" Type="System.Double" Scope="User">
      <Value Profile="(Default)">100</Value>
    </Setting>
    <Setting Name="WindowWidth" Type="System.Double" Scope="User">
      <Value Profile="(Default)">1400</Value>
    </Setting>
    <Setting Name="WindowHeight" Type="System.Double" Scope="User">
      <Value Profile="(Default)">900</Value>
    </Setting>
    <Setting Name="WindowState" Type="System.Windows.WindowState" Scope="User">
      <Value Profile="(Default)">Maximized</Value>
    </Setting>

    <!-- إعدادات قاعدة البيانات -->
    <Setting Name="DatabaseConnectionString" Type="System.String" Scope="Application">
      <Value Profile="(Default)">Data Source=AvocatPro.db;Version=3;</Value>
    </Setting>

    <!-- إعدادات المظهر -->
    <Setting Name="Theme" Type="System.String" Scope="User">
      <Value Profile="(Default)">Light</Value>
    </Setting>
    <Setting Name="AccentColor" Type="System.String" Scope="User">
      <Value Profile="(Default)">#6366F1</Value>
    </Setting>
    <Setting Name="FontSize" Type="System.Double" Scope="User">
      <Value Profile="(Default)">14</Value>
    </Setting>

    <!-- إعدادات اللغة والمنطقة -->
    <Setting Name="Language" Type="System.String" Scope="User">
      <Value Profile="(Default)">ar-MA</Value>
    </Setting>
    <Setting Name="Currency" Type="System.String" Scope="User">
      <Value Profile="(Default)">MAD</Value>
    </Setting>

    <!-- إعدادات النسخ الاحتياطي -->
    <Setting Name="AutoBackupEnabled" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="BackupInterval" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">24</Value>
    </Setting>
    <Setting Name="BackupPath" Type="System.String" Scope="User">
      <Value Profile="(Default)">C:\AvocatPro\Backups</Value>
    </Setting>

    <!-- إعدادات الإشعارات -->
    <Setting Name="NotificationsEnabled" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="EmailNotifications" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="SoundNotifications" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>

    <!-- إعدادات الأمان -->
    <Setting Name="SessionTimeout" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">30</Value>
    </Setting>
    <Setting Name="RequirePasswordChange" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="EnableAuditLog" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>

    <!-- إعدادات التقارير -->
    <Setting Name="DefaultReportFormat" Type="System.String" Scope="User">
      <Value Profile="(Default)">PDF</Value>
    </Setting>
    <Setting Name="ReportsPath" Type="System.String" Scope="User">
      <Value Profile="(Default)">C:\AvocatPro\Reports</Value>
    </Setting>

    <!-- إعدادات الطباعة -->
    <Setting Name="DefaultPrinter" Type="System.String" Scope="User">
      <Value Profile="(Default)"></Value>
    </Setting>
    <Setting Name="PrintOrientation" Type="System.String" Scope="User">
      <Value Profile="(Default)">Portrait</Value>
    </Setting>

    <!-- إعدادات المكتب -->
    <Setting Name="OfficeName" Type="System.String" Scope="User">
      <Value Profile="(Default)">مكتب المحاماة</Value>
    </Setting>
    <Setting Name="OfficeAddress" Type="System.String" Scope="User">
      <Value Profile="(Default)">الرباط، المملكة المغربية</Value>
    </Setting>
    <Setting Name="OfficePhone" Type="System.String" Scope="User">
      <Value Profile="(Default)">+212 537 123 456</Value>
    </Setting>
    <Setting Name="OfficeEmail" Type="System.String" Scope="User">
      <Value Profile="(Default)"><EMAIL></Value>
    </Setting>

    <!-- إعدادات متقدمة -->
    <Setting Name="EnableAdvancedFeatures" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="LogLevel" Type="System.String" Scope="User">
      <Value Profile="(Default)">Info</Value>
    </Setting>
    <Setting Name="MaxLogFileSize" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">10</Value>
    </Setting>
  </Settings>
</SettingsFile>
