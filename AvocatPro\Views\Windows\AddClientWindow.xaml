<Window x:Class="AvocatPro.Views.Windows.AddClientWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة موكل جديد" 
        Height="700" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">

    <Window.Resources>
        <!-- أنماط النموذج -->
        <Style x:Key="FormLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#333"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>

        <Style x:Key="FormTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource ModernTextBoxStyle}">
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="FormComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource ModernComboBoxStyle}">
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1976D2"/>
            <Setter Property="Margin" Value="0,20,0,15"/>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#1976D2"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1565C0"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#F5F5F5"/>
            <Setter Property="Foreground" Value="#333"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#E0E0E0"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- رأس النافذة -->
        <Border Grid.Row="0" Background="#1976D2" Padding="20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="👤" FontSize="24" Foreground="White" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="إضافة موكل جديد" FontSize="20" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="أدخل بيانات الموكل الجديد بعناية" FontSize="12" Foreground="#E3F2FD"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- محتوى النموذج -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="30">
            <StackPanel>
                <!-- المعلومات الأساسية -->
                <TextBlock Text="📋 المعلومات الأساسية" Style="{StaticResource SectionHeaderStyle}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="الاسم الكامل *" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="FullNameTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="أدخل الاسم الكامل للموكل"/>

                        <TextBlock Text="نوع الموكل *" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="ClientTypeComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="فرد" Tag="Individual" IsSelected="True"/>
                            <ComboBoxItem Content="شركة" Tag="Company"/>
                            <ComboBoxItem Content="مؤسسة حكومية" Tag="Government"/>
                            <ComboBoxItem Content="جمعية" Tag="Association"/>
                        </ComboBox>

                        <TextBlock Text="مرجع المكتب" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="OfficeReferenceTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="رقم مرجع داخلي للمكتب"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="رقم الهوية/السجل التجاري" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="IdentityNumberTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="رقم الهوية للأفراد أو السجل التجاري للشركات"/>

                        <TextBlock Text="الجنسية" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="NationalityComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="سعودي" IsSelected="True"/>
                            <ComboBoxItem Content="مصري"/>
                            <ComboBoxItem Content="أردني"/>
                            <ComboBoxItem Content="لبناني"/>
                            <ComboBoxItem Content="سوري"/>
                            <ComboBoxItem Content="أخرى"/>
                        </ComboBox>

                        <TextBlock Text="مرجع الملف" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="FileReferenceTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="رقم مرجع الملف المرتبط"/>
                    </StackPanel>
                </Grid>

                <!-- معلومات الاتصال -->
                <TextBlock Text="📞 معلومات الاتصال" Style="{StaticResource SectionHeaderStyle}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="رقم الهاتف الأساسي *" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="PrimaryPhoneTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="رقم الهاتف الأساسي"/>

                        <TextBlock Text="رقم الهاتف الثانوي" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="SecondaryPhoneTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="رقم هاتف إضافي (اختياري)"/>

                        <TextBlock Text="البريد الإلكتروني" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="EmailTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="عنوان البريد الإلكتروني"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="العنوان الكامل" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="AddressTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Height="80" TextWrapping="Wrap" AcceptsReturn="True"
                                Text="" ToolTip="العنوان الكامل للموكل"/>

                        <TextBlock Text="المدينة" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="CityComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="الرياض" IsSelected="True"/>
                            <ComboBoxItem Content="جدة"/>
                            <ComboBoxItem Content="الدمام"/>
                            <ComboBoxItem Content="مكة المكرمة"/>
                            <ComboBoxItem Content="المدينة المنورة"/>
                            <ComboBoxItem Content="أخرى"/>
                        </ComboBox>
                    </StackPanel>
                </Grid>

                <!-- معلومات إضافية -->
                <TextBlock Text="📝 معلومات إضافية" Style="{StaticResource SectionHeaderStyle}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="المهنة/النشاط التجاري" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="ProfessionTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="مهنة الموكل أو نشاطه التجاري"/>

                        <TextBlock Text="حالة الموكل" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="StatusComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="نشط" Tag="Active" IsSelected="True"/>
                            <ComboBoxItem Content="غير نشط" Tag="Inactive"/>
                            <ComboBoxItem Content="محظور" Tag="Blocked"/>
                        </ComboBox>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="ملاحظات" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="NotesTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Height="80" TextWrapping="Wrap" AcceptsReturn="True"
                                Text="" ToolTip="ملاحظات إضافية عن الموكل"/>
                    </StackPanel>
                </Grid>
            </StackPanel>
        </ScrollViewer>

        <!-- أزرار الإجراءات -->
        <Border Grid.Row="2" Background="#F8F9FA" Padding="20" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Name="SaveButton" Style="{StaticResource PrimaryButtonStyle}" Click="SaveButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="💾" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ الموكل"/>
                    </StackPanel>
                </Button>
                
                <Button Name="SaveAndNewButton" Style="{StaticResource PrimaryButtonStyle}" 
                       Background="#4CAF50" Click="SaveAndNewButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="➕" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ وإضافة آخر"/>
                    </StackPanel>
                </Button>
                
                <Button Name="CancelButton" Style="{StaticResource SecondaryButtonStyle}" Click="CancelButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="❌" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="إلغاء"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
