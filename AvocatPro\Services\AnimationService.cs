using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using AvocatPro.Models;

namespace AvocatPro.Services
{
    public class AnimationService
    {
        private readonly TimeSpan _defaultDuration = TimeSpan.FromMilliseconds(300);
        private readonly IEasingFunction _defaultEasing = new CubicEase { EasingMode = EasingMode.EaseOut };

        // 🎭 انتقالات الصفحات
        public async Task AnimatePageTransition(FrameworkElement oldPage, FrameworkElement newPage, PageTransitionType transitionType = PageTransitionType.SlideLeft)
        {
            if (oldPage == null && newPage == null) return;

            switch (transitionType)
            {
                case PageTransitionType.SlideLeft:
                    await AnimateSlideTransition(oldPage, newPage, SlideDirection.Left);
                    break;
                case PageTransitionType.SlideRight:
                    await AnimateSlideTransition(oldPage, newPage, SlideDirection.Right);
                    break;
                case PageTransitionType.SlideUp:
                    await AnimateSlideTransition(oldPage, newPage, SlideDirection.Up);
                    break;
                case PageTransitionType.SlideDown:
                    await AnimateSlideTransition(oldPage, newPage, SlideDirection.Down);
                    break;
                case PageTransitionType.Fade:
                    await AnimateFadeTransition(oldPage, newPage);
                    break;
                case PageTransitionType.Scale:
                    await AnimateScaleTransition(oldPage, newPage);
                    break;
                case PageTransitionType.Flip:
                    await AnimateFlipTransition(oldPage, newPage);
                    break;
            }
        }

        // 🔄 انتقالات الانزلاق
        private async Task AnimateSlideTransition(FrameworkElement oldPage, FrameworkElement newPage, SlideDirection direction)
        {
            if (oldPage?.Parent is Panel container)
            {
                var containerWidth = container.ActualWidth;
                var containerHeight = container.ActualHeight;

                // إعداد الصفحة الجديدة
                if (newPage != null)
                {
                    var newTransform = new TranslateTransform();
                    newPage.RenderTransform = newTransform;

                    switch (direction)
                    {
                        case SlideDirection.Left:
                            newTransform.X = containerWidth;
                            break;
                        case SlideDirection.Right:
                            newTransform.X = -containerWidth;
                            break;
                        case SlideDirection.Up:
                            newTransform.Y = containerHeight;
                            break;
                        case SlideDirection.Down:
                            newTransform.Y = -containerHeight;
                            break;
                    }

                    container.Children.Add(newPage);
                }

                // أنيميشن الصفحة القديمة
                if (oldPage != null)
                {
                    var oldTransform = new TranslateTransform();
                    oldPage.RenderTransform = oldTransform;

                    var oldAnimation = new DoubleAnimation
                    {
                        Duration = _defaultDuration,
                        EasingFunction = _defaultEasing
                    };

                    switch (direction)
                    {
                        case SlideDirection.Left:
                            oldAnimation.To = -containerWidth;
                            oldTransform.BeginAnimation(TranslateTransform.XProperty, oldAnimation);
                            break;
                        case SlideDirection.Right:
                            oldAnimation.To = containerWidth;
                            oldTransform.BeginAnimation(TranslateTransform.XProperty, oldAnimation);
                            break;
                        case SlideDirection.Up:
                            oldAnimation.To = -containerHeight;
                            oldTransform.BeginAnimation(TranslateTransform.YProperty, oldAnimation);
                            break;
                        case SlideDirection.Down:
                            oldAnimation.To = containerHeight;
                            oldTransform.BeginAnimation(TranslateTransform.YProperty, oldAnimation);
                            break;
                    }
                }

                // أنيميشن الصفحة الجديدة
                if (newPage != null)
                {
                    var newTransform = (TranslateTransform)newPage.RenderTransform;
                    var newAnimation = new DoubleAnimation
                    {
                        To = 0,
                        Duration = _defaultDuration,
                        EasingFunction = _defaultEasing
                    };

                    switch (direction)
                    {
                        case SlideDirection.Left:
                        case SlideDirection.Right:
                            newTransform.BeginAnimation(TranslateTransform.XProperty, newAnimation);
                            break;
                        case SlideDirection.Up:
                        case SlideDirection.Down:
                            newTransform.BeginAnimation(TranslateTransform.YProperty, newAnimation);
                            break;
                    }
                }

                await Task.Delay(_defaultDuration);

                // تنظيف الصفحة القديمة
                if (oldPage != null && container.Children.Contains(oldPage))
                {
                    container.Children.Remove(oldPage);
                }
            }
        }

        // 🌫️ انتقال التلاشي
        private async Task AnimateFadeTransition(FrameworkElement oldPage, FrameworkElement newPage)
        {
            if (oldPage?.Parent is Panel container)
            {
                // إعداد الصفحة الجديدة
                if (newPage != null)
                {
                    newPage.Opacity = 0;
                    container.Children.Add(newPage);

                    var fadeInAnimation = new DoubleAnimation
                    {
                        From = 0,
                        To = 1,
                        Duration = _defaultDuration,
                        EasingFunction = _defaultEasing
                    };

                    newPage.BeginAnimation(UIElement.OpacityProperty, fadeInAnimation);
                }

                // أنيميشن الصفحة القديمة
                if (oldPage != null)
                {
                    var fadeOutAnimation = new DoubleAnimation
                    {
                        From = 1,
                        To = 0,
                        Duration = _defaultDuration,
                        EasingFunction = _defaultEasing
                    };

                    oldPage.BeginAnimation(UIElement.OpacityProperty, fadeOutAnimation);
                }

                await Task.Delay(_defaultDuration);

                // تنظيف الصفحة القديمة
                if (oldPage != null && container.Children.Contains(oldPage))
                {
                    container.Children.Remove(oldPage);
                }
            }
        }

        // 📏 انتقال التكبير/التصغير
        private async Task AnimateScaleTransition(FrameworkElement oldPage, FrameworkElement newPage)
        {
            if (oldPage?.Parent is Panel container)
            {
                // إعداد الصفحة الجديدة
                if (newPage != null)
                {
                    var scaleTransform = new ScaleTransform(0.8, 0.8);
                    var transformGroup = new TransformGroup();
                    transformGroup.Children.Add(scaleTransform);
                    newPage.RenderTransform = transformGroup;
                    newPage.RenderTransformOrigin = new Point(0.5, 0.5);
                    newPage.Opacity = 0;

                    container.Children.Add(newPage);

                    var scaleAnimation = new DoubleAnimation
                    {
                        From = 0.8,
                        To = 1.0,
                        Duration = _defaultDuration,
                        EasingFunction = new BackEase { EasingMode = EasingMode.EaseOut, Amplitude = 0.3 }
                    };

                    var fadeAnimation = new DoubleAnimation
                    {
                        From = 0,
                        To = 1,
                        Duration = _defaultDuration,
                        EasingFunction = _defaultEasing
                    };

                    scaleTransform.BeginAnimation(ScaleTransform.ScaleXProperty, scaleAnimation);
                    scaleTransform.BeginAnimation(ScaleTransform.ScaleYProperty, scaleAnimation);
                    newPage.BeginAnimation(UIElement.OpacityProperty, fadeAnimation);
                }

                // أنيميشن الصفحة القديمة
                if (oldPage != null)
                {
                    var oldScaleTransform = new ScaleTransform(1.0, 1.0);
                    oldPage.RenderTransform = oldScaleTransform;
                    oldPage.RenderTransformOrigin = new Point(0.5, 0.5);

                    var scaleOutAnimation = new DoubleAnimation
                    {
                        From = 1.0,
                        To = 1.1,
                        Duration = _defaultDuration,
                        EasingFunction = _defaultEasing
                    };

                    var fadeOutAnimation = new DoubleAnimation
                    {
                        From = 1,
                        To = 0,
                        Duration = _defaultDuration,
                        EasingFunction = _defaultEasing
                    };

                    oldScaleTransform.BeginAnimation(ScaleTransform.ScaleXProperty, scaleOutAnimation);
                    oldScaleTransform.BeginAnimation(ScaleTransform.ScaleYProperty, scaleOutAnimation);
                    oldPage.BeginAnimation(UIElement.OpacityProperty, fadeOutAnimation);
                }

                await Task.Delay(_defaultDuration);

                // تنظيف الصفحة القديمة
                if (oldPage != null && container.Children.Contains(oldPage))
                {
                    container.Children.Remove(oldPage);
                }
            }
        }

        // 🔄 انتقال القلب
        private async Task AnimateFlipTransition(FrameworkElement oldPage, FrameworkElement newPage)
        {
            if (oldPage?.Parent is Panel container)
            {
                var flipDuration = TimeSpan.FromMilliseconds(150);

                // أنيميشن الصفحة القديمة (النصف الأول من القلب)
                if (oldPage != null)
                {
                    var scaleTransform = new ScaleTransform(1.0, 1.0);
                    oldPage.RenderTransform = scaleTransform;
                    oldPage.RenderTransformOrigin = new Point(0.5, 0.5);

                    var scaleAnimation = new DoubleAnimation
                    {
                        From = 1.0,
                        To = 0.0,
                        Duration = flipDuration,
                        EasingFunction = _defaultEasing
                    };

                    scaleTransform.BeginAnimation(ScaleTransform.ScaleXProperty, scaleAnimation);
                }

                await Task.Delay(flipDuration);

                // إزالة الصفحة القديمة وإضافة الجديدة
                if (oldPage != null && container.Children.Contains(oldPage))
                {
                    container.Children.Remove(oldPage);
                }

                if (newPage != null)
                {
                    var scaleTransform = new ScaleTransform(0.0, 1.0);
                    newPage.RenderTransform = scaleTransform;
                    newPage.RenderTransformOrigin = new Point(0.5, 0.5);

                    container.Children.Add(newPage);

                    var scaleAnimation = new DoubleAnimation
                    {
                        From = 0.0,
                        To = 1.0,
                        Duration = flipDuration,
                        EasingFunction = _defaultEasing
                    };

                    scaleTransform.BeginAnimation(ScaleTransform.ScaleXProperty, scaleAnimation);
                }

                await Task.Delay(flipDuration);
            }
        }

        // ✨ تأثيرات العناصر
        public async Task AnimateElementEntry(FrameworkElement element, ElementAnimationType animationType = ElementAnimationType.FadeIn)
        {
            switch (animationType)
            {
                case ElementAnimationType.FadeIn:
                    await AnimateFadeIn(element);
                    break;
                case ElementAnimationType.SlideInFromLeft:
                    await AnimateSlideIn(element, SlideDirection.Left);
                    break;
                case ElementAnimationType.SlideInFromRight:
                    await AnimateSlideIn(element, SlideDirection.Right);
                    break;
                case ElementAnimationType.SlideInFromTop:
                    await AnimateSlideIn(element, SlideDirection.Up);
                    break;
                case ElementAnimationType.SlideInFromBottom:
                    await AnimateSlideIn(element, SlideDirection.Down);
                    break;
                case ElementAnimationType.ScaleIn:
                    await AnimateScaleIn(element);
                    break;
                case ElementAnimationType.Bounce:
                    await AnimateBounceIn(element);
                    break;
            }
        }

        public async Task AnimateElementExit(FrameworkElement element, ElementAnimationType animationType = ElementAnimationType.FadeOut)
        {
            switch (animationType)
            {
                case ElementAnimationType.FadeOut:
                    await AnimateFadeOut(element);
                    break;
                case ElementAnimationType.SlideOutToLeft:
                    await AnimateSlideOut(element, SlideDirection.Left);
                    break;
                case ElementAnimationType.SlideOutToRight:
                    await AnimateSlideOut(element, SlideDirection.Right);
                    break;
                case ElementAnimationType.SlideOutToTop:
                    await AnimateSlideOut(element, SlideDirection.Up);
                    break;
                case ElementAnimationType.SlideOutToBottom:
                    await AnimateSlideOut(element, SlideDirection.Down);
                    break;
                case ElementAnimationType.ScaleOut:
                    await AnimateScaleOut(element);
                    break;
            }
        }

        private async Task AnimateFadeOut(FrameworkElement element)
        {
            var animation = new DoubleAnimation
            {
                From = element.Opacity,
                To = 0,
                Duration = _defaultDuration,
                EasingFunction = _defaultEasing
            };

            element.BeginAnimation(UIElement.OpacityProperty, animation);
            await Task.Delay(_defaultDuration);
        }

        private async Task AnimateSlideOut(FrameworkElement element, SlideDirection direction)
        {
            var transform = element.RenderTransform as TranslateTransform ?? new TranslateTransform();
            element.RenderTransform = transform;

            var distance = 50.0;
            double toX = 0, toY = 0;

            switch (direction)
            {
                case SlideDirection.Left:
                    toX = -distance;
                    break;
                case SlideDirection.Right:
                    toX = distance;
                    break;
                case SlideDirection.Up:
                    toY = -distance;
                    break;
                case SlideDirection.Down:
                    toY = distance;
                    break;
            }

            var animationX = new DoubleAnimation
            {
                From = transform.X,
                To = toX,
                Duration = _defaultDuration,
                EasingFunction = _defaultEasing
            };

            var animationY = new DoubleAnimation
            {
                From = transform.Y,
                To = toY,
                Duration = _defaultDuration,
                EasingFunction = _defaultEasing
            };

            transform.BeginAnimation(TranslateTransform.XProperty, animationX);
            transform.BeginAnimation(TranslateTransform.YProperty, animationY);
            await Task.Delay(_defaultDuration);
        }

        private async Task AnimateScaleOut(FrameworkElement element)
        {
            var transform = new ScaleTransform();
            element.RenderTransform = transform;
            element.RenderTransformOrigin = new Point(0.5, 0.5);

            var animationX = new DoubleAnimation
            {
                From = 1,
                To = 0,
                Duration = _defaultDuration,
                EasingFunction = _defaultEasing
            };

            var animationY = new DoubleAnimation
            {
                From = 1,
                To = 0,
                Duration = _defaultDuration,
                EasingFunction = _defaultEasing
            };

            transform.BeginAnimation(ScaleTransform.ScaleXProperty, animationX);
            transform.BeginAnimation(ScaleTransform.ScaleYProperty, animationY);
            await Task.Delay(_defaultDuration);
        }

        private async Task AnimateFadeIn(FrameworkElement element)
        {
            element.Opacity = 0;
            var animation = new DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = _defaultDuration,
                EasingFunction = _defaultEasing
            };

            element.BeginAnimation(UIElement.OpacityProperty, animation);
            await Task.Delay(_defaultDuration);
        }

        private async Task AnimateSlideIn(FrameworkElement element, SlideDirection direction)
        {
            var transform = new TranslateTransform();
            element.RenderTransform = transform;

            var distance = 50.0;
            switch (direction)
            {
                case SlideDirection.Left:
                    transform.X = -distance;
                    break;
                case SlideDirection.Right:
                    transform.X = distance;
                    break;
                case SlideDirection.Up:
                    transform.Y = -distance;
                    break;
                case SlideDirection.Down:
                    transform.Y = distance;
                    break;
            }

            element.Opacity = 0;

            var slideAnimation = new DoubleAnimation
            {
                To = 0,
                Duration = _defaultDuration,
                EasingFunction = _defaultEasing
            };

            var fadeAnimation = new DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = _defaultDuration,
                EasingFunction = _defaultEasing
            };

            switch (direction)
            {
                case SlideDirection.Left:
                case SlideDirection.Right:
                    transform.BeginAnimation(TranslateTransform.XProperty, slideAnimation);
                    break;
                case SlideDirection.Up:
                case SlideDirection.Down:
                    transform.BeginAnimation(TranslateTransform.YProperty, slideAnimation);
                    break;
            }

            element.BeginAnimation(UIElement.OpacityProperty, fadeAnimation);
            await Task.Delay(_defaultDuration);
        }

        private async Task AnimateScaleIn(FrameworkElement element)
        {
            var transform = new ScaleTransform(0.5, 0.5);
            element.RenderTransform = transform;
            element.RenderTransformOrigin = new Point(0.5, 0.5);
            element.Opacity = 0;

            var scaleAnimation = new DoubleAnimation
            {
                From = 0.5,
                To = 1.0,
                Duration = _defaultDuration,
                EasingFunction = new BackEase { EasingMode = EasingMode.EaseOut, Amplitude = 0.3 }
            };

            var fadeAnimation = new DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = _defaultDuration,
                EasingFunction = _defaultEasing
            };

            transform.BeginAnimation(ScaleTransform.ScaleXProperty, scaleAnimation);
            transform.BeginAnimation(ScaleTransform.ScaleYProperty, scaleAnimation);
            element.BeginAnimation(UIElement.OpacityProperty, fadeAnimation);

            await Task.Delay(_defaultDuration);
        }

        private async Task AnimateBounceIn(FrameworkElement element)
        {
            var transform = new ScaleTransform(0.3, 0.3);
            element.RenderTransform = transform;
            element.RenderTransformOrigin = new Point(0.5, 0.5);

            var bounceAnimation = new DoubleAnimation
            {
                From = 0.3,
                To = 1.0,
                Duration = TimeSpan.FromMilliseconds(600),
                EasingFunction = new BounceEase { EasingMode = EasingMode.EaseOut, Bounces = 2, Bounciness = 2 }
            };

            transform.BeginAnimation(ScaleTransform.ScaleXProperty, bounceAnimation);
            transform.BeginAnimation(ScaleTransform.ScaleYProperty, bounceAnimation);

            await Task.Delay(600);
        }

        // 🎯 تأثيرات التفاعل
        public void AnimateButtonClick(Button button)
        {
            var transform = new ScaleTransform(1.0, 1.0);
            button.RenderTransform = transform;
            button.RenderTransformOrigin = new Point(0.5, 0.5);

            var scaleDown = new DoubleAnimation
            {
                From = 1.0,
                To = 0.95,
                Duration = TimeSpan.FromMilliseconds(100),
                AutoReverse = true,
                EasingFunction = _defaultEasing
            };

            transform.BeginAnimation(ScaleTransform.ScaleXProperty, scaleDown);
            transform.BeginAnimation(ScaleTransform.ScaleYProperty, scaleDown);
        }

        public void AnimateHover(FrameworkElement element, bool isEntering)
        {
            var transform = element.RenderTransform as ScaleTransform ?? new ScaleTransform(1.0, 1.0);
            element.RenderTransform = transform;
            element.RenderTransformOrigin = new Point(0.5, 0.5);

            var targetScale = isEntering ? 1.05 : 1.0;
            var scaleAnimation = new DoubleAnimation
            {
                To = targetScale,
                Duration = TimeSpan.FromMilliseconds(200),
                EasingFunction = _defaultEasing
            };

            transform.BeginAnimation(ScaleTransform.ScaleXProperty, scaleAnimation);
            transform.BeginAnimation(ScaleTransform.ScaleYProperty, scaleAnimation);
        }

        // 🔔 تأثيرات الإشعارات
        public async Task AnimateNotification(FrameworkElement notification)
        {
            // انزلاق من الأعلى
            var transform = new TranslateTransform(0, -100);
            notification.RenderTransform = transform;
            notification.Opacity = 0;

            var slideAnimation = new DoubleAnimation
            {
                From = -100,
                To = 0,
                Duration = TimeSpan.FromMilliseconds(400),
                EasingFunction = new BackEase { EasingMode = EasingMode.EaseOut }
            };

            var fadeAnimation = new DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = TimeSpan.FromMilliseconds(400),
                EasingFunction = _defaultEasing
            };

            transform.BeginAnimation(TranslateTransform.YProperty, slideAnimation);
            notification.BeginAnimation(UIElement.OpacityProperty, fadeAnimation);

            await Task.Delay(400);
        }
    }

    // 📊 نماذج البيانات للأنيميشن
    public enum PageTransitionType
    {
        SlideLeft,
        SlideRight,
        SlideUp,
        SlideDown,
        Fade,
        Scale,
        Flip
    }

    public enum SlideDirection
    {
        Left,
        Right,
        Up,
        Down
    }

    public enum ElementAnimationType
    {
        FadeIn,
        FadeOut,
        SlideInFromLeft,
        SlideInFromRight,
        SlideInFromTop,
        SlideInFromBottom,
        SlideOutToLeft,
        SlideOutToRight,
        SlideOutToTop,
        SlideOutToBottom,
        ScaleIn,
        ScaleOut,
        Bounce
    }
}
