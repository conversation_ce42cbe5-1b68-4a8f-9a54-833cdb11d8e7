<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('legal_cases', function (Blueprint $table) {
            $table->id();
            $table->string('case_number')->unique();
            $table->string('title');
            $table->text('description')->nullable();
            $table->foreignId('client_id')->constrained('clients')->onDelete('cascade');
            $table->enum('case_type', [
                'civil', 'commercial', 'criminal', 'administrative', 
                'family', 'labor', 'real_estate', 'intellectual_property',
                'tax', 'constitutional', 'international'
            ]);
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->enum('status', [
                'draft', 'active', 'pending', 'on_hold', 
                'closed', 'won', 'lost', 'settled'
            ])->default('draft');
            $table->string('court_name')->nullable();
            $table->string('court_id')->nullable(); // For Moroccan courts integration
            $table->string('judge_name')->nullable();
            $table->string('opponent_name')->nullable();
            $table->text('opponent_details')->nullable();
            $table->string('opponent_lawyer')->nullable();
            $table->date('filing_date')->nullable();
            $table->date('first_hearing_date')->nullable();
            $table->date('last_hearing_date')->nullable();
            $table->date('next_hearing_date')->nullable();
            $table->decimal('claim_amount', 15, 2)->nullable();
            $table->string('currency', 3)->default('MAD');
            $table->decimal('court_fees', 10, 2)->nullable();
            $table->decimal('lawyer_fees', 10, 2)->nullable();
            $table->decimal('other_expenses', 10, 2)->nullable();
            $table->text('case_summary')->nullable();
            $table->text('legal_basis')->nullable();
            $table->text('evidence_summary')->nullable();
            $table->text('strategy_notes')->nullable();
            $table->json('tags')->nullable();
            $table->json('custom_fields')->nullable();
            $table->foreignId('assigned_lawyer')->nullable()->constrained('users');
            $table->foreignId('created_by')->nullable()->constrained('users');
            $table->foreignId('updated_by')->nullable()->constrained('users');
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index('case_number');
            $table->index(['client_id', 'status']);
            $table->index('case_type');
            $table->index('status');
            $table->index('priority');
            $table->index('filing_date');
            $table->index('next_hearing_date');
            $table->index('assigned_lawyer');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('legal_cases');
    }
};
