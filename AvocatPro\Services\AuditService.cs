using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AvocatPro.Data;
using AvocatPro.Models;
using Microsoft.EntityFrameworkCore;

namespace AvocatPro.Services
{
    public class AuditService
    {
        private readonly List<AuditLog> _auditLogs; // محاكاة قاعدة البيانات

        public AuditService()
        {
            _auditLogs = new List<AuditLog>();
        }

        // 📝 تسجيل النشاطات
        public async Task LogActivityAsync(int userId, string action, string details, string ipAddress = "", string userAgent = "")
        {
            try
            {
                var auditLog = new AuditLog
                {
                    Id = _auditLogs.Count + 1,
                    UserId = userId,
                    Action = action,
                    Details = details,
                    Timestamp = DateTime.Now,
                    IpAddress = ipAddress,
                    UserAgent = userAgent
                };

                _auditLogs.Add(auditLog);
                await Task.CompletedTask;

                // في الإنتاج، يجب حفظ هذا في قاعدة البيانات
                System.Diagnostics.Debug.WriteLine($"[AUDIT] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - User {userId}: {action} - {details}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تسجيل النشاط: {ex.Message}");
            }
        }

        // 📊 استرجاع سجلات المراجعة
        public async Task<List<AuditLog>> GetUserAuditLogsAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            await Task.CompletedTask;

            var query = _auditLogs.Where(log => log.UserId == userId);

            if (fromDate.HasValue)
                query = query.Where(log => log.Timestamp >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(log => log.Timestamp <= toDate.Value);

            return query.OrderByDescending(log => log.Timestamp).ToList();
        }

        public async Task<List<AuditLog>> GetAllAuditLogsAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            await Task.CompletedTask;

            var query = _auditLogs.AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(log => log.Timestamp >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(log => log.Timestamp <= toDate.Value);

            return query.OrderByDescending(log => log.Timestamp).ToList();
        }

        // 🔍 البحث في السجلات
        public async Task<List<AuditLog>> SearchAuditLogsAsync(string searchTerm, int? userId = null)
        {
            await Task.CompletedTask;

            var query = _auditLogs.AsQueryable();

            if (userId.HasValue)
                query = query.Where(log => log.UserId == userId.Value);

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(log => 
                    log.Action.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    log.Details.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));
            }

            return query.OrderByDescending(log => log.Timestamp).ToList();
        }

        // 📈 إحصائيات النشاطات
        public async Task<AuditStatistics> GetAuditStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            await Task.CompletedTask;

            var query = _auditLogs.AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(log => log.Timestamp >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(log => log.Timestamp <= toDate.Value);

            var logs = query.ToList();

            return new AuditStatistics
            {
                TotalActivities = logs.Count,
                UniqueUsers = logs.Select(log => log.UserId).Distinct().Count(),
                MostActiveUser = GetMostActiveUser(logs),
                MostCommonAction = GetMostCommonAction(logs),
                ActivitiesByHour = GetActivitiesByHour(logs),
                ActivitiesByDay = GetActivitiesByDay(logs),
                SecurityEvents = logs.Count(log => IsSecurityEvent(log.Action))
            };
        }

        // 🚨 تنبيهات الأمان
        public async Task<List<SecurityAlert>> GetSecurityAlertsAsync()
        {
            await Task.CompletedTask;

            var alerts = new List<SecurityAlert>();
            var recentLogs = _auditLogs.Where(log => log.Timestamp >= DateTime.Now.AddHours(-24)).ToList();

            // تحقق من محاولات تسجيل الدخول المتعددة الفاشلة
            var failedLogins = recentLogs
                .Where(log => log.Action.Contains("فشل تسجيل الدخول"))
                .GroupBy(log => log.UserId)
                .Where(group => group.Count() >= 5)
                .ToList();

            foreach (var group in failedLogins)
            {
                alerts.Add(new SecurityAlert
                {
                    Type = SecurityAlertType.MultipleFailedLogins,
                    Message = $"محاولات تسجيل دخول فاشلة متعددة للمستخدم {group.Key}",
                    Severity = AlertSeverity.High,
                    Timestamp = DateTime.Now,
                    UserId = group.Key
                });
            }

            // تحقق من النشاطات المشبوهة
            var suspiciousActivities = recentLogs
                .Where(log => IsSuspiciousActivity(log))
                .ToList();

            foreach (var activity in suspiciousActivities)
            {
                alerts.Add(new SecurityAlert
                {
                    Type = SecurityAlertType.SuspiciousActivity,
                    Message = $"نشاط مشبوه: {activity.Action}",
                    Severity = AlertSeverity.Medium,
                    Timestamp = activity.Timestamp,
                    UserId = activity.UserId
                });
            }

            return alerts.OrderByDescending(alert => alert.Timestamp).ToList();
        }

        // 🧹 تنظيف السجلات القديمة
        public async Task CleanupOldLogsAsync(int daysToKeep = 365)
        {
            await Task.CompletedTask;

            var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
            var logsToRemove = _auditLogs.Where(log => log.Timestamp < cutoffDate).ToList();

            foreach (var log in logsToRemove)
            {
                _auditLogs.Remove(log);
            }

            System.Diagnostics.Debug.WriteLine($"تم حذف {logsToRemove.Count} سجل قديم");
        }

        // 🔧 دوال مساعدة
        private int GetMostActiveUser(List<AuditLog> logs)
        {
            return logs.GroupBy(log => log.UserId)
                      .OrderByDescending(group => group.Count())
                      .FirstOrDefault()?.Key ?? 0;
        }

        private string GetMostCommonAction(List<AuditLog> logs)
        {
            return logs.GroupBy(log => log.Action)
                      .OrderByDescending(group => group.Count())
                      .FirstOrDefault()?.Key ?? "";
        }

        private Dictionary<int, int> GetActivitiesByHour(List<AuditLog> logs)
        {
            return logs.GroupBy(log => log.Timestamp.Hour)
                      .ToDictionary(group => group.Key, group => group.Count());
        }

        private Dictionary<string, int> GetActivitiesByDay(List<AuditLog> logs)
        {
            return logs.GroupBy(log => log.Timestamp.Date.ToString("yyyy-MM-dd"))
                      .ToDictionary(group => group.Key, group => group.Count());
        }

        private bool IsSecurityEvent(string action)
        {
            var securityKeywords = new[] { "تسجيل الدخول", "تسجيل الخروج", "تغيير كلمة المرور", "مصادقة ثنائية", "فشل" };
            return securityKeywords.Any(keyword => action.Contains(keyword, StringComparison.OrdinalIgnoreCase));
        }

        private bool IsSuspiciousActivity(AuditLog log)
        {
            // تحديد النشاطات المشبوهة
            var suspiciousKeywords = new[] { "حذف", "تعديل حساس", "وصول غير مصرح" };
            return suspiciousKeywords.Any(keyword => log.Action.Contains(keyword, StringComparison.OrdinalIgnoreCase));
        }
    }

    // 📊 نماذج البيانات للمراجعة
    public class AuditStatistics
    {
        public int TotalActivities { get; set; }
        public int UniqueUsers { get; set; }
        public int MostActiveUser { get; set; }
        public string MostCommonAction { get; set; } = string.Empty;
        public Dictionary<int, int> ActivitiesByHour { get; set; } = new();
        public Dictionary<string, int> ActivitiesByDay { get; set; } = new();
        public int SecurityEvents { get; set; }
    }

    public class SecurityAlert
    {
        public SecurityAlertType Type { get; set; }
        public string Message { get; set; } = string.Empty;
        public AlertSeverity Severity { get; set; }
        public DateTime Timestamp { get; set; }
        public int UserId { get; set; }
    }

    public enum SecurityAlertType
    {
        MultipleFailedLogins,
        SuspiciousActivity,
        UnauthorizedAccess,
        DataBreach,
        SystemAnomaly
    }

    public enum AlertSeverity
    {
        Low,
        Medium,
        High,
        Critical
    }
}
