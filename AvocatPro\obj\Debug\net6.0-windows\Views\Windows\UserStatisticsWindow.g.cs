﻿#pragma checksum "..\..\..\..\..\Views\Windows\UserStatisticsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "14DBBC203BD90EBAD5DCA79F8B4AC05695BEC3DA"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Windows {
    
    
    /// <summary>
    /// UserStatisticsWindow
    /// </summary>
    public partial class UserStatisticsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 20 "..\..\..\..\..\Views\Windows\UserStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserNameText;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\..\..\Views\Windows\UserStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LoginCountText;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\..\Views\Windows\UserStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SessionTimeText;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\..\..\Views\Windows\UserStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActionsCountText;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\..\Views\Windows\UserStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ErrorCountText;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\..\..\Views\Windows\UserStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker FromDatePicker;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\..\..\Views\Windows\UserStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ToDatePicker;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\..\Views\Windows\UserStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\..\Views\Windows\UserStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas LoginActivityChart;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\..\Views\Windows\UserStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas OperationsChart;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\..\Views\Windows\UserStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ActivitySearchBox;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\..\Views\Windows\UserStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ActivityTypeFilter;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\..\..\Views\Windows\UserStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ActivityLevelFilter;
        
        #line default
        #line hidden
        
        
        #line 139 "..\..\..\..\..\Views\Windows\UserStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ActivitiesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\..\..\Views\Windows\UserStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProductivityRatingText;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\..\Views\Windows\UserStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock QualityRatingText;
        
        #line default
        #line hidden
        
        
        #line 191 "..\..\..\..\..\Views\Windows\UserStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PunctualityRatingText;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\..\..\Views\Windows\UserStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CollaborationRatingText;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\..\..\Views\Windows\UserStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddRatingButton;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\..\..\Views\Windows\UserStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid RatingsHistoryDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/windows/userstatisticswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\UserStatisticsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.UserNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.LoginCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.SessionTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.ActionsCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.ErrorCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.FromDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 7:
            this.ToDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 8:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 93 "..\..\..\..\..\Views\Windows\UserStatisticsWindow.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.LoginActivityChart = ((System.Windows.Controls.Canvas)(target));
            return;
            case 10:
            this.OperationsChart = ((System.Windows.Controls.Canvas)(target));
            return;
            case 11:
            this.ActivitySearchBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.ActivityTypeFilter = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 13:
            this.ActivityLevelFilter = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 14:
            this.ActivitiesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 15:
            this.ProductivityRatingText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.QualityRatingText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.PunctualityRatingText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.CollaborationRatingText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.AddRatingButton = ((System.Windows.Controls.Button)(target));
            
            #line 207 "..\..\..\..\..\Views\Windows\UserStatisticsWindow.xaml"
            this.AddRatingButton.Click += new System.Windows.RoutedEventHandler(this.AddRatingButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.RatingsHistoryDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

