# AvocatPro - نظام إدارة مكتب المحاماة

## نظرة عامة

AvocatPro هو نظام شامل لإدارة مكاتب المحاماة في المغرب، مصمم خصيصاً لتلبية احتياجات المحامين والمكاتب القانونية. يوفر النظام حلولاً متكاملة لإدارة الموكلين، القضايا، الجلسات، المواعيد، والإدارة المالية.

## الميزات الرئيسية

### 🏢 إدارة شاملة للمكتب
- **إدارة الموكلين**: نظام متقدم لإدارة بيانات الموكلين (أفراد وشركات)
- **إدارة القضايا**: متابعة شاملة للملفات والقضايا القانونية
- **إدارة الجلسات**: جدولة ومتابعة جلسات المحكمة
- **إدارة المواعيد**: تنظيم المواعيد والاستشارات

### 💰 الإدارة المالية
- **تتبع الأتعاب**: إدارة أتعاب المحاماة والاستشارات
- **إدارة المصاريف**: تسجيل ومتابعة مصاريف القضايا
- **التقارير المالية**: تقارير مفصلة عن الإيرادات والمصاريف
- **الفواتير**: إنشاء وإدارة الفواتير

### 📊 التقارير والإحصائيات
- **لوحة تحكم تفاعلية**: نظرة عامة على أنشطة المكتب
- **تقارير مخصصة**: تقارير قابلة للتخصيص حسب الحاجة
- **إحصائيات متقدمة**: تحليلات شاملة للأداء
- **تصدير البيانات**: تصدير إلى Excel وPDF

### 🔒 الأمان والحماية
- **نظام مستخدمين متقدم**: أدوار وصلاحيات مختلفة
- **تشفير البيانات**: حماية عالية للمعلومات الحساسة
- **نسخ احتياطية**: نظام نسخ احتياطي تلقائي
- **سجل الأنشطة**: تتبع جميع العمليات والتغييرات

### 🌐 واجهة مستخدم حديثة
- **تصميم عصري**: واجهة مستخدم جذابة وسهلة الاستخدام
- **دعم اللغة العربية**: واجهة باللغة العربية مع دعم RTL
- **تجربة مستخدم محسنة**: تصميم متجاوب وسريع
- **إشعارات ذكية**: تنبيهات للمواعيد والمهام المهمة

## المتطلبات التقنية

### متطلبات النظام
- **نظام التشغيل**: Windows 10 أو أحدث
- **إطار العمل**: .NET 6.0
- **قاعدة البيانات**: SQLite (مدمجة)
- **الذاكرة**: 4 GB RAM كحد أدنى
- **مساحة القرص**: 500 MB مساحة فارغة

### التقنيات المستخدمة
- **WPF**: Windows Presentation Foundation
- **Entity Framework Core**: لإدارة قاعدة البيانات
- **Material Design**: للتصميم الحديث
- **BCrypt**: لتشفير كلمات المرور
- **AutoMapper**: لتحويل البيانات
- **FluentValidation**: للتحقق من صحة البيانات

## التثبيت والإعداد

### 1. متطلبات التطوير
```bash
# تثبيت .NET 6.0 SDK
# تثبيت Visual Studio 2022 أو Visual Studio Code
```

### 2. استنساخ المشروع
```bash
git clone https://github.com/your-repo/AvocatPro.git
cd AvocatPro
```

### 3. استعادة الحزم
```bash
dotnet restore
```

### 4. تشغيل التطبيق
```bash
dotnet run
```

### 5. بيانات تسجيل الدخول الافتراضية
- **اسم المستخدم**: admin
- **كلمة المرور**: Admin@123

## الهيكل التنظيمي للمشروع

```
AvocatPro/
├── Models/                 # نماذج البيانات
├── ViewModels/            # نماذج العرض (MVVM)
├── Views/                 # واجهات المستخدم
│   ├── Pages/            # صفحات التطبيق
│   └── Windows/          # النوافذ
├── Services/             # الخدمات والمنطق التجاري
├── Data/                 # سياق قاعدة البيانات
├── Helpers/              # الأدوات المساعدة
├── Converters/           # محولات البيانات
├── Validators/           # مدققات البيانات
├── Resources/            # الموارد والملفات
└── Styles/              # أنماط التصميم
```

## الاستخدام

### تسجيل الدخول
1. قم بتشغيل التطبيق
2. أدخل بيانات تسجيل الدخول
3. اختر "تذكرني" للحفظ التلقائي

### إدارة الموكلين
1. انتقل إلى قسم "إدارة الموكلين"
2. اضغط على "إضافة موكل جديد"
3. املأ البيانات المطلوبة
4. احفظ البيانات

### إدارة القضايا
1. انتقل إلى قسم "إدارة الملفات"
2. اربط القضية بموكل
3. أضف تفاصيل القضية
4. تابع الجلسات والتطورات

## المساهمة في التطوير

نرحب بمساهماتكم في تطوير AvocatPro! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع جديد للميزة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى الفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## الدعم والمساعدة

### التوثيق
- [دليل المستخدم](docs/user-guide.md)
- [دليل المطور](docs/developer-guide.md)
- [API Documentation](docs/api.md)

### التواصل
- **البريد الإلكتروني**: <EMAIL>
- **الموقع الإلكتروني**: www.avocatpro.ma
- **الدعم الفني**: +212 5 XX XX XX XX

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الشكر والتقدير

- فريق تطوير AvocatPro
- مجتمع المطورين المغاربة
- جميع المساهمين في المشروع

---

**AvocatPro** - نظام إدارة مكتب المحاماة الاحترافي
© 2025 AvocatPro Solutions. جميع الحقوق محفوظة.
