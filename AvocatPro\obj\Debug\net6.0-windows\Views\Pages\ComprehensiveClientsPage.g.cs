﻿#pragma checksum "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "209C8D09BF7B007044FB734836F69C027F1AEE0F"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Pages {
    
    
    /// <summary>
    /// ComprehensiveClientsPage
    /// </summary>
    public partial class ComprehensiveClientsPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 114 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalClientsText;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalClientsChangeText;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActiveClientsText;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActiveClientsPercentText;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CompaniesText;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CompaniesPercentText;
        
        #line default
        #line hidden
        
        
        #line 183 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActiveCasesText;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActiveCasesChangeText;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 241 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ClientTypeFilter;
        
        #line default
        #line hidden
        
        
        #line 249 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusFilter;
        
        #line default
        #line hidden
        
        
        #line 333 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ClientsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 570 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ResultsCountText;
        
        #line default
        #line hidden
        
        
        #line 587 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentPageText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/pages/comprehensiveclientspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 42 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddClient_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 58 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportClients_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 74 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PrintClients_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.TotalClientsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.TotalClientsChangeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.ActiveClientsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.ActiveClientsPercentText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.CompaniesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.CompaniesPercentText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.ActiveCasesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.ActiveCasesChangeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 211 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 13:
            this.ClientTypeFilter = ((System.Windows.Controls.ComboBox)(target));
            
            #line 242 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
            this.ClientTypeFilter.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ClientTypeFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 14:
            this.StatusFilter = ((System.Windows.Controls.ComboBox)(target));
            
            #line 250 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
            this.StatusFilter.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.StatusFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 258 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshClients_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 299 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportExcel_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 315 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportPdf_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.ClientsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 23:
            this.ResultsCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            
            #line 575 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PreviousPage_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.CurrentPageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            
            #line 591 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NextPage_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 19:
            
            #line 503 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewClient_Click);
            
            #line default
            #line hidden
            break;
            case 20:
            
            #line 517 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditClient_Click);
            
            #line default
            #line hidden
            break;
            case 21:
            
            #line 531 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewCases_Click);
            
            #line default
            #line hidden
            break;
            case 22:
            
            #line 545 "..\..\..\..\..\Views\Pages\ComprehensiveClientsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteClient_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

