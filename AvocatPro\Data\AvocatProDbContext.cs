using Microsoft.EntityFrameworkCore;
using AvocatPro.Models;
using AvocatPro.Models.UserManagement;

namespace AvocatPro.Data;

/// <summary>
/// سياق قاعدة البيانات الرئيسي لتطبيق AvocatPro
/// </summary>
public class AvocatProDbContext : DbContext
{
    public AvocatProDbContext(DbContextOptions<AvocatProDbContext> options) : base(options)
    {
    }

    // الجداول الرئيسية
    public DbSet<User> Users { get; set; }
    public DbSet<Client> Clients { get; set; }
    public DbSet<Case> Cases { get; set; }
    public DbSet<Session> Sessions { get; set; }
    public DbSet<Appointment> Appointments { get; set; }

    // الجداول المالية (محذوفة - تم استبدالها بطباعة الشيكات والأظرفة)
    // public DbSet<Expense> Expenses { get; set; }
    // public DbSet<Revenue> Revenues { get; set; }
    // public DbSet<CaseExpense> CaseExpenses { get; set; }

    // جداول إدارة المستخدمين المتقدمة
    public DbSet<UserProfile> UserProfiles { get; set; }
    public DbSet<PasswordSettings> PasswordSettings { get; set; }
    public DbSet<Models.UserManagement.Permission> Permissions { get; set; }
    public DbSet<Models.UserManagement.UserPermission> UserPermissions { get; set; }
    public DbSet<PermissionGroup> PermissionGroups { get; set; }
    public DbSet<UserPermissionGroup> UserPermissionGroups { get; set; }
    public DbSet<Models.UserManagement.UserActivity> UserActivities { get; set; }
    public DbSet<LoginLog> LoginLogs { get; set; }
    public DbSet<UserStatistics> UserStatistics { get; set; }
    public DbSet<UserPerformanceRating> UserPerformanceRatings { get; set; }

    // الجداول المساعدة
    public DbSet<ClientContact> ClientContacts { get; set; }
    public DbSet<ClientDocument> ClientDocuments { get; set; }
    public DbSet<CaseDocument> CaseDocuments { get; set; }
    public DbSet<SessionDocument> SessionDocuments { get; set; }
    public DbSet<CaseNote> CaseNotes { get; set; }
    public DbSet<AppointmentReminder> AppointmentReminders { get; set; }
    public DbSet<OfficeSettings> OfficeSettings { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // تكوين الفهارس
        ConfigureIndexes(modelBuilder);

        // تكوين العلاقات
        ConfigureRelationships(modelBuilder);

        // تكوين القيود
        ConfigureConstraints(modelBuilder);

        // تكوين البيانات الافتراضية
        ConfigureDefaultData(modelBuilder);
    }

    private void ConfigureIndexes(ModelBuilder modelBuilder)
    {
        // فهارس المستخدمين
        modelBuilder.Entity<User>()
            .HasIndex(u => u.Username)
            .IsUnique();

        modelBuilder.Entity<User>()
            .HasIndex(u => u.Email)
            .IsUnique();

        // فهارس الموكلين
        modelBuilder.Entity<Client>()
            .HasIndex(c => c.OfficeReference)
            .IsUnique();

        modelBuilder.Entity<Client>()
            .HasIndex(c => c.Phone);

        modelBuilder.Entity<Client>()
            .HasIndex(c => c.Email);

        // فهارس القضايا
        modelBuilder.Entity<Case>()
            .HasIndex(c => c.OfficeReference)
            .IsUnique();

        modelBuilder.Entity<Case>()
            .HasIndex(c => c.CourtReference);

        modelBuilder.Entity<Case>()
            .HasIndex(c => c.Status);

        // فهارس الجلسات
        modelBuilder.Entity<Session>()
            .HasIndex(s => s.SessionDate);

        modelBuilder.Entity<Session>()
            .HasIndex(s => s.Status);

        // فهارس المواعيد
        modelBuilder.Entity<Appointment>()
            .HasIndex(a => a.AppointmentDate);

        modelBuilder.Entity<Appointment>()
            .HasIndex(a => a.Status);
    }

    private void ConfigureRelationships(ModelBuilder modelBuilder)
    {
        // علاقة المستخدم بالقضايا المكلف بها
        modelBuilder.Entity<Case>()
            .HasOne(c => c.AssignedLawyer)
            .WithMany(u => u.AssignedCases)
            .HasForeignKey(c => c.AssignedLawyerId)
            .OnDelete(DeleteBehavior.SetNull);

        // علاقة المستخدم بالجلسات المكلف بها
        modelBuilder.Entity<Session>()
            .HasOne(s => s.AssignedLawyer)
            .WithMany(u => u.AssignedSessions)
            .HasForeignKey(s => s.AssignedLawyerId)
            .OnDelete(DeleteBehavior.SetNull);

        // علاقة المستخدم بالمواعيد المنشأة
        modelBuilder.Entity<Appointment>()
            .HasOne(a => a.CreatedByUser)
            .WithMany(u => u.CreatedAppointments)
            .HasForeignKey(a => a.CreatedByUserId)
            .OnDelete(DeleteBehavior.Restrict);

        // علاقة الموكل بالقضايا
        modelBuilder.Entity<Case>()
            .HasOne(c => c.Client)
            .WithMany(cl => cl.Cases)
            .HasForeignKey(c => c.ClientId)
            .OnDelete(DeleteBehavior.Restrict);

        // علاقة القضية بالجلسات
        modelBuilder.Entity<Session>()
            .HasOne(s => s.Case)
            .WithMany(c => c.Sessions)
            .HasForeignKey(s => s.CaseId)
            .OnDelete(DeleteBehavior.Cascade);

        // علاقة الموكل بالمواعيد
        modelBuilder.Entity<Appointment>()
            .HasOne(a => a.Client)
            .WithMany(c => c.Appointments)
            .HasForeignKey(a => a.ClientId)
            .OnDelete(DeleteBehavior.SetNull);

        // علاقة القضية بالمواعيد
        modelBuilder.Entity<Appointment>()
            .HasOne(a => a.Case)
            .WithMany()
            .HasForeignKey(a => a.CaseId)
            .OnDelete(DeleteBehavior.SetNull);
    }

    private void ConfigureConstraints(ModelBuilder modelBuilder)
    {
        // قيود الأسعار والمبالغ
        modelBuilder.Entity<Case>()
            .Property(c => c.FinancialValue)
            .HasPrecision(18, 2);

        modelBuilder.Entity<Case>()
            .Property(c => c.LawyerFees)
            .HasPrecision(18, 2);

        modelBuilder.Entity<Case>()
            .Property(c => c.AdditionalCosts)
            .HasPrecision(18, 2);

        // modelBuilder.Entity<Expense>()
        //     .Property(e => e.Amount)
        //     .HasPrecision(18, 2);

        // modelBuilder.Entity<Revenue>()
        //     .Property(r => r.Amount)
        //     .HasPrecision(18, 2);

        // modelBuilder.Entity<CaseExpense>()
        //     .Property(ce => ce.Amount)
        //     .HasPrecision(18, 2);

        // إعدادات جداول إدارة المستخدمين
        ConfigureUserManagementConstraints(modelBuilder);

        modelBuilder.Entity<Appointment>()
            .Property(a => a.Cost)
            .HasPrecision(18, 2);

        modelBuilder.Entity<Session>()
            .Property(s => s.Expenses)
            .HasPrecision(18, 2);
    }

    /// <summary>
    /// إعدادات قيود جداول إدارة المستخدمين
    /// </summary>
    private void ConfigureUserManagementConstraints(ModelBuilder modelBuilder)
    {
        // فهارس إدارة المستخدمين
        modelBuilder.Entity<UserProfile>()
            .HasIndex(up => up.Email)
            .IsUnique();

        modelBuilder.Entity<UserProfile>()
            .HasIndex(up => up.Phone);

        modelBuilder.Entity<Models.UserManagement.Permission>()
            .HasIndex(p => p.Code)
            .IsUnique();

        modelBuilder.Entity<Models.UserManagement.Permission>()
            .HasIndex(p => p.Module);

        modelBuilder.Entity<Models.UserManagement.UserActivity>()
            .HasIndex(ua => new { ua.UserId, ua.CreatedAt });

        modelBuilder.Entity<Models.UserManagement.UserActivity>()
            .HasIndex(ua => ua.Module);

        modelBuilder.Entity<LoginLog>()
            .HasIndex(ll => new { ll.UserId, ll.LoginTime });

        modelBuilder.Entity<UserStatistics>()
            .HasIndex(us => new { us.UserId, us.StatDate })
            .IsUnique();

        // علاقات إدارة المستخدمين
        modelBuilder.Entity<UserProfile>()
            .HasOne(up => up.User)
            .WithOne(u => u.UserProfile)
            .HasForeignKey<UserProfile>(up => up.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<PasswordSettings>()
            .HasOne(ps => ps.User)
            .WithOne(u => u.PasswordSettings)
            .HasForeignKey<PasswordSettings>(ps => ps.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<Models.UserManagement.UserPermission>()
            .HasOne(up => up.User)
            .WithMany(u => u.UserPermissions)
            .HasForeignKey(up => up.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<Models.UserManagement.UserPermission>()
            .HasOne(up => up.Permission)
            .WithMany()
            .HasForeignKey(up => up.PermissionId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<UserPermissionGroup>()
            .HasOne(upg => upg.User)
            .WithMany(u => u.UserPermissionGroups)
            .HasForeignKey(upg => upg.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<UserPermissionGroup>()
            .HasOne(upg => upg.PermissionGroup)
            .WithMany()
            .HasForeignKey(upg => upg.PermissionGroupId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<Models.UserManagement.UserActivity>()
            .HasOne(ua => ua.User)
            .WithMany()
            .HasForeignKey(ua => ua.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<LoginLog>()
            .HasOne(ll => ll.User)
            .WithMany()
            .HasForeignKey(ll => ll.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<UserStatistics>()
            .HasOne(us => us.User)
            .WithMany()
            .HasForeignKey(us => us.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<UserPerformanceRating>()
            .HasOne(upr => upr.User)
            .WithMany()
            .HasForeignKey(upr => upr.UserId)
            .OnDelete(DeleteBehavior.Restrict);

        modelBuilder.Entity<UserPerformanceRating>()
            .HasOne(upr => upr.RatedByUser)
            .WithMany()
            .HasForeignKey(upr => upr.RatedBy)
            .OnDelete(DeleteBehavior.Restrict);
    }

    private void ConfigureDefaultData(ModelBuilder modelBuilder)
    {
        // إنشاء مستخدم افتراضي (مدير النظام)
        modelBuilder.Entity<User>().HasData(
            new User
            {
                Id = 1,
                Username = "admin",
                FullName = "مدير النظام",
                Email = "<EMAIL>",
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
                Role = UserRole.Admin,
                IsActive = true,
                CreatedAt = DateTime.Now,
                CreatedBy = "System"
            }
        );

        // إعدادات المكتب الافتراضية
        modelBuilder.Entity<OfficeSettings>().HasData(
            new OfficeSettings
            {
                Id = 1,
                OfficeName = "مكتب المحاماة",
                OfficeAddress = "الرباط، المغرب",
                OfficePhone = "+212 5 37 XX XX XX",
                OfficeEmail = "<EMAIL>",
                CreatedAt = DateTime.Now,
                CreatedBy = "System"
            }
        );
    }

    public override int SaveChanges()
    {
        UpdateTimestamps();
        return base.SaveChanges();
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        UpdateTimestamps();
        return await base.SaveChangesAsync(cancellationToken);
    }

    private void UpdateTimestamps()
    {
        var entries = ChangeTracker.Entries()
            .Where(e => e.Entity is BaseEntity && (e.State == EntityState.Added || e.State == EntityState.Modified));

        foreach (var entry in entries)
        {
            var entity = (BaseEntity)entry.Entity;

            if (entry.State == EntityState.Added)
            {
                entity.CreatedAt = DateTime.Now;
            }
            else if (entry.State == EntityState.Modified)
            {
                entity.UpdatedAt = DateTime.Now;
            }
        }
    }
}
