using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using AvocatPro.Models;
using AvocatPro.Models.UserManagement;
using AvocatPro.Services;
using AvocatPro.Data;
using Microsoft.EntityFrameworkCore;

namespace AvocatPro.Views.Pages;

/// <summary>
/// صفحة إدارة المستخدمين المتقدمة
/// </summary>
public partial class AdvancedUserManagementPage : Page, INotifyPropertyChanged
{
    private readonly UserManagementService _userManagementService;
    private readonly PermissionService _permissionService;
    private readonly ActivityLogService _activityLogService;
    private readonly AvocatProDbContext _context;
    private User? _currentUser;

    private ObservableCollection<UserViewModel> _users = new();
    private string _searchText = string.Empty;
    private UserStatus? _selectedStatusFilter;
    private UserRole? _selectedRoleFilter;

    public AdvancedUserManagementPage()
    {
        InitializeComponent();
        
        // تهيئة الخدمات
        _context = new AvocatProDbContext();
        _activityLogService = new ActivityLogService(_context);
        _userManagementService = new UserManagementService(_context, _activityLogService);
        _permissionService = new PermissionService(_context, _activityLogService);
        
        DataContext = this;
        
        InitializeControls();
        LoadDataAsync();
    }

    #region Properties

    public ObservableCollection<UserViewModel> Users
    {
        get => _users;
        set => SetProperty(ref _users, value);
    }

    public string SearchText
    {
        get => _searchText;
        set
        {
            SetProperty(ref _searchText, value);
            FilterUsers();
        }
    }

    #endregion

    #region Initialization

    /// <summary>
    /// تهيئة العناصر
    /// </summary>
    private void InitializeControls()
    {
        try
        {
            // تهيئة فلتر الحالة
            StatusFilterComboBox.ItemsSource = new[]
            {
                new { Value = (UserStatus?)null, Display = "جميع الحالات" },
                new { Value = (UserStatus?)UserStatus.Active, Display = "نشط" },
                new { Value = (UserStatus?)UserStatus.Inactive, Display = "غير نشط" },
                new { Value = (UserStatus?)UserStatus.Suspended, Display = "معلق" },
                new { Value = (UserStatus?)UserStatus.Banned, Display = "محظور" },
                new { Value = (UserStatus?)UserStatus.PendingActivation, Display = "في انتظار التفعيل" }
            };
            StatusFilterComboBox.DisplayMemberPath = "Display";
            StatusFilterComboBox.SelectedValuePath = "Value";
            StatusFilterComboBox.SelectedIndex = 0;

            // تهيئة فلتر الدور
            RoleFilterComboBox.ItemsSource = new[]
            {
                new { Value = (UserRole?)null, Display = "جميع الأدوار" },
                new { Value = (UserRole?)UserRole.Admin, Display = "مدير النظام" },
                new { Value = (UserRole?)UserRole.SeniorLawyer, Display = "محامي رئيسي" },
                new { Value = (UserRole?)UserRole.Lawyer, Display = "محامي" },
                new { Value = (UserRole?)UserRole.Secretary, Display = "سكرتير" },
                new { Value = (UserRole?)UserRole.User, Display = "مستخدم عادي" }
            };
            RoleFilterComboBox.DisplayMemberPath = "Display";
            RoleFilterComboBox.SelectedValuePath = "Value";
            RoleFilterComboBox.SelectedIndex = 0;

            // ربط أحداث الفلاتر
            StatusFilterComboBox.SelectionChanged += FilterComboBox_SelectionChanged;
            RoleFilterComboBox.SelectionChanged += FilterComboBox_SelectionChanged;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تهيئة العناصر: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// تحميل البيانات
    /// </summary>
    private async void LoadDataAsync()
    {
        try
        {
            await LoadUsersAsync();
            await UpdateStatisticsAsync();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// تحميل المستخدمين
    /// </summary>
    private async Task LoadUsersAsync()
    {
        try
        {
            var users = await _userManagementService.GetAllUsersWithDetailsAsync();
            var userViewModels = users.Select(u => new UserViewModel(u)).ToList();
            
            Users.Clear();
            foreach (var user in userViewModels)
            {
                Users.Add(user);
            }

            UsersDataGrid.ItemsSource = Users;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل المستخدمين: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// تحديث الإحصائيات
    /// </summary>
    private async Task UpdateStatisticsAsync()
    {
        try
        {
            var users = await _context.Users.Include(u => u.UserProfile).ToListAsync();
            
            TotalUsersText.Text = users.Count.ToString();
            ActiveUsersText.Text = users.Count(u => u.UserProfile?.Status == UserStatus.Active).ToString();
            LockedUsersText.Text = users.Count(u => u.UserProfile?.Status == UserStatus.Suspended).ToString();
            InactiveUsersText.Text = users.Count(u => u.UserProfile?.Status == UserStatus.Inactive).ToString();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحديث الإحصائيات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    #endregion

    #region Event Handlers

    /// <summary>
    /// معالج تغيير الفلاتر
    /// </summary>
    private void FilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        FilterUsers();
    }

    /// <summary>
    /// فلترة المستخدمين
    /// </summary>
    private void FilterUsers()
    {
        try
        {
            var filteredUsers = Users.AsEnumerable();

            // فلترة بالنص
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                filteredUsers = filteredUsers.Where(u => 
                    u.Username.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    u.FullName.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    u.Email.Contains(SearchText, StringComparison.OrdinalIgnoreCase));
            }

            // فلترة بالحالة
            _selectedStatusFilter = StatusFilterComboBox.SelectedValue as UserStatus?;
            if (_selectedStatusFilter.HasValue)
            {
                filteredUsers = filteredUsers.Where(u => u.Status == _selectedStatusFilter.Value);
            }

            // فلترة بالدور
            _selectedRoleFilter = RoleFilterComboBox.SelectedValue as UserRole?;
            if (_selectedRoleFilter.HasValue)
            {
                filteredUsers = filteredUsers.Where(u => u.Role == _selectedRoleFilter.Value);
            }

            UsersDataGrid.ItemsSource = filteredUsers.ToList();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فلترة المستخدمين: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// إضافة مستخدم جديد
    /// </summary>
    private async void AddUserButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var addUserWindow = new AddUserWindow();
            if (addUserWindow.ShowDialog() == true)
            {
                await LoadUsersAsync();
                await UpdateStatisticsAsync();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إضافة المستخدم: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// تعديل مستخدم
    /// </summary>
    private async void EditUserButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (sender is Button button && button.DataContext is UserViewModel userViewModel)
            {
                var editUserWindow = new EditUserWindow(userViewModel.UserId);
                if (editUserWindow.ShowDialog() == true)
                {
                    await LoadUsersAsync();
                    await UpdateStatisticsAsync();
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تعديل المستخدم: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// إدارة صلاحيات المستخدم
    /// </summary>
    private void ManagePermissionsButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (sender is Button button && button.DataContext is UserViewModel userViewModel)
            {
                var permissionsWindow = new UserPermissionsWindow(userViewModel.UserId);
                permissionsWindow.ShowDialog();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إدارة الصلاحيات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// عرض إحصائيات المستخدم
    /// </summary>
    private void ViewStatsButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (sender is Button button && button.DataContext is UserViewModel userViewModel)
            {
                var statsWindow = new UserStatisticsWindow(userViewModel.UserId);
                statsWindow.ShowDialog();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في عرض الإحصائيات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// حذف مستخدم
    /// </summary>
    private async void DeleteUserButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (sender is Button button && button.DataContext is UserViewModel userViewModel)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف المستخدم '{userViewModel.FullName}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    var deleteResult = await _userManagementService.DeleteUserAsync(userViewModel.UserId, _currentUser?.Id ?? 1);
                    
                    if (deleteResult.Success)
                    {
                        MessageBox.Show(deleteResult.Message, "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        await LoadUsersAsync();
                        await UpdateStatisticsAsync();
                    }
                    else
                    {
                        MessageBox.Show(deleteResult.Message, "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في حذف المستخدم: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// النقر المزدوج على المستخدم
    /// </summary>
    private async void UsersDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
    {
        if (UsersDataGrid.SelectedItem is UserViewModel userViewModel)
        {
            await EditUserButton_Click(sender, new RoutedEventArgs());
        }
    }

    #endregion

    #region INotifyPropertyChanged

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (Equals(field, value)) return false;
        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }

    #endregion
}

/// <summary>
/// نموذج عرض المستخدم
/// </summary>
public class UserViewModel
{
    private readonly User _user;

    public UserViewModel(User user)
    {
        _user = user;
    }

    public int UserId => _user.Id;
    public string Username => _user.Username;
    public string FullName => _user.UserProfile?.FullName ?? "غير محدد";
    public string Email => _user.UserProfile?.Email ?? "غير محدد";
    public UserRole Role => _user.Role;
    public UserStatus Status => _user.UserProfile?.Status ?? UserStatus.Inactive;

    public string RoleDisplay => Role switch
    {
        UserRole.Admin => "مدير النظام",
        UserRole.SeniorLawyer => "محامي رئيسي",
        UserRole.Lawyer => "محامي",
        UserRole.Secretary => "سكرتير",
        UserRole.User => "مستخدم عادي",
        _ => "غير محدد"
    };

    public string StatusDisplay => Status switch
    {
        UserStatus.Active => "نشط",
        UserStatus.Inactive => "غير نشط",
        UserStatus.Suspended => "معلق",
        UserStatus.Banned => "محظور",
        UserStatus.PendingActivation => "في انتظار التفعيل",
        UserStatus.Expired => "منتهي الصلاحية",
        _ => "غير محدد"
    };

    public string StatusColor => Status switch
    {
        UserStatus.Active => "#4CAF50",
        UserStatus.Inactive => "#9E9E9E",
        UserStatus.Suspended => "#FF9800",
        UserStatus.Banned => "#F44336",
        UserStatus.PendingActivation => "#2196F3",
        UserStatus.Expired => "#795548",
        _ => "#9E9E9E"
    };

    public string LastLoginDisplay
    {
        get
        {
            var lastLogin = _user.UserProfile?.LastLoginAt;
            if (lastLogin.HasValue)
            {
                var timeSpan = DateTime.Now - lastLogin.Value;
                if (timeSpan.TotalDays < 1)
                    return "اليوم";
                else if (timeSpan.TotalDays < 7)
                    return $"منذ {(int)timeSpan.TotalDays} أيام";
                else if (timeSpan.TotalDays < 30)
                    return $"منذ {(int)(timeSpan.TotalDays / 7)} أسابيع";
                else
                    return lastLogin.Value.ToString("dd/MM/yyyy");
            }
            return "لم يسجل دخول";
        }
    }

    public bool IsLocked => _user.PasswordSettings?.IsLocked ?? false;
    public bool MustChangePassword => _user.PasswordSettings?.MustChangePassword ?? false;
}
