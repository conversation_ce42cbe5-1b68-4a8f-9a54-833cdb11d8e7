using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using AvocatPro.Models;
using AvocatPro.Services;

namespace AvocatPro.Views.Windows
{
    /// <summary>
    /// نافذة إضافة موعد متقدمة
    /// </summary>
    public partial class AdvancedAddAppointmentWindow : Window
    {
        #region Fields

        private readonly AppointmentsService _appointmentsService;
        private readonly NotificationService _notificationService;
        private AdvancedAppointmentModel? _existingAppointment;
        private bool _isEditMode;

        #endregion

        #region Properties

        public AdvancedAppointmentModel NewAppointment { get; private set; }
        public new bool DialogResult { get; private set; }

        #endregion

        #region Constructors

        /// <summary>
        /// منشئ لإضافة موعد جديد
        /// </summary>
        public AdvancedAddAppointmentWindow()
        {
            InitializeComponent();
            
            _appointmentsService = new AppointmentsService();
            _notificationService = new NotificationService();
            _isEditMode = false;
            
            NewAppointment = new AdvancedAppointmentModel();
            
            InitializeControls();
            SetupDefaultValues();
        }

        /// <summary>
        /// منشئ لتعديل موعد موجود
        /// </summary>
        public AdvancedAddAppointmentWindow(AdvancedAppointmentModel appointment)
        {
            InitializeComponent();
            
            _appointmentsService = new AppointmentsService();
            _notificationService = new NotificationService();
            _existingAppointment = appointment;
            _isEditMode = true;
            
            NewAppointment = new AdvancedAppointmentModel();
            
            InitializeControls();
            LoadExistingAppointment();
            
            WindowTitle.Text = "تعديل الموعد";
            Title = "تعديل الموعد";
        }

        #endregion

        #region Initialization

        private void InitializeControls()
        {
            try
            {
                SetupAppointmentTypes();
                SetupCategories();
                SetupPriorities();
                SetupLawyers();
                SetupTimeControls();
                SetupReminderOptions();
                SetupRecurrencePatterns();
                
                // تعيين التاريخ الافتراضي
                AppointmentDatePicker.SelectedDate = DateTime.Today;
                
                // تعيين رقم الموعد
                if (!_isEditMode)
                {
                    var appointmentNumber = _appointmentsService.GenerateAppointmentNumber();
                    AppointmentNumberText.Text = $"رقم الموعد: {appointmentNumber}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النافذة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SetupAppointmentTypes()
        {
            var types = _appointmentsService.GetAppointmentTypes();
            foreach (var type in types)
            {
                AppointmentTypeComboBox.Items.Add(type);
            }
            if (AppointmentTypeComboBox.Items.Count > 0)
                AppointmentTypeComboBox.SelectedIndex = 0;
        }

        private void SetupCategories()
        {
            var categories = _appointmentsService.GetAppointmentCategories();
            foreach (var category in categories)
            {
                CategoryComboBox.Items.Add(category);
            }
            if (CategoryComboBox.Items.Count > 0)
                CategoryComboBox.SelectedIndex = 0;
        }

        private void SetupPriorities()
        {
            var priorities = _appointmentsService.GetPriorityLevels();
            foreach (var priority in priorities)
            {
                PriorityComboBox.Items.Add(priority);
            }
            if (PriorityComboBox.Items.Count > 0)
                PriorityComboBox.SelectedIndex = 2; // عادي
        }

        private void SetupLawyers()
        {
            var lawyers = _appointmentsService.GetLawyers();
            foreach (var lawyer in lawyers)
            {
                AssignedLawyerComboBox.Items.Add(lawyer);
            }
            if (AssignedLawyerComboBox.Items.Count > 0)
                AssignedLawyerComboBox.SelectedIndex = 0;
        }

        private void SetupTimeControls()
        {
            // إعداد الساعات (من 8 صباحاً إلى 6 مساءً)
            for (int hour = 8; hour <= 18; hour++)
            {
                HourComboBox.Items.Add(hour.ToString("00"));
            }
            HourComboBox.SelectedIndex = 1; // 9 صباحاً

            // إعداد الدقائق
            for (int minute = 0; minute < 60; minute += 15)
            {
                MinuteComboBox.Items.Add(minute.ToString("00"));
            }
            MinuteComboBox.SelectedIndex = 0; // 00
        }

        private void SetupReminderOptions()
        {
            var reminderOptions = _appointmentsService.GetReminderOptions();
            foreach (var option in reminderOptions)
            {
                ReminderTimeComboBox.Items.Add(new ComboBoxItem 
                { 
                    Content = option.Key, 
                    Tag = option.Value 
                });
            }
            ReminderTimeComboBox.SelectedIndex = 2; // 15 دقيقة
        }

        private void SetupRecurrencePatterns()
        {
            var patterns = _appointmentsService.GetRecurrencePatterns();
            foreach (var pattern in patterns)
            {
                RecurrencePatternComboBox.Items.Add(pattern);
            }
            if (RecurrencePatternComboBox.Items.Count > 0)
                RecurrencePatternComboBox.SelectedIndex = 2; // أسبوعي
        }

        private void SetupDefaultValues()
        {
            // قيم افتراضية
            TitleTextBox.Text = "استشارة قانونية";
            LocationTextBox.Text = "المكتب الرئيسي";
            DurationTextBox.Text = "60";
            FeesTextBox.Text = "0";
            
            // تفعيل التذكيرات الافتراضية
            ReminderEnabledCheckBox.IsChecked = true;
            DesktopReminderCheckBox.IsChecked = true;
            EmailReminderCheckBox.IsChecked = false;
            SmsReminderCheckBox.IsChecked = false;
        }

        private void LoadExistingAppointment()
        {
            if (_existingAppointment == null) return;

            try
            {
                // المعلومات الأساسية
                TitleTextBox.Text = _existingAppointment.Title;
                DescriptionTextBox.Text = _existingAppointment.Description;
                AppointmentDatePicker.SelectedDate = _existingAppointment.AppointmentDate;
                
                // الوقت
                HourComboBox.SelectedItem = _existingAppointment.AppointmentTime.Hours.ToString("00");
                MinuteComboBox.SelectedItem = _existingAppointment.AppointmentTime.Minutes.ToString("00");
                
                DurationTextBox.Text = _existingAppointment.Duration.TotalMinutes.ToString();
                
                // الأنواع والفئات
                AppointmentTypeComboBox.SelectedItem = _existingAppointment.AppointmentType;
                CategoryComboBox.SelectedItem = _existingAppointment.Category;
                PriorityComboBox.SelectedItem = _existingAppointment.Priority;
                
                // معلومات الموكل
                ClientNameTextBox.Text = _existingAppointment.ClientName;
                ClientPhoneTextBox.Text = _existingAppointment.ClientPhone;
                ClientEmailTextBox.Text = _existingAppointment.ClientEmail;
                AssignedLawyerComboBox.SelectedItem = _existingAppointment.AssignedLawyer;
                LocationTextBox.Text = _existingAppointment.Location;
                RelatedFileNumberTextBox.Text = _existingAppointment.RelatedFileNumber;
                FeesTextBox.Text = _existingAppointment.Fees.ToString();
                
                // التذكيرات
                ReminderEnabledCheckBox.IsChecked = _existingAppointment.ReminderEnabled;
                EmailReminderCheckBox.IsChecked = _existingAppointment.EmailReminderEnabled;
                SmsReminderCheckBox.IsChecked = _existingAppointment.SmsReminderEnabled;
                
                // التكرار
                IsRecurringCheckBox.IsChecked = _existingAppointment.IsRecurring;
                if (_existingAppointment.IsRecurring)
                {
                    RecurrencePatternComboBox.SelectedItem = _existingAppointment.RecurrencePattern;
                    RecurrenceEndDatePicker.SelectedDate = _existingAppointment.RecurrenceEndDate;
                }
                
                NotesTextBox.Text = _existingAppointment.Notes;
                
                AppointmentNumberText.Text = $"رقم الموعد: {_existingAppointment.AppointmentNumber}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الموعد: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region Validation

        private bool ValidateInput()
        {
            var errors = new List<string>();

            // التحقق من الحقول المطلوبة
            if (string.IsNullOrWhiteSpace(TitleTextBox.Text))
                errors.Add("عنوان الموعد مطلوب");

            if (AppointmentTypeComboBox.SelectedItem == null)
                errors.Add("نوع الموعد مطلوب");

            if (string.IsNullOrWhiteSpace(ClientNameTextBox.Text))
                errors.Add("اسم الموكل مطلوب");

            if (AssignedLawyerComboBox.SelectedItem == null)
                errors.Add("المحامي المكلف مطلوب");

            if (PriorityComboBox.SelectedItem == null)
                errors.Add("الأولوية مطلوبة");

            if (AppointmentDatePicker.SelectedDate == null)
                errors.Add("تاريخ الموعد مطلوب");

            if (HourComboBox.SelectedItem == null || MinuteComboBox.SelectedItem == null)
                errors.Add("وقت الموعد مطلوب");

            // التحقق من صحة المدة
            if (!int.TryParse(DurationTextBox.Text, out int duration) || duration <= 0)
                errors.Add("مدة الموعد يجب أن تكون رقماً صحيحاً أكبر من صفر");

            // التحقق من صحة الرسوم
            if (!decimal.TryParse(FeesTextBox.Text, out decimal fees) || fees < 0)
                errors.Add("الرسوم يجب أن تكون رقماً صحيحاً غير سالب");

            // التحقق من التاريخ
            if (AppointmentDatePicker.SelectedDate != null)
            {
                var appointmentDateTime = AppointmentDatePicker.SelectedDate.Value.Date;
                if (HourComboBox.SelectedItem != null && MinuteComboBox.SelectedItem != null)
                {
                    var hour = int.Parse(HourComboBox.SelectedItem.ToString());
                    var minute = int.Parse(MinuteComboBox.SelectedItem.ToString());
                    appointmentDateTime = appointmentDateTime.AddHours(hour).AddMinutes(minute);

                    if (appointmentDateTime < DateTime.Now && !_isEditMode)
                        errors.Add("لا يمكن جدولة موعد في وقت سابق");
                }
            }

            // التحقق من التكرار
            if (IsRecurringCheckBox.IsChecked == true)
            {
                if (RecurrencePatternComboBox.SelectedItem == null)
                    errors.Add("نمط التكرار مطلوب عند تفعيل التكرار");

                if (RecurrenceEndDatePicker.SelectedDate == null)
                    errors.Add("تاريخ انتهاء التكرار مطلوب");
                else if (RecurrenceEndDatePicker.SelectedDate <= AppointmentDatePicker.SelectedDate)
                    errors.Add("تاريخ انتهاء التكرار يجب أن يكون بعد تاريخ الموعد");
            }

            if (errors.Any())
            {
                var errorMessage = "يرجى تصحيح الأخطاء التالية:\n\n" + string.Join("\n", errors);
                MessageBox.Show(errorMessage, "أخطاء في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        #endregion

        #region Event Handlers

        private void IsRecurringCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            RecurrencePanel.Visibility = Visibility.Visible;
        }

        private void IsRecurringCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            RecurrencePanel.Visibility = Visibility.Collapsed;
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInput())
            {
                CreateAppointmentFromInput();
                DialogResult = true;
                Close();
            }
        }

        private void SaveAndAddAnotherButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInput())
            {
                CreateAppointmentFromInput();
                DialogResult = true;
                
                // إعادة تعيين النموذج لموعد جديد
                var newWindow = new AdvancedAddAppointmentWindow();
                newWindow.ShowDialog();
                
                Close();
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        #endregion

        #region Helper Methods

        private void CreateAppointmentFromInput()
        {
            try
            {
                // إنشاء الموعد من البيانات المدخلة
                NewAppointment.Id = _isEditMode ? _existingAppointment.Id : 0;
                NewAppointment.AppointmentNumber = _isEditMode ? _existingAppointment.AppointmentNumber : _appointmentsService.GenerateAppointmentNumber();
                NewAppointment.Title = TitleTextBox.Text.Trim();
                NewAppointment.Description = DescriptionTextBox.Text.Trim();
                NewAppointment.AppointmentDate = AppointmentDatePicker.SelectedDate.Value;
                
                var hour = int.Parse(HourComboBox.SelectedItem.ToString());
                var minute = int.Parse(MinuteComboBox.SelectedItem.ToString());
                NewAppointment.AppointmentTime = new TimeSpan(hour, minute, 0);
                
                NewAppointment.Duration = TimeSpan.FromMinutes(int.Parse(DurationTextBox.Text));
                NewAppointment.AppointmentType = AppointmentTypeComboBox.SelectedItem.ToString();
                NewAppointment.Category = CategoryComboBox.SelectedItem?.ToString() ?? "";
                NewAppointment.Location = LocationTextBox.Text.Trim();
                NewAppointment.ClientName = ClientNameTextBox.Text.Trim();
                NewAppointment.ClientPhone = ClientPhoneTextBox.Text.Trim();
                NewAppointment.ClientEmail = ClientEmailTextBox.Text.Trim();
                NewAppointment.AssignedLawyer = AssignedLawyerComboBox.SelectedItem.ToString();
                NewAppointment.Status = _isEditMode ? _existingAppointment.Status : "مجدول";
                NewAppointment.Priority = PriorityComboBox.SelectedItem.ToString();
                NewAppointment.RelatedFileNumber = RelatedFileNumberTextBox.Text.Trim();
                NewAppointment.Fees = decimal.Parse(FeesTextBox.Text);
                NewAppointment.Notes = NotesTextBox.Text.Trim();
                
                // إعدادات التذكير
                NewAppointment.ReminderEnabled = ReminderEnabledCheckBox.IsChecked == true;
                if (ReminderTimeComboBox.SelectedItem is ComboBoxItem reminderItem)
                {
                    NewAppointment.ReminderMinutes = (int)reminderItem.Tag;
                }
                NewAppointment.EmailReminderEnabled = EmailReminderCheckBox.IsChecked == true;
                NewAppointment.SmsReminderEnabled = SmsReminderCheckBox.IsChecked == true;
                
                // إعدادات التكرار
                NewAppointment.IsRecurring = IsRecurringCheckBox.IsChecked == true;
                if (NewAppointment.IsRecurring)
                {
                    NewAppointment.RecurrencePattern = RecurrencePatternComboBox.SelectedItem.ToString();
                    NewAppointment.RecurrenceEndDate = RecurrenceEndDatePicker.SelectedDate;
                }
                
                // معلومات الإنشاء
                if (!_isEditMode)
                {
                    NewAppointment.CreatedDate = DateTime.Now;
                    NewAppointment.CreatedBy = "المستخدم الحالي"; // يمكن تحديثه لاحقاً
                }
                else
                {
                    NewAppointment.LastUpdated = DateTime.Now;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء الموعد: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion
    }
}
