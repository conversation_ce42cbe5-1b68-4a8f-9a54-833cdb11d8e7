using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;

namespace AvocatPro.Services
{
    public class PerformanceOptimizer
    {
        private static PerformanceOptimizer _instance;
        public static PerformanceOptimizer Instance => _instance ??= new PerformanceOptimizer();

        private readonly ConcurrentDictionary<string, object> _cache;
        private readonly Timer _memoryCleanupTimer;
        private readonly Timer _performanceMonitorTimer;
        private readonly List<PerformanceMetric> _performanceHistory;
        private readonly object _lockObject = new object();

        public event EventHandler<PerformanceEventArgs> PerformanceAlert;
        public event EventHandler<CacheEventArgs> CacheUpdated;

        private PerformanceOptimizer()
        {
            _cache = new ConcurrentDictionary<string, object>();
            _performanceHistory = new List<PerformanceMetric>();
            
            // تنظيف الذاكرة كل 5 دقائق
            _memoryCleanupTimer = new Timer(CleanupMemory, null!, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
            
            // مراقبة الأداء كل دقيقة
            _performanceMonitorTimer = new Timer(MonitorPerformance, null!, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
        }

        #region إدارة الذاكرة التخزين المؤقت

        public T GetFromCache<T>(string key, Func<T> factory, TimeSpan? expiry = null)
        {
            var cacheKey = $"{typeof(T).Name}_{key}";
            
            if (_cache.TryGetValue(cacheKey, out var cachedItem))
            {
                if (cachedItem is CacheItem<T> item && !item.IsExpired)
                {
                    item.LastAccessed = DateTime.Now;
                    return item.Value;
                }
                else
                {
                    _cache.TryRemove(cacheKey, out _);
                }
            }

            var value = factory();
            var cacheItemToStore = new CacheItem<T>
            {
                Value = value,
                CreatedAt = DateTime.Now,
                LastAccessed = DateTime.Now,
                ExpiryTime = expiry.HasValue ? DateTime.Now.Add(expiry.Value) : DateTime.Now.AddHours(1)
            };

            _cache.TryAdd(cacheKey, cacheItemToStore);
            CacheUpdated?.Invoke(this, new CacheEventArgs(cacheKey, "Added"));
            
            return value;
        }

        public async Task<T> GetFromCacheAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiry = null)
        {
            var cacheKey = $"{typeof(T).Name}_{key}";
            
            if (_cache.TryGetValue(cacheKey, out var cachedItem))
            {
                if (cachedItem is CacheItem<T> item && !item.IsExpired)
                {
                    item.LastAccessed = DateTime.Now;
                    return item.Value;
                }
                else
                {
                    _cache.TryRemove(cacheKey, out _);
                }
            }

            var value = await factory();
            var cacheItemToStore = new CacheItem<T>
            {
                Value = value,
                CreatedAt = DateTime.Now,
                LastAccessed = DateTime.Now,
                ExpiryTime = expiry.HasValue ? DateTime.Now.Add(expiry.Value) : DateTime.Now.AddHours(1)
            };

            _cache.TryAdd(cacheKey, cacheItemToStore);
            CacheUpdated?.Invoke(this, new CacheEventArgs(cacheKey, "Added"));
            
            return value;
        }

        public void InvalidateCache(string pattern = null)
        {
            if (string.IsNullOrEmpty(pattern))
            {
                _cache.Clear();
                CacheUpdated?.Invoke(this, new CacheEventArgs("*", "Cleared"));
                return;
            }

            var keysToRemove = _cache.Keys.Where(k => k.Contains(pattern)).ToList();
            foreach (var key in keysToRemove)
            {
                _cache.TryRemove(key, out _);
                CacheUpdated?.Invoke(this, new CacheEventArgs(key, "Removed"));
            }
        }

        private void CleanupMemory(object state)
        {
            try
            {
                // تنظيف العناصر المنتهية الصلاحية
                var expiredKeys = _cache
                    .Where(kvp => kvp.Value is ICacheItem item && item.IsExpired)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in expiredKeys)
                {
                    _cache.TryRemove(key, out _);
                }

                // تنظيف العناصر غير المستخدمة لفترة طويلة
                var oldKeys = _cache
                    .Where(kvp => kvp.Value is ICacheItem item && 
                                  DateTime.Now - item.LastAccessed > TimeSpan.FromHours(2))
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in oldKeys)
                {
                    _cache.TryRemove(key, out _);
                }

                // إجبار تنظيف الذاكرة
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                if (expiredKeys.Count > 0 || oldKeys.Count > 0)
                {
                    CacheUpdated?.Invoke(this, new CacheEventArgs($"{expiredKeys.Count + oldKeys.Count} items", "Cleaned"));
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في تنظيف الذاكرة: {ex.Message}");
            }
        }

        #endregion

        #region مراقبة الأداء

        private void MonitorPerformance(object state)
        {
            try
            {
                var process = Process.GetCurrentProcess();
                var metric = new PerformanceMetric
                {
                    Timestamp = DateTime.Now,
                    MemoryUsage = process.WorkingSet64 / (1024 * 1024), // MB
                    CpuUsage = GetCpuUsage(),
                    ThreadCount = process.Threads.Count,
                    HandleCount = process.HandleCount,
                    CacheSize = _cache.Count
                };

                lock (_lockObject)
                {
                    _performanceHistory.Add(metric);
                    
                    // الاحتفاظ بآخر 1440 قياس (24 ساعة بقياس كل دقيقة)
                    if (_performanceHistory.Count > 1440)
                    {
                        _performanceHistory.RemoveAt(0);
                    }
                }

                // فحص التحذيرات
                CheckPerformanceAlerts(metric);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في مراقبة الأداء: {ex.Message}");
            }
        }

        private double GetCpuUsage()
        {
            try
            {
                using var process = Process.GetCurrentProcess();
                var startTime = DateTime.UtcNow;
                var startCpuUsage = process.TotalProcessorTime;
                
                Thread.Sleep(100); // انتظار قصير للقياس
                
                var endTime = DateTime.UtcNow;
                var endCpuUsage = process.TotalProcessorTime;
                
                var cpuUsedMs = (endCpuUsage - startCpuUsage).TotalMilliseconds;
                var totalMsPassed = (endTime - startTime).TotalMilliseconds;
                var cpuUsageTotal = cpuUsedMs / (Environment.ProcessorCount * totalMsPassed);
                
                return cpuUsageTotal * 100;
            }
            catch
            {
                return 0;
            }
        }

        private void CheckPerformanceAlerts(PerformanceMetric metric)
        {
            // تحذير استخدام الذاكرة العالي (أكثر من 500 MB)
            if (metric.MemoryUsage > 500)
            {
                PerformanceAlert?.Invoke(this, new PerformanceEventArgs
                {
                    Type = PerformanceAlertType.HighMemoryUsage,
                    Message = $"استخدام ذاكرة عالي: {metric.MemoryUsage:F1} MB",
                    Metric = metric
                });
            }

            // تحذير استخدام المعالج العالي (أكثر من 80%)
            if (metric.CpuUsage > 80)
            {
                PerformanceAlert?.Invoke(this, new PerformanceEventArgs
                {
                    Type = PerformanceAlertType.HighCpuUsage,
                    Message = $"استخدام معالج عالي: {metric.CpuUsage:F1}%",
                    Metric = metric
                });
            }

            // تحذير عدد الخيوط العالي (أكثر من 50)
            if (metric.ThreadCount > 50)
            {
                PerformanceAlert?.Invoke(this, new PerformanceEventArgs
                {
                    Type = PerformanceAlertType.HighThreadCount,
                    Message = $"عدد خيوط عالي: {metric.ThreadCount}",
                    Metric = metric
                });
            }
        }

        public PerformanceReport GetPerformanceReport(TimeSpan? period = null)
        {
            lock (_lockObject)
            {
                var cutoffTime = DateTime.Now - (period ?? TimeSpan.FromHours(1));
                var relevantMetrics = _performanceHistory.Where(m => m.Timestamp >= cutoffTime).ToList();

                if (!relevantMetrics.Any())
                    return new PerformanceReport();

                return new PerformanceReport
                {
                    Period = period ?? TimeSpan.FromHours(1),
                    AverageMemoryUsage = relevantMetrics.Average(m => m.MemoryUsage),
                    MaxMemoryUsage = relevantMetrics.Max(m => m.MemoryUsage),
                    AverageCpuUsage = relevantMetrics.Average(m => m.CpuUsage),
                    MaxCpuUsage = relevantMetrics.Max(m => m.CpuUsage),
                    AverageThreadCount = relevantMetrics.Average(m => m.ThreadCount),
                    MaxThreadCount = relevantMetrics.Max(m => m.ThreadCount),
                    CacheHitRate = CalculateCacheHitRate(),
                    TotalSamples = relevantMetrics.Count
                };
            }
        }

        private double CalculateCacheHitRate()
        {
            // محاكاة حساب معدل نجاح الذاكرة التخزين المؤقت
            return _cache.Count > 0 ? 85.0 : 0.0; // 85% معدل افتراضي
        }

        #endregion

        #region تحسين الواجهة

        public void OptimizeUIElement(FrameworkElement element)
        {
            if (element == null) return;

            // تفعيل تسريع الأجهزة
            RenderOptions.SetBitmapScalingMode(element, BitmapScalingMode.HighQuality);
            RenderOptions.SetEdgeMode(element, EdgeMode.Aliased);
            
            // تحسين الرسم
            if (element is Panel panel)
            {
                panel.SetValue(Panel.IsItemsHostProperty, true);
            }

            // تحسين النصوص
            if (element is TextBlock textBlock)
            {
                TextOptions.SetTextFormattingMode(textBlock, TextFormattingMode.Display);
                TextOptions.SetTextRenderingMode(textBlock, TextRenderingMode.ClearType);
            }
        }

        public void EnableVirtualization(ItemsControl itemsControl)
        {
            if (itemsControl == null) return;

            VirtualizingPanel.SetIsVirtualizing(itemsControl, true);
            VirtualizingPanel.SetVirtualizationMode(itemsControl, VirtualizationMode.Recycling);
            VirtualizingPanel.SetScrollUnit(itemsControl, ScrollUnit.Item);
        }

        #endregion

        public CacheStatistics GetCacheStatistics()
        {
            return new CacheStatistics
            {
                TotalItems = _cache.Count,
                MemoryUsage = EstimateCacheMemoryUsage(),
                HitRate = CalculateCacheHitRate(),
                ExpiredItems = _cache.Values.OfType<ICacheItem>().Count(item => item.IsExpired)
            };
        }

        private long EstimateCacheMemoryUsage()
        {
            // تقدير تقريبي لاستخدام الذاكرة
            return _cache.Count * 1024; // 1KB لكل عنصر (تقدير)
        }

        public void Dispose()
        {
            _memoryCleanupTimer?.Dispose();
            _performanceMonitorTimer?.Dispose();
            _cache.Clear();
        }
    }

    #region فئات البيانات

    public interface ICacheItem
    {
        DateTime CreatedAt { get; set; }
        DateTime LastAccessed { get; set; }
        DateTime ExpiryTime { get; set; }
        bool IsExpired { get; }
    }

    public class CacheItem<T> : ICacheItem
    {
        public T Value { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime LastAccessed { get; set; }
        public DateTime ExpiryTime { get; set; }
        public bool IsExpired => DateTime.Now > ExpiryTime;
    }

    public class PerformanceMetric
    {
        public DateTime Timestamp { get; set; }
        public double MemoryUsage { get; set; } // MB
        public double CpuUsage { get; set; } // %
        public int ThreadCount { get; set; }
        public int HandleCount { get; set; }
        public int CacheSize { get; set; }
    }

    public class PerformanceReport
    {
        public TimeSpan Period { get; set; }
        public double AverageMemoryUsage { get; set; }
        public double MaxMemoryUsage { get; set; }
        public double AverageCpuUsage { get; set; }
        public double MaxCpuUsage { get; set; }
        public double AverageThreadCount { get; set; }
        public double MaxThreadCount { get; set; }
        public double CacheHitRate { get; set; }
        public int TotalSamples { get; set; }
    }

    public class CacheStatistics
    {
        public int TotalItems { get; set; }
        public long MemoryUsage { get; set; }
        public double HitRate { get; set; }
        public int ExpiredItems { get; set; }
    }

    public enum PerformanceAlertType
    {
        HighMemoryUsage,
        HighCpuUsage,
        HighThreadCount,
        CacheOverflow
    }

    public class PerformanceEventArgs : EventArgs
    {
        public PerformanceAlertType Type { get; set; }
        public string Message { get; set; }
        public PerformanceMetric Metric { get; set; }
    }

    public class CacheEventArgs : EventArgs
    {
        public string Key { get; }
        public string Action { get; }

        public CacheEventArgs(string key, string action)
        {
            Key = key;
            Action = action;
        }
    }

    #endregion
}
