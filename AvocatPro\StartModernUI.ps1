# تشغيل الواجهة الحديثة لنظام AvocatPro
Write-Host "🚀 تشغيل الواجهة الحديثة لنظام AvocatPro..." -ForegroundColor Green
Write-Host ""

# الانتقال إلى مجلد المشروع
Set-Location $PSScriptRoot

try {
    # بناء المشروع
    Write-Host "🔨 بناء المشروع..." -ForegroundColor Yellow
    dotnet build --configuration Release --verbosity quiet
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ خطأ في بناء المشروع!" -ForegroundColor Red
        Read-Host "اضغط Enter للمتابعة..."
        exit 1
    }
    
    Write-Host "✅ تم بناء المشروع بنجاح" -ForegroundColor Green
    Write-Host ""
    
    # تشغيل الواجهة الحديثة
    Write-Host "🎨 تشغيل الواجهة الحديثة..." -ForegroundColor Cyan
    dotnet run --configuration Release
    
} catch {
    Write-Host "❌ خطأ في تشغيل التطبيق: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "اضغط Enter للمتابعة..."
}
