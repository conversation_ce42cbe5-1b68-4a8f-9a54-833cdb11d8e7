<Window x:Class="AvocatPro.Views.Windows.SimpleLoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="AvocatPro - تسجيل الدخول" Height="650" Width="900"
        WindowStartupLocation="CenterScreen" ResizeMode="NoResize"
        FlowDirection="RightToLeft" Background="#F8FAFC">

    <Grid>
        <!-- الخلفية المتدرجة -->
        <Grid.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#667EEA" Offset="0"/>
                <GradientStop Color="#764BA2" Offset="1"/>
            </LinearGradientBrush>
        </Grid.Background>

        <!-- المحتوى الرئيسي -->
        <Grid HorizontalAlignment="Center" VerticalAlignment="Center">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="400"/>
                <ColumnDefinition Width="100"/>
                <ColumnDefinition Width="400"/>
            </Grid.ColumnDefinitions>

            <!-- الجانب الأيسر - معلومات البرنامج -->
            <Border Grid.Column="0" Background="White" CornerRadius="20,0,0,20" Padding="40">
                <Border.Effect>
                    <DropShadowEffect Color="#000000" BlurRadius="30" ShadowDepth="0" Opacity="0.1"/>
                </Border.Effect>
                <StackPanel VerticalAlignment="Center">
                    <!-- شعار البرنامج -->
                    <Border Width="120" Height="120" CornerRadius="60" Background="#667EEA"
                            HorizontalAlignment="Center" Margin="0,0,0,30">
                        <TextBlock Text="⚖️" FontSize="60" Foreground="White"
                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>

                    <!-- اسم البرنامج -->
                    <TextBlock Text="AvocatPro" FontSize="36" FontWeight="Bold"
                               Foreground="#1F2937" HorizontalAlignment="Center" Margin="0,0,0,10"/>

                    <!-- وصف البرنامج -->
                    <TextBlock Text="نظام إدارة المكاتب القانونية المتطور" FontSize="16"
                               Foreground="#6B7280" HorizontalAlignment="Center"
                               TextWrapping="Wrap" TextAlignment="Center" Margin="0,0,0,30"/>

                    <!-- الميزات -->
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <Border Width="8" Height="8" CornerRadius="4" Background="#10B981" Margin="0,0,15,0" VerticalAlignment="Center"/>
                            <TextBlock Text="إدارة شاملة للموكلين والقضايا" FontSize="14" Foreground="#374151"/>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <Border Width="8" Height="8" CornerRadius="4" Background="#10B981" Margin="0,0,15,0" VerticalAlignment="Center"/>
                            <TextBlock Text="نظام مالي متقدم ومتكامل" FontSize="14" Foreground="#374151"/>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <Border Width="8" Height="8" CornerRadius="4" Background="#10B981" Margin="0,0,15,0" VerticalAlignment="Center"/>
                            <TextBlock Text="تقارير وإحصائيات تفصيلية" FontSize="14" Foreground="#374151"/>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <Border Width="8" Height="8" CornerRadius="4" Background="#10B981" Margin="0,0,15,0" VerticalAlignment="Center"/>
                            <TextBlock Text="واجهة عربية حديثة وسهلة الاستخدام" FontSize="14" Foreground="#374151"/>
                        </StackPanel>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- الجانب الأيمن - نموذج تسجيل الدخول -->
            <Border Grid.Column="2" Background="White" CornerRadius="0,20,20,0" Padding="40">
                <Border.Effect>
                    <DropShadowEffect Color="#000000" BlurRadius="30" ShadowDepth="0" Opacity="0.1"/>
                </Border.Effect>
                <StackPanel VerticalAlignment="Center">
                    <!-- عنوان تسجيل الدخول -->
                    <TextBlock Text="تسجيل الدخول" FontSize="28" FontWeight="Bold"
                               Foreground="#1F2937" HorizontalAlignment="Center" Margin="0,0,0,40"/>

                    <!-- حقل اسم المستخدم -->
                    <TextBlock Text="اسم المستخدم" FontSize="16" FontWeight="SemiBold"
                               Foreground="#374151" Margin="0,0,0,10"/>
                    <Border Background="#F9FAFB" BorderBrush="#D1D5DB" BorderThickness="2"
                            CornerRadius="12" Margin="0,0,0,25">
                        <TextBox x:Name="UsernameTextBox" Background="Transparent" BorderThickness="0"
                                 Padding="16,14" FontSize="16" Text="admin" Foreground="#1F2937"
                                 VerticalAlignment="Center"/>
                    </Border>

                    <!-- حقل كلمة المرور -->
                    <TextBlock Text="كلمة المرور" FontSize="16" FontWeight="SemiBold"
                               Foreground="#374151" Margin="0,0,0,10"/>
                    <Border Background="#F9FAFB" BorderBrush="#D1D5DB" BorderThickness="2"
                            CornerRadius="12" Margin="0,0,0,25">
                        <PasswordBox x:Name="PasswordBox" Background="Transparent" BorderThickness="0"
                                     Padding="16,14" FontSize="16" Foreground="#1F2937"
                                     VerticalAlignment="Center"/>
                    </Border>

                    <!-- تذكرني -->
                    <CheckBox x:Name="RememberMeCheckBox" Content="تذكرني لمدة 30 يوماً"
                              FontSize="14" Foreground="#6B7280" Margin="0,0,0,30"
                              HorizontalAlignment="Right"/>

                    <!-- زر تسجيل الدخول -->
                    <Button x:Name="LoginButton" Content="تسجيل الدخول"
                            Background="#667EEA" Foreground="White" BorderThickness="0"
                            Height="50" FontSize="18" FontWeight="Bold"
                            Cursor="Hand" Click="Login_Click" Margin="0,0,0,20">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    CornerRadius="12" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#5A67D8"/>
                                                </Trigger>
                                                <Trigger Property="IsPressed" Value="True">
                                                    <Setter Property="Background" Value="#4C51BF"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>

                    <!-- رابط نسيت كلمة المرور -->
                    <TextBlock HorizontalAlignment="Center" Margin="0,0,0,30">
                        <Hyperlink Foreground="#667EEA" TextDecorations="None">
                            <TextBlock Text="نسيت كلمة المرور؟" FontSize="14"/>
                        </Hyperlink>
                    </TextBlock>

                    <!-- الفوتر -->
                    <Border Background="#F8FAFC" CornerRadius="8" Padding="20" Margin="0,20,0,0">
                        <StackPanel>
                            <!-- حالة الاتصال -->
                            <Grid Margin="0,0,0,15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- حالة الإنترنت -->
                                <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Ellipse x:Name="InternetStatusIndicator" Width="10" Height="10"
                                             Fill="#10B981" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                    <TextBlock x:Name="InternetStatusText" Text="متصل بالإنترنت"
                                               FontSize="12" Foreground="#374151" VerticalAlignment="Center"/>
                                </StackPanel>

                                <!-- حالة السيرفر -->
                                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Ellipse x:Name="ServerStatusIndicator" Width="10" Height="10"
                                             Fill="#10B981" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                    <TextBlock x:Name="ServerStatusText" Text="السيرفر نشط"
                                               FontSize="12" Foreground="#374151" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Grid>

                            <!-- حقوق النشر -->
                            <TextBlock Text="© 2024 GenerationFive - جميع الحقوق محفوظة"
                                       FontSize="11" Foreground="#9CA3AF" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Border>
        </Grid>

        <!-- زر الإغلاق -->
        <Button x:Name="CloseButton" Content="✕"
                Background="#EF4444" Foreground="White"
                BorderThickness="0" FontSize="16" FontWeight="Bold"
                Width="35" Height="35"
                HorizontalAlignment="Left" VerticalAlignment="Top"
                Margin="20,20,0,0" Cursor="Hand"
                Click="Close_Click">
            <Button.Style>
                <Style TargetType="Button">
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}"
                                        CornerRadius="17">
                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#DC2626"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </Button.Style>
        </Button>
    </Grid>
</Window>
