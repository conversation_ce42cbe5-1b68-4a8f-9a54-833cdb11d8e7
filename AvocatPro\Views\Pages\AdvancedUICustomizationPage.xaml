<Page x:Class="AvocatPro.Views.Pages.AdvancedUICustomizationPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="تخصيص الواجهة المتقدم" Background="#F8FAFC">

    <Page.Resources>
        <!-- Modern Button Style -->
        <Style TargetType="Button" x:Key="ModernButtonStyle">
            <Setter Property="Background" Value="#3B82F6"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="6" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#2563EB"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#1D4ED8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style TargetType="Border" x:Key="CardStyle">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style TargetType="TextBlock" x:Key="SectionTitleStyle">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1F2937"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style TargetType="TextBlock" x:Key="SubtitleStyle">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#6B7280"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>
    </Page.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Page Header -->
        <Border Grid.Row="0" Background="White" Padding="30,20" BorderBrush="#E5E7EB" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="🎨 تخصيص الواجهة المتقدم" FontSize="24" FontWeight="Bold" Foreground="#1F2937"/>
                    <TextBlock Text="خصص مظهر التطبيق وتخطيط الواجهة حسب احتياجاتك" FontSize="14" Foreground="#6B7280" Margin="0,5,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="SaveChangesButton" Content="💾 حفظ التغييرات" 
                            Style="{StaticResource ModernButtonStyle}" 
                            Click="SaveChanges_Click" Margin="0,0,10,0"/>
                    <Button x:Name="ResetToDefaultButton" Content="🔄 استعادة الافتراضي" 
                            Style="{StaticResource ModernButtonStyle}" 
                            Background="#EF4444" Click="ResetToDefault_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="30">
            <StackPanel>
                
                <!-- Theme Management -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="🌈 إدارة الثيمات" Style="{StaticResource SectionTitleStyle}"/>
                        <TextBlock Text="اختر من الثيمات المتاحة أو أنشئ ثيم مخصص" Style="{StaticResource SubtitleStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,15,0">
                                <Label Content="الثيم الحالي" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <ComboBox x:Name="ThemeComboBox" Height="40" FontSize="14" 
                                          SelectionChanged="Theme_SelectionChanged">
                                    <ComboBoxItem Content="🌞 الوضع النهاري" IsSelected="True"/>
                                    <ComboBoxItem Content="🌙 الوضع الليلي"/>
                                    <ComboBoxItem Content="💼 المهني"/>
                                    <ComboBoxItem Content="✨ الأنيق"/>
                                </ComboBox>
                                
                                <CheckBox x:Name="AutoThemeCheckBox" Content="تبديل تلقائي حسب الوقت" 
                                          Margin="0,10,0,0" FontSize="14"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="15,0,0,0">
                                <Label Content="معاينة الألوان" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <Grid Height="100">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <Border Grid.Column="0" Background="#3B82F6" CornerRadius="4" Margin="2">
                                        <TextBlock Text="أساسي" HorizontalAlignment="Center" VerticalAlignment="Center" 
                                                   Foreground="White" FontSize="10"/>
                                    </Border>
                                    <Border Grid.Column="1" Background="#10B981" CornerRadius="4" Margin="2">
                                        <TextBlock Text="نجاح" HorizontalAlignment="Center" VerticalAlignment="Center" 
                                                   Foreground="White" FontSize="10"/>
                                    </Border>
                                    <Border Grid.Column="2" Background="#F59E0B" CornerRadius="4" Margin="2">
                                        <TextBlock Text="تحذير" HorizontalAlignment="Center" VerticalAlignment="Center" 
                                                   Foreground="White" FontSize="10"/>
                                    </Border>
                                    <Border Grid.Column="3" Background="#EF4444" CornerRadius="4" Margin="2">
                                        <TextBlock Text="خطأ" HorizontalAlignment="Center" VerticalAlignment="Center" 
                                                   Foreground="White" FontSize="10"/>
                                    </Border>
                                </Grid>
                                
                                <Button Content="🎨 إنشاء ثيم مخصص" Style="{StaticResource ModernButtonStyle}"
                                        Background="#8B5CF6" Click="CreateCustomTheme_Click" Margin="0,10,0,0"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- UI Layout -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="📐 تخطيط الواجهة" Style="{StaticResource SectionTitleStyle}"/>
                        <TextBlock Text="اختر تخطيط الواجهة أو خصص ترتيب العناصر" Style="{StaticResource SubtitleStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,15,0">
                                <Label Content="التخطيط المحدد" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <ComboBox x:Name="LayoutComboBox" Height="40" FontSize="14"
                                          SelectionChanged="Layout_SelectionChanged">
                                    <ComboBoxItem Content="📱 التخطيط الافتراضي" IsSelected="True"/>
                                    <ComboBoxItem Content="📦 التخطيط المدمج"/>
                                    <ComboBoxItem Content="🖥️ التخطيط الموسع"/>
                                </ComboBox>
                                
                                <Button Content="✏️ تخصيص التخطيط" Style="{StaticResource ModernButtonStyle}"
                                        Background="#10B981" Click="CustomizeLayout_Click" Margin="0,10,0,0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="15,0,0,0">
                                <Label Content="معاينة التخطيط" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <Border Background="#F3F4F6" CornerRadius="8" Height="120" Padding="10">
                                    <Grid x:Name="LayoutPreviewGrid">
                                        <!-- Layout preview will be added dynamically -->
                                        <TextBlock Text="معاينة التخطيط" HorizontalAlignment="Center" 
                                                   VerticalAlignment="Center" Foreground="#9CA3AF"/>
                                    </Grid>
                                </Border>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Font and Text Settings -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="🔤 إعدادات الخطوط والنصوص" Style="{StaticResource SectionTitleStyle}"/>
                        <TextBlock Text="تخصيص أنواع وأحجام الخطوط المستخدمة في التطبيق" Style="{StaticResource SubtitleStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <Label Content="نوع الخط الأساسي" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <ComboBox x:Name="PrimaryFontComboBox" Height="40" FontSize="14">
                                    <ComboBoxItem Content="Segoe UI" IsSelected="True"/>
                                    <ComboBoxItem Content="Arial"/>
                                    <ComboBoxItem Content="Tahoma"/>
                                    <ComboBoxItem Content="Calibri"/>
                                    <ComboBoxItem Content="Times New Roman"/>
                                </ComboBox>
                                
                                <Label Content="حجم الخط الأساسي" FontWeight="SemiBold" Margin="0,10,0,5"/>
                                <Slider x:Name="PrimaryFontSizeSlider" Minimum="10" Maximum="20" Value="14" 
                                        TickFrequency="1" IsSnapToTickEnabled="True"/>
                                <TextBlock x:Name="PrimaryFontSizeLabel" Text="14px" HorizontalAlignment="Center" 
                                           FontSize="12" Foreground="#6B7280"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="10,0,10,0">
                                <Label Content="نوع خط العناوين" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <ComboBox x:Name="HeaderFontComboBox" Height="40" FontSize="14">
                                    <ComboBoxItem Content="Segoe UI" IsSelected="True"/>
                                    <ComboBoxItem Content="Arial Black"/>
                                    <ComboBoxItem Content="Impact"/>
                                    <ComboBoxItem Content="Trebuchet MS"/>
                                </ComboBox>
                                
                                <Label Content="حجم خط العناوين" FontWeight="SemiBold" Margin="0,10,0,5"/>
                                <Slider x:Name="HeaderFontSizeSlider" Minimum="16" Maximum="32" Value="24" 
                                        TickFrequency="2" IsSnapToTickEnabled="True"/>
                                <TextBlock x:Name="HeaderFontSizeLabel" Text="24px" HorizontalAlignment="Center" 
                                           FontSize="12" Foreground="#6B7280"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2" Margin="10,0,0,0">
                                <Label Content="معاينة النص" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <Border Background="#F9FAFB" CornerRadius="6" Padding="15" Height="120">
                                    <StackPanel>
                                        <TextBlock x:Name="HeaderPreview" Text="عنوان تجريبي" 
                                                   FontSize="18" FontWeight="Bold" Margin="0,0,0,10"/>
                                        <TextBlock x:Name="TextPreview" Text="هذا نص تجريبي لمعاينة الخط المحدد. يمكنك رؤية كيف سيظهر النص في التطبيق." 
                                                   FontSize="14" TextWrapping="Wrap"/>
                                    </StackPanel>
                                </Border>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Animation Settings -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="🎬 إعدادات الرسوم المتحركة" Style="{StaticResource SectionTitleStyle}"/>
                        <TextBlock Text="تحكم في الرسوم المتحركة والانتقالات في التطبيق" Style="{StaticResource SubtitleStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,15,0">
                                <CheckBox x:Name="EnableAnimationsCheckBox" Content="تفعيل الرسوم المتحركة" 
                                          FontSize="14" IsChecked="True" Margin="0,0,0,10"/>
                                <CheckBox x:Name="EnableTransitionsCheckBox" Content="تفعيل انتقالات الصفحات" 
                                          FontSize="14" IsChecked="True" Margin="0,0,0,10"/>
                                <CheckBox x:Name="EnableHoverEffectsCheckBox" Content="تأثيرات التمرير" 
                                          FontSize="14" IsChecked="True" Margin="0,0,0,10"/>
                                
                                <Label Content="سرعة الرسوم المتحركة" FontWeight="SemiBold" Margin="0,10,0,5"/>
                                <ComboBox x:Name="AnimationSpeedComboBox" Height="40" FontSize="14">
                                    <ComboBoxItem Content="بطيء"/>
                                    <ComboBoxItem Content="عادي" IsSelected="True"/>
                                    <ComboBoxItem Content="سريع"/>
                                    <ComboBoxItem Content="فوري"/>
                                </ComboBox>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="15,0,0,0">
                                <Label Content="نوع الانتقال" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <ComboBox x:Name="TransitionTypeComboBox" Height="40" FontSize="14">
                                    <ComboBoxItem Content="انزلاق" IsSelected="True"/>
                                    <ComboBoxItem Content="تلاشي"/>
                                    <ComboBoxItem Content="تكبير/تصغير"/>
                                    <ComboBoxItem Content="قلب"/>
                                    <ComboBoxItem Content="ارتداد"/>
                                </ComboBox>
                                
                                <Button Content="🎭 معاينة الانتقال" Style="{StaticResource ModernButtonStyle}"
                                        Background="#8B5CF6" Click="PreviewTransition_Click" Margin="0,20,0,0"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>
    </Grid>
</Page>
