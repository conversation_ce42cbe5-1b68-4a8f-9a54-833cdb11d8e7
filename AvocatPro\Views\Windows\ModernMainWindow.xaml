<Window x:Class="AvocatPro.Views.Windows.ModernMainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:AvocatPro.Views.Controls"
        Title="AvocatPro - نظام إدارة مكاتب المحاماة" 
        Height="900" Width="1400" 
        MinHeight="700" MinWidth="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        FlowDirection="RightToLeft"
        Background="#F8FAFC">

    <Window.Resources>
        <!-- أنماط النافذة الحديثة -->
        <Style x:Key="ModernWindowStyle" TargetType="Window">
            <Setter Property="WindowChrome.WindowChrome">
                <Setter.Value>
                    <WindowChrome CornerRadius="15" 
                                  GlassFrameThickness="0" 
                                  UseAeroCaptionButtons="False"
                                  CaptionHeight="40"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- أنماط شريط العنوان المخصص -->
        <Style x:Key="TitleBarButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#6B7280"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Width" Value="45"/>
            <Setter Property="Height" Value="35"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" 
                                Background="{TemplateBinding Background}"
                                CornerRadius="8">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#F3F4F6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- أنماط زر الإغلاق -->
        <Style x:Key="CloseButtonStyle" TargetType="Button" BasedOn="{StaticResource TitleBarButtonStyle}">
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#FEE2E2"/>
                    <Setter Property="Foreground" Value="#DC2626"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان المخصص -->
        <Border Grid.Row="0" Background="White" CornerRadius="15,15,0,0" Height="50" 
                MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.05" BlurRadius="10" ShadowDepth="2"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- شعار وعنوان البرنامج -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <Border Background="#6366F1" CornerRadius="8" Width="30" Height="30" Margin="0,0,10,0">
                        <TextBlock Text="⚖️" FontSize="16" Foreground="White" 
                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock Text="AvocatPro" FontSize="16" FontWeight="Bold" Foreground="#1F2937"/>
                        <TextBlock Text="نظام إدارة مكاتب المحاماة" FontSize="10" Foreground="#6B7280"/>
                    </StackPanel>
                </StackPanel>

                <!-- معلومات الحالة -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                    <!-- حالة الاتصال -->
                    <Border Background="#ECFDF5" CornerRadius="20" Padding="10,5" Margin="0,0,15,0">
                        <StackPanel Orientation="Horizontal">
                            <Ellipse Width="8" Height="8" Fill="#10B981" Margin="0,0,5,0"/>
                            <TextBlock Text="متصل" FontSize="11" Foreground="#059669" FontWeight="SemiBold"/>
                        </StackPanel>
                    </Border>

                    <!-- الوقت والتاريخ -->
                    <TextBlock x:Name="DateTimeDisplay" FontSize="12" Foreground="#6B7280" VerticalAlignment="Center"/>
                </StackPanel>

                <!-- أزرار التحكم في النافذة -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" Margin="0,0,15,0">
                    <Button Style="{StaticResource TitleBarButtonStyle}" Click="MinimizeWindow_Click" ToolTip="تصغير">
                        <TextBlock Text="🗕" FontSize="12"/>
                    </Button>
                    <Button Style="{StaticResource TitleBarButtonStyle}" Click="MaximizeWindow_Click" ToolTip="تكبير/استعادة">
                        <TextBlock Text="🗖" FontSize="12"/>
                    </Button>
                    <Button Style="{StaticResource CloseButtonStyle}" Click="CloseWindow_Click" ToolTip="إغلاق">
                        <TextBlock Text="✕" FontSize="12"/>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- القائمة الجانبية -->
            <controls:ModernSidebarControl x:Name="SidebarControl"
                                           Grid.Column="0"/>

            <!-- منطقة المحتوى الرئيسي -->
            <Border Grid.Column="1" Background="White" CornerRadius="25,0,15,15" Margin="0,0,15,15">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.05" BlurRadius="15" ShadowDepth="3"/>
                </Border.Effect>
                
                <Frame x:Name="MainContentFrame" 
                       NavigationUIVisibility="Hidden"
                       Background="Transparent"
                       Margin="0"/>
            </Border>
        </Grid>

        <!-- شريط الحالة السفلي -->
        <Border Grid.Row="1" VerticalAlignment="Bottom" Background="White" 
                CornerRadius="15,15,0,0" Height="35" Margin="15,0,15,0">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.03" BlurRadius="8" ShadowDepth="2"/>
            </Border.Effect>
            
            <Grid Margin="20,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- معلومات النظام -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="الإصدار 2.0.1" FontSize="11" Foreground="#9CA3AF" Margin="0,0,20,0"/>
                    <TextBlock Text="قاعدة البيانات: متصلة" FontSize="11" Foreground="#059669" Margin="0,0,20,0"/>
                    <TextBlock x:Name="UserCountDisplay" Text="المستخدمين النشطين: 3" FontSize="11" Foreground="#6B7280"/>
                </StackPanel>

                <!-- مؤشرات الأداء -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,20,0">
                    <TextBlock Text="الذاكرة:" FontSize="11" Foreground="#6B7280" Margin="0,0,5,0"/>
                    <TextBlock x:Name="MemoryUsageDisplay" Text="245 MB" FontSize="11" Foreground="#059669"/>
                </StackPanel>

                <!-- حالة النسخ الاحتياطي -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,20,0">
                    <TextBlock Text="آخر نسخة احتياطية:" FontSize="11" Foreground="#6B7280" Margin="0,0,5,0"/>
                    <TextBlock x:Name="LastBackupDisplay" Text="اليوم 14:30" FontSize="11" Foreground="#059669"/>
                </StackPanel>

                <!-- زر المساعدة السريعة -->
                <Button Grid.Column="3" Background="Transparent" BorderThickness="0"
                        Padding="8,4" Cursor="Hand" Click="QuickHelp_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="❓" FontSize="12" Margin="0,0,5,0"/>
                        <TextBlock Text="مساعدة سريعة" FontSize="11" Foreground="#6366F1"/>
                    </StackPanel>
                </Button>
            </Grid>
        </Border>

        <!-- طبقة التحميل -->
        <Border x:Name="LoadingOverlay" Grid.RowSpan="2" Background="#80000000" 
                Visibility="Collapsed" Panel.ZIndex="1000">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <Border Background="White" CornerRadius="15" Padding="40,30">
                    <StackPanel HorizontalAlignment="Center">
                        <ProgressBar Width="200" Height="4" IsIndeterminate="True" 
                                     Background="#E5E7EB" Foreground="#6366F1" Margin="0,0,0,15"/>
                        <TextBlock x:Name="LoadingText" Text="جاري التحميل..." 
                                   FontSize="14" Foreground="#1F2937" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Border>

        <!-- نافذة الإشعارات المنبثقة -->
        <Border x:Name="NotificationPanel" Grid.RowSpan="2" 
                HorizontalAlignment="Left" VerticalAlignment="Top" 
                Margin="30,80,0,0" Panel.ZIndex="999" Visibility="Collapsed">
            <Border Background="White" CornerRadius="12" Padding="20" MinWidth="300">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.15" BlurRadius="20" ShadowDepth="5"/>
                </Border.Effect>
                
                <StackPanel>
                    <Grid Margin="0,0,0,15">
                        <TextBlock Text="الإشعارات" FontSize="16" FontWeight="SemiBold" Foreground="#1F2937"/>
                        <Button HorizontalAlignment="Left" Background="Transparent" BorderThickness="0" 
                                Padding="5" Cursor="Hand" Click="CloseNotifications_Click">
                            <TextBlock Text="✕" FontSize="12" Foreground="#9CA3AF"/>
                        </Button>
                    </Grid>
                    
                    <ScrollViewer MaxHeight="300" VerticalScrollBarVisibility="Auto">
                        <StackPanel x:Name="NotificationsList">
                            <!-- الإشعارات ستضاف هنا ديناميكياً -->
                        </StackPanel>
                    </ScrollViewer>
                </StackPanel>
            </Border>
        </Border>
    </Grid>
</Window>
