using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using AvocatPro.Models;
using AvocatPro.Views.Windows;

namespace AvocatPro.Views.Pages;

public partial class AppointmentsPageNew : Page
{
    private readonly User _currentUser;
    private readonly ObservableCollection<AppointmentDisplayModel> _allAppointments;
    private readonly ObservableCollection<AppointmentDisplayModel> _filteredAppointments;
    private DateTime _currentMonth;
    private bool _isCalendarView = true;
    private System.Windows.Threading.DispatcherTimer _notificationTimer;

    public AppointmentsPageNew(User currentUser)
    {
        InitializeComponent();
        _currentUser = currentUser;
        _allAppointments = new ObservableCollection<AppointmentDisplayModel>();
        _filteredAppointments = new ObservableCollection<AppointmentDisplayModel>();
        _currentMonth = DateTime.Now;
        
        this.Loaded += AppointmentsPageNew_Loaded;
        InitializeNotificationTimer();
    }

    private void AppointmentsPageNew_Loaded(object sender, RoutedEventArgs e)
    {
        try
        {
            if (AppointmentsDataGrid != null)
            {
                AppointmentsDataGrid.ItemsSource = _filteredAppointments;
            }
            
            LoadSampleData();
            UpdateStatistics();
            UpdateCurrentMonthDisplay();
            BuildCalendar();
            UpdateStatusText();
            CheckPendingNotifications();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل صفحة المواعيد: {ex.Message}", "خطأ", 
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void InitializeNotificationTimer()
    {
        _notificationTimer = new System.Windows.Threading.DispatcherTimer();
        _notificationTimer.Interval = TimeSpan.FromMinutes(1); // فحص كل دقيقة
        _notificationTimer.Tick += NotificationTimer_Tick;
        _notificationTimer.Start();
    }

    private void NotificationTimer_Tick(object sender, EventArgs e)
    {
        CheckPendingNotifications();
    }

    private void CheckPendingNotifications()
    {
        try
        {
            var pendingNotifications = _allAppointments.Where(a => a.ShouldNotifyNow).ToList();
            var overdueAppointments = _allAppointments.Where(a => a.IsOverdue).ToList();
            
            var totalPending = pendingNotifications.Count + overdueAppointments.Count;
            
            if (totalPending > 0)
            {
                NotificationBadge.Visibility = Visibility.Visible;
                NotificationCount.Text = totalPending.ToString();
                
                // إظهار تنبيه فوري للمواعيد الحالية
                foreach (var appointment in pendingNotifications)
                {
                    ShowInstantNotification(appointment);
                }
            }
            else
            {
                NotificationBadge.Visibility = Visibility.Collapsed;
            }
            
            // تحديث عدد التنبيهات المعلقة في الإحصائيات
            PendingNotificationsTextBlock.Text = totalPending.ToString();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في فحص التنبيهات: {ex.Message}");
        }
    }

    private void ShowInstantNotification(AppointmentDisplayModel appointment)
    {
        var message = $"🔔 تنبيه موعد!\n\n" +
                     $"العنوان: {appointment.Title}\n" +
                     $"الوقت: {appointment.TimeRangeDisplay}\n" +
                     $"الموكل: {appointment.ClientDisplay}\n" +
                     $"المكان: {appointment.LocationDisplay}";

        MessageBox.Show(message, "تنبيه موعد", MessageBoxButton.OK, MessageBoxImage.Information);
        
        // تحديث حالة التنبيه
        appointment.IsNotified = true;
        appointment.NotificationDate = DateTime.Now;
    }

    private void LoadSampleData()
    {
        var sampleAppointments = new List<AppointmentDisplayModel>
        {
            new AppointmentDisplayModel
            {
                Id = 1,
                Title = "استشارة قانونية - قضية عمالية",
                Description = "مناقشة تفاصيل قضية فصل تعسفي",
                AppointmentDate = DateTime.Now.AddDays(1),
                StartTime = new TimeSpan(9, 0, 0),
                EndTime = new TimeSpan(10, 0, 0),
                Type = AppointmentType.Consultation,
                Status = AppointmentStatus.Confirmed,
                Priority = AppointmentPriority.High,
                ClientId = 1,
                ClientName = "فاطمة أحمد سالم",
                CaseId = 2,
                CaseTitle = "قضية عمالية - فصل تعسفي",
                Location = "مكتب المحاماة - الطابق الثاني",
                RequiredAttendees = "المحامي المختص، الموكل",
                Cost = 500,
                IsPaid = true,
                NotificationMinutes = 30,
                AppointmentNotes = "إحضار جميع الوثائق المتعلقة بالعمل",
                ContactPhone = "0501234567",
                ContactEmail = "<EMAIL>",
                CreatedByUserName = "د. محمد أحمد الشريف",
                CreatedAt = DateTime.Now.AddDays(-2)
            },
            new AppointmentDisplayModel
            {
                Id = 2,
                Title = "اجتماع مع شركة النور للتجارة",
                Description = "مناقشة تفاصيل عقد الشراكة الجديد",
                AppointmentDate = DateTime.Now.AddDays(3),
                StartTime = new TimeSpan(14, 0, 0),
                EndTime = new TimeSpan(15, 30, 0),
                Type = AppointmentType.Meeting,
                Status = AppointmentStatus.Scheduled,
                Priority = AppointmentPriority.Medium,
                ClientId = 4,
                ClientName = "شركة النور للتجارة والاستثمار",
                CaseId = 3,
                CaseTitle = "قضية تجارية - نزاع شراكة",
                Location = "مقر الشركة - الرياض",
                RequiredAttendees = "المدير التنفيذي، المحامي، المستشار القانوني",
                Cost = 1000,
                IsPaid = false,
                NotificationMinutes = 60,
                AppointmentNotes = "مراجعة مسودة العقد قبل الاجتماع",
                ContactPhone = "0112345678",
                ContactEmail = "<EMAIL>",
                CreatedByUserName = "أ. خالد سالم العتيبي",
                CreatedAt = DateTime.Now.AddDays(-1)
            },
            new AppointmentDisplayModel
            {
                Id = 3,
                Title = "مكالمة هاتفية - متابعة قضية",
                Description = "متابعة سير القضية مع المحكمة",
                AppointmentDate = DateTime.Now,
                StartTime = new TimeSpan(11, 0, 0),
                EndTime = new TimeSpan(11, 30, 0),
                Type = AppointmentType.PhoneCall,
                Status = AppointmentStatus.InProgress,
                Priority = AppointmentPriority.Urgent,
                ClientId = 1,
                ClientName = "أحمد محمد علي السعيد",
                CaseId = 1,
                CaseTitle = "دعوى مطالبة مالية",
                Location = "مكالمة هاتفية",
                RequiredAttendees = "المحامي، الموكل",
                Cost = 200,
                IsPaid = true,
                NotificationMinutes = 15,
                AppointmentNotes = "الاستعداد لجلسة المحكمة القادمة",
                ContactPhone = "0509876543",
                ContactEmail = "<EMAIL>",
                CreatedByUserName = "د. محمد أحمد الشريف",
                CreatedAt = DateTime.Now.AddDays(-5)
            },
            new AppointmentDisplayModel
            {
                Id = 4,
                Title = "زيارة ميدانية - معاينة عقار",
                Description = "معاينة العقار محل النزاع",
                AppointmentDate = DateTime.Now.AddDays(7),
                StartTime = new TimeSpan(10, 0, 0),
                EndTime = new TimeSpan(12, 0, 0),
                Type = AppointmentType.FieldVisit,
                Status = AppointmentStatus.Scheduled,
                Priority = AppointmentPriority.High,
                ClientId = 3,
                ClientName = "مريم عبدالله الزهراني",
                CaseId = 5,
                CaseTitle = "قضية عقارية - منازعة ملكية",
                Location = "العقار - حي الملز، الرياض",
                RequiredAttendees = "المحامي، الموكل، الخبير العقاري",
                Cost = 800,
                IsPaid = false,
                NotificationMinutes = 120,
                AppointmentNotes = "إحضار جميع الوثائق العقارية",
                ContactPhone = "0551234567",
                ContactEmail = "<EMAIL>",
                CreatedByUserName = "د. نورا محمد الزهراني",
                CreatedAt = DateTime.Now.AddDays(-3)
            },
            new AppointmentDisplayModel
            {
                Id = 5,
                Title = "توقيع عقد استشارة قانونية",
                Description = "توقيع عقد الاستشارة القانونية الشاملة",
                AppointmentDate = DateTime.Now.AddDays(-2),
                StartTime = new TimeSpan(16, 0, 0),
                EndTime = new TimeSpan(17, 0, 0),
                Type = AppointmentType.ContractSigning,
                Status = AppointmentStatus.Completed,
                Priority = AppointmentPriority.Medium,
                ClientId = 5,
                ClientName = "جمعية البر الخيرية",
                CaseId = 6,
                CaseTitle = "قضية جنائية - اختلاس",
                Location = "مكتب المحاماة",
                RequiredAttendees = "ممثل الجمعية، المحامي",
                Cost = 2000,
                IsPaid = true,
                NotificationMinutes = 30,
                AppointmentNotes = "تم توقيع العقد بنجاح",
                ContactPhone = "0114567890",
                ContactEmail = "<EMAIL>",
                CreatedByUserName = "أ. خالد سالم العتيبي",
                CreatedAt = DateTime.Now.AddDays(-7)
            },
            new AppointmentDisplayModel
            {
                Id = 6,
                Title = "موعد شخصي - إجازة",
                Description = "إجازة شخصية",
                AppointmentDate = DateTime.Now.AddDays(14),
                StartTime = new TimeSpan(9, 0, 0),
                EndTime = new TimeSpan(17, 0, 0),
                Type = AppointmentType.Personal,
                Status = AppointmentStatus.Scheduled,
                Priority = AppointmentPriority.Low,
                ClientId = null,
                ClientName = null,
                CaseId = null,
                CaseTitle = null,
                Location = "خارج المكتب",
                RequiredAttendees = "غير محدد",
                Cost = 0,
                IsPaid = true,
                NotificationMinutes = 1440,
                AppointmentNotes = "إجازة مجدولة مسبقاً",
                ContactPhone = null,
                ContactEmail = null,
                CreatedByUserName = "د. محمد أحمد الشريف",
                CreatedAt = DateTime.Now.AddDays(-10)
            }
        };

        _allAppointments.Clear();
        _filteredAppointments.Clear();
        
        foreach (var appointment in sampleAppointments)
        {
            _allAppointments.Add(appointment);
            _filteredAppointments.Add(appointment);
        }
    }

    private void UpdateStatistics()
    {
        try
        {
            var today = DateTime.Today;
            var startOfWeek = today.AddDays(-(int)today.DayOfWeek + (int)DayOfWeek.Saturday);
            var endOfWeek = startOfWeek.AddDays(6);

            if (TotalAppointmentsTextBlock != null)
            {
                TotalAppointmentsTextBlock.Text = _allAppointments.Count.ToString();
                TodayAppointmentsTextBlock.Text = _allAppointments.Count(a => a.AppointmentDate.Date == today).ToString();
                WeekAppointmentsTextBlock.Text = _allAppointments.Count(a => a.AppointmentDate.Date >= startOfWeek && a.AppointmentDate.Date <= endOfWeek).ToString();
                ConfirmedAppointmentsTextBlock.Text = _allAppointments.Count(a => a.Status == AppointmentStatus.Confirmed).ToString();
                
                var totalRevenue = _allAppointments.Where(a => a.IsPaid && a.Cost.HasValue).Sum(a => a.Cost.Value);
                TotalRevenueTextBlock.Text = totalRevenue.ToString("N0") + " ريال";
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الإحصائيات: {ex.Message}");
        }
    }

    private void UpdateCurrentMonthDisplay()
    {
        try
        {
            if (CurrentMonthTextBlock != null)
            {
                var culture = new CultureInfo("ar-SA");
                CurrentMonthTextBlock.Text = _currentMonth.ToString("MMMM yyyy", culture);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث عرض الشهر: {ex.Message}");
        }
    }

    private void UpdateStatusText()
    {
        try
        {
            if (StatusTextBlock != null)
            {
                StatusTextBlock.Text = $"عرض {_filteredAppointments.Count} من أصل {_allAppointments.Count} موعد";
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث نص الحالة: {ex.Message}");
        }
    }

    private void ApplyFilters()
    {
        try
        {
            var selectedType = (AppointmentTypeFilterComboBox?.SelectedItem as ComboBoxItem)?.Tag?.ToString();
            var selectedStatus = (AppointmentStatusFilterComboBox?.SelectedItem as ComboBoxItem)?.Tag?.ToString();
            var selectedPriority = (PriorityFilterComboBox?.SelectedItem as ComboBoxItem)?.Tag?.ToString();

            var filtered = _allAppointments.Where(appointment =>
            {
                var matchesType = selectedType == "All" || selectedType == null ||
                                 appointment.Type.ToString() == selectedType;

                var matchesStatus = selectedStatus == "All" || selectedStatus == null ||
                                   appointment.Status.ToString() == selectedStatus;

                var matchesPriority = selectedPriority == "All" || selectedPriority == null ||
                                     appointment.Priority.ToString() == selectedPriority;

                return matchesType && matchesStatus && matchesPriority;
            });

            _filteredAppointments.Clear();
            foreach (var appointment in filtered)
            {
                _filteredAppointments.Add(appointment);
            }

            if (_isCalendarView)
            {
                BuildCalendar();
            }
            
            UpdateStatusText();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق المرشحات: {ex.Message}");
        }
    }

    private void BuildCalendar()
    {
        try
        {
            if (CalendarGrid == null) return;

            CalendarGrid.Children.Clear();

            var firstDayOfMonth = new DateTime(_currentMonth.Year, _currentMonth.Month, 1);
            var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);

            // البداية من السبت (أول يوم في الأسبوع العربي)
            var startDate = firstDayOfMonth;
            while (startDate.DayOfWeek != DayOfWeek.Saturday)
            {
                startDate = startDate.AddDays(-1);
            }

            // إنشاء 42 يوم (6 أسابيع × 7 أيام)
            for (int i = 0; i < 42; i++)
            {
                var currentDate = startDate.AddDays(i);
                var dayBorder = CreateCalendarDay(currentDate, firstDayOfMonth, lastDayOfMonth);
                CalendarGrid.Children.Add(dayBorder);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في بناء التقويم: {ex.Message}");
        }
    }

    private Border CreateCalendarDay(DateTime date, DateTime firstDayOfMonth, DateTime lastDayOfMonth)
    {
        var dayBorder = new Border
        {
            Background = new SolidColorBrush(Colors.White),
            BorderBrush = new SolidColorBrush(Color.FromRgb(224, 224, 224)),
            BorderThickness = new Thickness(1),
            Margin = new Thickness(1),
            Padding = new Thickness(5),
            Height = 90,
            Cursor = System.Windows.Input.Cursors.Hand,
            Tag = date
        };

        var stackPanel = new StackPanel();

        // رقم اليوم
        var dayNumber = new TextBlock
        {
            Text = date.Day.ToString(),
            FontWeight = FontWeights.Bold,
            HorizontalAlignment = HorizontalAlignment.Left,
            Margin = new Thickness(0, 0, 0, 2)
        };

        // تلوين الأيام حسب الشهر
        if (date < firstDayOfMonth || date > lastDayOfMonth)
        {
            dayNumber.Foreground = new SolidColorBrush(Colors.LightGray);
            dayBorder.Background = new SolidColorBrush(Color.FromRgb(250, 250, 250));
        }
        else if (date.Date == DateTime.Today)
        {
            dayNumber.Foreground = new SolidColorBrush(Colors.White);
            dayBorder.Background = new SolidColorBrush(Color.FromRgb(25, 118, 210));
        }
        else if (date.DayOfWeek == DayOfWeek.Friday)
        {
            dayNumber.Foreground = new SolidColorBrush(Color.FromRgb(244, 67, 54));
        }
        else
        {
            dayNumber.Foreground = new SolidColorBrush(Color.FromRgb(33, 33, 33));
        }

        stackPanel.Children.Add(dayNumber);

        // إضافة المواعيد لهذا اليوم
        var appointmentsForDay = _filteredAppointments.Where(a => a.AppointmentDate.Date == date.Date).Take(3);
        foreach (var appointment in appointmentsForDay)
        {
            var appointmentBorder = new Border
            {
                CornerRadius = new CornerRadius(3),
                Padding = new Thickness(3, 1, 3, 1),
                Margin = new Thickness(0, 1, 0, 1),
                Cursor = System.Windows.Input.Cursors.Hand,
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(appointment.StatusColor)),
                ToolTip = $"{appointment.CalendarTitle}\n{appointment.TimeRangeDisplay}\n{appointment.CalendarDescription}"
            };

            var appointmentText = new TextBlock
            {
                Text = $"{appointment.StartTimeDisplay} {appointment.TypeIcon}",
                FontSize = 9,
                Foreground = new SolidColorBrush(Colors.White),
                TextTrimming = TextTrimming.CharacterEllipsis
            };

            appointmentBorder.Child = appointmentText;
            stackPanel.Children.Add(appointmentBorder);
        }

        // إضافة مؤشر إذا كان هناك مواعيد أكثر
        var remainingAppointments = _filteredAppointments.Count(a => a.AppointmentDate.Date == date.Date) - 3;
        if (remainingAppointments > 0)
        {
            var moreText = new TextBlock
            {
                Text = $"+{remainingAppointments} أخرى",
                FontSize = 8,
                Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102)),
                HorizontalAlignment = HorizontalAlignment.Center
            };
            stackPanel.Children.Add(moreText);
        }

        dayBorder.Child = stackPanel;
        dayBorder.MouseLeftButtonUp += (s, e) => DayBorder_Click(date);

        return dayBorder;
    }

    private void DayBorder_Click(DateTime date)
    {
        var appointmentsForDay = _filteredAppointments.Where(a => a.AppointmentDate.Date == date.Date).ToList();

        if (appointmentsForDay.Count == 0)
        {
            var result = MessageBox.Show($"لا توجد مواعيد في يوم {date:dd/MM/yyyy}\n\nهل تريد إضافة موعد جديد؟",
                                       "مواعيد اليوم", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                AddAppointmentButton_Click(null, null);
            }
        }
        else
        {
            var message = $"مواعيد يوم {date:dd/MM/yyyy}:\n\n";
            foreach (var appointment in appointmentsForDay)
            {
                message += $"• {appointment.TimeRangeDisplay} - {appointment.Title}\n";
                message += $"  النوع: {appointment.TypeDisplay} | الحالة: {appointment.StatusDisplay}\n";
                message += $"  الموكل: {appointment.ClientDisplay}\n\n";
            }

            MessageBox.Show(message, "مواعيد اليوم", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    // معالجات الأحداث
    private void AddAppointmentButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var addWindow = new AddAppointmentWindow();
            if (addWindow.ShowDialog() == true)
            {
                if (addWindow.NewAppointment != null)
                {
                    var newAppointmentDisplay = new AppointmentDisplayModel
                    {
                        Id = _allAppointments.Count + 1,
                        Title = addWindow.NewAppointment.Title,
                        Description = addWindow.NewAppointment.Description,
                        AppointmentDate = addWindow.NewAppointment.AppointmentDate,
                        StartTime = addWindow.NewAppointment.StartTime,
                        EndTime = addWindow.NewAppointment.EndTime,
                        Type = addWindow.NewAppointment.Type,
                        Status = addWindow.NewAppointment.Status,
                        Priority = addWindow.NewAppointment.Priority,
                        ClientId = addWindow.NewAppointment.ClientId,
                        ClientName = "موكل جديد", // في التطبيق الحقيقي من قاعدة البيانات
                        CaseId = addWindow.NewAppointment.CaseId,
                        CaseTitle = "قضية جديدة", // في التطبيق الحقيقي من قاعدة البيانات
                        Location = addWindow.NewAppointment.Location,
                        RequiredAttendees = addWindow.NewAppointment.RequiredAttendees,
                        Cost = addWindow.NewAppointment.Cost,
                        IsPaid = addWindow.NewAppointment.IsPaid,
                        NotificationMinutes = addWindow.NewAppointment.NotificationMinutes,
                        AppointmentNotes = addWindow.NewAppointment.AppointmentNotes,
                        ContactPhone = addWindow.NewAppointment.ContactPhone,
                        ContactEmail = addWindow.NewAppointment.ContactEmail,
                        IsRecurring = addWindow.NewAppointment.IsRecurring,
                        RecurrencePattern = addWindow.NewAppointment.RecurrencePattern,
                        CreatedByUserName = _currentUser.FullName,
                        CreatedAt = DateTime.Now
                    };

                    _allAppointments.Add(newAppointmentDisplay);
                    ApplyFilters();
                    UpdateStatistics();

                    var successMessage = "تم إضافة الموعد بنجاح!";
                    if (addWindow.SaveAndNotify)
                    {
                        successMessage += "\nسيتم إرسال التنبيه في الوقت المحدد.";
                        // هنا يمكن إضافة منطق إرسال التنبيه
                    }

                    MessageBox.Show(successMessage, "نجح الحفظ",
                                   MessageBoxButton.OK, MessageBoxImage.Information);

                    if (addWindow.SaveAndAddAnother)
                    {
                        AddAppointmentButton_Click(sender, e);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إضافة الموعد: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void RefreshButton_Click(object sender, RoutedEventArgs e)
    {
        LoadSampleData();
        UpdateStatistics();
        BuildCalendar();
        UpdateStatusText();
        CheckPendingNotifications();
        MessageBox.Show("تم تحديث البيانات بنجاح!", "تحديث",
                       MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void PrevMonthButton_Click(object sender, RoutedEventArgs e)
    {
        _currentMonth = _currentMonth.AddMonths(-1);
        UpdateCurrentMonthDisplay();
        BuildCalendar();
    }

    private void NextMonthButton_Click(object sender, RoutedEventArgs e)
    {
        _currentMonth = _currentMonth.AddMonths(1);
        UpdateCurrentMonthDisplay();
        BuildCalendar();
    }

    private void FilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        ApplyFilters();
    }

    private void ViewModeButton_Click(object sender, RoutedEventArgs e)
    {
        _isCalendarView = !_isCalendarView;

        if (_isCalendarView)
        {
            CalendarView.Visibility = Visibility.Visible;
            TableView.Visibility = Visibility.Collapsed;
            ViewModeIcon.Text = "📋";
            ViewModeText.Text = "عرض الجدول";
        }
        else
        {
            CalendarView.Visibility = Visibility.Collapsed;
            TableView.Visibility = Visibility.Visible;
            ViewModeIcon.Text = "📅";
            ViewModeText.Text = "عرض التقويم";
        }
    }

    private void NotificationsButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var pendingNotifications = _allAppointments.Where(a => a.ShouldNotifyNow).ToList();
            var overdueAppointments = _allAppointments.Where(a => a.IsOverdue).ToList();
            var todayAppointments = _allAppointments.Where(a => a.IsToday).ToList();
            var tomorrowAppointments = _allAppointments.Where(a => a.IsTomorrow).ToList();

            var message = "🔔 ملخص التنبيهات:\n\n";

            if (pendingNotifications.Count > 0)
            {
                message += $"⚠️ تنبيهات فورية ({pendingNotifications.Count}):\n";
                foreach (var appointment in pendingNotifications.Take(3))
                {
                    message += $"• {appointment.Title} - {appointment.TimeRangeDisplay}\n";
                }
                if (pendingNotifications.Count > 3)
                    message += $"... و {pendingNotifications.Count - 3} أخرى\n";
                message += "\n";
            }

            if (overdueAppointments.Count > 0)
            {
                message += $"🔴 مواعيد متأخرة ({overdueAppointments.Count}):\n";
                foreach (var appointment in overdueAppointments.Take(3))
                {
                    message += $"• {appointment.Title} - {appointment.FullDateTimeDisplay}\n";
                }
                if (overdueAppointments.Count > 3)
                    message += $"... و {overdueAppointments.Count - 3} أخرى\n";
                message += "\n";
            }

            if (todayAppointments.Count > 0)
            {
                message += $"📅 مواعيد اليوم ({todayAppointments.Count}):\n";
                foreach (var appointment in todayAppointments.Take(3))
                {
                    message += $"• {appointment.Title} - {appointment.TimeRangeDisplay}\n";
                }
                if (todayAppointments.Count > 3)
                    message += $"... و {todayAppointments.Count - 3} أخرى\n";
                message += "\n";
            }

            if (tomorrowAppointments.Count > 0)
            {
                message += $"📆 مواعيد الغد ({tomorrowAppointments.Count}):\n";
                foreach (var appointment in tomorrowAppointments.Take(3))
                {
                    message += $"• {appointment.Title} - {appointment.TimeRangeDisplay}\n";
                }
                if (tomorrowAppointments.Count > 3)
                    message += $"... و {tomorrowAppointments.Count - 3} أخرى\n";
            }

            if (pendingNotifications.Count == 0 && overdueAppointments.Count == 0 &&
                todayAppointments.Count == 0 && tomorrowAppointments.Count == 0)
            {
                message = "✅ لا توجد تنبيهات معلقة حالياً\n\nجميع المواعيد منظمة بشكل جيد!";
            }

            MessageBox.Show(message, "التنبيهات والإشعارات", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في عرض التنبيهات: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void SendNotificationsButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var appointmentsToNotify = _allAppointments.Where(a =>
                !a.IsNotified &&
                a.AppointmentDate.Date >= DateTime.Today &&
                a.Status == AppointmentStatus.Scheduled || a.Status == AppointmentStatus.Confirmed).ToList();

            if (appointmentsToNotify.Count == 0)
            {
                MessageBox.Show("لا توجد مواعيد تحتاج إلى إرسال تنبيهات.", "إرسال التنبيهات",
                               MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var result = MessageBox.Show($"سيتم إرسال تنبيهات لـ {appointmentsToNotify.Count} موعد.\n\nهل تريد المتابعة؟",
                                       "تأكيد إرسال التنبيهات", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                foreach (var appointment in appointmentsToNotify)
                {
                    // محاكاة إرسال التنبيه
                    appointment.IsNotified = true;
                    appointment.NotificationDate = DateTime.Now;
                }

                MessageBox.Show($"تم إرسال {appointmentsToNotify.Count} تنبيه بنجاح!\n\n" +
                               "تم إرسال التنبيهات عبر:\n" +
                               "• الإشعارات الفورية\n" +
                               "• البريد الإلكتروني\n" +
                               "• الرسائل النصية",
                               "نجح الإرسال", MessageBoxButton.OK, MessageBoxImage.Information);

                CheckPendingNotifications();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إرسال التنبيهات: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ExportButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var dialog = new Microsoft.Win32.SaveFileDialog
            {
                Filter = "Excel Files (*.xlsx)|*.xlsx|PDF Files (*.pdf)|*.pdf|CSV Files (*.csv)|*.csv",
                DefaultExt = "xlsx",
                FileName = $"مواعيد_المكتب_{DateTime.Now:yyyyMMdd}"
            };

            if (dialog.ShowDialog() == true)
            {
                MessageBox.Show($"تم تصدير {_filteredAppointments.Count} موعد بنجاح إلى:\n{dialog.FileName}",
                               "نجح التصدير", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    // معالجات أحداث الجدول
    private void ViewAppointmentButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int appointmentId)
        {
            var appointment = _allAppointments.FirstOrDefault(a => a.Id == appointmentId);
            if (appointment != null)
            {
                MessageBox.Show($"تفاصيل الموعد:\n\n" +
                               $"العنوان: {appointment.Title}\n" +
                               $"التاريخ والوقت: {appointment.FullDateTimeDisplay}\n" +
                               $"النوع: {appointment.TypeDisplay}\n" +
                               $"الحالة: {appointment.StatusDisplay}\n" +
                               $"الأولوية: {appointment.PriorityDisplay}\n" +
                               $"الموكل: {appointment.ClientDisplay}\n" +
                               $"القضية: {appointment.CaseDisplay}\n" +
                               $"المكان: {appointment.LocationDisplay}\n" +
                               $"الوصف: {appointment.Description}\n" +
                               $"الحضور المطلوب: {appointment.RequiredAttendeesDisplay}\n" +
                               $"التكلفة: {appointment.CostDisplay}\n" +
                               $"حالة الدفع: {appointment.PaymentStatusDisplay}\n" +
                               $"التنبيه: {appointment.NotificationTimeDisplay}\n" +
                               $"الملاحظات: {appointment.AppointmentNotesDisplay}",
                               "تفاصيل الموعد", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }

    private void EditAppointmentButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int appointmentId)
        {
            MessageBox.Show($"سيتم فتح نافذة تعديل الموعد رقم: {appointmentId}", "تعديل الموعد",
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void ConfirmAppointmentButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int appointmentId)
        {
            var appointment = _allAppointments.FirstOrDefault(a => a.Id == appointmentId);
            if (appointment != null)
            {
                var result = MessageBox.Show($"هل تريد تأكيد الموعد:\n{appointment.Title} - {appointment.FullDateTimeDisplay}؟",
                                           "تأكيد الموعد", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    appointment.Status = AppointmentStatus.Confirmed;
                    ApplyFilters();
                    UpdateStatistics();

                    MessageBox.Show("تم تأكيد الموعد بنجاح!\nسيتم إرسال تنبيه للموكل.", "نجح التأكيد",
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }
    }

    private void NotifyAppointmentButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int appointmentId)
        {
            var appointment = _allAppointments.FirstOrDefault(a => a.Id == appointmentId);
            if (appointment != null)
            {
                appointment.IsNotified = true;
                appointment.NotificationDate = DateTime.Now;

                MessageBox.Show($"تم إرسال تنبيه للموعد:\n\n" +
                               $"العنوان: {appointment.Title}\n" +
                               $"التاريخ والوقت: {appointment.FullDateTimeDisplay}\n" +
                               $"الموكل: {appointment.ClientDisplay}\n\n" +
                               "تم الإرسال عبر:\n" +
                               "• الإشعارات الفورية\n" +
                               "• البريد الإلكتروني\n" +
                               "• الرسائل النصية",
                               "تم إرسال التنبيه", MessageBoxButton.OK, MessageBoxImage.Information);

                CheckPendingNotifications();
            }
        }
    }

    private void PostponeAppointmentButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int appointmentId)
        {
            var appointment = _allAppointments.FirstOrDefault(a => a.Id == appointmentId);
            if (appointment != null)
            {
                var result = MessageBox.Show($"هل تريد تأجيل الموعد:\n{appointment.Title} - {appointment.FullDateTimeDisplay}؟",
                                           "تأجيل الموعد", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    appointment.Status = AppointmentStatus.Postponed;
                    ApplyFilters();
                    UpdateStatistics();

                    MessageBox.Show("تم تأجيل الموعد بنجاح!\nيمكنك إعادة جدولته لاحقاً.", "نجح التأجيل",
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }
    }

    private void DeleteAppointmentButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int appointmentId)
        {
            var appointment = _allAppointments.FirstOrDefault(a => a.Id == appointmentId);
            if (appointment != null)
            {
                var result = MessageBox.Show($"هل تريد حذف الموعد:\n{appointment.Title} - {appointment.FullDateTimeDisplay}؟\n\nلا يمكن التراجع عن هذا الإجراء!",
                                           "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    _allAppointments.Remove(appointment);
                    ApplyFilters();
                    UpdateStatistics();

                    MessageBox.Show("تم حذف الموعد بنجاح!", "نجح الحذف",
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }
    }
}
