using AvocatPro.Models;

namespace AvocatPro.Services;

/// <summary>
/// واجهة خدمة إدارة الموكلين
/// </summary>
public interface IClientService
{
    /// <summary>
    /// الحصول على جميع الموكلين
    /// </summary>
    /// <returns>قائمة الموكلين</returns>
    Task<List<Client>> GetAllClientsAsync();

    /// <summary>
    /// الحصول على الموكلين مع التصفحة
    /// </summary>
    /// <param name="pageNumber">رقم الصفحة</param>
    /// <param name="pageSize">حجم الصفحة</param>
    /// <param name="searchTerm">مصطلح البحث</param>
    /// <param name="clientType">نوع الموكل</param>
    /// <param name="status">حالة الموكل</param>
    /// <returns>نتيجة التصفحة</returns>
    Task<PagedResult<Client>> GetClientsPagedAsync(int pageNumber, int pageSize, 
        string? searchTerm = null, ClientType? clientType = null, ClientStatus? status = null);

    /// <summary>
    /// الحصول على موكل بالمعرف
    /// </summary>
    /// <param name="id">معرف الموكل</param>
    /// <returns>الموكل</returns>
    Task<Client?> GetClientByIdAsync(int id);

    /// <summary>
    /// الحصول على موكل بمرجع المكتب
    /// </summary>
    /// <param name="officeReference">مرجع المكتب</param>
    /// <returns>الموكل</returns>
    Task<Client?> GetClientByOfficeReferenceAsync(string officeReference);

    /// <summary>
    /// إنشاء موكل جديد
    /// </summary>
    /// <param name="client">بيانات الموكل</param>
    /// <returns>الموكل المنشأ</returns>
    Task<Client> CreateClientAsync(Client client);

    /// <summary>
    /// تحديث بيانات الموكل
    /// </summary>
    /// <param name="client">بيانات الموكل المحدثة</param>
    /// <returns>الموكل المحدث</returns>
    Task<Client> UpdateClientAsync(Client client);

    /// <summary>
    /// حذف موكل (حذف منطقي)
    /// </summary>
    /// <param name="id">معرف الموكل</param>
    /// <param name="deletedBy">المستخدم الذي قام بالحذف</param>
    /// <returns>true في حالة النجاح</returns>
    Task<bool> DeleteClientAsync(int id, string deletedBy);

    /// <summary>
    /// البحث في الموكلين
    /// </summary>
    /// <param name="searchTerm">مصطلح البحث</param>
    /// <returns>قائمة الموكلين المطابقين</returns>
    Task<List<Client>> SearchClientsAsync(string searchTerm);

    /// <summary>
    /// الحصول على الموكلين حسب النوع
    /// </summary>
    /// <param name="type">نوع الموكل</param>
    /// <returns>قائمة الموكلين</returns>
    Task<List<Client>> GetClientsByTypeAsync(ClientType type);

    /// <summary>
    /// الحصول على الموكلين حسب الحالة
    /// </summary>
    /// <param name="status">حالة الموكل</param>
    /// <returns>قائمة الموكلين</returns>
    Task<List<Client>> GetClientsByStatusAsync(ClientStatus status);

    /// <summary>
    /// الحصول على الموكلين حسب المدينة
    /// </summary>
    /// <param name="city">المدينة</param>
    /// <returns>قائمة الموكلين</returns>
    Task<List<Client>> GetClientsByCityAsync(string city);

    /// <summary>
    /// التحقق من توفر مرجع المكتب
    /// </summary>
    /// <param name="officeReference">مرجع المكتب</param>
    /// <param name="excludeClientId">معرف الموكل المستثنى (للتحديث)</param>
    /// <returns>true إذا كان متاحاً</returns>
    Task<bool> IsOfficeReferenceAvailableAsync(string officeReference, int? excludeClientId = null);

    /// <summary>
    /// توليد مرجع مكتب جديد
    /// </summary>
    /// <returns>مرجع المكتب الجديد</returns>
    Task<string> GenerateOfficeReferenceAsync();

    /// <summary>
    /// الحصول على إحصائيات الموكلين
    /// </summary>
    /// <returns>إحصائيات الموكلين</returns>
    Task<ClientStatistics> GetClientStatisticsAsync();

    /// <summary>
    /// تصدير الموكلين إلى Excel
    /// </summary>
    /// <param name="clients">قائمة الموكلين</param>
    /// <param name="filePath">مسار الملف</param>
    /// <returns>true في حالة النجاح</returns>
    Task<bool> ExportClientsToExcelAsync(List<Client> clients, string filePath);

    /// <summary>
    /// تصدير الموكلين إلى PDF
    /// </summary>
    /// <param name="clients">قائمة الموكلين</param>
    /// <param name="filePath">مسار الملف</param>
    /// <returns>true في حالة النجاح</returns>
    Task<bool> ExportClientsToPdfAsync(List<Client> clients, string filePath);

    /// <summary>
    /// استيراد الموكلين من Excel
    /// </summary>
    /// <param name="filePath">مسار الملف</param>
    /// <returns>قائمة الموكلين المستوردين</returns>
    Task<List<Client>> ImportClientsFromExcelAsync(string filePath);

    /// <summary>
    /// إضافة جهة اتصال للموكل
    /// </summary>
    /// <param name="clientId">معرف الموكل</param>
    /// <param name="contact">بيانات جهة الاتصال</param>
    /// <returns>جهة الاتصال المضافة</returns>
    Task<ClientContact> AddClientContactAsync(int clientId, ClientContact contact);

    /// <summary>
    /// إضافة مستند للموكل
    /// </summary>
    /// <param name="clientId">معرف الموكل</param>
    /// <param name="document">بيانات المستند</param>
    /// <returns>المستند المضاف</returns>
    Task<ClientDocument> AddClientDocumentAsync(int clientId, ClientDocument document);

    /// <summary>
    /// الحصول على جهات اتصال الموكل
    /// </summary>
    /// <param name="clientId">معرف الموكل</param>
    /// <returns>قائمة جهات الاتصال</returns>
    Task<List<ClientContact>> GetClientContactsAsync(int clientId);

    /// <summary>
    /// الحصول على مستندات الموكل
    /// </summary>
    /// <param name="clientId">معرف الموكل</param>
    /// <returns>قائمة المستندات</returns>
    Task<List<ClientDocument>> GetClientDocumentsAsync(int clientId);
}

/// <summary>
/// نتيجة التصفحة
/// </summary>
/// <typeparam name="T">نوع العنصر</typeparam>
public class PagedResult<T>
{
    public List<T> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPages;
}

/// <summary>
/// إحصائيات الموكلين
/// </summary>
public class ClientStatistics
{
    public int TotalClients { get; set; }
    public int ActiveClients { get; set; }
    public int InactiveClients { get; set; }
    public int BlockedClients { get; set; }
    public Dictionary<ClientType, int> ClientsByType { get; set; } = new();
    public Dictionary<string, int> ClientsByCity { get; set; } = new();
    public int NewClientsThisMonth { get; set; }
    public int NewClientsThisYear { get; set; }
    public double AverageRating { get; set; }
}
