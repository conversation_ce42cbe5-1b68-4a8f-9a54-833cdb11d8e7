using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AvocatPro.Models;

namespace AvocatPro.Services
{
    public class PerformanceManagementService
    {
        private readonly AuditService _auditService;
        private readonly List<UserPerformanceMetric> _metrics;
        private readonly List<CustomerSatisfactionSurvey> _surveys;
        private readonly List<TaskTimeTracking> _timeTrackings;

        public PerformanceManagementService()
        {
            _auditService = new AuditService();
            _metrics = new List<UserPerformanceMetric>();
            _surveys = new List<CustomerSatisfactionSurvey>();
            _timeTrackings = new List<TaskTimeTracking>();
            
            InitializeDefaultMetrics();
        }

        // 📊 مؤشرات الأداء الرئيسية (KPIs)
        public async Task<KPIReport> GenerateKPIReportAsync(DateTime fromDate, DateTime toDate, int? userId = null)
        {
            await Task.CompletedTask;

            var filteredMetrics = _metrics.Where(m => m.Date >= fromDate && m.Date <= toDate);
            
            if (userId.HasValue)
                filteredMetrics = filteredMetrics.Where(m => m.UserId == userId.Value);

            var metrics = filteredMetrics.ToList();

            var report = new KPIReport
            {
                ReportPeriod = new DateRange { From = fromDate, To = toDate },
                GeneratedAt = DateTime.Now,
                TotalCases = metrics.Sum(m => m.CasesHandled),
                CompletedCases = metrics.Sum(m => m.CasesCompleted),
                AverageResolutionTime = metrics.Any() ? metrics.Average(m => m.AverageResolutionDays) : 0,
                ClientSatisfactionScore = await GetAverageClientSatisfactionAsync(fromDate, toDate),
                RevenueGenerated = metrics.Sum(m => m.RevenueGenerated),
                BillableHours = metrics.Sum(m => m.BillableHours),
                SuccessRate = metrics.Any() ? (metrics.Sum(m => m.CasesWon) / (double)metrics.Sum(m => m.CasesCompleted)) * 100 : 0,
                ProductivityScore = CalculateProductivityScore(metrics),
                TopPerformers = GetTopPerformers(metrics, 5),
                PerformanceTrends = CalculatePerformanceTrends(metrics)
            };

            await _auditService.LogActivityAsync(userId ?? 0, "إنشاء تقرير KPI", 
                $"تم إنشاء تقرير مؤشرات الأداء للفترة من {fromDate:yyyy-MM-dd} إلى {toDate:yyyy-MM-dd}");

            return report;
        }

        // 📝 تقييم رضا العملاء
        public async Task<SurveyResult> CreateCustomerSatisfactionSurveyAsync(CustomerSatisfactionRequest request)
        {
            try
            {
                var survey = new CustomerSatisfactionSurvey
                {
                    Id = Guid.NewGuid().ToString(),
                    ClientId = request.ClientId,
                    CaseId = request.CaseId,
                    LawyerId = request.LawyerId,
                    CreatedAt = DateTime.Now,
                    Questions = request.Questions,
                    Status = SurveyStatus.Sent
                };

                _surveys.Add(survey);

                // محاكاة إرسال الاستبيان للعميل
                await SendSurveyToClientAsync(survey);

                await _auditService.LogActivityAsync(request.LawyerId, "إنشاء استبيان رضا", 
                    $"تم إنشاء استبيان رضا للعميل {request.ClientId}");

                return new SurveyResult
                {
                    Success = true,
                    SurveyId = survey.Id,
                    Message = "تم إنشاء وإرسال الاستبيان بنجاح"
                };
            }
            catch (Exception ex)
            {
                return new SurveyResult
                {
                    Success = false,
                    Message = $"خطأ في إنشاء الاستبيان: {ex.Message}"
                };
            }
        }

        public async Task<SurveyResult> SubmitSurveyResponseAsync(string surveyId, List<SurveyResponse> responses)
        {
            try
            {
                var survey = _surveys.FirstOrDefault(s => s.Id == surveyId);
                if (survey == null)
                {
                    return new SurveyResult
                    {
                        Success = false,
                        Message = "الاستبيان غير موجود"
                    };
                }

                survey.Responses = responses;
                survey.CompletedAt = DateTime.Now;
                survey.Status = SurveyStatus.Completed;
                survey.OverallRating = responses.Where(r => r.QuestionType == QuestionType.Rating)
                                               .Average(r => r.RatingValue ?? 0);

                await _auditService.LogActivityAsync(0, "إكمال استبيان رضا", 
                    $"تم إكمال استبيان الرضا {surveyId}");

                return new SurveyResult
                {
                    Success = true,
                    SurveyId = surveyId,
                    Message = "تم إرسال الاستبيان بنجاح"
                };
            }
            catch (Exception ex)
            {
                return new SurveyResult
                {
                    Success = false,
                    Message = $"خطأ في إرسال الاستبيان: {ex.Message}"
                };
            }
        }

        // ⏱️ تتبع الوقت المفصل للمهام
        public async Task<TimeTrackingResult> StartTimeTrackingAsync(TimeTrackingRequest request)
        {
            try
            {
                var tracking = new TaskTimeTracking
                {
                    Id = Guid.NewGuid().ToString(),
                    UserId = request.UserId,
                    TaskId = request.TaskId,
                    TaskDescription = request.TaskDescription,
                    CaseId = request.CaseId,
                    StartTime = DateTime.Now,
                    Status = TimeTrackingStatus.Active,
                    IsBillable = request.IsBillable
                };

                _timeTrackings.Add(tracking);

                return new TimeTrackingResult
                {
                    Success = true,
                    TrackingId = tracking.Id,
                    Message = "تم بدء تتبع الوقت"
                };
            }
            catch (Exception ex)
            {
                return new TimeTrackingResult
                {
                    Success = false,
                    Message = $"خطأ في بدء تتبع الوقت: {ex.Message}"
                };
            }
        }

        public async Task<TimeTrackingResult> StopTimeTrackingAsync(string trackingId, string? notes = null)
        {
            try
            {
                var tracking = _timeTrackings.FirstOrDefault(t => t.Id == trackingId);
                if (tracking == null)
                {
                    return new TimeTrackingResult
                    {
                        Success = false,
                        Message = "جلسة تتبع الوقت غير موجودة"
                    };
                }

                tracking.EndTime = DateTime.Now;
                tracking.Duration = tracking.EndTime.Value - tracking.StartTime;
                tracking.Notes = notes;
                tracking.Status = TimeTrackingStatus.Completed;

                await _auditService.LogActivityAsync(tracking.UserId, "إيقاف تتبع الوقت", 
                    $"تم إيقاف تتبع الوقت للمهمة: {tracking.TaskDescription}");

                return new TimeTrackingResult
                {
                    Success = true,
                    TrackingId = trackingId,
                    Duration = tracking.Duration,
                    Message = "تم إيقاف تتبع الوقت"
                };
            }
            catch (Exception ex)
            {
                return new TimeTrackingResult
                {
                    Success = false,
                    Message = $"خطأ في إيقاف تتبع الوقت: {ex.Message}"
                };
            }
        }

        // 📈 تحليل الإنتاجية للموظفين
        public async Task<ProductivityReport> GenerateProductivityReportAsync(int userId, DateTime fromDate, DateTime toDate)
        {
            await Task.CompletedTask;

            var userTimeTrackings = _timeTrackings.Where(t => 
                t.UserId == userId && 
                t.StartTime >= fromDate && 
                t.StartTime <= toDate &&
                t.Status == TimeTrackingStatus.Completed).ToList();

            var userMetrics = _metrics.Where(m => 
                m.UserId == userId && 
                m.Date >= fromDate && 
                m.Date <= toDate).ToList();

            var report = new ProductivityReport
            {
                UserId = userId,
                ReportPeriod = new DateRange { From = fromDate, To = toDate },
                TotalHoursWorked = userTimeTrackings.Sum(t => t.Duration?.TotalHours ?? 0),
                BillableHours = userTimeTrackings.Where(t => t.IsBillable).Sum(t => t.Duration?.TotalHours ?? 0),
                TasksCompleted = userTimeTrackings.Count,
                AverageTaskDuration = userTimeTrackings.Any() ? 
                    userTimeTrackings.Average(t => t.Duration?.TotalHours ?? 0) : 0,
                CasesHandled = userMetrics.Sum(m => m.CasesHandled),
                CasesCompleted = userMetrics.Sum(m => m.CasesCompleted),
                RevenueGenerated = userMetrics.Sum(m => m.RevenueGenerated),
                EfficiencyScore = CalculateEfficiencyScore(userTimeTrackings, userMetrics),
                ProductivityTrend = CalculateProductivityTrend(userMetrics),
                Recommendations = GenerateProductivityRecommendations(userTimeTrackings, userMetrics)
            };

            return report;
        }

        // 📊 إحصائيات شاملة
        public async Task<OverallStatistics> GetOverallStatisticsAsync(DateTime fromDate, DateTime toDate)
        {
            await Task.CompletedTask;

            var metrics = _metrics.Where(m => m.Date >= fromDate && m.Date <= toDate).ToList();
            var completedSurveys = _surveys.Where(s => s.Status == SurveyStatus.Completed && 
                                                      s.CompletedAt >= fromDate && 
                                                      s.CompletedAt <= toDate).ToList();

            return new OverallStatistics
            {
                ReportPeriod = new DateRange { From = fromDate, To = toDate },
                TotalUsers = metrics.Select(m => m.UserId).Distinct().Count(),
                TotalCases = metrics.Sum(m => m.CasesHandled),
                CompletedCases = metrics.Sum(m => m.CasesCompleted),
                TotalRevenue = metrics.Sum(m => m.RevenueGenerated),
                AverageClientSatisfaction = completedSurveys.Any() ? completedSurveys.Average(s => s.OverallRating) : 0,
                TotalBillableHours = metrics.Sum(m => m.BillableHours),
                AverageResolutionTime = metrics.Any() ? metrics.Average(m => m.AverageResolutionDays) : 0,
                SuccessRate = metrics.Any() ? (metrics.Sum(m => m.CasesWon) / (double)metrics.Sum(m => m.CasesCompleted)) * 100 : 0,
                DepartmentPerformance = GetDepartmentPerformance(metrics),
                MonthlyTrends = GetMonthlyTrends(metrics)
            };
        }

        // 🔧 دوال مساعدة
        private void InitializeDefaultMetrics()
        {
            // إضافة بيانات وهمية للاختبار
            var random = new Random();
            for (int i = 0; i < 30; i++)
            {
                _metrics.Add(new UserPerformanceMetric
                {
                    UserId = random.Next(1, 6),
                    Date = DateTime.Now.AddDays(-i),
                    CasesHandled = random.Next(1, 5),
                    CasesCompleted = random.Next(0, 3),
                    CasesWon = random.Next(0, 2),
                    BillableHours = random.Next(4, 9),
                    RevenueGenerated = random.Next(1000, 5000),
                    AverageResolutionDays = random.Next(5, 30)
                });
            }
        }

        private async Task<double> GetAverageClientSatisfactionAsync(DateTime fromDate, DateTime toDate)
        {
            await Task.CompletedTask;
            
            var completedSurveys = _surveys.Where(s => 
                s.Status == SurveyStatus.Completed && 
                s.CompletedAt >= fromDate && 
                s.CompletedAt <= toDate).ToList();

            return completedSurveys.Any() ? completedSurveys.Average(s => s.OverallRating) : 0;
        }

        private double CalculateProductivityScore(List<UserPerformanceMetric> metrics)
        {
            if (!metrics.Any()) return 0;

            var completionRate = metrics.Sum(m => m.CasesCompleted) / (double)metrics.Sum(m => m.CasesHandled);
            var successRate = metrics.Sum(m => m.CasesWon) / (double)metrics.Sum(m => m.CasesCompleted);
            var efficiencyScore = metrics.Average(m => m.BillableHours / Math.Max(m.AverageResolutionDays, 1));

            return (completionRate * 0.4 + successRate * 0.4 + efficiencyScore * 0.2) * 100;
        }

        private List<TopPerformer> GetTopPerformers(List<UserPerformanceMetric> metrics, int count)
        {
            return metrics.GroupBy(m => m.UserId)
                         .Select(g => new TopPerformer
                         {
                             UserId = g.Key,
                             Score = CalculateProductivityScore(g.ToList()),
                             CasesCompleted = g.Sum(m => m.CasesCompleted),
                             Revenue = g.Sum(m => m.RevenueGenerated)
                         })
                         .OrderByDescending(p => p.Score)
                         .Take(count)
                         .ToList();
        }

        private List<PerformanceTrend> CalculatePerformanceTrends(List<PerformanceMetric> metrics)
        {
            return metrics.GroupBy(m => m.Date.ToString("yyyy-MM"))
                         .Select(g => new PerformanceTrend
                         {
                             Period = g.Key,
                             CasesCompleted = g.Sum(m => m.CasesCompleted),
                             Revenue = g.Sum(m => m.RevenueGenerated),
                             ProductivityScore = CalculateProductivityScore(g.ToList())
                         })
                         .OrderBy(t => t.Period)
                         .ToList();
        }

        private double CalculateEfficiencyScore(List<TaskTimeTracking> timeTrackings, List<UserPerformanceMetric> metrics)
        {
            if (!timeTrackings.Any() || !metrics.Any()) return 0;

            var totalHours = timeTrackings.Sum(t => t.Duration?.TotalHours ?? 0);
            var casesCompleted = metrics.Sum(m => m.CasesCompleted);

            return casesCompleted > 0 ? (casesCompleted / totalHours) * 100 : 0;
        }

        private ProductivityTrend CalculateProductivityTrend(List<UserPerformanceMetric> metrics)
        {
            if (metrics.Count < 2) return ProductivityTrend.Stable;

            var recentScore = CalculateProductivityScore(metrics.TakeLast(7).ToList());
            var previousScore = CalculateProductivityScore(metrics.Take(metrics.Count - 7).ToList());

            var change = recentScore - previousScore;
            
            return change switch
            {
                > 5 => ProductivityTrend.Improving,
                < -5 => ProductivityTrend.Declining,
                _ => ProductivityTrend.Stable
            };
        }

        private List<string> GenerateProductivityRecommendations(List<TaskTimeTracking> timeTrackings, List<UserPerformanceMetric> metrics)
        {
            var recommendations = new List<string>();

            var avgTaskDuration = timeTrackings.Any() ? timeTrackings.Average(t => t.Duration?.TotalHours ?? 0) : 0;
            if (avgTaskDuration > 4)
            {
                recommendations.Add("يُنصح بتقسيم المهام الكبيرة إلى مهام أصغر لتحسين الإنتاجية");
            }

            var billableRatio = timeTrackings.Any() ? 
                timeTrackings.Count(t => t.IsBillable) / (double)timeTrackings.Count : 0;
            if (billableRatio < 0.7)
            {
                recommendations.Add("يُنصح بزيادة نسبة الساعات القابلة للفوترة");
            }

            var completionRate = metrics.Any() ? 
                metrics.Sum(m => m.CasesCompleted) / (double)metrics.Sum(m => m.CasesHandled) : 0;
            if (completionRate < 0.8)
            {
                recommendations.Add("يُنصح بتحسين معدل إنجاز القضايا");
            }

            return recommendations;
        }

        private Dictionary<string, double> GetDepartmentPerformance(List<UserPerformanceMetric> metrics)
        {
            // محاكاة أداء الأقسام
            return new Dictionary<string, double>
            {
                { "القضايا المدنية", 85.5 },
                { "القضايا التجارية", 92.3 },
                { "قضايا الأسرة", 78.9 },
                { "القضايا الجنائية", 88.7 }
            };
        }

        private Dictionary<string, double> GetMonthlyTrends(List<UserPerformanceMetric> metrics)
        {
            return metrics.GroupBy(m => m.Date.ToString("yyyy-MM"))
                         .ToDictionary(g => g.Key, g => CalculateProductivityScore(g.ToList()));
        }

        private async Task SendSurveyToClientAsync(CustomerSatisfactionSurvey survey)
        {
            // محاكاة إرسال الاستبيان
            await Task.Delay(500);
            System.Diagnostics.Debug.WriteLine($"تم إرسال استبيان الرضا {survey.Id} للعميل {survey.ClientId}");
        }
    }

    // 📊 نماذج البيانات لإدارة الأداء
    public class UserPerformanceMetric
    {
        public int UserId { get; set; }
        public DateTime Date { get; set; }
        public int CasesHandled { get; set; }
        public int CasesCompleted { get; set; }
        public int CasesWon { get; set; }
        public double BillableHours { get; set; }
        public decimal RevenueGenerated { get; set; }
        public double AverageResolutionDays { get; set; }
    }

    public class KPIReport
    {
        public DateRange ReportPeriod { get; set; } = new();
        public DateTime GeneratedAt { get; set; }
        public int TotalCases { get; set; }
        public int CompletedCases { get; set; }
        public double AverageResolutionTime { get; set; }
        public double ClientSatisfactionScore { get; set; }
        public decimal RevenueGenerated { get; set; }
        public double BillableHours { get; set; }
        public double SuccessRate { get; set; }
        public double ProductivityScore { get; set; }
        public List<TopPerformer> TopPerformers { get; set; } = new();
        public List<PerformanceTrend> PerformanceTrends { get; set; } = new();
    }

    public class CustomerSatisfactionSurvey
    {
        public string Id { get; set; } = string.Empty;
        public int ClientId { get; set; }
        public int? CaseId { get; set; }
        public int LawyerId { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public SurveyStatus Status { get; set; }
        public List<SurveyQuestion> Questions { get; set; } = new();
        public List<SurveyResponse> Responses { get; set; } = new();
        public double OverallRating { get; set; }
    }

    public class TaskTimeTracking
    {
        public string Id { get; set; } = string.Empty;
        public int UserId { get; set; }
        public string TaskId { get; set; } = string.Empty;
        public string TaskDescription { get; set; } = string.Empty;
        public int? CaseId { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public TimeSpan? Duration { get; set; }
        public bool IsBillable { get; set; }
        public string? Notes { get; set; }
        public TimeTrackingStatus Status { get; set; }
    }

    public class ProductivityReport
    {
        public int UserId { get; set; }
        public DateRange ReportPeriod { get; set; } = new();
        public double TotalHoursWorked { get; set; }
        public double BillableHours { get; set; }
        public int TasksCompleted { get; set; }
        public double AverageTaskDuration { get; set; }
        public int CasesHandled { get; set; }
        public int CasesCompleted { get; set; }
        public decimal RevenueGenerated { get; set; }
        public double EfficiencyScore { get; set; }
        public ProductivityTrend ProductivityTrend { get; set; }
        public List<string> Recommendations { get; set; } = new();
    }

    public class OverallStatistics
    {
        public DateRange ReportPeriod { get; set; } = new();
        public int TotalUsers { get; set; }
        public int TotalCases { get; set; }
        public int CompletedCases { get; set; }
        public decimal TotalRevenue { get; set; }
        public double AverageClientSatisfaction { get; set; }
        public double TotalBillableHours { get; set; }
        public double AverageResolutionTime { get; set; }
        public double SuccessRate { get; set; }
        public Dictionary<string, double> DepartmentPerformance { get; set; } = new();
        public Dictionary<string, double> MonthlyTrends { get; set; } = new();
    }

    public class DateRange
    {
        public DateTime From { get; set; }
        public DateTime To { get; set; }
    }

    public class TopPerformer
    {
        public int UserId { get; set; }
        public double Score { get; set; }
        public int CasesCompleted { get; set; }
        public decimal Revenue { get; set; }
    }

    public class PerformanceTrend
    {
        public string Period { get; set; } = string.Empty;
        public int CasesCompleted { get; set; }
        public decimal Revenue { get; set; }
        public double ProductivityScore { get; set; }
    }

    public class SurveyQuestion
    {
        public string Id { get; set; } = string.Empty;
        public string Text { get; set; } = string.Empty;
        public QuestionType Type { get; set; }
        public List<string> Options { get; set; } = new();
        public bool IsRequired { get; set; }
    }

    public class SurveyResponse
    {
        public string QuestionId { get; set; } = string.Empty;
        public QuestionType QuestionType { get; set; }
        public string? TextValue { get; set; }
        public int? RatingValue { get; set; }
        public List<string> SelectedOptions { get; set; } = new();
    }

    // طلبات الخدمة
    public class CustomerSatisfactionRequest
    {
        public int ClientId { get; set; }
        public int? CaseId { get; set; }
        public int LawyerId { get; set; }
        public List<SurveyQuestion> Questions { get; set; } = new();
    }

    public class TimeTrackingRequest
    {
        public int UserId { get; set; }
        public string TaskId { get; set; } = string.Empty;
        public string TaskDescription { get; set; } = string.Empty;
        public int? CaseId { get; set; }
        public bool IsBillable { get; set; } = true;
    }

    // نتائج الخدمات
    public class SurveyResult
    {
        public bool Success { get; set; }
        public string SurveyId { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
    }

    public class TimeTrackingResult
    {
        public bool Success { get; set; }
        public string TrackingId { get; set; } = string.Empty;
        public TimeSpan? Duration { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    // التعدادات
    public enum SurveyStatus
    {
        Draft,
        Sent,
        InProgress,
        Completed,
        Expired
    }

    public enum QuestionType
    {
        Text,
        Rating,
        MultipleChoice,
        SingleChoice,
        YesNo
    }

    public enum TimeTrackingStatus
    {
        Active,
        Paused,
        Completed,
        Cancelled
    }

    public enum ProductivityTrend
    {
        Improving,
        Stable,
        Declining
    }
}
