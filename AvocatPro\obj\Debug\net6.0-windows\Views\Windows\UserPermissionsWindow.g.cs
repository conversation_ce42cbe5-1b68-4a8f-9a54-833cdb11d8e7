﻿#pragma checksum "..\..\..\..\..\Views\Windows\UserPermissionsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "8F785A417F418863FA89DC00E15BE81532893F4E"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Windows {
    
    
    /// <summary>
    /// UserPermissionsWindow
    /// </summary>
    public partial class UserPermissionsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 21 "..\..\..\..\..\Views\Windows\UserPermissionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserNameText;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\..\..\Views\Windows\UserPermissionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AvailablePermissionsSearchBox;
        
        #line default
        #line hidden
        
        
        #line 45 "..\..\..\..\..\Views\Windows\UserPermissionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TreeView AvailablePermissionsTreeView;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\..\Views\Windows\UserPermissionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox GrantedPermissionsSearchBox;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\..\Views\Windows\UserPermissionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox GrantedPermissionsListBox;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\..\Views\Windows\UserPermissionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox AvailableGroupsListBox;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\..\..\Views\Windows\UserPermissionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox UserGroupsListBox;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\..\Views\Windows\UserPermissionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid PermissionHistoryDataGrid;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\..\Views\Windows\UserPermissionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button GrantPermissionButton;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\..\Views\Windows\UserPermissionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RevokePermissionButton;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\..\Views\Windows\UserPermissionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddToGroupButton;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\..\Views\Windows\UserPermissionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RemoveFromGroupButton;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\..\Views\Windows\UserPermissionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/windows/userpermissionswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\UserPermissionsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.UserNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.AvailablePermissionsSearchBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.AvailablePermissionsTreeView = ((System.Windows.Controls.TreeView)(target));
            return;
            case 4:
            this.GrantedPermissionsSearchBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.GrantedPermissionsListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 6:
            this.AvailableGroupsListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 7:
            this.UserGroupsListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 8:
            this.PermissionHistoryDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 9:
            this.GrantPermissionButton = ((System.Windows.Controls.Button)(target));
            
            #line 106 "..\..\..\..\..\Views\Windows\UserPermissionsWindow.xaml"
            this.GrantPermissionButton.Click += new System.Windows.RoutedEventHandler(this.GrantPermissionButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.RevokePermissionButton = ((System.Windows.Controls.Button)(target));
            
            #line 109 "..\..\..\..\..\Views\Windows\UserPermissionsWindow.xaml"
            this.RevokePermissionButton.Click += new System.Windows.RoutedEventHandler(this.RevokePermissionButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.AddToGroupButton = ((System.Windows.Controls.Button)(target));
            
            #line 112 "..\..\..\..\..\Views\Windows\UserPermissionsWindow.xaml"
            this.AddToGroupButton.Click += new System.Windows.RoutedEventHandler(this.AddToGroupButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.RemoveFromGroupButton = ((System.Windows.Controls.Button)(target));
            
            #line 115 "..\..\..\..\..\Views\Windows\UserPermissionsWindow.xaml"
            this.RemoveFromGroupButton.Click += new System.Windows.RoutedEventHandler(this.RemoveFromGroupButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 118 "..\..\..\..\..\Views\Windows\UserPermissionsWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

