<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Client extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'secondary_phone',
        'address',
        'city',
        'postal_code',
        'national_id',
        'passport_number',
        'date_of_birth',
        'gender',
        'nationality',
        'profession',
        'company_name',
        'client_type', // individual, company
        'office_reference',
        'file_reference',
        'notes',
        'status', // active, inactive, blocked
        'preferred_language',
        'preferred_contact_method',
        'emergency_contact_name',
        'emergency_contact_phone',
        'tax_number',
        'commercial_register',
        'legal_representative',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $dates = [
        'date_of_birth',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * Activity log options
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'email', 'phone', 'status'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the cases for the client
     */
    public function cases()
    {
        return $this->hasMany(LegalCase::class);
    }

    /**
     * Get the appointments for the client
     */
    public function appointments()
    {
        return $this->hasMany(Appointment::class);
    }

    /**
     * Get the documents for the client
     */
    public function documents()
    {
        return $this->hasMany(Document::class);
    }

    /**
     * Get the invoices for the client
     */
    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Get the payments for the client
     */
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the user who created this client
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this client
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope for active clients
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for individual clients
     */
    public function scopeIndividual($query)
    {
        return $query->where('client_type', 'individual');
    }

    /**
     * Scope for company clients
     */
    public function scopeCompany($query)
    {
        return $query->where('client_type', 'company');
    }

    /**
     * Search clients by name, email, or phone
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('email', 'like', "%{$search}%")
              ->orWhere('phone', 'like', "%{$search}%")
              ->orWhere('office_reference', 'like', "%{$search}%")
              ->orWhere('file_reference', 'like', "%{$search}%");
        });
    }

    /**
     * Get client's full name with title
     */
    public function getFullNameAttribute()
    {
        if ($this->client_type === 'company') {
            return $this->company_name ?: $this->name;
        }
        
        $title = $this->gender === 'female' ? 'السيدة' : 'السيد';
        return "{$title} {$this->name}";
    }

    /**
     * Get client's display name for lists
     */
    public function getDisplayNameAttribute()
    {
        $name = $this->client_type === 'company' ? $this->company_name : $this->name;
        $reference = $this->office_reference ? " ({$this->office_reference})" : '';
        return $name . $reference;
    }

    /**
     * Get client's age
     */
    public function getAgeAttribute()
    {
        if (!$this->date_of_birth) {
            return null;
        }
        
        return $this->date_of_birth->age;
    }

    /**
     * Get client's primary contact
     */
    public function getPrimaryContactAttribute()
    {
        switch ($this->preferred_contact_method) {
            case 'email':
                return $this->email;
            case 'phone':
                return $this->phone;
            case 'whatsapp':
                return $this->phone;
            default:
                return $this->phone ?: $this->email;
        }
    }

    /**
     * Get total cases count
     */
    public function getTotalCasesAttribute()
    {
        return $this->cases()->count();
    }

    /**
     * Get active cases count
     */
    public function getActiveCasesAttribute()
    {
        return $this->cases()->where('status', 'active')->count();
    }

    /**
     * Get total invoiced amount
     */
    public function getTotalInvoicedAttribute()
    {
        return $this->invoices()->sum('amount');
    }

    /**
     * Get total paid amount
     */
    public function getTotalPaidAttribute()
    {
        return $this->payments()->sum('amount');
    }

    /**
     * Get outstanding balance
     */
    public function getOutstandingBalanceAttribute()
    {
        return $this->total_invoiced - $this->total_paid;
    }

    /**
     * Check if client has outstanding balance
     */
    public function hasOutstandingBalance()
    {
        return $this->outstanding_balance > 0;
    }

    /**
     * Get client's last activity
     */
    public function getLastActivityAttribute()
    {
        $lastAppointment = $this->appointments()->latest()->first();
        $lastCase = $this->cases()->latest()->first();
        
        if (!$lastAppointment && !$lastCase) {
            return $this->updated_at;
        }
        
        if (!$lastAppointment) {
            return $lastCase->updated_at;
        }
        
        if (!$lastCase) {
            return $lastAppointment->updated_at;
        }
        
        return $lastAppointment->updated_at->gt($lastCase->updated_at) 
            ? $lastAppointment->updated_at 
            : $lastCase->updated_at;
    }

    /**
     * Generate unique office reference
     */
    public static function generateOfficeReference()
    {
        $year = date('Y');
        $lastClient = static::whereYear('created_at', $year)
            ->whereNotNull('office_reference')
            ->orderBy('office_reference', 'desc')
            ->first();
            
        if (!$lastClient) {
            return "CL{$year}001";
        }
        
        $lastNumber = (int) substr($lastClient->office_reference, -3);
        $newNumber = str_pad($lastNumber + 1, 3, '0', STR_PAD_LEFT);
        
        return "CL{$year}{$newNumber}";
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($client) {
            if (!$client->office_reference) {
                $client->office_reference = static::generateOfficeReference();
            }
            
            if (auth()->check()) {
                $client->created_by = auth()->id();
            }
        });
        
        static::updating(function ($client) {
            if (auth()->check()) {
                $client->updated_by = auth()->id();
            }
        });
    }
}
