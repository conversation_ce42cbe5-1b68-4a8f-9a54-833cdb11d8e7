using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;

namespace AvocatPro.Views.Pages
{
    /// <summary>
    /// صفحة إدارة الموكلين الأساسية
    /// </summary>
    public partial class BasicClientsPage : Page
    {
        public BasicClientsPage()
        {
            InitializeComponent();
            LoadData();
        }

        /// <summary>
        /// تحميل البيانات
        /// </summary>
        private void LoadData()
        {
            try
            {
                var clients = new List<BasicClient>
                {
                    new BasicClient { Name = "أحمد محمد", ClientType = "فرد", Phone = "0501234567", Status = "نشط" },
                    new BasicClient { Name = "شركة النور", ClientType = "شركة", Phone = "0502345678", Status = "نشط" },
                    new BasicClient { Name = "فاطمة علي", ClientType = "فرد", Phone = "0503456789", Status = "نشط" },
                    new BasicClient { Name = "مؤسسة البناء", ClientType = "شركة", Phone = "0504567890", Status = "غير نشط" }
                };

                ClientsListBox.ItemsSource = clients;
                StatusText.Text = $"تم تحميل {clients.Count} موكل";
            }
            catch (Exception ex)
            {
                StatusText.Text = $"خطأ: {ex.Message}";
            }
        }

        /// <summary>
        /// إضافة موكل جديد
        /// </summary>
        private void AddClient_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم إضافة ميزة إضافة الموكلين قريباً", "قيد التطوير");
        }
    }

    /// <summary>
    /// نموذج الموكل الأساسي
    /// </summary>
    public class BasicClient
    {
        public string Name { get; set; } = "";
        public string ClientType { get; set; } = "";
        public string Phone { get; set; } = "";
        public string Status { get; set; } = "";
    }
}
