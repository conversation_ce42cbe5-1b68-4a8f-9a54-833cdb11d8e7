using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using AvocatPro.Data;
using AvocatPro.Models;
using AvocatPro.Models.UserManagement;

namespace AvocatPro.Services;

/// <summary>
/// إصلاح سريع لخدمة إدارة المستخدمين
/// </summary>
public static class UserManagementServiceFix
{
    /// <summary>
    /// إصلاح جميع مراجع ActivityType في UserManagementService
    /// </summary>
    public static void FixActivityTypeReferences()
    {
        // هذا الملف يحتوي على الإصلاحات المطلوبة
        // يجب استبدال جميع مراجع ActivityType بـ Models.UserManagement.ActivityType
        // يجب استبدال جميع مراجع ActivityLevel بـ Models.UserManagement.ActivityLevel
    }

    /// <summary>
    /// قائمة الإصلاحات المطلوبة
    /// </summary>
    public static readonly Dictionary<string, string> RequiredFixes = new()
    {
        { "ActivityType.PasswordReset", "Models.UserManagement.ActivityType.PasswordReset" },
        { "ActivityType.AccountLock", "Models.UserManagement.ActivityType.AccountLock" },
        { "ActivityType.AccountUnlock", "Models.UserManagement.ActivityType.AccountUnlock" },
        { "ActivityType.PermissionGrant", "Models.UserManagement.ActivityType.PermissionGrant" },
        { "ActivityType.PermissionRevoke", "Models.UserManagement.ActivityType.PermissionRevoke" },
        { "ActivityLevel.Warning", "Models.UserManagement.ActivityLevel.Warning" }
    };
}
