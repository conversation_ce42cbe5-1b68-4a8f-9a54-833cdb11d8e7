using Microsoft.EntityFrameworkCore;
using AvocatPro.Data;
using AvocatPro.Models;

namespace AvocatPro.Services;

/// <summary>
/// خدمة المصادقة والأمان
/// </summary>
public class AuthenticationService : IAuthenticationService
{
    private readonly AvocatProDbContext _context;
    private const int MaxFailedAttempts = 5;
    private const int LockoutDurationMinutes = 30;

    public AuthenticationService(AvocatProDbContext context)
    {
        _context = context;
    }

    public async Task<User?> LoginAsync(string username, string password)
    {
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.Username == username && !u.IsDeleted);

        if (user == null)
        {
            await RecordFailedLoginAttemptAsync(username);
            return null;
        }

        // التحقق من قفل الحساب
        if (await IsAccountLockedAsync(user.Id))
        {
            return null;
        }

        // التحقق من كلمة المرور
        if (!VerifyPassword(password, user.PasswordHash))
        {
            await RecordFailedLoginAttemptAsync(username);
            
            // قفل الحساب إذا تجاوز عدد المحاولات الفاشلة
            if (user.FailedLoginAttempts >= MaxFailedAttempts - 1)
            {
                await LockUserAccountAsync(user.Id, LockoutDurationMinutes);
            }
            
            return null;
        }

        // تسجيل دخول ناجح
        user.LastLoginAt = DateTime.Now;
        await ResetFailedLoginAttemptsAsync(user.Id);
        await _context.SaveChangesAsync();

        return user;
    }

    public async Task LogoutAsync(int userId)
    {
        // يمكن إضافة منطق إضافي هنا مثل تسجيل وقت الخروج
        await Task.CompletedTask;
    }

    public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user == null || !VerifyPassword(currentPassword, user.PasswordHash))
        {
            return false;
        }

        var strengthResult = ValidatePasswordStrength(newPassword);
        if (!strengthResult.IsValid)
        {
            return false;
        }

        user.PasswordHash = HashPassword(newPassword);
        user.UpdatedAt = DateTime.Now;
        
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<string?> RequestPasswordResetAsync(string email)
    {
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.Email == email && !u.IsDeleted);

        if (user == null)
        {
            return null;
        }

        var resetToken = Guid.NewGuid().ToString();
        user.ResetPasswordToken = resetToken;
        user.ResetPasswordExpiry = DateTime.Now.AddHours(1); // صالح لمدة ساعة

        await _context.SaveChangesAsync();
        return resetToken;
    }

    public async Task<bool> ResetPasswordAsync(string email, string resetToken, string newPassword)
    {
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.Email == email && 
                                    u.ResetPasswordToken == resetToken &&
                                    u.ResetPasswordExpiry > DateTime.Now &&
                                    !u.IsDeleted);

        if (user == null)
        {
            return false;
        }

        var strengthResult = ValidatePasswordStrength(newPassword);
        if (!strengthResult.IsValid)
        {
            return false;
        }

        user.PasswordHash = HashPassword(newPassword);
        user.ResetPasswordToken = null;
        user.ResetPasswordExpiry = null;
        user.UpdatedAt = DateTime.Now;

        await ResetFailedLoginAttemptsAsync(user.Id);
        await UnlockUserAccountAsync(user.Id);
        
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> VerifySecurityAnswerAsync(int userId, string securityAnswer)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user?.SecurityAnswerHash == null)
        {
            return false;
        }

        return VerifyPassword(securityAnswer, user.SecurityAnswerHash);
    }

    public async Task SetSecurityQuestionAsync(int userId, string securityQuestion, string securityAnswer)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user != null)
        {
            user.SecurityQuestion = securityQuestion;
            user.SecurityAnswerHash = HashPassword(securityAnswer);
            user.UpdatedAt = DateTime.Now;
            
            await _context.SaveChangesAsync();
        }
    }

    public async Task<bool> HasPermissionAsync(int userId, UserRole requiredRole)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user == null || !user.IsActive || user.IsDeleted)
        {
            return false;
        }

        // المدير له جميع الصلاحيات
        if (user.Role == UserRole.Admin)
        {
            return true;
        }

        // التحقق من الدور المطلوب
        return (int)user.Role <= (int)requiredRole;
    }

    public async Task LockUserAccountAsync(int userId, int lockDurationMinutes)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user != null)
        {
            user.LockedUntil = DateTime.Now.AddMinutes(lockDurationMinutes);
            user.UpdatedAt = DateTime.Now;
            
            await _context.SaveChangesAsync();
        }
    }

    public async Task UnlockUserAccountAsync(int userId)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user != null)
        {
            user.LockedUntil = null;
            user.UpdatedAt = DateTime.Now;
            
            await _context.SaveChangesAsync();
        }
    }

    public async Task<bool> IsAccountLockedAsync(int userId)
    {
        var user = await _context.Users.FindAsync(userId);
        return user?.LockedUntil > DateTime.Now;
    }

    public async Task RecordFailedLoginAttemptAsync(string username)
    {
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.Username == username && !u.IsDeleted);

        if (user != null)
        {
            user.FailedLoginAttempts++;
            user.UpdatedAt = DateTime.Now;
            
            await _context.SaveChangesAsync();
        }
    }

    public async Task ResetFailedLoginAttemptsAsync(int userId)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user != null)
        {
            user.FailedLoginAttempts = 0;
            user.UpdatedAt = DateTime.Now;
            
            await _context.SaveChangesAsync();
        }
    }

    public PasswordStrengthResult ValidatePasswordStrength(string password)
    {
        var result = new PasswordStrengthResult();
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(password))
        {
            errors.Add("كلمة المرور مطلوبة");
            result.IsValid = false;
            result.Strength = PasswordStrength.VeryWeak;
            result.Errors = errors;
            return result;
        }

        // الحد الأدنى للطول
        if (password.Length < 8)
        {
            errors.Add("كلمة المرور يجب أن تكون 8 أحرف على الأقل");
        }

        // التحقق من وجود أحرف كبيرة
        if (!password.Any(char.IsUpper))
        {
            errors.Add("كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل");
        }

        // التحقق من وجود أحرف صغيرة
        if (!password.Any(char.IsLower))
        {
            errors.Add("كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل");
        }

        // التحقق من وجود أرقام
        if (!password.Any(char.IsDigit))
        {
            errors.Add("كلمة المرور يجب أن تحتوي على رقم واحد على الأقل");
        }

        // التحقق من وجود رموز خاصة
        var specialChars = "!@#$%^&*()_+-=[]{}|;:,.<>?";
        if (!password.Any(c => specialChars.Contains(c)))
        {
            errors.Add("كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل");
        }

        result.IsValid = errors.Count == 0;
        result.Errors = errors;

        // تحديد قوة كلمة المرور
        var score = 0;
        if (password.Length >= 8) score++;
        if (password.Length >= 12) score++;
        if (password.Any(char.IsUpper)) score++;
        if (password.Any(char.IsLower)) score++;
        if (password.Any(char.IsDigit)) score++;
        if (password.Any(c => specialChars.Contains(c))) score++;

        result.Strength = score switch
        {
            <= 2 => PasswordStrength.VeryWeak,
            3 => PasswordStrength.Weak,
            4 => PasswordStrength.Medium,
            5 => PasswordStrength.Strong,
            _ => PasswordStrength.VeryStrong
        };

        return result;
    }

    public string HashPassword(string password)
    {
        return BCrypt.Net.BCrypt.HashPassword(password);
    }

    public bool VerifyPassword(string password, string hashedPassword)
    {
        return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
    }
}
