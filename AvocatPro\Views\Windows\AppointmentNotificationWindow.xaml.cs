using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using AvocatPro.Models;

namespace AvocatPro.Views.Windows;

public partial class AppointmentNotificationWindow : Window
{
    private readonly List<AppointmentDisplayModel> _appointments;
    private System.Windows.Threading.DispatcherTimer _refreshTimer;

    public AppointmentNotificationWindow(List<AppointmentDisplayModel> appointments)
    {
        InitializeComponent();
        _appointments = appointments ?? new List<AppointmentDisplayModel>();
        
        InitializeTimer();
        LoadNotifications();
    }

    private void InitializeTimer()
    {
        _refreshTimer = new System.Windows.Threading.DispatcherTimer();
        _refreshTimer.Interval = TimeSpan.FromMinutes(1); // تحديث كل دقيقة
        _refreshTimer.Tick += RefreshTimer_Tick;
        _refreshTimer.Start();
    }

    private void RefreshTimer_Tick(object sender, EventArgs e)
    {
        LoadNotifications();
    }

    private void LoadNotifications()
    {
        try
        {
            var urgentNotifications = _appointments.Where(a => a.ShouldNotifyNow).ToList();
            var todayAppointments = _appointments.Where(a => a.IsToday).ToList();
            var tomorrowAppointments = _appointments.Where(a => a.IsTomorrow).ToList();
            var overdueAppointments = _appointments.Where(a => a.IsOverdue).ToList();

            // تحديث العدادات
            UrgentCount.Text = urgentNotifications.Count.ToString();
            TodayCount.Text = todayAppointments.Count.ToString();
            TomorrowCount.Text = tomorrowAppointments.Count.ToString();
            OverdueCount.Text = overdueAppointments.Count.ToString();

            // إخفاء البادجات إذا كان العدد صفر
            UrgentBadge.Visibility = urgentNotifications.Count > 0 ? Visibility.Visible : Visibility.Collapsed;
            TodayBadge.Visibility = todayAppointments.Count > 0 ? Visibility.Visible : Visibility.Collapsed;
            TomorrowBadge.Visibility = tomorrowAppointments.Count > 0 ? Visibility.Visible : Visibility.Collapsed;
            OverdueBadge.Visibility = overdueAppointments.Count > 0 ? Visibility.Visible : Visibility.Collapsed;

            // تحميل التنبيهات في كل تبويب
            LoadUrgentNotifications(urgentNotifications);
            LoadTodayNotifications(todayAppointments);
            LoadTomorrowNotifications(tomorrowAppointments);
            LoadOverdueNotifications(overdueAppointments);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل التنبيهات: {ex.Message}", "خطأ", 
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void LoadUrgentNotifications(List<AppointmentDisplayModel> notifications)
    {
        UrgentNotificationsPanel.Children.Clear();

        if (notifications.Count == 0)
        {
            var noNotificationsText = new TextBlock
            {
                Text = "✅ لا توجد تنبيهات فورية حالياً",
                FontSize = 16,
                Foreground = new SolidColorBrush(Colors.Gray),
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 50, 0, 0)
            };
            UrgentNotificationsPanel.Children.Add(noNotificationsText);
            return;
        }

        foreach (var appointment in notifications)
        {
            var card = CreateNotificationCard(appointment, "UrgentNotificationCardStyle");
            UrgentNotificationsPanel.Children.Add(card);
        }
    }

    private void LoadTodayNotifications(List<AppointmentDisplayModel> notifications)
    {
        TodayNotificationsPanel.Children.Clear();

        if (notifications.Count == 0)
        {
            var noNotificationsText = new TextBlock
            {
                Text = "📅 لا توجد مواعيد اليوم",
                FontSize = 16,
                Foreground = new SolidColorBrush(Colors.Gray),
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 50, 0, 0)
            };
            TodayNotificationsPanel.Children.Add(noNotificationsText);
            return;
        }

        foreach (var appointment in notifications.OrderBy(a => a.StartTime))
        {
            var card = CreateNotificationCard(appointment, "TodayNotificationCardStyle");
            TodayNotificationsPanel.Children.Add(card);
        }
    }

    private void LoadTomorrowNotifications(List<AppointmentDisplayModel> notifications)
    {
        TomorrowNotificationsPanel.Children.Clear();

        if (notifications.Count == 0)
        {
            var noNotificationsText = new TextBlock
            {
                Text = "📆 لا توجد مواعيد غداً",
                FontSize = 16,
                Foreground = new SolidColorBrush(Colors.Gray),
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 50, 0, 0)
            };
            TomorrowNotificationsPanel.Children.Add(noNotificationsText);
            return;
        }

        foreach (var appointment in notifications.OrderBy(a => a.StartTime))
        {
            var card = CreateNotificationCard(appointment, "NotificationCardStyle");
            TomorrowNotificationsPanel.Children.Add(card);
        }
    }

    private void LoadOverdueNotifications(List<AppointmentDisplayModel> notifications)
    {
        OverdueNotificationsPanel.Children.Clear();

        if (notifications.Count == 0)
        {
            var noNotificationsText = new TextBlock
            {
                Text = "✅ لا توجد مواعيد متأخرة",
                FontSize = 16,
                Foreground = new SolidColorBrush(Colors.Gray),
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 50, 0, 0)
            };
            OverdueNotificationsPanel.Children.Add(noNotificationsText);
            return;
        }

        foreach (var appointment in notifications.OrderByDescending(a => a.AppointmentDate))
        {
            var card = CreateNotificationCard(appointment, "UrgentNotificationCardStyle");
            OverdueNotificationsPanel.Children.Add(card);
        }
    }

    private Border CreateNotificationCard(AppointmentDisplayModel appointment, string styleKey)
    {
        var card = new Border
        {
            Style = (Style)FindResource(styleKey)
        };

        var grid = new Grid();
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

        // المحتوى الرئيسي
        var contentPanel = new StackPanel();
        Grid.SetColumn(contentPanel, 0);

        // العنوان والوقت
        var headerPanel = new StackPanel { Orientation = Orientation.Horizontal };
        
        var titleText = new TextBlock
        {
            Text = $"{appointment.TypeIcon} {appointment.Title}",
            FontSize = 16,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(appointment.StatusColor))
        };
        
        var timeText = new TextBlock
        {
            Text = appointment.TimeRangeDisplay,
            FontSize = 14,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush(Colors.DarkBlue),
            Margin = new Thickness(10, 0, 0, 0)
        };

        headerPanel.Children.Add(titleText);
        headerPanel.Children.Add(timeText);
        contentPanel.Children.Add(headerPanel);

        // التفاصيل
        var detailsText = new TextBlock
        {
            Text = $"الموكل: {appointment.ClientDisplay}\nالمكان: {appointment.LocationDisplay}\nالحالة: {appointment.StatusDisplay}",
            FontSize = 12,
            Foreground = new SolidColorBrush(Colors.Gray),
            Margin = new Thickness(0, 5, 0, 0)
        };
        contentPanel.Children.Add(detailsText);

        // الأزرار
        var buttonsPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 10, 0, 0) };

        var viewButton = new Button
        {
            Content = "👁️ عرض",
            Style = (Style)FindResource("ActionButtonStyle"),
            Tag = appointment.Id
        };
        viewButton.Click += ViewAppointmentButton_Click;

        var notifyButton = new Button
        {
            Content = "🔔 تنبيه",
            Style = (Style)FindResource("ActionButtonStyle"),
            Background = new SolidColorBrush(Color.FromRgb(255, 152, 0)),
            Tag = appointment.Id
        };
        notifyButton.Click += NotifyAppointmentButton_Click;

        var postponeButton = new Button
        {
            Content = "⏰ تأجيل",
            Style = (Style)FindResource("ActionButtonStyle"),
            Background = new SolidColorBrush(Color.FromRgb(156, 39, 176)),
            Tag = appointment.Id
        };
        postponeButton.Click += PostponeAppointmentButton_Click;

        buttonsPanel.Children.Add(viewButton);
        buttonsPanel.Children.Add(notifyButton);
        buttonsPanel.Children.Add(postponeButton);
        contentPanel.Children.Add(buttonsPanel);

        // أيقونة الأولوية
        var priorityIcon = new TextBlock
        {
            Text = appointment.PriorityIcon,
            FontSize = 24,
            VerticalAlignment = VerticalAlignment.Top,
            Margin = new Thickness(10, 0, 0, 0)
        };
        Grid.SetColumn(priorityIcon, 1);

        grid.Children.Add(contentPanel);
        grid.Children.Add(priorityIcon);
        card.Child = grid;

        return card;
    }

    private void ViewAppointmentButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int appointmentId)
        {
            var appointment = _appointments.FirstOrDefault(a => a.Id == appointmentId);
            if (appointment != null)
            {
                MessageBox.Show($"تفاصيل الموعد:\n\n" +
                               $"العنوان: {appointment.Title}\n" +
                               $"التاريخ والوقت: {appointment.FullDateTimeDisplay}\n" +
                               $"النوع: {appointment.TypeDisplay}\n" +
                               $"الحالة: {appointment.StatusDisplay}\n" +
                               $"الأولوية: {appointment.PriorityDisplay}\n" +
                               $"الموكل: {appointment.ClientDisplay}\n" +
                               $"المكان: {appointment.LocationDisplay}\n" +
                               $"الوصف: {appointment.Description}",
                               "تفاصيل الموعد", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }

    private void NotifyAppointmentButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int appointmentId)
        {
            var appointment = _appointments.FirstOrDefault(a => a.Id == appointmentId);
            if (appointment != null)
            {
                appointment.IsNotified = true;
                appointment.NotificationDate = DateTime.Now;
                
                MessageBox.Show($"تم إرسال تنبيه للموعد:\n{appointment.Title}\n\nتم الإرسال عبر جميع الوسائل المفعلة.", 
                               "تم إرسال التنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                
                LoadNotifications(); // تحديث العرض
            }
        }
    }

    private void PostponeAppointmentButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int appointmentId)
        {
            var appointment = _appointments.FirstOrDefault(a => a.Id == appointmentId);
            if (appointment != null)
            {
                var result = MessageBox.Show($"هل تريد تأجيل الموعد:\n{appointment.Title}؟", 
                                           "تأجيل الموعد", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    appointment.Status = AppointmentStatus.Postponed;
                    MessageBox.Show("تم تأجيل الموعد بنجاح!", "نجح التأجيل", 
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    LoadNotifications(); // تحديث العرض
                }
            }
        }
    }

    private void RefreshNotificationsButton_Click(object sender, RoutedEventArgs e)
    {
        LoadNotifications();
        MessageBox.Show("تم تحديث التنبيهات بنجاح!", "تحديث", 
                       MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void SendAllNotificationsButton_Click(object sender, RoutedEventArgs e)
    {
        var pendingNotifications = _appointments.Where(a => !a.IsNotified && 
            (a.ShouldNotifyNow || a.IsToday || a.IsTomorrow)).ToList();

        if (pendingNotifications.Count == 0)
        {
            MessageBox.Show("لا توجد تنبيهات معلقة للإرسال.", "إرسال التنبيهات", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }

        var result = MessageBox.Show($"سيتم إرسال {pendingNotifications.Count} تنبيه.\n\nهل تريد المتابعة؟", 
                                   "تأكيد الإرسال", MessageBoxButton.YesNo, MessageBoxImage.Question);

        if (result == MessageBoxResult.Yes)
        {
            foreach (var appointment in pendingNotifications)
            {
                appointment.IsNotified = true;
                appointment.NotificationDate = DateTime.Now;
            }

            MessageBox.Show($"تم إرسال {pendingNotifications.Count} تنبيه بنجاح!", "نجح الإرسال", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
            
            LoadNotifications();
        }
    }

    private void MarkAllAsReadButton_Click(object sender, RoutedEventArgs e)
    {
        var unreadNotifications = _appointments.Where(a => !a.IsNotified).ToList();

        if (unreadNotifications.Count == 0)
        {
            MessageBox.Show("جميع التنبيهات مقروءة بالفعل.", "تحديد كمقروء", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }

        var result = MessageBox.Show($"سيتم تحديد {unreadNotifications.Count} تنبيه كمقروء.\n\nهل تريد المتابعة؟", 
                                   "تأكيد التحديد", MessageBoxButton.YesNo, MessageBoxImage.Question);

        if (result == MessageBoxResult.Yes)
        {
            foreach (var appointment in unreadNotifications)
            {
                appointment.IsNotified = true;
                appointment.NotificationDate = DateTime.Now;
            }

            MessageBox.Show($"تم تحديد {unreadNotifications.Count} تنبيه كمقروء!", "نجح التحديد", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
            
            LoadNotifications();
        }
    }

    private void SaveSettingsButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("تم حفظ إعدادات التنبيهات بنجاح!\n\nسيتم تطبيق الإعدادات الجديدة على جميع المواعيد القادمة.", 
                       "حفظ الإعدادات", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        _refreshTimer?.Stop();
        Close();
    }

    protected override void OnClosed(EventArgs e)
    {
        _refreshTimer?.Stop();
        base.OnClosed(e);
    }
}
