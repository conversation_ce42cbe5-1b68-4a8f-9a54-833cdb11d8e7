using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using AvocatPro.Models;
using AvocatPro.Views.Windows;

namespace AvocatPro.Views.Pages;

public partial class FinancialPageNew : Page
{
    private readonly User _currentUser;
    private readonly ObservableCollection<ExpenseDisplayModel> _allExpenses;
    private readonly ObservableCollection<ExpenseDisplayModel> _filteredExpenses;
    private readonly ObservableCollection<RevenueDisplayModel> _allRevenues;
    private readonly ObservableCollection<RevenueDisplayModel> _filteredRevenues;
    private DateTime _periodStart;
    private DateTime _periodEnd;

    public FinancialPageNew(User currentUser)
    {
        InitializeComponent();
        _currentUser = currentUser;
        _allExpenses = new ObservableCollection<ExpenseDisplayModel>();
        _filteredExpenses = new ObservableCollection<ExpenseDisplayModel>();
        _allRevenues = new ObservableCollection<RevenueDisplayModel>();
        _filteredRevenues = new ObservableCollection<RevenueDisplayModel>();
        
        this.Loaded += FinancialPageNew_Loaded;
    }

    private void FinancialPageNew_Loaded(object sender, RoutedEventArgs e)
    {
        try
        {
            if (ExpensesDataGrid != null && RevenuesDataGrid != null)
            {
                ExpensesDataGrid.ItemsSource = _filteredExpenses;
                RevenuesDataGrid.ItemsSource = _filteredRevenues;
            }
            
            SetCurrentPeriod();
            LoadSampleData();
            UpdateStatistics();
            UpdateStatusText();
            BuildCharts();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل الصفحة المالية: {ex.Message}", "خطأ", 
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void SetCurrentPeriod()
    {
        var now = DateTime.Now;
        _periodStart = new DateTime(now.Year, now.Month, 1);
        _periodEnd = _periodStart.AddMonths(1).AddDays(-1);
    }

    private void LoadSampleData()
    {
        LoadSampleExpenses();
        LoadSampleRevenues();
    }

    private void LoadSampleExpenses()
    {
        var sampleExpenses = new List<ExpenseDisplayModel>
        {
            new ExpenseDisplayModel
            {
                Id = 1,
                Reference = "EXP-20241201-001",
                Description = "رسوم محكمة - دعوى مطالبة مالية",
                Amount = 500,
                ExpenseDate = DateTime.Now.AddDays(-5),
                Category = ExpenseCategory.Legal,
                Type = ExpenseType.CourtFees,
                CaseId = 1,
                CaseTitle = "دعوى مطالبة مالية",
                ClientId = 1,
                ClientName = "أحمد محمد علي السعيد",
                Vendor = "وزارة العدل",
                InvoiceNumber = "INV-2024-001",
                PaymentMethod = PaymentMethod.BankTransfer,
                PaymentStatus = PaymentStatus.Paid,
                PaymentDate = DateTime.Now.AddDays(-5),
                IsReimbursable = true,
                Attachments = "[\"فاتورة_رسوم_المحكمة.pdf\"]",
                CreatedAt = DateTime.Now.AddDays(-5)
            },
            new ExpenseDisplayModel
            {
                Id = 2,
                Reference = "EXP-********-002",
                Description = "مصاريف انتقال للمحكمة",
                Amount = 150,
                ExpenseDate = DateTime.Now.AddDays(-3),
                Category = ExpenseCategory.Operational,
                Type = ExpenseType.Transportation,
                CaseId = 2,
                CaseTitle = "قضية عمالية - فصل تعسفي",
                ClientId = 2,
                ClientName = "فاطمة أحمد سالم",
                Vendor = "شركة أوبر",
                PaymentMethod = PaymentMethod.CreditCard,
                PaymentStatus = PaymentStatus.Paid,
                PaymentDate = DateTime.Now.AddDays(-3),
                IsReimbursable = true,
                CreatedAt = DateTime.Now.AddDays(-3)
            },
            new ExpenseDisplayModel
            {
                Id = 3,
                Reference = "EXP-********-003",
                Description = "رسوم خبرة عقارية",
                Amount = 2000,
                ExpenseDate = DateTime.Now.AddDays(-1),
                Category = ExpenseCategory.Legal,
                Type = ExpenseType.ExpertFees,
                CaseId = 5,
                CaseTitle = "قضية عقارية - منازعة ملكية",
                ClientId = 3,
                ClientName = "مريم عبدالله الزهراني",
                Vendor = "مكتب الخبرة العقارية",
                InvoiceNumber = "EXP-2024-005",
                PaymentMethod = PaymentMethod.Check,
                PaymentStatus = PaymentStatus.Pending,
                IsReimbursable = true,
                Attachments = "[\"تقرير_الخبرة_العقارية.pdf\", \"فاتورة_الخبرة.pdf\"]",
                CreatedAt = DateTime.Now.AddDays(-1)
            },
            new ExpenseDisplayModel
            {
                Id = 4,
                Reference = "EXP-20241204-004",
                Description = "مصاريف طباعة وتصوير",
                Amount = 75,
                ExpenseDate = DateTime.Now,
                Category = ExpenseCategory.Administrative,
                Type = ExpenseType.Printing,
                Vendor = "مكتبة الرياض",
                PaymentMethod = PaymentMethod.Cash,
                PaymentStatus = PaymentStatus.Paid,
                PaymentDate = DateTime.Now,
                IsReimbursable = false,
                CreatedAt = DateTime.Now
            }
        };

        _allExpenses.Clear();
        _filteredExpenses.Clear();
        
        foreach (var expense in sampleExpenses)
        {
            _allExpenses.Add(expense);
            _filteredExpenses.Add(expense);
        }
    }

    private void LoadSampleRevenues()
    {
        var sampleRevenues = new List<RevenueDisplayModel>
        {
            new RevenueDisplayModel
            {
                Id = 1,
                Reference = "REV-20241201-001",
                Description = "أتعاب محاماة - دعوى مطالبة مالية",
                Amount = 15000,
                RevenueDate = DateTime.Now.AddDays(-10),
                Type = RevenueType.LawyerFees,
                ClientId = 1,
                ClientName = "أحمد محمد علي السعيد",
                CaseId = 1,
                CaseTitle = "دعوى مطالبة مالية",
                InvoiceNumber = "INV-2024-001",
                PaymentMethod = PaymentMethod.BankTransfer,
                PaymentStatus = PaymentStatus.Paid,
                PaymentDate = DateTime.Now.AddDays(-8),
                DueDate = DateTime.Now.AddDays(-5),
                Attachments = "[\"عقد_الاتعاب.pdf\", \"فاتورة_الاتعاب.pdf\"]",
                CreatedAt = DateTime.Now.AddDays(-10)
            },
            new RevenueDisplayModel
            {
                Id = 2,
                Reference = "REV-********-002",
                Description = "أتعاب استشارة قانونية",
                Amount = 5000,
                RevenueDate = DateTime.Now.AddDays(-7),
                Type = RevenueType.ConsultationFees,
                ClientId = 4,
                ClientName = "شركة النور للتجارة والاستثمار",
                InvoiceNumber = "INV-2024-002",
                PaymentMethod = PaymentMethod.BankTransfer,
                PaymentStatus = PaymentStatus.Pending,
                DueDate = DateTime.Now.AddDays(23),
                Attachments = "[\"فاتورة_الاستشارة.pdf\"]",
                CreatedAt = DateTime.Now.AddDays(-7)
            },
            new RevenueDisplayModel
            {
                Id = 3,
                Reference = "REV-********-003",
                Description = "أتعاب عقد شراكة تجارية",
                Amount = 8000,
                RevenueDate = DateTime.Now.AddDays(-3),
                Type = RevenueType.ContractFees,
                ClientId = 4,
                ClientName = "شركة النور للتجارة والاستثمار",
                CaseId = 3,
                CaseTitle = "قضية تجارية - نزاع شراكة",
                InvoiceNumber = "INV-2024-003",
                PaymentMethod = PaymentMethod.Check,
                PaymentStatus = PaymentStatus.PartiallyPaid,
                PaymentDate = DateTime.Now.AddDays(-1),
                DueDate = DateTime.Now.AddDays(27),
                CreatedAt = DateTime.Now.AddDays(-3)
            },
            new RevenueDisplayModel
            {
                Id = 4,
                Reference = "REV-20241204-004",
                Description = "أتعاب قضية أسرة - نفقة",
                Amount = 3000,
                RevenueDate = DateTime.Now.AddDays(-15),
                Type = RevenueType.LawyerFees,
                ClientId = 3,
                ClientName = "مريم عبدالله الزهراني",
                CaseId = 4,
                CaseTitle = "قضية أسرة - نفقة",
                InvoiceNumber = "INV-2024-004",
                PaymentMethod = PaymentMethod.Cash,
                PaymentStatus = PaymentStatus.Overdue,
                DueDate = DateTime.Now.AddDays(-5),
                CreatedAt = DateTime.Now.AddDays(-15)
            }
        };

        _allRevenues.Clear();
        _filteredRevenues.Clear();
        
        foreach (var revenue in sampleRevenues)
        {
            _allRevenues.Add(revenue);
            _filteredRevenues.Add(revenue);
        }
    }

    private void UpdateStatistics()
    {
        try
        {
            var totalRevenues = _filteredRevenues.Sum(r => r.Amount);
            var totalExpenses = _filteredExpenses.Sum(e => e.Amount);
            var netProfit = totalRevenues - totalExpenses;
            var pendingRevenues = _filteredRevenues.Where(r => r.IsPending).Sum(r => r.Amount);
            var overdueRevenues = _filteredRevenues.Where(r => r.IsOverdue).Sum(r => r.Amount);
            var reimbursableExpenses = _filteredExpenses.Where(e => e.IsReimbursable && !e.IsReimbursed).Sum(e => e.Amount);

            if (TotalRevenuesTextBlock != null)
            {
                TotalRevenuesTextBlock.Text = totalRevenues.ToString("N0") + " درهم";
                TotalExpensesTextBlock.Text = totalExpenses.ToString("N0") + " درهم";
                NetProfitTextBlock.Text = netProfit.ToString("N0") + " ريال";
                NetProfitTextBlock.Foreground = new SolidColorBrush(netProfit >= 0 ? Colors.Green : Colors.Red);
                PendingRevenuesTextBlock.Text = pendingRevenues.ToString("N0") + " درهم";
                OverdueRevenuesTextBlock.Text = overdueRevenues.ToString("N0") + " درهم";
                ReimbursableExpensesTextBlock.Text = reimbursableExpenses.ToString("N0") + " درهم";
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الإحصائيات: {ex.Message}");
        }
    }

    private void UpdateStatusText()
    {
        try
        {
            if (ExpensesStatusTextBlock != null && RevenuesStatusTextBlock != null)
            {
                ExpensesStatusTextBlock.Text = $"عرض {_filteredExpenses.Count} من أصل {_allExpenses.Count} مصروف";
                RevenuesStatusTextBlock.Text = $"عرض {_filteredRevenues.Count} من أصل {_allRevenues.Count} إيراد";
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث نص الحالة: {ex.Message}");
        }
    }

    private void BuildCharts()
    {
        try
        {
            BuildMonthlyChart();
            BuildExpenseCategoryChart();
            BuildRevenueTypeChart();
            BuildProfitabilitySummary();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في بناء الرسوم البيانية: {ex.Message}");
        }
    }

    private void BuildMonthlyChart()
    {
        if (MonthlyChartPanel == null) return;

        MonthlyChartPanel.Children.Clear();

        // تجميع البيانات حسب الشهر
        var monthlyData = new Dictionary<string, (decimal Revenues, decimal Expenses)>();
        
        foreach (var revenue in _filteredRevenues)
        {
            var monthKey = revenue.MonthYear;
            if (!monthlyData.ContainsKey(monthKey))
                monthlyData[monthKey] = (0, 0);
            monthlyData[monthKey] = (monthlyData[monthKey].Revenues + revenue.Amount, monthlyData[monthKey].Expenses);
        }

        foreach (var expense in _filteredExpenses)
        {
            var monthKey = expense.MonthYear;
            if (!monthlyData.ContainsKey(monthKey))
                monthlyData[monthKey] = (0, 0);
            monthlyData[monthKey] = (monthlyData[monthKey].Revenues, monthlyData[monthKey].Expenses + expense.Amount);
        }

        foreach (var kvp in monthlyData.OrderBy(x => x.Key))
        {
            var monthPanel = new StackPanel { Margin = new Thickness(0, 5, 0, 5) };
            
            var monthLabel = new TextBlock
            {
                Text = DateTime.ParseExact(kvp.Key, "yyyy-MM", CultureInfo.InvariantCulture).ToString("MMMM yyyy", new CultureInfo("ar-SA")),
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 5)
            };
            monthPanel.Children.Add(monthLabel);

            var revenueBar = CreateChartBar("الإيرادات", kvp.Value.Revenues, Colors.Green, 200);
            var expenseBar = CreateChartBar("المصاريف", kvp.Value.Expenses, Colors.Red, 200);
            
            monthPanel.Children.Add(revenueBar);
            monthPanel.Children.Add(expenseBar);
            
            MonthlyChartPanel.Children.Add(monthPanel);
        }
    }

    private void BuildExpenseCategoryChart()
    {
        if (ExpenseCategoryChartPanel == null) return;

        ExpenseCategoryChartPanel.Children.Clear();

        var categoryData = _filteredExpenses
            .GroupBy(e => e.Category)
            .Select(g => new { Category = g.Key, Amount = g.Sum(e => e.Amount) })
            .OrderByDescending(x => x.Amount);

        var maxAmount = categoryData.Max(x => x.Amount);

        foreach (var item in categoryData)
        {
            var categoryDisplay = item.Category switch
            {
                ExpenseCategory.Administrative => "مصاريف إدارية",
                ExpenseCategory.Legal => "مصاريف قانونية",
                ExpenseCategory.Operational => "مصاريف تشغيلية",
                ExpenseCategory.Technology => "مصاريف تقنية",
                ExpenseCategory.Marketing => "مصاريف تسويق",
                ExpenseCategory.Travel => "مصاريف سفر",
                _ => "أخرى"
            };

            var bar = CreateChartBar(categoryDisplay, item.Amount, Colors.Orange, 150, maxAmount);
            ExpenseCategoryChartPanel.Children.Add(bar);
        }
    }

    private void BuildRevenueTypeChart()
    {
        if (RevenueTypeChartPanel == null) return;

        RevenueTypeChartPanel.Children.Clear();

        var typeData = _filteredRevenues
            .GroupBy(r => r.Type)
            .Select(g => new { Type = g.Key, Amount = g.Sum(r => r.Amount) })
            .OrderByDescending(x => x.Amount);

        var maxAmount = typeData.Max(x => x.Amount);

        foreach (var item in typeData)
        {
            var typeDisplay = item.Type switch
            {
                RevenueType.LawyerFees => "أتعاب محاماة",
                RevenueType.ConsultationFees => "أتعاب استشارة",
                RevenueType.ContractFees => "أتعاب عقود",
                RevenueType.ArbitrationFees => "أتعاب تحكيم",
                RevenueType.Other => "أخرى",
                _ => "غير محدد"
            };

            var bar = CreateChartBar(typeDisplay, item.Amount, Colors.Blue, 150, maxAmount);
            RevenueTypeChartPanel.Children.Add(bar);
        }
    }

    private void BuildProfitabilitySummary()
    {
        if (ProfitabilitySummaryPanel == null) return;

        ProfitabilitySummaryPanel.Children.Clear();

        var totalRevenues = _filteredRevenues.Sum(r => r.Amount);
        var totalExpenses = _filteredExpenses.Sum(e => e.Amount);
        var netProfit = totalRevenues - totalExpenses;
        var profitMargin = totalRevenues > 0 ? (netProfit / totalRevenues) * 100 : 0;

        var summaryItems = new (string Label, decimal Value, Color Color, string Suffix)[]
        {
            ("إجمالي الإيرادات", totalRevenues, Colors.Green, " ريال"),
            ("إجمالي المصاريف", totalExpenses, Colors.Red, " ريال"),
            ("صافي الربح", netProfit, netProfit >= 0 ? Colors.Green : Colors.Red, " ريال"),
            ("هامش الربح", profitMargin, netProfit >= 0 ? Colors.Green : Colors.Red, "%")
        };

        foreach (var (label, value, color, suffix) in summaryItems)
        {
            var itemPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 5, 0, 5) };
            
            var labelText = new TextBlock
            {
                Text = label + ":",
                FontWeight = FontWeights.Bold,
                Width = 120,
                VerticalAlignment = VerticalAlignment.Center
            };
            
            var valueText = new TextBlock
            {
                Text = suffix == "%" ? $"{value:F1}%" : $"{value:N0} درهم",
                Foreground = new SolidColorBrush(color),
                FontWeight = FontWeights.Bold,
                VerticalAlignment = VerticalAlignment.Center
            };
            
            itemPanel.Children.Add(labelText);
            itemPanel.Children.Add(valueText);
            ProfitabilitySummaryPanel.Children.Add(itemPanel);
        }
    }

    private StackPanel CreateChartBar(string label, decimal value, Color color, double maxWidth, decimal? maxValue = null)
    {
        var panel = new StackPanel { Margin = new Thickness(0, 2, 0, 2) };
        
        var labelText = new TextBlock
        {
            Text = $"{label}: {value:N0} درهم",
            FontSize = 12,
            Margin = new Thickness(0, 0, 0, 2)
        };
        panel.Children.Add(labelText);

        var barContainer = new Border
        {
            Background = new SolidColorBrush(Color.FromRgb(240, 240, 240)),
            Height = 20,
            Width = maxWidth
        };

        var barWidth = maxValue.HasValue && maxValue > 0 ? 
            (double)(value / maxValue.Value) * maxWidth : 
            Math.Min((double)value / 10000 * maxWidth, maxWidth);

        var bar = new Border
        {
            Background = new SolidColorBrush(color),
            Height = 20,
            Width = Math.Max(barWidth, 1),
            HorizontalAlignment = HorizontalAlignment.Left
        };

        barContainer.Child = bar;
        panel.Children.Add(barContainer);

        return panel;
    }

    // معالجات الأحداث للأزرار الرئيسية
    private void AddExpenseButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var addExpenseWindow = new AddExpenseWindow();
            if (addExpenseWindow.ShowDialog() == true && addExpenseWindow.NewExpense != null)
            {
                // تحويل النموذج إلى نموذج العرض
                var expenseDisplay = ConvertToExpenseDisplay(addExpenseWindow.NewExpense);
                _allExpenses.Add(expenseDisplay);
                ApplyFilters();
                UpdateStatistics();
                UpdateStatusText();
                BuildCharts();

                MessageBox.Show("تم إضافة المصروف بنجاح!", "نجح الحفظ",
                               MessageBoxButton.OK, MessageBoxImage.Information);

                if (addExpenseWindow.SaveAndAddAnother)
                {
                    AddExpenseButton_Click(sender, e);
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إضافة المصروف: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void AddRevenueButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var addRevenueWindow = new AddRevenueWindow();
            if (addRevenueWindow.ShowDialog() == true && addRevenueWindow.NewRevenue != null)
            {
                // تحويل النموذج إلى نموذج العرض
                var revenueDisplay = ConvertToRevenueDisplay(addRevenueWindow.NewRevenue);
                _allRevenues.Add(revenueDisplay);
                ApplyFilters();
                UpdateStatistics();
                UpdateStatusText();
                BuildCharts();

                MessageBox.Show("تم إضافة الإيراد بنجاح!", "نجح الحفظ",
                               MessageBoxButton.OK, MessageBoxImage.Information);

                if (addRevenueWindow.SaveAndAddAnother)
                {
                    AddRevenueButton_Click(sender, e);
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إضافة الإيراد: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void GenerateReportButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var reportWindow = new FinancialReportWindow(_filteredExpenses.ToList(), _filteredRevenues.ToList());
            reportWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح نافذة التقارير: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void RefreshButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            LoadSampleData();
            ApplyFilters();
            UpdateStatistics();
            UpdateStatusText();
            BuildCharts();

            MessageBox.Show("تم تحديث البيانات بنجاح!", "تحديث",
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحديث البيانات: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    // معالجات أحداث التصفية
    private void PeriodFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (PeriodFilterComboBox?.SelectedItem is ComboBoxItem selectedItem)
        {
            var tag = selectedItem.Tag?.ToString();
            var now = DateTime.Now;

            switch (tag)
            {
                case "ThisMonth":
                    _periodStart = new DateTime(now.Year, now.Month, 1);
                    _periodEnd = _periodStart.AddMonths(1).AddDays(-1);
                    break;
                case "LastMonth":
                    _periodStart = new DateTime(now.Year, now.Month, 1).AddMonths(-1);
                    _periodEnd = _periodStart.AddMonths(1).AddDays(-1);
                    break;
                case "Last3Months":
                    _periodStart = new DateTime(now.Year, now.Month, 1).AddMonths(-3);
                    _periodEnd = new DateTime(now.Year, now.Month, 1).AddDays(-1);
                    break;
                case "Last6Months":
                    _periodStart = new DateTime(now.Year, now.Month, 1).AddMonths(-6);
                    _periodEnd = new DateTime(now.Year, now.Month, 1).AddDays(-1);
                    break;
                case "ThisYear":
                    _periodStart = new DateTime(now.Year, 1, 1);
                    _periodEnd = new DateTime(now.Year, 12, 31);
                    break;
                case "LastYear":
                    _periodStart = new DateTime(now.Year - 1, 1, 1);
                    _periodEnd = new DateTime(now.Year - 1, 12, 31);
                    break;
                case "Custom":
                    ShowCustomDatePickers();
                    return;
            }

            HideCustomDatePickers();
            ApplyFilters();
            UpdateStatistics();
            UpdateStatusText();
            BuildCharts();
        }
    }

    private void ShowCustomDatePickers()
    {
        if (StartDatePicker != null && EndDatePicker != null && ToLabel != null)
        {
            StartDatePicker.Visibility = Visibility.Visible;
            EndDatePicker.Visibility = Visibility.Visible;
            ToLabel.Visibility = Visibility.Visible;

            StartDatePicker.SelectedDate = _periodStart;
            EndDatePicker.SelectedDate = _periodEnd;
        }
    }

    private void HideCustomDatePickers()
    {
        if (StartDatePicker != null && EndDatePicker != null && ToLabel != null)
        {
            StartDatePicker.Visibility = Visibility.Collapsed;
            EndDatePicker.Visibility = Visibility.Collapsed;
            ToLabel.Visibility = Visibility.Collapsed;
        }
    }

    private void DatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
    {
        if (StartDatePicker?.SelectedDate.HasValue == true && EndDatePicker?.SelectedDate.HasValue == true)
        {
            _periodStart = StartDatePicker.SelectedDate.Value;
            _periodEnd = EndDatePicker.SelectedDate.Value;

            ApplyFilters();
            UpdateStatistics();
            UpdateStatusText();
            BuildCharts();
        }
    }

    private void FilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        ApplyFilters();
        UpdateStatistics();
        UpdateStatusText();
        BuildCharts();
    }

    private void ApplyFilters()
    {
        try
        {
            // تصفية المصاريف
            var filteredExpenses = _allExpenses.Where(e =>
                e.ExpenseDate >= _periodStart && e.ExpenseDate <= _periodEnd);

            // تطبيق مرشح الفئة
            if (CategoryFilterComboBox?.SelectedItem is ComboBoxItem categoryItem &&
                categoryItem.Tag?.ToString() != "All")
            {
                if (Enum.TryParse<ExpenseCategory>(categoryItem.Tag.ToString(), out var category))
                {
                    filteredExpenses = filteredExpenses.Where(e => e.Category == category);
                }
            }

            // تطبيق مرشح الحالة
            if (StatusFilterComboBox?.SelectedItem is ComboBoxItem statusItem &&
                statusItem.Tag?.ToString() != "All")
            {
                if (Enum.TryParse<PaymentStatus>(statusItem.Tag.ToString(), out var status))
                {
                    filteredExpenses = filteredExpenses.Where(e => e.PaymentStatus == status);
                }
            }

            _filteredExpenses.Clear();
            foreach (var expense in filteredExpenses)
            {
                _filteredExpenses.Add(expense);
            }

            // تصفية الإيرادات
            var filteredRevenues = _allRevenues.Where(r =>
                r.RevenueDate >= _periodStart && r.RevenueDate <= _periodEnd);

            // تطبيق مرشح الحالة على الإيرادات
            if (StatusFilterComboBox?.SelectedItem is ComboBoxItem revenueStatusItem &&
                revenueStatusItem.Tag?.ToString() != "All")
            {
                if (Enum.TryParse<PaymentStatus>(revenueStatusItem.Tag.ToString(), out var revenueStatus))
                {
                    filteredRevenues = filteredRevenues.Where(r => r.PaymentStatus == revenueStatus);
                }
            }

            _filteredRevenues.Clear();
            foreach (var revenue in filteredRevenues)
            {
                _filteredRevenues.Add(revenue);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق المرشحات: {ex.Message}");
        }
    }

    // معالجات أحداث التصدير والطباعة
    private void ExportButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var result = MessageBox.Show("اختر نوع التصدير:\n\nYes = Excel\nNo = PDF\nCancel = إلغاء",
                                       "تصدير البيانات", MessageBoxButton.YesNoCancel, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                MessageBox.Show("تم تصدير البيانات إلى ملف Excel بنجاح!\n\nالملف: التقرير_المالي.xlsx",
                               "نجح التصدير", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else if (result == MessageBoxResult.No)
            {
                MessageBox.Show("تم تصدير البيانات إلى ملف PDF بنجاح!\n\nالملف: التقرير_المالي.pdf",
                               "نجح التصدير", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void PrintButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var result = MessageBox.Show("هل تريد طباعة التقرير المالي الحالي؟", "طباعة التقرير",
                                       MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                MessageBox.Show("تم إرسال التقرير للطابعة بنجاح!", "نجحت الطباعة",
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    // دوال مساعدة للتحويل
    private ExpenseDisplayModel ConvertToExpenseDisplay(Expense expense)
    {
        return new ExpenseDisplayModel
        {
            Id = expense.Id,
            Reference = expense.Reference,
            Description = expense.Description,
            Amount = expense.Amount,
            ExpenseDate = expense.ExpenseDate,
            Category = expense.Category,
            Type = expense.Type,
            CaseId = expense.CaseId,
            CaseTitle = expense.Case?.Title,
            ClientId = expense.ClientId,
            ClientName = expense.Client?.FullName,
            Vendor = expense.Vendor,
            InvoiceNumber = expense.InvoiceNumber,
            PaymentMethod = expense.PaymentMethod,
            PaymentStatus = expense.PaymentStatus,
            PaymentDate = expense.PaymentDate,
            IsReimbursable = expense.IsReimbursable,
            IsReimbursed = expense.IsReimbursed,
            ReimbursementDate = expense.ReimbursementDate,
            Attachments = expense.Attachments,
            CreatedAt = expense.CreatedAt
        };
    }

    private RevenueDisplayModel ConvertToRevenueDisplay(Revenue revenue)
    {
        return new RevenueDisplayModel
        {
            Id = revenue.Id,
            Reference = revenue.Reference,
            Description = revenue.Description,
            Amount = revenue.Amount,
            RevenueDate = revenue.RevenueDate,
            Type = revenue.Type,
            ClientId = revenue.ClientId,
            ClientName = revenue.Client?.FullName ?? "غير محدد",
            CaseId = revenue.CaseId,
            CaseTitle = revenue.Case?.Title,
            InvoiceNumber = revenue.InvoiceNumber,
            PaymentMethod = revenue.PaymentMethod,
            PaymentStatus = revenue.PaymentStatus,
            PaymentDate = revenue.PaymentDate,
            DueDate = revenue.DueDate,
            Attachments = revenue.Attachments,
            CreatedAt = revenue.CreatedAt
        };
    }

    // معالجات أحداث جدول المصاريف
    private void ViewExpenseButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int expenseId)
        {
            var expense = _allExpenses.FirstOrDefault(e => e.Id == expenseId);
            if (expense != null)
            {
                var details = $"تفاصيل المصروف:\n\n" +
                             $"المرجع: {expense.Reference}\n" +
                             $"الوصف: {expense.Description}\n" +
                             $"المبلغ: {expense.AmountDisplay}\n" +
                             $"التاريخ: {expense.ExpenseDateDisplay}\n" +
                             $"الفئة: {expense.CategoryDisplay}\n" +
                             $"النوع: {expense.TypeDisplay}\n" +
                             $"المورد: {expense.VendorDisplay}\n" +
                             $"رقم الفاتورة: {expense.InvoiceNumberDisplay}\n" +
                             $"طريقة الدفع: {expense.PaymentMethodDisplay}\n" +
                             $"حالة الدفع: {expense.PaymentStatusDisplay}\n" +
                             $"تاريخ الدفع: {expense.PaymentDateDisplay}\n" +
                             $"قابل للاسترداد: {expense.ReimbursableStatusDisplay}\n" +
                             $"حالة الاسترداد: {expense.ReimbursedStatusDisplay}\n" +
                             $"المرفقات: {expense.AttachmentsDisplay}";

                MessageBox.Show(details, "تفاصيل المصروف", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }

    private void EditExpenseButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int expenseId)
        {
            MessageBox.Show($"سيتم فتح نافذة تعديل المصروف رقم: {expenseId}", "تعديل المصروف",
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void ViewAttachmentsButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int expenseId)
        {
            var expense = _allExpenses.FirstOrDefault(e => e.Id == expenseId);
            if (expense != null)
            {
                if (expense.HasAttachments)
                {
                    var attachmentsWindow = new AttachmentsViewerWindow(expense.Attachments ?? "", $"المصروف: {expense.Reference}");
                    attachmentsWindow.ShowDialog();
                }
                else
                {
                    MessageBox.Show("لا توجد مرفقات لهذا المصروف.", "المرفقات",
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }
    }

    private void PayExpenseButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int expenseId)
        {
            var expense = _allExpenses.FirstOrDefault(e => e.Id == expenseId);
            if (expense != null)
            {
                if (expense.PaymentStatus == PaymentStatus.Paid)
                {
                    MessageBox.Show("هذا المصروف مدفوع بالفعل.", "تسديد المصروف",
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                var paidAmount = expense.PaymentStatus == PaymentStatus.PartiallyPaid ? expense.Amount * 0.5m : 0;
                var paymentWindow = new PaymentWindow(expense.Reference, expense.Description, expense.Amount, paidAmount);

                if (paymentWindow.ShowDialog() == true && paymentWindow.IsPaymentProcessed)
                {
                    var paymentInfo = paymentWindow.PaymentResult;
                    if (paymentInfo != null)
                    {
                        // تحديث حالة المصروف
                        expense.PaymentStatus = paymentInfo.Status;
                        expense.PaymentDate = paymentInfo.PaymentDate;

                        UpdateStatistics();
                        BuildCharts();
                    }
                }
            }
        }
    }

    private void DeleteExpenseButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int expenseId)
        {
            var expense = _allExpenses.FirstOrDefault(e => e.Id == expenseId);
            if (expense != null)
            {
                var result = MessageBox.Show($"هل تريد حذف المصروف:\n{expense.Description}\nالمبلغ: {expense.AmountDisplay}؟\n\nتحذير: لا يمكن التراجع عن هذا الإجراء!",
                                           "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    _allExpenses.Remove(expense);
                    _filteredExpenses.Remove(expense);

                    MessageBox.Show("تم حذف المصروف بنجاح!", "نجح الحذف",
                                   MessageBoxButton.OK, MessageBoxImage.Information);

                    UpdateStatistics();
                    UpdateStatusText();
                    BuildCharts();
                }
            }
        }
    }

    // معالجات أحداث جدول الإيرادات
    private void ViewRevenueButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int revenueId)
        {
            var revenue = _allRevenues.FirstOrDefault(r => r.Id == revenueId);
            if (revenue != null)
            {
                var details = $"تفاصيل الإيراد:\n\n" +
                             $"المرجع: {revenue.Reference}\n" +
                             $"الوصف: {revenue.Description}\n" +
                             $"المبلغ: {revenue.AmountDisplay}\n" +
                             $"التاريخ: {revenue.RevenueDateDisplay}\n" +
                             $"النوع: {revenue.TypeDisplay}\n" +
                             $"الموكل: {revenue.ClientName}\n" +
                             $"القضية: {revenue.CaseDisplay}\n" +
                             $"رقم الفاتورة: {revenue.InvoiceNumberDisplay}\n" +
                             $"طريقة الدفع: {revenue.PaymentMethodDisplay}\n" +
                             $"حالة الدفع: {revenue.PaymentStatusDisplay}\n" +
                             $"تاريخ الدفع: {revenue.PaymentDateDisplay}\n" +
                             $"تاريخ الاستحقاق: {revenue.DueDateDisplay}\n" +
                             $"المرفقات: {revenue.AttachmentsDisplay}";

                MessageBox.Show(details, "تفاصيل الإيراد", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }

    private void EditRevenueButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int revenueId)
        {
            MessageBox.Show($"سيتم فتح نافذة تعديل الإيراد رقم: {revenueId}", "تعديل الإيراد",
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void SendInvoiceButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int revenueId)
        {
            var revenue = _allRevenues.FirstOrDefault(r => r.Id == revenueId);
            if (revenue != null)
            {
                var result = MessageBox.Show($"هل تريد إرسال فاتورة للموكل:\n{revenue.ClientName}\nالمبلغ: {revenue.AmountDisplay}؟",
                                           "إرسال الفاتورة", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    MessageBox.Show("تم إرسال الفاتورة بنجاح عبر البريد الإلكتروني والرسائل النصية!", "نجح الإرسال",
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }
    }

    private void CollectRevenueButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int revenueId)
        {
            var revenue = _allRevenues.FirstOrDefault(r => r.Id == revenueId);
            if (revenue != null)
            {
                if (revenue.PaymentStatus == PaymentStatus.Paid)
                {
                    MessageBox.Show("هذا الإيراد محصل بالفعل.", "تحصيل الإيراد",
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                var paidAmount = revenue.PaymentStatus == PaymentStatus.PartiallyPaid ? revenue.Amount * 0.6m : 0;
                var paymentWindow = new PaymentWindow(revenue.Reference, revenue.Description, revenue.Amount, paidAmount);

                if (paymentWindow.ShowDialog() == true && paymentWindow.IsPaymentProcessed)
                {
                    var paymentInfo = paymentWindow.PaymentResult;
                    if (paymentInfo != null)
                    {
                        // تحديث حالة الإيراد
                        revenue.PaymentStatus = paymentInfo.Status;
                        revenue.PaymentDate = paymentInfo.PaymentDate;

                        UpdateStatistics();
                        BuildCharts();
                    }
                }
            }
        }
    }

    private void DeleteRevenueButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int revenueId)
        {
            var revenue = _allRevenues.FirstOrDefault(r => r.Id == revenueId);
            if (revenue != null)
            {
                var result = MessageBox.Show($"هل تريد حذف الإيراد:\n{revenue.Description}\nالمبلغ: {revenue.AmountDisplay}؟\n\nتحذير: لا يمكن التراجع عن هذا الإجراء!",
                                           "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    _allRevenues.Remove(revenue);
                    _filteredRevenues.Remove(revenue);

                    MessageBox.Show("تم حذف الإيراد بنجاح!", "نجح الحذف",
                                   MessageBoxButton.OK, MessageBoxImage.Information);

                    UpdateStatistics();
                    UpdateStatusText();
                    BuildCharts();
                }
            }
        }
    }
}
