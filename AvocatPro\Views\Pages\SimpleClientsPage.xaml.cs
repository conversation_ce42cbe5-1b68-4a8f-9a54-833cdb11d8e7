using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace AvocatPro.Views.Pages
{
    /// <summary>
    /// صفحة إدارة الموكلين الشاملة
    /// </summary>
    public partial class SimpleClientsPage : Page, INotifyPropertyChanged
    {
        #region Properties

        private ObservableCollection<ClientModel> _clients;
        public ObservableCollection<ClientModel> Clients
        {
            get => _clients;
            set
            {
                _clients = value;
                OnPropertyChanged(nameof(Clients));
            }
        }

        private ObservableCollection<ClientModel> _filteredClients;
        public ObservableCollection<ClientModel> FilteredClients
        {
            get => _filteredClients;
            set
            {
                _filteredClients = value;
                OnPropertyChanged(nameof(FilteredClients));
            }
        }

        #endregion

        #region Constructor

        public SimpleClientsPage()
        {
            InitializeComponent();
            DataContext = this;
            LoadSampleData();
            InitializeFilters();
        }

        #endregion

        #region Data Loading

        /// <summary>
        /// تحميل البيانات الوهمية
        /// </summary>
        private void LoadSampleData()
        {
            Clients = new ObservableCollection<ClientModel>
            {
                new ClientModel
                {
                    Id = 1,
                    Name = "أحمد محمد علي",
                    ClientType = "فرد",
                    OfficeRef = "CLT-001",
                    Phone = "+966501234567",
                    Email = "<EMAIL>",
                    Address = "الرياض، المملكة العربية السعودية",
                    CasesCount = 3,
                    Status = "نشط",
                    InitialLetter = "أ",
                    CreatedDate = DateTime.Now.AddDays(-30)
                },
                new ClientModel
                {
                    Id = 2,
                    Name = "شركة النور للتجارة",
                    ClientType = "شركة",
                    OfficeRef = "CLT-002",
                    Phone = "+966502345678",
                    Email = "<EMAIL>",
                    Address = "جدة، المملكة العربية السعودية",
                    CasesCount = 7,
                    Status = "نشط",
                    InitialLetter = "ش",
                    CreatedDate = DateTime.Now.AddDays(-45)
                },
                new ClientModel
                {
                    Id = 3,
                    Name = "فاطمة عبدالله",
                    ClientType = "فرد",
                    OfficeRef = "CLT-003",
                    Phone = "+966503456789",
                    Email = "<EMAIL>",
                    Address = "الدمام، المملكة العربية السعودية",
                    CasesCount = 2,
                    Status = "نشط",
                    InitialLetter = "ف",
                    CreatedDate = DateTime.Now.AddDays(-15)
                },
                new ClientModel
                {
                    Id = 4,
                    Name = "مؤسسة البناء الحديث",
                    ClientType = "شركة",
                    OfficeRef = "CLT-004",
                    Phone = "+966504567890",
                    Email = "<EMAIL>",
                    Address = "مكة المكرمة، المملكة العربية السعودية",
                    CasesCount = 5,
                    Status = "غير نشط",
                    InitialLetter = "م",
                    CreatedDate = DateTime.Now.AddDays(-60)
                },
                new ClientModel
                {
                    Id = 5,
                    Name = "خالد سعد الدين",
                    ClientType = "فرد",
                    OfficeRef = "CLT-005",
                    Phone = "+966505678901",
                    Email = "<EMAIL>",
                    Address = "المدينة المنورة، المملكة العربية السعودية",
                    CasesCount = 1,
                    Status = "نشط",
                    InitialLetter = "خ",
                    CreatedDate = DateTime.Now.AddDays(-10)
                },
                new ClientModel
                {
                    Id = 6,
                    Name = "شركة التقنية المتقدمة",
                    ClientType = "شركة",
                    OfficeRef = "CLT-006",
                    Phone = "+966506789012",
                    Email = "<EMAIL>",
                    Address = "الخبر، المملكة العربية السعودية",
                    CasesCount = 4,
                    Status = "نشط",
                    InitialLetter = "ش",
                    CreatedDate = DateTime.Now.AddDays(-25)
                },
                new ClientModel
                {
                    Id = 7,
                    Name = "نورا أحمد الزهراني",
                    ClientType = "فرد",
                    OfficeRef = "CLT-007",
                    Phone = "+966507890123",
                    Email = "<EMAIL>",
                    Address = "أبها، المملكة العربية السعودية",
                    CasesCount = 2,
                    Status = "نشط",
                    InitialLetter = "ن",
                    CreatedDate = DateTime.Now.AddDays(-5)
                }
            };

            FilteredClients = new ObservableCollection<ClientModel>(Clients);
            ClientsDataGrid.ItemsSource = FilteredClients;
            UpdateResultsCount();
        }

        #endregion

        #region Initialization

        /// <summary>
        /// تهيئة عناصر التصفية
        /// </summary>
        private void InitializeFilters()
        {
            ClientTypeFilter.SelectedIndex = 0; // جميع الأنواع
            StatusFilter.SelectedIndex = 0; // جميع الحالات
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// إضافة موكل جديد
        /// </summary>
        private void AddClient_Click(object sender, RoutedEventArgs e)
        {
            var addClientDialog = new AddClientDialog();
            if (addClientDialog.ShowDialog() == true)
            {
                // إضافة الموكل الجديد إلى القائمة
                var newClient = addClientDialog.NewClient;
                newClient.Id = Clients.Count + 1;
                newClient.OfficeRef = $"CLT-{newClient.Id:000}";
                newClient.CreatedDate = DateTime.Now;

                Clients.Add(newClient);
                ApplyFilters();

                MessageBox.Show("تم إضافة الموكل بنجاح!", "نجح",
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// تصدير البيانات
        /// </summary>
        private void ExportClients_Click(object sender, RoutedEventArgs e)
        {
            var contextMenu = new ContextMenu();

            var exportPdfItem = new MenuItem { Header = "تصدير PDF" };
            exportPdfItem.Click += (s, args) => ExportToPdf();

            var exportExcelItem = new MenuItem { Header = "تصدير Excel" };
            exportExcelItem.Click += (s, args) => ExportToExcel();

            contextMenu.Items.Add(exportPdfItem);
            contextMenu.Items.Add(exportExcelItem);

            var button = sender as Button;
            contextMenu.PlacementTarget = button;
            contextMenu.IsOpen = true;
        }

        /// <summary>
        /// البحث في النص
        /// </summary>
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        /// <summary>
        /// تصفية نوع الموكل
        /// </summary>
        private void ClientTypeFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        /// <summary>
        /// تصفية الحالة
        /// </summary>
        private void StatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void RefreshClients_Click(object sender, RoutedEventArgs e)
        {
            LoadSampleData();
            SearchTextBox.Text = "";
            ClientTypeFilter.SelectedIndex = 0;
            StatusFilter.SelectedIndex = 0;

            MessageBox.Show("تم تحديث البيانات بنجاح!", "تحديث",
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// طباعة قائمة الموكلين
        /// </summary>
        private void PrintClients_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير ميزة الطباعة قريباً", "قيد التطوير",
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// تصدير إلى Excel
        /// </summary>
        private void ExportExcel_Click(object sender, RoutedEventArgs e)
        {
            ExportToExcel();
        }

        /// <summary>
        /// عرض تفاصيل الموكل
        /// </summary>
        private void ViewClient_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var client = button?.Tag as ClientModel;

            if (client != null)
            {
                var viewDialog = new ViewClientDialog(client);
                viewDialog.ShowDialog();
            }
        }

        /// <summary>
        /// تعديل الموكل
        /// </summary>
        private void EditClient_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var client = button?.Tag as ClientModel;

            if (client != null)
            {
                var editDialog = new AddClientDialog(client);
                if (editDialog.ShowDialog() == true)
                {
                    // تحديث بيانات الموكل
                    var index = Clients.IndexOf(client);
                    if (index >= 0)
                    {
                        Clients[index] = editDialog.NewClient;
                        ApplyFilters();

                        MessageBox.Show("تم تحديث بيانات الموكل بنجاح!", "نجح",
                                       MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
        }

        /// <summary>
        /// حذف الموكل
        /// </summary>
        private void DeleteClient_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var client = button?.Tag as ClientModel;

            if (client != null)
            {
                var result = MessageBox.Show($"هل أنت متأكد من حذف الموكل '{client.Name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                                           "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    Clients.Remove(client);
                    ApplyFilters();

                    MessageBox.Show("تم حذف الموكل بنجاح!", "نجح",
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        /// <summary>
        /// الصفحة السابقة
        /// </summary>
        private void PreviousPage_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير ميزة التنقل بين الصفحات قريباً", "قيد التطوير",
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// الصفحة التالية
        /// </summary>
        private void NextPage_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير ميزة التنقل بين الصفحات قريباً", "قيد التطوير",
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// تطبيق المرشحات على البيانات
        /// </summary>
        private void ApplyFilters()
        {
            if (Clients == null) return;

            var filtered = Clients.AsEnumerable();

            // تصفية النص
            if (!string.IsNullOrWhiteSpace(SearchTextBox?.Text))
            {
                var searchText = SearchTextBox.Text.ToLower();
                filtered = filtered.Where(c =>
                    c.Name.ToLower().Contains(searchText) ||
                    c.OfficeRef.ToLower().Contains(searchText) ||
                    c.Phone.Contains(searchText) ||
                    c.Email.ToLower().Contains(searchText));
            }

            // تصفية نوع الموكل
            if (ClientTypeFilter?.SelectedIndex > 0)
            {
                var selectedType = (ClientTypeFilter.SelectedItem as ComboBoxItem)?.Content.ToString();
                if (selectedType == "أفراد")
                    filtered = filtered.Where(c => c.ClientType == "فرد");
                else if (selectedType == "شركات")
                    filtered = filtered.Where(c => c.ClientType == "شركة");
            }

            // تصفية الحالة
            if (StatusFilter?.SelectedIndex > 0)
            {
                var selectedStatus = (StatusFilter.SelectedItem as ComboBoxItem)?.Content.ToString();
                if (!string.IsNullOrEmpty(selectedStatus))
                    filtered = filtered.Where(c => c.Status == selectedStatus);
            }

            FilteredClients = new ObservableCollection<ClientModel>(filtered);
            ClientsDataGrid.ItemsSource = FilteredClients;
            UpdateResultsCount();
        }

        /// <summary>
        /// تحديث عداد النتائج
        /// </summary>
        private void UpdateResultsCount()
        {
            if (FilteredClients != null && ResultsCountText != null)
            {
                var total = FilteredClients.Count;
                ResultsCountText.Text = $"عرض 1-{Math.Min(10, total)} من {total} موكل";
            }
        }

        /// <summary>
        /// تصدير إلى PDF
        /// </summary>
        private void ExportToPdf()
        {
            try
            {
                MessageBox.Show("سيتم تطوير ميزة تصدير PDF قريباً", "قيد التطوير",
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير PDF: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تصدير إلى Excel
        /// </summary>
        private void ExportToExcel()
        {
            try
            {
                MessageBox.Show("سيتم تطوير ميزة تصدير Excel قريباً", "قيد التطوير",
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير Excel: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Client Model

    /// <summary>
    /// نموذج بيانات الموكل
    /// </summary>
    public class ClientModel : INotifyPropertyChanged
    {
        private int _id;
        private string _name;
        private string _clientType;
        private string _officeRef;
        private string _phone;
        private string _email;
        private string _address;
        private int _casesCount;
        private string _status;
        private string _initialLetter;
        private DateTime _createdDate;

        public int Id
        {
            get => _id;
            set { _id = value; OnPropertyChanged(nameof(Id)); }
        }

        public string Name
        {
            get => _name;
            set
            {
                _name = value;
                OnPropertyChanged(nameof(Name));
                // تحديث الحرف الأول
                InitialLetter = !string.IsNullOrEmpty(value) ? value.Substring(0, 1) : "؟";
            }
        }

        public string ClientType
        {
            get => _clientType;
            set { _clientType = value; OnPropertyChanged(nameof(ClientType)); }
        }

        public string OfficeRef
        {
            get => _officeRef;
            set { _officeRef = value; OnPropertyChanged(nameof(OfficeRef)); }
        }

        public string Phone
        {
            get => _phone;
            set { _phone = value; OnPropertyChanged(nameof(Phone)); }
        }

        public string Email
        {
            get => _email;
            set { _email = value; OnPropertyChanged(nameof(Email)); }
        }

        public string Address
        {
            get => _address;
            set { _address = value; OnPropertyChanged(nameof(Address)); }
        }

        public int CasesCount
        {
            get => _casesCount;
            set { _casesCount = value; OnPropertyChanged(nameof(CasesCount)); }
        }

        public string Status
        {
            get => _status;
            set { _status = value; OnPropertyChanged(nameof(Status)); }
        }

        public string InitialLetter
        {
            get => _initialLetter;
            set { _initialLetter = value; OnPropertyChanged(nameof(InitialLetter)); }
        }

        public DateTime CreatedDate
        {
            get => _createdDate;
            set { _createdDate = value; OnPropertyChanged(nameof(CreatedDate)); }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    #endregion

    #region Dialog Classes (Placeholder)

    /// <summary>
    /// نافذة إضافة/تعديل موكل (مؤقتة)
    /// </summary>
    public class AddClientDialog : Window
    {
        public ClientModel NewClient { get; set; }

        public AddClientDialog(ClientModel existingClient = null)
        {
            Title = existingClient == null ? "إضافة موكل جديد" : "تعديل بيانات الموكل";
            Width = 500;
            Height = 400;
            WindowStartupLocation = WindowStartupLocation.CenterOwner;

            NewClient = existingClient ?? new ClientModel
            {
                Name = "موكل جديد",
                ClientType = "فرد",
                Phone = "+966500000000",
                Email = "<EMAIL>",
                Address = "العنوان",
                Status = "نشط",
                CasesCount = 0
            };
        }

        protected override void OnSourceInitialized(EventArgs e)
        {
            base.OnSourceInitialized(e);
            DialogResult = true; // مؤقت - سيتم استبداله بنافذة حقيقية
        }
    }

    /// <summary>
    /// نافذة عرض تفاصيل الموكل (مؤقتة)
    /// </summary>
    public class ViewClientDialog : Window
    {
        public ViewClientDialog(ClientModel client)
        {
            Title = $"تفاصيل الموكل: {client.Name}";
            Width = 600;
            Height = 500;
            WindowStartupLocation = WindowStartupLocation.CenterOwner;

            var content = new TextBlock
            {
                Text = $"الاسم: {client.Name}\nالنوع: {client.ClientType}\nالهاتف: {client.Phone}\nالبريد: {client.Email}\nالعنوان: {client.Address}\nعدد القضايا: {client.CasesCount}\nالحالة: {client.Status}",
                Margin = new Thickness(20),
                FontSize = 14,
                TextWrapping = TextWrapping.Wrap
            };

            Content = content;
        }
    }

    #endregion
}
