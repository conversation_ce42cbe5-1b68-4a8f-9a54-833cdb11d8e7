using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Microsoft.Win32;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Data.SqlClient;
using System.Configuration;
using AvocatPro.Services;

namespace AvocatPro.Views.Pages
{
    public partial class AdvancedComprehensiveSettingsPage : Page
    {
        private readonly SettingsService _settingsService;
        private bool _isDarkMode = false;

        public AdvancedComprehensiveSettingsPage()
        {
            InitializeComponent();
            _settingsService = new SettingsService();
            LoadAllSettings();
            SetupEventHandlers();
        }

        #region تحميل الإعدادات
        private void LoadAllSettings()
        {
            try
            {
                LoadOfficeSettings();
                LoadEmailSettings();
                LoadNotificationSettings();
                LoadAppearanceSettings();
                LoadDatabaseSettings();
                LoadLanguageSettings();
                LoadMaintenanceSettings();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadOfficeSettings()
        {
            var settings = _settingsService.Settings;
            OfficeNameTextBox.Text = settings.OfficeName ?? "مكتب المحامي الذكي للاستشارات القانونية";
            LicenseNumberTextBox.Text = settings.LicenseNumber ?? "";
            MainPhoneTextBox.Text = settings.MainPhone ?? "";
            OfficeAddressTextBox.Text = settings.OfficeAddress ?? "";
            OfficialEmailTextBox.Text = settings.OfficialEmail ?? "";
            WebsiteTextBox.Text = settings.Website ?? "";
        }

        private void LoadEmailSettings()
        {
            var settings = _settingsService.Settings;
            SmtpServerTextBox.Text = settings.SmtpServer ?? "smtp.gmail.com";
            SmtpPortTextBox.Text = settings.SmtpPort.ToString();
            EmailUsernameTextBox.Text = settings.SmtpUsername ?? "";
            EnableSslCheckBox.IsChecked = settings.SmtpEnableSsl;
        }

        private void LoadNotificationSettings()
        {
            var settings = _settingsService.Settings;
            EnableSoundNotificationsCheckBox.IsChecked = settings.EnableSoundNotifications;
            AppointmentSoundCheckBox.IsChecked = settings.AppointmentSoundEnabled;
            EmailSoundCheckBox.IsChecked = settings.EmailSoundEnabled;
            SystemSoundCheckBox.IsChecked = settings.SystemSoundEnabled;
            VolumeSlider.Value = settings.NotificationVolume;
            VolumeLabel.Text = $"{settings.NotificationVolume}%";
            
            EnableVisualNotificationsCheckBox.IsChecked = settings.EnableVisualNotifications;
            PopupNotificationsCheckBox.IsChecked = settings.PopupNotificationsEnabled;
            TaskbarNotificationsCheckBox.IsChecked = settings.TaskbarNotificationsEnabled;
            FlashingNotificationsCheckBox.IsChecked = settings.FlashingNotificationsEnabled;
            NotificationDurationTextBox.Text = settings.NotificationDuration.ToString();
        }

        private void LoadAppearanceSettings()
        {
            var settings = _settingsService.Settings;
            _isDarkMode = settings.IsDarkMode;
            UpdateDarkModeButton();
            
            if (settings.IsDarkMode)
                DarkModeRadio.IsChecked = true;
            else if (settings.IsAutoMode)
                AutoModeRadio.IsChecked = true;
            else
                LightModeRadio.IsChecked = true;

            FontSizeComboBox.SelectedIndex = GetFontSizeIndex(settings.FontSize);
            FontFamilyComboBox.SelectedIndex = GetFontFamilyIndex(settings.FontFamily);
            IconSizeComboBox.SelectedIndex = GetIconSizeIndex(settings.IconSize);
            ShowIconLabelsCheckBox.IsChecked = settings.ShowIconLabels;
            AnimatedIconsCheckBox.IsChecked = settings.AnimatedIcons;
        }

        private void LoadDatabaseSettings()
        {
            var settings = _settingsService.Settings;
            DatabaseTypeComboBox.SelectedIndex = GetDatabaseTypeIndex(settings.DatabaseType);
            DatabaseNameTextBox.Text = settings.DatabaseName ?? "AvocatProDB";
            ServerAddressTextBox.Text = settings.ServerAddress ?? "localhost";
            DatabasePortTextBox.Text = settings.DatabasePort.ToString();
            DatabaseUsernameTextBox.Text = settings.DatabaseUsername ?? "";
            IntegratedSecurityCheckBox.IsChecked = settings.UseIntegratedSecurity;
            ConnectionTimeoutTextBox.Text = settings.ConnectionTimeout.ToString();
            MaxConnectionsTextBox.Text = settings.MaxConnections.ToString();
            EnableConnectionPoolingCheckBox.IsChecked = settings.EnableConnectionPooling;
            EnableQueryCachingCheckBox.IsChecked = settings.EnableQueryCaching;
            EnableAutoBackupCheckBox.IsChecked = settings.AutoBackup;
        }

        private void LoadLanguageSettings()
        {
            var settings = _settingsService.Settings;
            InterfaceLanguageComboBox.SelectedIndex = GetLanguageIndex(settings.Language);
            TextDirectionComboBox.SelectedIndex = settings.IsRightToLeft ? 0 : 1;
            TimezoneComboBox.SelectedIndex = GetTimezoneIndex(settings.Timezone);
            DateFormatComboBox.SelectedIndex = GetDateFormatIndex(settings.DateFormat);
            CurrencyComboBox.SelectedIndex = GetCurrencyIndex(settings.Currency);
            CurrencySymbolTextBox.Text = settings.CurrencySymbol ?? "د.م.";
            CurrencyPositionComboBox.SelectedIndex = settings.CurrencyPosition == "Before" ? 0 : 1;
            DecimalPlacesComboBox.SelectedIndex = settings.DecimalPlaces;
        }

        private void LoadMaintenanceSettings()
        {
            var settings = _settingsService.Settings;
            EnableLoggingCheckBox.IsChecked = settings.EnableLogging;
            LogErrorsCheckBox.IsChecked = settings.LogErrors;
            LogWarningsCheckBox.IsChecked = settings.LogWarnings;
            LogInfoCheckBox.IsChecked = settings.LogInfo;
            LogLevelComboBox.SelectedIndex = GetLogLevelIndex(settings.LogLevel);
        }
        #endregion

        #region معالجات الأحداث الرئيسية
        private void SetupEventHandlers()
        {
            VolumeSlider.ValueChanged += (s, e) => VolumeLabel.Text = $"{(int)e.NewValue}%";
            DatabaseTypeComboBox.SelectionChanged += DatabaseType_SelectionChanged;
        }

        private void DarkModeToggle_Click(object sender, RoutedEventArgs e)
        {
            _isDarkMode = !_isDarkMode;
            UpdateDarkModeButton();
            ApplyTheme(_isDarkMode);
            
            if (_isDarkMode)
                DarkModeRadio.IsChecked = true;
            else
                LightModeRadio.IsChecked = true;
        }

        private void UpdateDarkModeButton()
        {
            DarkModeToggleButton.Content = _isDarkMode ? "☀️" : "🌙";
            DarkModeToggleButton.ToolTip = _isDarkMode ? "تبديل إلى الوضع النهاري" : "تبديل إلى الوضع الليلي";
        }

        private void ApplyTheme(bool isDark)
        {
            try
            {
                var backgroundColor = isDark ? "#1F2937" : "#F8FAFC";
                var textColor = isDark ? "#F9FAFB" : "#1F2937";
                
                this.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(backgroundColor));
                
                // تطبيق الثيم على جميع العناصر
                foreach (TabItem tab in SettingsTabControl.Items)
                {
                    if (tab.Content is Border border)
                    {
                        border.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(isDark ? "#374151" : "White"));
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق الثيم: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void SaveAllSettings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SaveAllButton.IsEnabled = false;
                SaveAllButton.Content = "⏳ جاري الحفظ...";
                
                await SaveAllSettingsAsync();
                
                MessageBox.Show("تم حفظ جميع الإعدادات بنجاح!", "نجح الحفظ", 
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                SaveAllButton.IsEnabled = true;
                SaveAllButton.Content = "💾";
            }
        }

        private async void ResetAllSettings_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟\n\nسيتم فقدان جميع التخصيصات الحالية!", 
                                       "تأكيد إعادة التعيين", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Warning);
            
            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    ResetAllButton.IsEnabled = false;
                    ResetAllButton.Content = "⏳ جاري إعادة التعيين...";
                    
                    await _settingsService.ResetToDefaultsAsync();
                    LoadAllSettings();
                    
                    MessageBox.Show("تم إعادة تعيين جميع الإعدادات بنجاح!", "تمت إعادة التعيين", 
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إعادة تعيين الإعدادات: {ex.Message}", "خطأ", 
                                   MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    ResetAllButton.IsEnabled = true;
                    ResetAllButton.Content = "🔄";
                }
            }
        }

        private async void TestConnection_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                TestConnectionButton.IsEnabled = false;
                TestConnectionButton.Content = "⏳";
                
                var results = await RunConnectionTestsAsync();
                ShowConnectionTestResults(results);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار الاتصالات: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                TestConnectionButton.IsEnabled = true;
                TestConnectionButton.Content = "🔗";
            }
        }
        #endregion

        #region معالجات أحداث إعدادات المكتب
        private void SelectLogo_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new OpenFileDialog
            {
                Title = "اختيار شعار المكتب",
                Filter = "ملفات الصور (*.png;*.jpg;*.jpeg;*.gif;*.bmp)|*.png;*.jpg;*.jpeg;*.gif;*.bmp|جميع الملفات (*.*)|*.*",
                CheckFileExists = true
            };

            if (dialog.ShowDialog() == true)
            {
                try
                {
                    LogoPreview.Source = new System.Windows.Media.Imaging.BitmapImage(new Uri(dialog.FileName));
                    MessageBox.Show("تم تحديد الشعار بنجاح!", "نجح", 
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحميل الشعار: {ex.Message}", "خطأ", 
                                   MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void SelectPrimaryColor_Click(object sender, RoutedEventArgs e)
        {
            var colorDialog = new System.Windows.Forms.ColorDialog();
            if (colorDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                var color = Color.FromArgb(colorDialog.Color.A, colorDialog.Color.R, colorDialog.Color.G, colorDialog.Color.B);
                PrimaryColorPreview.Fill = new SolidColorBrush(color);
            }
        }

        private void SelectSecondaryColor_Click(object sender, RoutedEventArgs e)
        {
            var colorDialog = new System.Windows.Forms.ColorDialog();
            if (colorDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                var color = Color.FromArgb(colorDialog.Color.A, colorDialog.Color.R, colorDialog.Color.G, colorDialog.Color.B);
                SecondaryColorPreview.Fill = new SolidColorBrush(color);
            }
        }
        #endregion

        #region معالجات أحداث البريد الإلكتروني
        private async void TestEmailConnection_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                button.IsEnabled = false;
                button.Content = "⏳ جاري الاختبار...";

                var smtpServer = SmtpServerTextBox.Text;
                var port = int.Parse(SmtpPortTextBox.Text);
                var username = EmailUsernameTextBox.Text;
                var password = EmailPasswordBox.Password;
                var enableSsl = EnableSslCheckBox.IsChecked ?? false;

                var result = await TestEmailConnectionAsync(smtpServer, port, username, password, enableSsl);

                if (result)
                {
                    MessageBox.Show("تم الاتصال بخادم البريد الإلكتروني بنجاح!", "نجح الاختبار",
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("فشل في الاتصال بخادم البريد الإلكتروني. يرجى التحقق من الإعدادات.", "فشل الاختبار",
                                   MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار البريد الإلكتروني: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                var button = sender as Button;
                button.IsEnabled = true;
                button.Content = "🧪 اختبار الاتصال";
            }
        }

        private async Task<bool> TestEmailConnectionAsync(string server, int port, string username, string password, bool enableSsl)
        {
            try
            {
                using (var client = new System.Net.Mail.SmtpClient(server, port))
                {
                    client.EnableSsl = enableSsl;
                    client.Credentials = new System.Net.NetworkCredential(username, password);
                    client.Timeout = 10000; // 10 seconds timeout

                    // إرسال رسالة اختبار
                    var testMessage = new System.Net.Mail.MailMessage
                    {
                        From = new System.Net.Mail.MailAddress(username),
                        Subject = "اختبار الاتصال - AvocatPro",
                        Body = "هذه رسالة اختبار للتأكد من صحة إعدادات البريد الإلكتروني.",
                        IsBodyHtml = false
                    };
                    testMessage.To.Add(username); // إرسال للنفس

                    await client.SendMailAsync(testMessage);
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }
        #endregion

        #region معالجات أحداث الإشعارات
        private void TestSound_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var volume = (int)VolumeSlider.Value;
                PlayTestSound(volume);
                MessageBox.Show($"تم تشغيل الصوت التجريبي بمستوى {volume}%", "اختبار الصوت",
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل الصوت: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PlayTestSound(int volume)
        {
            try
            {
                // تشغيل صوت النظام الافتراضي
                System.Media.SystemSounds.Asterisk.Play();
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تشغيل الصوت: {ex.Message}");
            }
        }
        #endregion

        #region معالجات أحداث المظهر
        private void CustomizeMenuNames_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var customizeWindow = new MenuCustomizationWindow();
                customizeWindow.Owner = Window.GetWindow(this);
                customizeWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة تخصيص القوائم: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ChangeIconSet_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new OpenFileDialog
            {
                Title = "اختيار مجموعة أيقونات",
                Filter = "ملفات الأيقونات (*.ico;*.png)|*.ico;*.png|جميع الملفات (*.*)|*.*",
                Multiselect = true
            };

            if (dialog.ShowDialog() == true)
            {
                try
                {
                    // معالجة تغيير مجموعة الأيقونات
                    MessageBox.Show($"تم اختيار {dialog.FileNames.Length} أيقونة. سيتم تطبيقها عند إعادة تشغيل البرنامج.",
                                   "تم اختيار الأيقونات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحميل الأيقونات: {ex.Message}", "خطأ",
                                   MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void ResetAppearance_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد استعادة إعدادات المظهر الافتراضية؟", "تأكيد الاستعادة",
                                       MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    // استعادة الإعدادات الافتراضية للمظهر
                    LightModeRadio.IsChecked = true;
                    FontSizeComboBox.SelectedIndex = 1; // متوسط
                    FontFamilyComboBox.SelectedIndex = 0; // Segoe UI
                    IconSizeComboBox.SelectedIndex = 1; // متوسط
                    ShowIconLabelsCheckBox.IsChecked = true;
                    AnimatedIconsCheckBox.IsChecked = true;

                    ApplyTheme(false);

                    MessageBox.Show("تم استعادة إعدادات المظهر الافتراضية بنجاح!", "تمت الاستعادة",
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في استعادة إعدادات المظهر: {ex.Message}", "خطأ",
                                   MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
        #endregion

        #region معالجات أحداث قاعدة البيانات
        private void DatabaseType_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (DatabaseTypeComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                var dbType = selectedItem.Content.ToString();
                var isLocal = dbType.Contains("SQLite");

                // تفعيل/تعطيل الحقول حسب نوع قاعدة البيانات
                ServerAddressTextBox.IsEnabled = !isLocal;
                DatabasePortTextBox.IsEnabled = !isLocal;
                DatabaseUsernameTextBox.IsEnabled = !isLocal;
                DatabasePasswordBox.IsEnabled = !isLocal;
                IntegratedSecurityCheckBox.IsEnabled = !isLocal;

                // تعيين القيم الافتراضية
                if (isLocal)
                {
                    ServerAddressTextBox.Text = "localhost";
                    DatabasePortTextBox.Text = "0";
                }
                else
                {
                    UpdateDatabasePortForType(dbType);
                }
            }
        }

        private void UpdateDatabasePortForType(string dbType)
        {
            var defaultPorts = new System.Collections.Generic.Dictionary<string, string>
            {
                { "SQL Server", "1433" },
                { "MySQL", "3306" },
                { "PostgreSQL", "5432" },
                { "Oracle", "1521" },
                { "MongoDB", "27017" }
            };

            foreach (var port in defaultPorts)
            {
                if (dbType.Contains(port.Key))
                {
                    DatabasePortTextBox.Text = port.Value;
                    break;
                }
            }
        }

        private async void TestDatabaseConnection_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                button.IsEnabled = false;
                button.Content = "⏳ جاري الاختبار...";

                var connectionString = BuildConnectionString();
                var result = await TestDatabaseConnectionAsync(connectionString);

                if (result)
                {
                    MessageBox.Show("تم الاتصال بقاعدة البيانات بنجاح!", "نجح الاختبار",
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("فشل في الاتصال بقاعدة البيانات. يرجى التحقق من الإعدادات.", "فشل الاختبار",
                                   MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار قاعدة البيانات: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                var button = sender as Button;
                button.IsEnabled = true;
                button.Content = "🔗 اختبار الاتصال";
            }
        }

        private string BuildConnectionString()
        {
            var dbType = (DatabaseTypeComboBox.SelectedItem as ComboBoxItem)?.Content.ToString();

            if (dbType.Contains("SQLite"))
            {
                return $"Data Source={DatabaseNameTextBox.Text}.db;Version=3;";
            }
            else if (dbType.Contains("SQL Server"))
            {
                var builder = new SqlConnectionStringBuilder
                {
                    DataSource = $"{ServerAddressTextBox.Text},{DatabasePortTextBox.Text}",
                    InitialCatalog = DatabaseNameTextBox.Text,
                    ConnectTimeout = int.Parse(ConnectionTimeoutTextBox.Text)
                };

                if (IntegratedSecurityCheckBox.IsChecked == true)
                {
                    builder.IntegratedSecurity = true;
                }
                else
                {
                    builder.UserID = DatabaseUsernameTextBox.Text;
                    builder.Password = DatabasePasswordBox.Password;
                }

                return builder.ConnectionString;
            }

            // يمكن إضافة المزيد من أنواع قواعد البيانات هنا
            return "";
        }

        private async Task<bool> TestDatabaseConnectionAsync(string connectionString)
        {
            try
            {
                if (connectionString.Contains("SQLite"))
                {
                    // اختبار SQLite
                    using (var connection = new System.Data.SQLite.SQLiteConnection(connectionString))
                    {
                        await connection.OpenAsync();
                        return connection.State == System.Data.ConnectionState.Open;
                    }
                }
                else
                {
                    // اختبار SQL Server
                    using (var connection = new SqlConnection(connectionString))
                    {
                        await connection.OpenAsync();
                        return connection.State == System.Data.ConnectionState.Open;
                    }
                }
            }
            catch
            {
                return false;
            }
        }
        #endregion

        #region معالجات أحداث الصيانة والفحص
        private async void RunSystemDiagnostics_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                button.IsEnabled = false;
                button.Content = "⏳ جاري الفحص...";

                var diagnostics = await RunSystemDiagnosticsAsync();
                ShowDiagnosticsResults(diagnostics);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فحص النظام: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                var button = sender as Button;
                button.IsEnabled = true;
                button.Content = "🔍 فحص شامل للنظام";
            }
        }

        private async void CheckDatabaseIntegrity_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                button.IsEnabled = false;
                button.Content = "⏳ جاري فحص قاعدة البيانات...";

                var result = await CheckDatabaseIntegrityAsync();
                MessageBox.Show(result ? "قاعدة البيانات سليمة ولا توجد أخطاء." : "تم العثور على أخطاء في قاعدة البيانات.",
                               "نتيجة فحص قاعدة البيانات",
                               MessageBoxButton.OK,
                               result ? MessageBoxImage.Information : MessageBoxImage.Warning);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فحص قاعدة البيانات: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                var button = sender as Button;
                button.IsEnabled = true;
                button.Content = "🗄️ فحص قاعدة البيانات";
            }
        }

        private async void CheckMissingFiles_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                button.IsEnabled = false;
                button.Content = "⏳ جاري فحص الملفات...";

                var missingFiles = await CheckMissingFilesAsync();
                if (missingFiles.Count == 0)
                {
                    MessageBox.Show("جميع الملفات موجودة ولا توجد ملفات مفقودة.", "فحص الملفات",
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    var message = $"تم العثور على {missingFiles.Count} ملف مفقود:\n\n" +
                                 string.Join("\n", missingFiles.Take(10));
                    if (missingFiles.Count > 10)
                        message += $"\n... و {missingFiles.Count - 10} ملف آخر";

                    MessageBox.Show(message, "ملفات مفقودة",
                                   MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فحص الملفات: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                var button = sender as Button;
                button.IsEnabled = true;
                button.Content = "📁 فحص الملفات المفقودة";
            }
        }

        private async void CleanTempFiles_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                button.IsEnabled = false;
                button.Content = "⏳ جاري التنظيف...";

                var cleanedSize = await CleanTempFilesAsync();
                MessageBox.Show($"تم تنظيف {cleanedSize:F2} ميجابايت من الملفات المؤقتة.", "تنظيف مكتمل",
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تنظيف الملفات المؤقتة: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                var button = sender as Button;
                button.IsEnabled = true;
                button.Content = "🧹 تنظيف الملفات المؤقتة";
            }
        }

        private async void AnalyzePerformance_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                button.IsEnabled = false;
                button.Content = "⏳ جاري التحليل...";

                var performance = await AnalyzePerformanceAsync();
                ShowPerformanceResults(performance);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحليل الأداء: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                var button = sender as Button;
                button.IsEnabled = true;
                button.Content = "📊 تحليل الأداء";
            }
        }

        private async void GenerateSystemReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                button.IsEnabled = false;
                button.Content = "⏳ جاري إنشاء التقرير...";

                var reportPath = await GenerateSystemReportAsync();
                var result = MessageBox.Show($"تم إنشاء تقرير حالة النظام بنجاح!\n\nمسار الملف: {reportPath}\n\nهل تريد فتح التقرير؟",
                                           "تم إنشاء التقرير",
                                           MessageBoxButton.YesNo,
                                           MessageBoxImage.Information);

                if (result == MessageBoxResult.Yes)
                {
                    Process.Start(new ProcessStartInfo(reportPath) { UseShellExecute = true });
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                var button = sender as Button;
                button.IsEnabled = true;
                button.Content = "📋 تقرير حالة النظام";
            }
        }

        private void ViewLogs_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var logsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
                if (Directory.Exists(logsPath))
                {
                    Process.Start(new ProcessStartInfo(logsPath) { UseShellExecute = true });
                }
                else
                {
                    MessageBox.Show("مجلد السجلات غير موجود.", "السجلات",
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح مجلد السجلات: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        #endregion

        #region الوظائف المساعدة
        private async Task SaveAllSettingsAsync()
        {
            var settings = _settingsService.Settings;

            // حفظ إعدادات المكتب
            settings.OfficeName = OfficeNameTextBox.Text;
            settings.LicenseNumber = LicenseNumberTextBox.Text;
            settings.MainPhone = MainPhoneTextBox.Text;
            settings.OfficeAddress = OfficeAddressTextBox.Text;
            settings.OfficialEmail = OfficialEmailTextBox.Text;
            settings.Website = WebsiteTextBox.Text;

            // حفظ إعدادات البريد الإلكتروني
            settings.SmtpServer = SmtpServerTextBox.Text;
            settings.SmtpPort = int.Parse(SmtpPortTextBox.Text);
            settings.SmtpUsername = EmailUsernameTextBox.Text;
            settings.SmtpEnableSsl = EnableSslCheckBox.IsChecked ?? false;

            // حفظ إعدادات الإشعارات
            settings.EnableSoundNotifications = EnableSoundNotificationsCheckBox.IsChecked ?? false;
            settings.AppointmentSoundEnabled = AppointmentSoundCheckBox.IsChecked ?? false;
            settings.EmailSoundEnabled = EmailSoundCheckBox.IsChecked ?? false;
            settings.SystemSoundEnabled = SystemSoundCheckBox.IsChecked ?? false;
            settings.NotificationVolume = (int)VolumeSlider.Value;
            settings.EnableVisualNotifications = EnableVisualNotificationsCheckBox.IsChecked ?? false;
            settings.PopupNotificationsEnabled = PopupNotificationsCheckBox.IsChecked ?? false;
            settings.TaskbarNotificationsEnabled = TaskbarNotificationsCheckBox.IsChecked ?? false;
            settings.FlashingNotificationsEnabled = FlashingNotificationsCheckBox.IsChecked ?? false;
            settings.NotificationDuration = int.Parse(NotificationDurationTextBox.Text);

            // حفظ إعدادات المظهر
            settings.IsDarkMode = DarkModeRadio.IsChecked ?? false;
            settings.IsAutoMode = AutoModeRadio.IsChecked ?? false;

            // حفظ إعدادات قاعدة البيانات
            settings.DatabaseType = GetSelectedDatabaseType();
            settings.DatabaseName = DatabaseNameTextBox.Text;
            settings.ServerAddress = ServerAddressTextBox.Text;
            settings.DatabasePort = int.Parse(DatabasePortTextBox.Text);
            settings.DatabaseUsername = DatabaseUsernameTextBox.Text;
            settings.UseIntegratedSecurity = IntegratedSecurityCheckBox.IsChecked ?? false;
            settings.ConnectionTimeout = int.Parse(ConnectionTimeoutTextBox.Text);
            settings.MaxConnections = int.Parse(MaxConnectionsTextBox.Text);
            settings.EnableConnectionPooling = EnableConnectionPoolingCheckBox.IsChecked ?? false;
            settings.EnableQueryCaching = EnableQueryCachingCheckBox.IsChecked ?? false;
            settings.AutoBackup = EnableAutoBackupCheckBox.IsChecked ?? false;

            // حفظ إعدادات اللغة
            settings.Language = GetSelectedLanguage();
            settings.IsRightToLeft = TextDirectionComboBox.SelectedIndex == 0;
            settings.Timezone = GetSelectedTimezone();
            settings.DateFormat = GetSelectedDateFormat();
            settings.Currency = GetSelectedCurrency();
            settings.CurrencySymbol = CurrencySymbolTextBox.Text;
            settings.CurrencyPosition = CurrencyPositionComboBox.SelectedIndex == 0 ? "Before" : "After";
            settings.DecimalPlaces = DecimalPlacesComboBox.SelectedIndex;

            // حفظ إعدادات الصيانة
            settings.EnableLogging = EnableLoggingCheckBox.IsChecked ?? false;
            settings.LogErrors = LogErrorsCheckBox.IsChecked ?? false;
            settings.LogWarnings = LogWarningsCheckBox.IsChecked ?? false;
            settings.LogInfo = LogInfoCheckBox.IsChecked ?? false;
            settings.LogLevel = GetSelectedLogLevel();

            await _settingsService.SaveSettingsAsync();
        }

        private async Task<System.Collections.Generic.Dictionary<string, object>> RunConnectionTestsAsync()
        {
            var results = new System.Collections.Generic.Dictionary<string, object>();

            // اختبار قاعدة البيانات
            try
            {
                var dbConnectionString = BuildConnectionString();
                results["Database"] = await TestDatabaseConnectionAsync(dbConnectionString);
            }
            catch (Exception ex)
            {
                results["Database"] = $"خطأ: {ex.Message}";
            }

            // اختبار البريد الإلكتروني
            try
            {
                var emailResult = await TestEmailConnectionAsync(
                    SmtpServerTextBox.Text,
                    int.Parse(SmtpPortTextBox.Text),
                    EmailUsernameTextBox.Text,
                    EmailPasswordBox.Password,
                    EnableSslCheckBox.IsChecked ?? false
                );
                results["Email"] = emailResult;
            }
            catch (Exception ex)
            {
                results["Email"] = $"خطأ: {ex.Message}";
            }

            // اختبار الإنترنت
            try
            {
                using (var client = new System.Net.WebClient())
                {
                    await client.DownloadStringTaskAsync("https://www.google.com");
                    results["Internet"] = true;
                }
            }
            catch
            {
                results["Internet"] = false;
            }

            return results;
        }

        private void ShowConnectionTestResults(System.Collections.Generic.Dictionary<string, object> results)
        {
            var message = "نتائج اختبار الاتصالات:\n\n";

            foreach (var result in results)
            {
                var status = result.Value.ToString() == "True" ? "✅ نجح" :
                           result.Value.ToString() == "False" ? "❌ فشل" :
                           $"❌ {result.Value}";
                message += $"{GetConnectionTestName(result.Key)}: {status}\n";
            }

            MessageBox.Show(message, "نتائج اختبار الاتصالات",
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private string GetConnectionTestName(string key)
        {
            return key switch
            {
                "Database" => "قاعدة البيانات",
                "Email" => "البريد الإلكتروني",
                "Internet" => "الإنترنت",
                _ => key
            };
        }

        // الوظائف المساعدة للحصول على القيم المحددة
        private string GetSelectedDatabaseType()
        {
            return (DatabaseTypeComboBox.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "SQLite";
        }

        private string GetSelectedLanguage()
        {
            return (InterfaceLanguageComboBox.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "العربية";
        }

        private string GetSelectedTimezone()
        {
            return (TimezoneComboBox.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "(GMT+01:00) المغرب";
        }

        private string GetSelectedDateFormat()
        {
            return (DateFormatComboBox.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "dd/MM/yyyy";
        }

        private string GetSelectedCurrency()
        {
            return (CurrencyComboBox.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "درهم مغربي (MAD)";
        }

        private string GetSelectedLogLevel()
        {
            return (LogLevelComboBox.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "متوسط";
        }

        // الوظائف المساعدة للحصول على الفهارس
        private int GetFontSizeIndex(int fontSize)
        {
            return fontSize switch
            {
                12 => 0,
                14 => 1,
                16 => 2,
                18 => 3,
                _ => 1
            };
        }

        private int GetFontFamilyIndex(string fontFamily)
        {
            return fontFamily switch
            {
                "Segoe UI" => 0,
                "Arial" => 1,
                "Tahoma" => 2,
                "Calibri" => 3,
                "Times New Roman" => 4,
                _ => 0
            };
        }

        private int GetIconSizeIndex(int iconSize)
        {
            return iconSize switch
            {
                16 => 0,
                24 => 1,
                32 => 2,
                48 => 3,
                _ => 1
            };
        }

        private int GetDatabaseTypeIndex(string dbType)
        {
            return dbType switch
            {
                "SQLite" => 0,
                "SQL Server" => 1,
                "MySQL" => 2,
                "PostgreSQL" => 3,
                "Oracle" => 4,
                "MongoDB" => 5,
                _ => 0
            };
        }

        private int GetLanguageIndex(string language)
        {
            return language switch
            {
                "العربية" => 0,
                "English" => 1,
                "Français" => 2,
                "Español" => 3,
                _ => 0
            };
        }

        private int GetTimezoneIndex(string timezone)
        {
            return timezone switch
            {
                "(GMT+01:00) المغرب" => 0,
                "(GMT+02:00) مصر" => 1,
                "(GMT+03:00) السعودية" => 2,
                "(GMT+04:00) الإمارات" => 3,
                _ => 0
            };
        }

        private int GetDateFormatIndex(string dateFormat)
        {
            return dateFormat switch
            {
                "dd/MM/yyyy" => 0,
                "MM/dd/yyyy" => 1,
                "yyyy-MM-dd" => 2,
                "dd-MM-yyyy" => 3,
                _ => 0
            };
        }

        private int GetCurrencyIndex(string currency)
        {
            return currency switch
            {
                "درهم مغربي (MAD)" => 0,
                "دولار أمريكي (USD)" => 1,
                "يورو (EUR)" => 2,
                "ريال سعودي (SAR)" => 3,
                "جنيه مصري (EGP)" => 4,
                _ => 0
            };
        }

        private int GetLogLevelIndex(string logLevel)
        {
            return logLevel switch
            {
                "أساسي" => 0,
                "متوسط" => 1,
                "مفصل" => 2,
                "تشخيصي" => 3,
                _ => 1
            };
        }

        // وظائف الصيانة والفحص
        private async Task<System.Collections.Generic.Dictionary<string, object>> RunSystemDiagnosticsAsync()
        {
            var diagnostics = new System.Collections.Generic.Dictionary<string, object>();

            // فحص الذاكرة
            var memoryUsage = GC.GetTotalMemory(false) / (1024 * 1024);
            diagnostics["Memory"] = $"{memoryUsage} MB";

            // فحص المعالج
            using (var process = Process.GetCurrentProcess())
            {
                diagnostics["CPU"] = $"{process.TotalProcessorTime.TotalMilliseconds} ms";
            }

            // فحص مساحة القرص
            var driveInfo = new DriveInfo(Path.GetPathRoot(AppDomain.CurrentDomain.BaseDirectory));
            var freeSpace = driveInfo.AvailableFreeSpace / (1024 * 1024 * 1024);
            diagnostics["DiskSpace"] = $"{freeSpace} GB متاح";

            return diagnostics;
        }

        private void ShowDiagnosticsResults(System.Collections.Generic.Dictionary<string, object> diagnostics)
        {
            var message = "نتائج فحص النظام:\n\n";
            message += $"استخدام الذاكرة: {diagnostics["Memory"]}\n";
            message += $"استخدام المعالج: {diagnostics["CPU"]}\n";
            message += $"مساحة القرص: {diagnostics["DiskSpace"]}\n";

            MessageBox.Show(message, "تشخيص النظام",
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private async Task<bool> CheckDatabaseIntegrityAsync()
        {
            try
            {
                // محاكاة فحص قاعدة البيانات
                await Task.Delay(2000);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private async Task<System.Collections.Generic.List<string>> CheckMissingFilesAsync()
        {
            var missingFiles = new System.Collections.Generic.List<string>();

            // قائمة الملفات المطلوبة
            var requiredFiles = new[]
            {
                "AvocatPro.exe",
                "AvocatPro.dll",
                "appsettings.json"
            };

            await Task.Run(() =>
            {
                foreach (var file in requiredFiles)
                {
                    var filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, file);
                    if (!File.Exists(filePath))
                    {
                        missingFiles.Add(file);
                    }
                }
            });

            return missingFiles;
        }

        private async Task<double> CleanTempFilesAsync()
        {
            double cleanedSize = 0;

            await Task.Run(() =>
            {
                var tempPaths = new[]
                {
                    Path.GetTempPath(),
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Temp"),
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs", "Old")
                };

                foreach (var tempPath in tempPaths)
                {
                    if (Directory.Exists(tempPath))
                    {
                        var files = Directory.GetFiles(tempPath, "*.*", SearchOption.AllDirectories);
                        foreach (var file in files)
                        {
                            try
                            {
                                var fileInfo = new FileInfo(file);
                                if (fileInfo.CreationTime < DateTime.Now.AddDays(-7))
                                {
                                    cleanedSize += fileInfo.Length / (1024.0 * 1024.0);
                                    File.Delete(file);
                                }
                            }
                            catch
                            {
                                // تجاهل الأخطاء في حذف الملفات
                            }
                        }
                    }
                }
            });

            return cleanedSize;
        }

        private async Task<System.Collections.Generic.Dictionary<string, object>> AnalyzePerformanceAsync()
        {
            var performance = new System.Collections.Generic.Dictionary<string, object>();

            await Task.Run(() =>
            {
                using (var process = Process.GetCurrentProcess())
                {
                    performance["WorkingSet"] = $"{process.WorkingSet64 / (1024 * 1024)} MB";
                    performance["Threads"] = process.Threads.Count;
                    performance["Handles"] = process.HandleCount;
                    performance["StartTime"] = process.StartTime.ToString("yyyy-MM-dd HH:mm:ss");
                }
            });

            return performance;
        }

        private void ShowPerformanceResults(System.Collections.Generic.Dictionary<string, object> performance)
        {
            var message = "تحليل الأداء:\n\n";
            message += $"الذاكرة المستخدمة: {performance["WorkingSet"]}\n";
            message += $"عدد الخيوط: {performance["Threads"]}\n";
            message += $"عدد المقابض: {performance["Handles"]}\n";
            message += $"وقت البدء: {performance["StartTime"]}\n";

            MessageBox.Show(message, "تحليل الأداء",
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private async Task<string> GenerateSystemReportAsync()
        {
            var reportPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop),
                                        $"AvocatPro_SystemReport_{DateTime.Now:yyyyMMdd_HHmmss}.txt");

            await Task.Run(() =>
            {
                var report = new System.Text.StringBuilder();
                report.AppendLine("تقرير حالة نظام AvocatPro");
                report.AppendLine("=" + new string('=', 50));
                report.AppendLine($"تاريخ التقرير: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                report.AppendLine();

                // معلومات النظام
                report.AppendLine("معلومات النظام:");
                report.AppendLine($"نظام التشغيل: {Environment.OSVersion}");
                report.AppendLine($"إصدار .NET: {Environment.Version}");
                report.AppendLine($"اسم الجهاز: {Environment.MachineName}");
                report.AppendLine($"اسم المستخدم: {Environment.UserName}");
                report.AppendLine();

                // معلومات التطبيق
                using (var process = Process.GetCurrentProcess())
                {
                    report.AppendLine("معلومات التطبيق:");
                    report.AppendLine($"الذاكرة المستخدمة: {process.WorkingSet64 / (1024 * 1024)} MB");
                    report.AppendLine($"عدد الخيوط: {process.Threads.Count}");
                    report.AppendLine($"وقت البدء: {process.StartTime:yyyy-MM-dd HH:mm:ss}");
                    report.AppendLine($"مدة التشغيل: {DateTime.Now - process.StartTime}");
                }

                File.WriteAllText(reportPath, report.ToString(), System.Text.Encoding.UTF8);
            });

            return reportPath;
        }
        #endregion

        #region فئة نافذة تخصيص القوائم (مؤقتة)
        public class MenuCustomizationWindow : Window
        {
            public MenuCustomizationWindow()
            {
                Title = "تخصيص أسماء القوائم";
                Width = 600;
                Height = 400;
                WindowStartupLocation = WindowStartupLocation.CenterOwner;

                Content = new TextBlock
                {
                    Text = "نافذة تخصيص أسماء القوائم - قيد التطوير",
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center,
                    FontSize = 16
                };
            }
        }
        #endregion
    }
}
