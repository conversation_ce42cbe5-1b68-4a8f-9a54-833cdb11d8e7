<Page x:Class="AvocatPro.Views.Pages.ModernDashboardPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="لوحة التحكم الحديثة" FlowDirection="RightToLeft"
      Background="#F8FAFC">

    <Page.Resources>
        <!-- أنماط البطاقات الحديثة -->
        <Style x:Key="ModernCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="20"/>
            <Setter Property="Padding" Value="25"/>
            <Setter Property="Margin" Value="15"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Opacity="0.08" BlurRadius="20" ShadowDepth="5"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- أنماط الأيقونات -->
        <Style x:Key="IconContainerStyle" TargetType="Border">
            <Setter Property="Width" Value="50"/>
            <Setter Property="Height" Value="50"/>
            <Setter Property="CornerRadius" Value="15"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- أنماط النصوص -->
        <Style x:Key="CardTitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#1F2937"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>

        <Style x:Key="CardValueStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="32"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#111827"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>

        <Style x:Key="CardSubtitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#6B7280"/>
        </Style>

        <!-- أنماط الأزرار الحديثة -->
        <Style x:Key="ModernActionButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#6366F1"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#5B5FE8"/>
                                <Setter TargetName="border" Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.02" ScaleY="1.02"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Page.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العلوي الحديث -->
        <Border Grid.Row="0" Background="White" CornerRadius="0,0,25,25" Padding="30,25">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.05" BlurRadius="15" ShadowDepth="3"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- شعار AvocatPro -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border Background="#6366F1" CornerRadius="12" Width="45" Height="45" Margin="0,0,15,0">
                        <TextBlock Text="⚖️" FontSize="22" Foreground="White" 
                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <StackPanel>
                        <TextBlock Text="AvocatPro" FontSize="24" FontWeight="Bold" Foreground="#1F2937"/>
                        <TextBlock Text="نظام إدارة مكاتب المحاماة" FontSize="12" Foreground="#6B7280"/>
                    </StackPanel>
                </StackPanel>

                <!-- شريط البحث الحديث -->
                <Border Grid.Column="1" Background="#F3F4F6" CornerRadius="15" Margin="40,0" Height="45">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0" Text="🔍" FontSize="16" Foreground="#9CA3AF" 
                                   VerticalAlignment="Center" Margin="15,0,10,0"/>
                        
                        <TextBox Grid.Column="1" Name="GlobalSearchBox" 
                                 Background="Transparent" BorderThickness="0"
                                 Text="البحث في الملفات، الموكلين، الجلسات..."
                                 Foreground="#9CA3AF" FontSize="14"
                                 VerticalAlignment="Center" Padding="5,0"
                                 GotFocus="GlobalSearchBox_GotFocus"
                                 LostFocus="GlobalSearchBox_LostFocus"/>
                        
                        <Button Grid.Column="2" Content="تصفية" Background="#E5E7EB" Foreground="#374151"
                                BorderThickness="0" Padding="15,8" Margin="5"
                                FontSize="12" Cursor="Hand"/>
                    </Grid>
                </Border>

                <!-- أزرار التحديث والإشعارات -->
                <Button Grid.Column="2" Background="#F3F4F6" BorderThickness="0"
                        Width="45" Height="45" Margin="0,0,10,0" Cursor="Hand">
                    <TextBlock Text="🔄" FontSize="18" Foreground="#6B7280"/>
                </Button>

                <!-- ملف المستخدم -->
                <Border Grid.Column="4" Background="#F3F4F6" CornerRadius="12" Padding="12,8">
                    <StackPanel Orientation="Horizontal">
                        <Border Background="#6366F1" CornerRadius="20" Width="35" Height="35" Margin="0,0,12,0">
                            <TextBlock Text="👤" FontSize="16" Foreground="White" 
                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock Text="أحمد محمد" FontSize="14" FontWeight="SemiBold" Foreground="#1F2937"/>
                            <TextBlock Text="مدير النظام" FontSize="11" Foreground="#6B7280"/>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="20,20,20,0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- بطاقات الإحصائيات الرئيسية -->
                <UniformGrid Grid.Row="0" Columns="4" Margin="0,0,0,20">
                    <!-- إجمالي الملفات -->
                    <Border Style="{StaticResource ModernCardStyle}">
                        <StackPanel>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="إجمالي الملفات" Style="{StaticResource CardTitleStyle}"/>
                                    <TextBlock Text="247" Style="{StaticResource CardValueStyle}" Foreground="#059669"/>
                                    <TextBlock Text="من أول أكتوبر" Style="{StaticResource CardSubtitleStyle}"/>
                                </StackPanel>
                                
                                <Border Grid.Column="1" Style="{StaticResource IconContainerStyle}" Background="#ECFDF5">
                                    <TextBlock Text="📁" FontSize="24" Foreground="#059669"/>
                                </Border>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- الموكلين النشطين -->
                    <Border Style="{StaticResource ModernCardStyle}">
                        <StackPanel>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="الموكلين النشطين" Style="{StaticResource CardTitleStyle}"/>
                                    <TextBlock Text="189" Style="{StaticResource CardValueStyle}" Foreground="#3B82F6"/>
                                    <TextBlock Text="زيادة 12% هذا الشهر" Style="{StaticResource CardSubtitleStyle}"/>
                                </StackPanel>
                                
                                <Border Grid.Column="1" Style="{StaticResource IconContainerStyle}" Background="#EFF6FF">
                                    <TextBlock Text="👥" FontSize="24" Foreground="#3B82F6"/>
                                </Border>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- الجلسات القادمة -->
                    <Border Style="{StaticResource ModernCardStyle}">
                        <StackPanel>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="الجلسات القادمة" Style="{StaticResource CardTitleStyle}"/>
                                    <TextBlock Text="23" Style="{StaticResource CardValueStyle}" Foreground="#F59E0B"/>
                                    <TextBlock Text="خلال الأسبوع القادم" Style="{StaticResource CardSubtitleStyle}"/>
                                </StackPanel>
                                
                                <Border Grid.Column="1" Style="{StaticResource IconContainerStyle}" Background="#FFFBEB">
                                    <TextBlock Text="⚖️" FontSize="24" Foreground="#F59E0B"/>
                                </Border>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- الإيرادات الشهرية -->
                    <Border Style="{StaticResource ModernCardStyle}">
                        <StackPanel>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="الإيرادات الشهرية" Style="{StaticResource CardTitleStyle}"/>
                                    <TextBlock Text="85,420" Style="{StaticResource CardValueStyle}" Foreground="#8B5CF6"/>
                                    <TextBlock Text="ريال سعودي" Style="{StaticResource CardSubtitleStyle}"/>
                                </StackPanel>
                                
                                <Border Grid.Column="1" Style="{StaticResource IconContainerStyle}" Background="#F3E8FF">
                                    <TextBlock Text="💰" FontSize="24" Foreground="#8B5CF6"/>
                                </Border>
                            </Grid>
                        </StackPanel>
                    </Border>
                </UniformGrid>

                <!-- قسم الوصول السريع -->
                <Border Grid.Row="1" Style="{StaticResource ModernCardStyle}" Margin="15,0,15,20">
                    <StackPanel>
                        <Grid Margin="0,0,0,20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="الوصول السريع" Style="{StaticResource CardTitleStyle}" FontSize="20"/>
                            <Button Grid.Column="1" Content="+ إضافة جديد" Style="{StaticResource ModernActionButtonStyle}"/>
                        </Grid>

                        <UniformGrid Columns="3" HorizontalAlignment="Stretch">
                            <!-- بطاقة إضافة موكل -->
                            <Border Background="#F8FAFC" CornerRadius="15" Padding="20" Margin="0,0,15,0" Cursor="Hand">
                                <StackPanel HorizontalAlignment="Center">
                                    <Border Background="#6366F1" CornerRadius="50" Width="80" Height="80" Margin="0,0,0,15">
                                        <TextBlock Text="👤" FontSize="35" Foreground="White" 
                                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                    <TextBlock Text="إضافة موكل جديد" FontSize="16" FontWeight="SemiBold" 
                                               Foreground="#1F2937" HorizontalAlignment="Center"/>
                                    <TextBlock Text="إدارة بيانات الموكلين" FontSize="12" Foreground="#6B7280" 
                                               HorizontalAlignment="Center" Margin="0,5,0,0"/>
                                </StackPanel>
                            </Border>

                            <!-- بطاقة إضافة ملف -->
                            <Border Background="#F8FAFC" CornerRadius="15" Padding="20" Margin="7.5,0" Cursor="Hand">
                                <StackPanel HorizontalAlignment="Center">
                                    <Border Background="#059669" CornerRadius="50" Width="80" Height="80" Margin="0,0,0,15">
                                        <TextBlock Text="📁" FontSize="35" Foreground="White" 
                                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                    <TextBlock Text="إضافة ملف جديد" FontSize="16" FontWeight="SemiBold" 
                                               Foreground="#1F2937" HorizontalAlignment="Center"/>
                                    <TextBlock Text="إنشاء قضية جديدة" FontSize="12" Foreground="#6B7280" 
                                               HorizontalAlignment="Center" Margin="0,5,0,0"/>
                                </StackPanel>
                            </Border>

                            <!-- بطاقة جدولة جلسة -->
                            <Border Background="#F8FAFC" CornerRadius="15" Padding="20" Margin="15,0,0,0" Cursor="Hand">
                                <StackPanel HorizontalAlignment="Center">
                                    <Border Background="#F59E0B" CornerRadius="50" Width="80" Height="80" Margin="0,0,0,15">
                                        <TextBlock Text="📅" FontSize="35" Foreground="White" 
                                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                    <TextBlock Text="جدولة جلسة" FontSize="16" FontWeight="SemiBold" 
                                               Foreground="#1F2937" HorizontalAlignment="Center"/>
                                    <TextBlock Text="إضافة موعد محكمة" FontSize="12" Foreground="#6B7280" 
                                               HorizontalAlignment="Center" Margin="0,5,0,0"/>
                                </StackPanel>
                            </Border>
                        </UniformGrid>
                    </StackPanel>
                </Border>

                <!-- قسم الأنشطة والجلسات -->
                <Grid Grid.Row="2" Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- الأنشطة الأخيرة -->
                    <Border Grid.Column="0" Style="{StaticResource ModernCardStyle}" Margin="15,0,7.5,0">
                        <StackPanel>
                            <Grid Margin="0,0,0,20">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0" Text="الأنشطة الأخيرة" Style="{StaticResource CardTitleStyle}" FontSize="18"/>
                                <TextBlock Grid.Column="1" Text="عرض الكل" FontSize="12" Foreground="#6366F1"
                                           Cursor="Hand" VerticalAlignment="Center"/>
                            </Grid>

                            <StackPanel>
                                <!-- نشاط 1 -->
                                <Border Background="#F8FAFC" CornerRadius="12" Padding="15" Margin="0,0,0,10">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <Border Grid.Column="0" Background="#ECFDF5" CornerRadius="8"
                                                Width="35" Height="35" Margin="0,0,12,0">
                                            <TextBlock Text="📁" FontSize="16" Foreground="#059669"
                                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>

                                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                            <TextBlock Text="تم إضافة ملف جديد" FontSize="14" FontWeight="SemiBold"
                                                       Foreground="#1F2937"/>
                                            <TextBlock Text="قضية تجارية - الموكل: أحمد السالم" FontSize="12"
                                                       Foreground="#6B7280" Margin="0,2,0,0"/>
                                        </StackPanel>

                                        <TextBlock Grid.Column="2" Text="منذ 5 دقائق" FontSize="11"
                                                   Foreground="#9CA3AF" VerticalAlignment="Center"/>
                                    </Grid>
                                </Border>

                                <!-- نشاط 2 -->
                                <Border Background="#F8FAFC" CornerRadius="12" Padding="15" Margin="0,0,0,10">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <Border Grid.Column="0" Background="#EFF6FF" CornerRadius="8"
                                                Width="35" Height="35" Margin="0,0,12,0">
                                            <TextBlock Text="👤" FontSize="16" Foreground="#3B82F6"
                                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>

                                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                            <TextBlock Text="موكل جديد مسجل" FontSize="14" FontWeight="SemiBold"
                                                       Foreground="#1F2937"/>
                                            <TextBlock Text="فاطمة محمد - شركة التقنية المتقدمة" FontSize="12"
                                                       Foreground="#6B7280" Margin="0,2,0,0"/>
                                        </StackPanel>

                                        <TextBlock Grid.Column="2" Text="منذ 15 دقيقة" FontSize="11"
                                                   Foreground="#9CA3AF" VerticalAlignment="Center"/>
                                    </Grid>
                                </Border>

                                <!-- نشاط 3 -->
                                <Border Background="#F8FAFC" CornerRadius="12" Padding="15" Margin="0,0,0,10">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <Border Grid.Column="0" Background="#FFFBEB" CornerRadius="8"
                                                Width="35" Height="35" Margin="0,0,12,0">
                                            <TextBlock Text="⚖️" FontSize="16" Foreground="#F59E0B"
                                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>

                                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                            <TextBlock Text="جلسة محكمة مجدولة" FontSize="14" FontWeight="SemiBold"
                                                       Foreground="#1F2937"/>
                                            <TextBlock Text="محكمة الرياض - قضية رقم 2024/123" FontSize="12"
                                                       Foreground="#6B7280" Margin="0,2,0,0"/>
                                        </StackPanel>

                                        <TextBlock Grid.Column="2" Text="منذ 30 دقيقة" FontSize="11"
                                                   Foreground="#9CA3AF" VerticalAlignment="Center"/>
                                    </Grid>
                                </Border>

                                <!-- نشاط 4 -->
                                <Border Background="#F8FAFC" CornerRadius="12" Padding="15">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <Border Grid.Column="0" Background="#F3E8FF" CornerRadius="8"
                                                Width="35" Height="35" Margin="0,0,12,0">
                                            <TextBlock Text="💰" FontSize="16" Foreground="#8B5CF6"
                                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>

                                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                            <TextBlock Text="دفعة مالية مستلمة" FontSize="14" FontWeight="SemiBold"
                                                       Foreground="#1F2937"/>
                                            <TextBlock Text="15,000 ريال - أتعاب قضية عقارية" FontSize="12"
                                                       Foreground="#6B7280" Margin="0,2,0,0"/>
                                        </StackPanel>

                                        <TextBlock Grid.Column="2" Text="منذ ساعة" FontSize="11"
                                                   Foreground="#9CA3AF" VerticalAlignment="Center"/>
                                    </Grid>
                                </Border>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- الجلسات القادمة -->
                    <Border Grid.Column="1" Style="{StaticResource ModernCardStyle}" Margin="7.5,0,15,0">
                        <StackPanel>
                            <Grid Margin="0,0,0,20">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0" Text="الجلسات القادمة" Style="{StaticResource CardTitleStyle}" FontSize="18"/>
                                <TextBlock Grid.Column="1" Text="عرض التقويم" FontSize="12" Foreground="#6366F1"
                                           Cursor="Hand" VerticalAlignment="Center"/>
                            </Grid>

                            <StackPanel>
                                <!-- جلسة 1 -->
                                <Border Background="#FEF2F2" CornerRadius="12" Padding="15" Margin="0,0,0,12"
                                        BorderBrush="#FCA5A5" BorderThickness="1">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0" Margin="0,0,15,0" HorizontalAlignment="Center">
                                            <TextBlock Text="15" FontSize="20" FontWeight="Bold"
                                                       Foreground="#DC2626" HorizontalAlignment="Center"/>
                                            <TextBlock Text="ديسمبر" FontSize="10" Foreground="#DC2626"
                                                       HorizontalAlignment="Center"/>
                                        </StackPanel>

                                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                            <TextBlock Text="محكمة الاستئناف - الرياض" FontSize="14" FontWeight="SemiBold"
                                                       Foreground="#1F2937"/>
                                            <TextBlock Text="قضية عقارية - الموكل: شركة البناء المتطور" FontSize="12"
                                                       Foreground="#6B7280" Margin="0,2,0,0"/>
                                            <TextBlock Text="الساعة 10:00 صباحاً" FontSize="11"
                                                       Foreground="#DC2626" Margin="0,2,0,0"/>
                                        </StackPanel>

                                        <Border Grid.Column="2" Background="#DC2626" CornerRadius="6"
                                                Padding="8,4" VerticalAlignment="Top">
                                            <TextBlock Text="عاجل" FontSize="10" Foreground="White" FontWeight="Bold"/>
                                        </Border>
                                    </Grid>
                                </Border>

                                <!-- جلسة 2 -->
                                <Border Background="#FEF3C7" CornerRadius="12" Padding="15" Margin="0,0,0,12"
                                        BorderBrush="#FCD34D" BorderThickness="1">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0" Margin="0,0,15,0" HorizontalAlignment="Center">
                                            <TextBlock Text="18" FontSize="20" FontWeight="Bold"
                                                       Foreground="#D97706" HorizontalAlignment="Center"/>
                                            <TextBlock Text="ديسمبر" FontSize="10" Foreground="#D97706"
                                                       HorizontalAlignment="Center"/>
                                        </StackPanel>

                                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                            <TextBlock Text="المحكمة التجارية - جدة" FontSize="14" FontWeight="SemiBold"
                                                       Foreground="#1F2937"/>
                                            <TextBlock Text="نزاع تجاري - الموكل: محمد العتيبي" FontSize="12"
                                                       Foreground="#6B7280" Margin="0,2,0,0"/>
                                            <TextBlock Text="الساعة 2:00 مساءً" FontSize="11"
                                                       Foreground="#D97706" Margin="0,2,0,0"/>
                                        </StackPanel>

                                        <Border Grid.Column="2" Background="#D97706" CornerRadius="6"
                                                Padding="8,4" VerticalAlignment="Top">
                                            <TextBlock Text="مهم" FontSize="10" Foreground="White" FontWeight="Bold"/>
                                        </Border>
                                    </Grid>
                                </Border>

                                <!-- جلسة 3 -->
                                <Border Background="#ECFDF5" CornerRadius="12" Padding="15"
                                        BorderBrush="#86EFAC" BorderThickness="1">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0" Margin="0,0,15,0" HorizontalAlignment="Center">
                                            <TextBlock Text="22" FontSize="20" FontWeight="Bold"
                                                       Foreground="#059669" HorizontalAlignment="Center"/>
                                            <TextBlock Text="ديسمبر" FontSize="10" Foreground="#059669"
                                                       HorizontalAlignment="Center"/>
                                        </StackPanel>

                                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                            <TextBlock Text="محكمة الأحوال الشخصية - الدمام" FontSize="14" FontWeight="SemiBold"
                                                       Foreground="#1F2937"/>
                                            <TextBlock Text="قضية أحوال شخصية - الموكلة: نورا أحمد" FontSize="12"
                                                       Foreground="#6B7280" Margin="0,2,0,0"/>
                                            <TextBlock Text="الساعة 11:30 صباحاً" FontSize="11"
                                                       Foreground="#059669" Margin="0,2,0,0"/>
                                        </StackPanel>

                                        <Border Grid.Column="2" Background="#059669" CornerRadius="6"
                                                Padding="8,4" VerticalAlignment="Top">
                                            <TextBlock Text="عادي" FontSize="10" Foreground="White" FontWeight="Bold"/>
                                        </Border>
                                    </Grid>
                                </Border>
                            </StackPanel>
                        </StackPanel>
                    </Border>
                </Grid>

                <!-- قسم التقارير السريعة -->
                <Border Grid.Row="3" Style="{StaticResource ModernCardStyle}" Margin="15,0,15,20">
                    <StackPanel>
                        <TextBlock Text="التقارير السريعة" Style="{StaticResource CardTitleStyle}" FontSize="20" Margin="0,0,0,20"/>

                        <UniformGrid Columns="4">
                            <!-- تقرير الملفات الشهرية -->
                            <Border Padding="20" Margin="0,0,10,0" Cursor="Hand">
                                <Border.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#6366F1" Offset="0"/>
                                        <GradientStop Color="#8B5CF6" Offset="1"/>
                                    </LinearGradientBrush>
                                </Border.Background>
                                <StackPanel>
                                    <TextBlock Text="📊" FontSize="30" Foreground="White"
                                               HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                    <TextBlock Text="تقرير الملفات" FontSize="14" FontWeight="SemiBold"
                                               Foreground="White" HorizontalAlignment="Center"/>
                                    <TextBlock Text="الشهرية" FontSize="12" Foreground="#E0E7FF"
                                               HorizontalAlignment="Center" Margin="0,2,0,0"/>
                                </StackPanel>
                            </Border>

                            <!-- تقرير الإيرادات -->
                            <Border Padding="20" Margin="5,0" Cursor="Hand">
                                <Border.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#059669" Offset="0"/>
                                        <GradientStop Color="#10B981" Offset="1"/>
                                    </LinearGradientBrush>
                                </Border.Background>
                                <StackPanel>
                                    <TextBlock Text="💹" FontSize="30" Foreground="White"
                                               HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                    <TextBlock Text="تقرير الإيرادات" FontSize="14" FontWeight="SemiBold"
                                               Foreground="White" HorizontalAlignment="Center"/>
                                    <TextBlock Text="والمصروفات" FontSize="12" Foreground="#D1FAE5"
                                               HorizontalAlignment="Center" Margin="0,2,0,0"/>
                                </StackPanel>
                            </Border>

                            <!-- تقرير الموكلين -->
                            <Border Padding="20" Margin="5,0" Cursor="Hand">
                                <Border.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#F59E0B" Offset="0"/>
                                        <GradientStop Color="#F97316" Offset="1"/>
                                    </LinearGradientBrush>
                                </Border.Background>
                                <StackPanel>
                                    <TextBlock Text="👥" FontSize="30" Foreground="White"
                                               HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                    <TextBlock Text="تقرير الموكلين" FontSize="14" FontWeight="SemiBold"
                                               Foreground="White" HorizontalAlignment="Center"/>
                                    <TextBlock Text="والأنشطة" FontSize="12" Foreground="#FEF3C7"
                                               HorizontalAlignment="Center" Margin="0,2,0,0"/>
                                </StackPanel>
                            </Border>

                            <!-- تقرير الأداء -->
                            <Border Padding="20" Margin="10,0,0,0" Cursor="Hand">
                                <Border.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#DC2626" Offset="0"/>
                                        <GradientStop Color="#EF4444" Offset="1"/>
                                    </LinearGradientBrush>
                                </Border.Background>
                                <StackPanel>
                                    <TextBlock Text="📈" FontSize="30" Foreground="White"
                                               HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                    <TextBlock Text="تقرير الأداء" FontSize="14" FontWeight="SemiBold"
                                               Foreground="White" HorizontalAlignment="Center"/>
                                    <TextBlock Text="العام" FontSize="12" Foreground="#FEE2E2"
                                               HorizontalAlignment="Center" Margin="0,2,0,0"/>
                                </StackPanel>
                            </Border>
                        </UniformGrid>
                    </StackPanel>
                </Border>
            </Grid>
        </ScrollViewer>
    </Grid>
</Page>
