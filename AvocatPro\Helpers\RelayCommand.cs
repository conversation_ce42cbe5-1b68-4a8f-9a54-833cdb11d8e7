using System.Windows.Input;

namespace AvocatPro.Helpers;

/// <summary>
/// أمر بسيط لتنفيذ العمليات
/// </summary>
public class RelayCommand : ICommand
{
    private readonly Func<Task>? _executeAsync;
    private readonly Func<bool>? _canExecute;
    private readonly Action? _execute;

    public RelayCommand(Func<Task> executeAsync, Func<bool>? canExecute = null)
    {
        _executeAsync = executeAsync;
        _canExecute = canExecute;
    }

    public RelayCommand(Action execute, Func<bool>? canExecute = null)
    {
        _execute = execute;
        _canExecute = canExecute;
    }

    public event EventHandler? CanExecuteChanged
    {
        add => CommandManager.RequerySuggested += value;
        remove => CommandManager.RequerySuggested -= value;
    }

    public bool CanExecute(object? parameter)
    {
        return _canExecute?.Invoke() ?? true;
    }

    public async void Execute(object? parameter)
    {
        if (_executeAsync != null)
        {
            await _executeAsync();
        }
        else
        {
            _execute?.Invoke();
        }
    }
}

/// <summary>
/// أمر مع معامل
/// </summary>
public class RelayCommand<T> : ICommand
{
    private readonly Func<T, Task>? _executeAsync;
    private readonly Func<T, bool>? _canExecute;
    private readonly Action<T>? _execute;

    public RelayCommand(Func<T, Task> executeAsync, Func<T, bool>? canExecute = null)
    {
        _executeAsync = executeAsync;
        _canExecute = canExecute;
    }

    public RelayCommand(Action<T> execute, Func<T, bool>? canExecute = null)
    {
        _execute = execute;
        _canExecute = canExecute;
    }

    public event EventHandler? CanExecuteChanged
    {
        add => CommandManager.RequerySuggested += value;
        remove => CommandManager.RequerySuggested -= value;
    }

    public bool CanExecute(object? parameter)
    {
        if (parameter is T typedParameter)
        {
            return _canExecute?.Invoke(typedParameter) ?? true;
        }
        return false;
    }

    public async void Execute(object? parameter)
    {
        if (parameter is T typedParameter)
        {
            if (_executeAsync != null)
            {
                await _executeAsync(typedParameter);
            }
            else
            {
                _execute?.Invoke(typedParameter);
            }
        }
    }
}
