using System;
using System.Windows;

namespace AvocatPro.Views.Windows;

public partial class UserPermissionsWindow : Window
{
    private readonly int _userId;

    public UserPermissionsWindow(int userId)
    {
        InitializeComponent();
        _userId = userId;
        LoadUserPermissions();
    }

    private void LoadUserPermissions()
    {
        UserNameText.Text = $"إدارة صلاحيات المستخدم رقم: {_userId}";
        
        // تحميل الصلاحيات (مبسط)
        MessageBox.Show($"تحميل صلاحيات المستخدم رقم: {_userId}", "معلومات", 
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void GrantPermissionButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("تم منح الصلاحية بنجاح!", "نجح", 
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void RevokePermissionButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("تم إلغاء الصلاحية بنجاح!", "نجح", 
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void AddToGroupButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("تم إضافة المستخدم لمجموعة الصلاحيات بنجاح!", "نجح", 
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void RemoveFromGroupButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("تم إزالة المستخدم من مجموعة الصلاحيات بنجاح!", "نجح", 
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }
}
