<Window x:Class="AvocatPro.Views.Windows.AddCaseWindowNew"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة ملف جديد" 
        Height="700" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">

    <Window.Resources>
        <!-- أنماط النموذج -->
        <Style x:Key="FormLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#333"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>

        <Style x:Key="FormTextBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Style.Triggers>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="BorderBrush" Value="#1976D2"/>
                    <Setter Property="BorderThickness" Value="2"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="FormComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1976D2"/>
            <Setter Property="Margin" Value="0,20,0,15"/>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#1976D2"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1565C0"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#F5F5F5"/>
            <Setter Property="Foreground" Value="#333"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#E0E0E0"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- رأس النافذة -->
        <Border Grid.Row="0" Background="#1976D2" Padding="20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📁" FontSize="24" Foreground="White" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="إضافة ملف قضية جديد" FontSize="20" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="أدخل بيانات الملف والقضية بعناية" FontSize="12" Foreground="#E3F2FD"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- محتوى النموذج -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="30">
            <StackPanel>
                <!-- المعلومات الأساسية -->
                <TextBlock Text="📋 المعلومات الأساسية" Style="{StaticResource SectionHeaderStyle}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="مرجع المكتب *" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="OfficeReferenceTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="رقم مرجع داخلي للمكتب"/>

                        <TextBlock Text="عنوان القضية *" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="TitleTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="عنوان مختصر للقضية"/>

                        <TextBlock Text="نوع القضية *" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="CaseTypeComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="مدنية" Tag="Civil" IsSelected="True"/>
                            <ComboBoxItem Content="جنائية" Tag="Criminal"/>
                            <ComboBoxItem Content="تجارية" Tag="Commercial"/>
                            <ComboBoxItem Content="إدارية" Tag="Administrative"/>
                            <ComboBoxItem Content="عمالية" Tag="Labor"/>
                            <ComboBoxItem Content="أسرة" Tag="Family"/>
                            <ComboBoxItem Content="عقارية" Tag="RealEstate"/>
                            <ComboBoxItem Content="ضريبية" Tag="Tax"/>
                        </ComboBox>

                        <TextBlock Text="المحكمة المختصة *" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="CourtComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="المحكمة العامة بالرياض" IsSelected="True"/>
                            <ComboBoxItem Content="المحكمة التجارية بالرياض"/>
                            <ComboBoxItem Content="محكمة الاستئناف بالرياض"/>
                            <ComboBoxItem Content="المحكمة العامة بجدة"/>
                            <ComboBoxItem Content="المحكمة التجارية بجدة"/>
                            <ComboBoxItem Content="المحكمة العامة بالدمام"/>
                            <ComboBoxItem Content="محكمة الأحوال الشخصية"/>
                            <ComboBoxItem Content="المحكمة الإدارية"/>
                            <ComboBoxItem Content="محكمة العمل"/>
                        </ComboBox>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="مرجع المحكمة" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="CourtReferenceTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="رقم القضية في المحكمة"/>

                        <TextBlock Text="الموكل *" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="ClientComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="أحمد محمد علي السعيد" Tag="1"/>
                            <ComboBoxItem Content="شركة النور للتجارة والاستثمار" Tag="2"/>
                            <ComboBoxItem Content="فاطمة أحمد سالم" Tag="3"/>
                            <ComboBoxItem Content="وزارة التربية والتعليم" Tag="4"/>
                            <ComboBoxItem Content="جمعية البر الخيرية" Tag="5"/>
                        </ComboBox>

                        <TextBlock Text="المحامي المكلف" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="LawyerComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="أحمد محمد المحامي" Tag="1"/>
                            <ComboBoxItem Content="فاطمة علي المحامية" Tag="2"/>
                            <ComboBoxItem Content="محمد سالم المحامي" Tag="3"/>
                        </ComboBox>

                        <TextBlock Text="الخصم/الطرف الآخر" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="OpponentTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="اسم الخصم أو الطرف الآخر"/>
                    </StackPanel>
                </Grid>

                <!-- تفاصيل القضية -->
                <TextBlock Text="📝 تفاصيل القضية" Style="{StaticResource SectionHeaderStyle}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="موضوع القضية" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="SubjectTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Height="80" TextWrapping="Wrap" AcceptsReturn="True"
                                Text="" ToolTip="موضوع القضية بالتفصيل"/>

                        <TextBlock Text="حالة القضية" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="StatusComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="نشطة" Tag="Active" IsSelected="True"/>
                            <ComboBoxItem Content="مؤجلة" Tag="Postponed"/>
                            <ComboBoxItem Content="مغلقة" Tag="Closed"/>
                            <ComboBoxItem Content="مؤرشفة" Tag="Archived"/>
                            <ComboBoxItem Content="ملغاة" Tag="Cancelled"/>
                        </ComboBox>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="وصف القضية" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="DescriptionTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Height="80" TextWrapping="Wrap" AcceptsReturn="True"
                                Text="" ToolTip="وصف تفصيلي للقضية"/>

                        <TextBlock Text="أولوية القضية" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="PriorityComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="منخفضة" Tag="Low"/>
                            <ComboBoxItem Content="متوسطة" Tag="Medium" IsSelected="True"/>
                            <ComboBoxItem Content="عالية" Tag="High"/>
                            <ComboBoxItem Content="عاجلة" Tag="Urgent"/>
                        </ComboBox>
                    </StackPanel>
                </Grid>

                <!-- المعلومات المالية -->
                <TextBlock Text="💰 المعلومات المالية" Style="{StaticResource SectionHeaderStyle}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="القيمة المالية للقضية" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="FinancialValueTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="القيمة المالية بالريال السعودي"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="أتعاب المحاماة" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="LawyerFeesTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="أتعاب المحاماة المتفق عليها"/>
                    </StackPanel>

                    <StackPanel Grid.Column="4">
                        <TextBlock Text="المصاريف الإضافية" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="AdditionalCostsTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="المصاريف الإضافية المتوقعة"/>
                    </StackPanel>
                </Grid>

                <!-- ملاحظات -->
                <TextBlock Text="📄 ملاحظات إضافية" Style="{StaticResource SectionHeaderStyle}"/>
                <TextBox Name="NotesTextBox" Style="{StaticResource FormTextBoxStyle}" 
                        Height="60" TextWrapping="Wrap" AcceptsReturn="True"
                        Text="" ToolTip="ملاحظات إضافية عن القضية"/>
            </StackPanel>
        </ScrollViewer>

        <!-- أزرار الإجراءات -->
        <Border Grid.Row="2" Background="#F8F9FA" Padding="20" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Name="SaveButton" Style="{StaticResource PrimaryButtonStyle}" Click="SaveButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="💾" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ الملف"/>
                    </StackPanel>
                </Button>
                
                <Button Name="SaveAndNewButton" Style="{StaticResource PrimaryButtonStyle}" 
                       Background="#4CAF50" Click="SaveAndNewButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="➕" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ وإضافة آخر"/>
                    </StackPanel>
                </Button>
                
                <Button Name="CancelButton" Style="{StaticResource SecondaryButtonStyle}" Click="CancelButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="❌" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="إلغاء"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
