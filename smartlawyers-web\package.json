{"name": "smartlawyers-web", "version": "1.0.0", "description": "Smart Lawyers Web Platform Frontend", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "watch": "vite build --watch", "hot": "vite --host", "preview": "vite preview", "lint": "eslint resources/js --ext .js,.vue", "lint:fix": "eslint resources/js --ext .js,.vue --fix", "format": "prettier --write resources/js/**/*.{js,vue,css,scss}"}, "devDependencies": {"@vitejs/plugin-vue": "^4.4.0", "axios": "^1.6.0", "bootstrap": "^5.3.2", "laravel-vite-plugin": "^0.8.0", "sass": "^1.69.0", "vite": "^4.5.0", "vue": "^3.3.8", "@popperjs/core": "^2.11.8", "eslint": "^8.52.0", "eslint-plugin-vue": "^9.17.0", "prettier": "^3.0.3"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.4.2", "@fullcalendar/core": "^6.1.9", "@fullcalendar/daygrid": "^6.1.9", "@fullcalendar/timegrid": "^6.1.9", "@fullcalendar/interaction": "^6.1.9", "@fullcalendar/vue3": "^6.1.9", "chart.js": "^4.4.0", "vue-chartjs": "^5.2.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "vue-toastification": "^2.0.0-rc.5", "vue3-datepicker": "^0.4.0", "vue-multiselect": "^3.0.0-beta.3", "vue-loading-overlay": "^6.0.4", "sweetalert2": "^11.7.32", "moment": "^2.29.4", "moment-hijri": "^2.1.2", "lodash": "^4.17.21", "alpinejs": "^3.13.1", "select2": "^4.1.0-rc.0", "datatables.net": "^1.13.6", "datatables.net-bs5": "^1.13.6", "datatables.net-responsive": "^2.5.0", "datatables.net-responsive-bs5": "^2.5.0", "quill": "^1.3.7", "dropzone": "^6.0.0-beta.2", "sortablejs": "^1.15.0", "vue-draggable-next": "^2.2.1", "vue-pdf-embed": "^1.2.1", "jspdf": "^2.5.1", "html2canvas": "^1.4.1", "xlsx": "^0.18.5", "socket.io-client": "^4.7.2", "laravel-echo": "^1.15.3", "pusher-js": "^8.3.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}