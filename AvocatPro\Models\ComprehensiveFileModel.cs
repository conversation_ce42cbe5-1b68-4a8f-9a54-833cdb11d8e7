using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace AvocatPro.Models
{
    /// <summary>
    /// نموذج شامل لإدارة الملفات القانونية مع التكامل مع المحاكم الإلكترونية
    /// </summary>
    public class ComprehensiveFileModel : INotifyPropertyChanged
    {
        #region Private Fields

        private int _id;
        private string _fileNumber;
        private string _courtReference;
        private string _officeReference;
        private string _fileType;
        private string _court;
        private string _caseType;
        private string _opponent;
        private string _subject;
        private string _client;
        private string _assignedLawyer;
        private string _procedureType;
        private string _decision;
        private DateTime _nextSessionDate;
        private DateTime _createdDate;
        private DateTime _lastUpdateDate;
        private string _status;
        private string _priority;
        private string _notes;
        private bool _isArchived;
        private bool _isElectronicTracking;
        private string _electronicFileCode;
        private int _fileYear;
        private string _appealCourt;
        private bool _searchInPrimaryCourts;
        private decimal _estimatedValue;
        private string _documents;
        private int _sessionsCount;
        private string _lastProcedure;
        private DateTime _lastSessionDate;

        #endregion

        #region Properties

        /// <summary>
        /// معرف الملف
        /// </summary>
        public int Id
        {
            get => _id;
            set { _id = value; OnPropertyChanged(nameof(Id)); }
        }

        /// <summary>
        /// رقم الملف الكامل
        /// </summary>
        public string FileNumber
        {
            get => _fileNumber;
            set { _fileNumber = value; OnPropertyChanged(nameof(FileNumber)); }
        }

        /// <summary>
        /// مرجع المحكمة
        /// </summary>
        public string CourtReference
        {
            get => _courtReference;
            set { _courtReference = value; OnPropertyChanged(nameof(CourtReference)); }
        }

        /// <summary>
        /// مرجع المكتب
        /// </summary>
        public string OfficeReference
        {
            get => _officeReference;
            set { _officeReference = value; OnPropertyChanged(nameof(OfficeReference)); }
        }

        /// <summary>
        /// نوع الملف
        /// </summary>
        public string FileType
        {
            get => _fileType;
            set { _fileType = value; OnPropertyChanged(nameof(FileType)); }
        }

        /// <summary>
        /// المحكمة المختصة
        /// </summary>
        public string Court
        {
            get => _court;
            set { _court = value; OnPropertyChanged(nameof(Court)); }
        }

        /// <summary>
        /// نوع القضية
        /// </summary>
        public string CaseType
        {
            get => _caseType;
            set { _caseType = value; OnPropertyChanged(nameof(CaseType)); }
        }

        /// <summary>
        /// الخصم
        /// </summary>
        public string Opponent
        {
            get => _opponent;
            set { _opponent = value; OnPropertyChanged(nameof(Opponent)); }
        }

        /// <summary>
        /// موضوع القضية
        /// </summary>
        public string Subject
        {
            get => _subject;
            set { _subject = value; OnPropertyChanged(nameof(Subject)); }
        }

        /// <summary>
        /// الموكل
        /// </summary>
        public string Client
        {
            get => _client;
            set { _client = value; OnPropertyChanged(nameof(Client)); }
        }

        /// <summary>
        /// المحامي المكلف
        /// </summary>
        public string AssignedLawyer
        {
            get => _assignedLawyer;
            set { _assignedLawyer = value; OnPropertyChanged(nameof(AssignedLawyer)); }
        }

        /// <summary>
        /// نوع الإجراء
        /// </summary>
        public string ProcedureType
        {
            get => _procedureType;
            set { _procedureType = value; OnPropertyChanged(nameof(ProcedureType)); }
        }

        /// <summary>
        /// القرار
        /// </summary>
        public string Decision
        {
            get => _decision;
            set { _decision = value; OnPropertyChanged(nameof(Decision)); }
        }

        /// <summary>
        /// تاريخ الجلسة المقبلة
        /// </summary>
        public DateTime NextSessionDate
        {
            get => _nextSessionDate;
            set { _nextSessionDate = value; OnPropertyChanged(nameof(NextSessionDate)); }
        }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate
        {
            get => _createdDate;
            set { _createdDate = value; OnPropertyChanged(nameof(CreatedDate)); }
        }

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime LastUpdateDate
        {
            get => _lastUpdateDate;
            set { _lastUpdateDate = value; OnPropertyChanged(nameof(LastUpdateDate)); }
        }

        /// <summary>
        /// حالة الملف
        /// </summary>
        public string Status
        {
            get => _status;
            set { _status = value; OnPropertyChanged(nameof(Status)); }
        }

        /// <summary>
        /// أولوية الملف
        /// </summary>
        public string Priority
        {
            get => _priority;
            set { _priority = value; OnPropertyChanged(nameof(Priority)); }
        }

        /// <summary>
        /// ملاحظات
        /// </summary>
        public string Notes
        {
            get => _notes;
            set { _notes = value; OnPropertyChanged(nameof(Notes)); }
        }

        /// <summary>
        /// هل الملف مؤرشف
        /// </summary>
        public bool IsArchived
        {
            get => _isArchived;
            set { _isArchived = value; OnPropertyChanged(nameof(IsArchived)); }
        }

        /// <summary>
        /// التتبع الإلكتروني مفعل
        /// </summary>
        public bool IsElectronicTracking
        {
            get => _isElectronicTracking;
            set { _isElectronicTracking = value; OnPropertyChanged(nameof(IsElectronicTracking)); }
        }

        /// <summary>
        /// رمز الملف الإلكتروني
        /// </summary>
        public string ElectronicFileCode
        {
            get => _electronicFileCode;
            set { _electronicFileCode = value; OnPropertyChanged(nameof(ElectronicFileCode)); }
        }

        /// <summary>
        /// سنة الملف
        /// </summary>
        public int FileYear
        {
            get => _fileYear;
            set { _fileYear = value; OnPropertyChanged(nameof(FileYear)); }
        }

        /// <summary>
        /// محكمة الاستئناف
        /// </summary>
        public string AppealCourt
        {
            get => _appealCourt;
            set { _appealCourt = value; OnPropertyChanged(nameof(AppealCourt)); }
        }

        /// <summary>
        /// البحث في المحاكم الابتدائية
        /// </summary>
        public bool SearchInPrimaryCourts
        {
            get => _searchInPrimaryCourts;
            set { _searchInPrimaryCourts = value; OnPropertyChanged(nameof(SearchInPrimaryCourts)); }
        }

        /// <summary>
        /// القيمة المقدرة للقضية
        /// </summary>
        public decimal EstimatedValue
        {
            get => _estimatedValue;
            set { _estimatedValue = value; OnPropertyChanged(nameof(EstimatedValue)); }
        }

        /// <summary>
        /// المستندات المرفقة
        /// </summary>
        public string Documents
        {
            get => _documents;
            set { _documents = value; OnPropertyChanged(nameof(Documents)); }
        }

        /// <summary>
        /// عدد الجلسات
        /// </summary>
        public int SessionsCount
        {
            get => _sessionsCount;
            set { _sessionsCount = value; OnPropertyChanged(nameof(SessionsCount)); }
        }

        /// <summary>
        /// آخر إجراء
        /// </summary>
        public string LastProcedure
        {
            get => _lastProcedure;
            set { _lastProcedure = value; OnPropertyChanged(nameof(LastProcedure)); }
        }

        /// <summary>
        /// تاريخ آخر جلسة
        /// </summary>
        public DateTime LastSessionDate
        {
            get => _lastSessionDate;
            set { _lastSessionDate = value; OnPropertyChanged(nameof(LastSessionDate)); }
        }

        #endregion

        #region Computed Properties

        /// <summary>
        /// الحرف الأول من نوع القضية
        /// </summary>
        public string CaseTypeInitial => !string.IsNullOrEmpty(CaseType) ? CaseType.Substring(0, 1) : "؟";

        /// <summary>
        /// لون الحالة
        /// </summary>
        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    "نشط" => "#10B981",
                    "في الجلسات" => "#F59E0B",
                    "مؤرشف" => "#6B7280",
                    "مغلق" => "#EF4444",
                    "معلق" => "#8B5CF6",
                    _ => "#6B7280"
                };
            }
        }

        /// <summary>
        /// لون الأولوية
        /// </summary>
        public string PriorityColor
        {
            get
            {
                return Priority switch
                {
                    "عالية" => "#EF4444",
                    "متوسطة" => "#F59E0B",
                    "منخفضة" => "#10B981",
                    _ => "#6B7280"
                };
            }
        }

        /// <summary>
        /// أيام حتى الجلسة المقبلة
        /// </summary>
        public int DaysUntilNextSession
        {
            get
            {
                if (NextSessionDate == default) return -1;
                return (NextSessionDate - DateTime.Now).Days;
            }
        }

        /// <summary>
        /// نص أيام الجلسة المقبلة
        /// </summary>
        public string NextSessionText
        {
            get
            {
                var days = DaysUntilNextSession;
                if (days < 0) return "لا توجد جلسة مقررة";
                if (days == 0) return "اليوم";
                if (days == 1) return "غداً";
                return $"خلال {days} أيام";
            }
        }

        #endregion

        #region Constructor

        public ComprehensiveFileModel()
        {
            _fileNumber = string.Empty;
            _courtReference = string.Empty;
            _officeReference = string.Empty;
            _fileType = string.Empty;
            _court = string.Empty;
            _caseType = string.Empty;
            _opponent = string.Empty;
            _subject = string.Empty;
            _client = string.Empty;
            _assignedLawyer = string.Empty;
            _procedureType = string.Empty;
            _decision = string.Empty;
            _status = "نشط";
            _priority = "متوسطة";
            _notes = string.Empty;
            _electronicFileCode = string.Empty;
            _appealCourt = string.Empty;
            _documents = string.Empty;
            _lastProcedure = string.Empty;
            _createdDate = DateTime.Now;
            _lastUpdateDate = DateTime.Now;
            _nextSessionDate = DateTime.Now.AddDays(30);
            _lastSessionDate = DateTime.Now;
            _fileYear = DateTime.Now.Year;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
