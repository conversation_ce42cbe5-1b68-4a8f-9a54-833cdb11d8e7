﻿#pragma checksum "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "4A86F15F14D5E001F1A325C46310A23F39852A14"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Windows {
    
    
    /// <summary>
    /// AppointmentNotificationWindow
    /// </summary>
    public partial class AppointmentNotificationWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 111 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border UrgentBadge;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UrgentCount;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel UrgentNotificationsPanel;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border TodayBadge;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TodayCount;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel TodayNotificationsPanel;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border TomorrowBadge;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TomorrowCount;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel TomorrowNotificationsPanel;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border OverdueBadge;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OverdueCount;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel OverdueNotificationsPanel;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnablePopupNotifications;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableEmailNotifications;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableSmsNotifications;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableSoundNotifications;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FirstNotificationComboBox;
        
        #line default
        #line hidden
        
        
        #line 228 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SecondNotificationComboBox;
        
        #line default
        #line hidden
        
        
        #line 240 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoMarkAsNotified;
        
        #line default
        #line hidden
        
        
        #line 242 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowOverdueWarnings;
        
        #line default
        #line hidden
        
        
        #line 244 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableWeekendNotifications;
        
        #line default
        #line hidden
        
        
        #line 246 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableHolidayNotifications;
        
        #line default
        #line hidden
        
        
        #line 251 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveSettingsButton;
        
        #line default
        #line hidden
        
        
        #line 272 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshNotificationsButton;
        
        #line default
        #line hidden
        
        
        #line 280 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SendAllNotificationsButton;
        
        #line default
        #line hidden
        
        
        #line 288 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MarkAllAsReadButton;
        
        #line default
        #line hidden
        
        
        #line 297 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/windows/appointmentnotificationwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.UrgentBadge = ((System.Windows.Controls.Border)(target));
            return;
            case 2:
            this.UrgentCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.UrgentNotificationsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 4:
            this.TodayBadge = ((System.Windows.Controls.Border)(target));
            return;
            case 5:
            this.TodayCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.TodayNotificationsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 7:
            this.TomorrowBadge = ((System.Windows.Controls.Border)(target));
            return;
            case 8:
            this.TomorrowCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.TomorrowNotificationsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 10:
            this.OverdueBadge = ((System.Windows.Controls.Border)(target));
            return;
            case 11:
            this.OverdueCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.OverdueNotificationsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 13:
            this.EnablePopupNotifications = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 14:
            this.EnableEmailNotifications = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 15:
            this.EnableSmsNotifications = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 16:
            this.EnableSoundNotifications = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 17:
            this.FirstNotificationComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 18:
            this.SecondNotificationComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 19:
            this.AutoMarkAsNotified = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 20:
            this.ShowOverdueWarnings = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 21:
            this.EnableWeekendNotifications = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 22:
            this.EnableHolidayNotifications = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 23:
            this.SaveSettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 252 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
            this.SaveSettingsButton.Click += new System.Windows.RoutedEventHandler(this.SaveSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.RefreshNotificationsButton = ((System.Windows.Controls.Button)(target));
            
            #line 273 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
            this.RefreshNotificationsButton.Click += new System.Windows.RoutedEventHandler(this.RefreshNotificationsButton_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.SendAllNotificationsButton = ((System.Windows.Controls.Button)(target));
            
            #line 281 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
            this.SendAllNotificationsButton.Click += new System.Windows.RoutedEventHandler(this.SendAllNotificationsButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.MarkAllAsReadButton = ((System.Windows.Controls.Button)(target));
            
            #line 289 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
            this.MarkAllAsReadButton.Click += new System.Windows.RoutedEventHandler(this.MarkAllAsReadButton_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 297 "..\..\..\..\..\Views\Windows\AppointmentNotificationWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

