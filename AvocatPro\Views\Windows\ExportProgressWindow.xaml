<Window x:Class="AvocatPro.Views.Windows.ExportProgressWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تصدير التقرير" 
        Height="300" Width="500"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent">

    <Border Background="White" CornerRadius="15" BorderBrush="#E0E0E0" BorderThickness="1">
        <Border.Effect>
            <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="8" Opacity="0.3" BlurRadius="15"/>
        </Border.Effect>
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- رأس النافذة -->
            <Border Grid.Row="0" Background="#1976D2" CornerRadius="15,15,0,0" Padding="20">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="📤" FontSize="24" Foreground="White" Margin="0,0,15,0"/>
                    <StackPanel>
                        <TextBlock Text="جاري تصدير التقرير..." FontSize="16" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Text="يرجى الانتظار حتى اكتمال العملية" FontSize="11" Foreground="#E3F2FD"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- محتوى النافذة -->
            <StackPanel Grid.Row="1" Margin="30,20,30,20">
                <!-- شريط التقدم الرئيسي -->
                <StackPanel Margin="0,0,0,20">
                    <TextBlock Name="MainStatusText" Text="بدء عملية التصدير..." FontSize="14" FontWeight="Bold" 
                              Margin="0,0,0,10" HorizontalAlignment="Center"/>
                    
                    <ProgressBar Name="MainProgressBar" Height="12" Background="#F5F5F5" 
                                Foreground="#4CAF50" BorderThickness="0" 
                                Value="0" Maximum="100" Margin="0,0,0,5"/>
                    
                    <TextBlock Name="MainProgressText" Text="0%" FontSize="12" 
                              Foreground="#666" HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- تفاصيل العملية -->
                <Border Background="#F8F9FA" CornerRadius="8" Padding="15" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="تفاصيل العملية:" FontSize="12" FontWeight="Bold" Margin="0,0,0,8"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- تحضير البيانات -->
                            <TextBlock Grid.Row="0" Grid.Column="0" Name="PrepareIcon" Text="⏳" FontSize="14" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="تحضير البيانات" FontSize="11" Margin="0,0,0,5"/>
                            <TextBlock Grid.Row="0" Grid.Column="2" Name="PrepareStatus" Text="في الانتظار" FontSize="11" 
                                      Foreground="#666" Margin="0,0,0,5"/>

                            <!-- إنشاء المحتوى -->
                            <TextBlock Grid.Row="1" Grid.Column="0" Name="ContentIcon" Text="⏳" FontSize="14" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Text="إنشاء المحتوى" FontSize="11" Margin="0,0,0,5"/>
                            <TextBlock Grid.Row="1" Grid.Column="2" Name="ContentStatus" Text="في الانتظار" FontSize="11" 
                                      Foreground="#666" Margin="0,0,0,5"/>

                            <!-- إنشاء الرسوم البيانية -->
                            <TextBlock Grid.Row="2" Grid.Column="0" Name="ChartsIcon" Text="⏳" FontSize="14" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Text="إنشاء الرسوم البيانية" FontSize="11" Margin="0,0,0,5"/>
                            <TextBlock Grid.Row="2" Grid.Column="2" Name="ChartsStatus" Text="في الانتظار" FontSize="11" 
                                      Foreground="#666" Margin="0,0,0,5"/>

                            <!-- تنسيق التقرير -->
                            <TextBlock Grid.Row="3" Grid.Column="0" Name="FormatIcon" Text="⏳" FontSize="14" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="3" Grid.Column="1" Text="تنسيق التقرير" FontSize="11" Margin="0,0,0,5"/>
                            <TextBlock Grid.Row="3" Grid.Column="2" Name="FormatStatus" Text="في الانتظار" FontSize="11" 
                                      Foreground="#666" Margin="0,0,0,5"/>

                            <!-- حفظ الملف -->
                            <TextBlock Grid.Row="4" Grid.Column="0" Name="SaveIcon" Text="⏳" FontSize="14" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="4" Grid.Column="1" Text="حفظ الملف" FontSize="11" Margin="0,0,0,5"/>
                            <TextBlock Grid.Row="4" Grid.Column="2" Name="SaveStatus" Text="في الانتظار" FontSize="11" 
                                      Foreground="#666" Margin="0,0,0,5"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- معلومات إضافية -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <TextBlock Text="الوقت المتبقي:" FontSize="11" Foreground="#666" Margin="0,0,5,0"/>
                    <TextBlock Name="TimeRemainingText" Text="~30 ثانية" FontSize="11" FontWeight="Bold" Foreground="#1976D2"/>
                </StackPanel>
            </StackPanel>

            <!-- أزرار الإجراءات -->
            <Border Grid.Row="2" Background="#F8F9FA" CornerRadius="0,0,15,15" Padding="20">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button Name="CancelButton" Background="#F44336" Foreground="White"
                           Padding="15,8" BorderThickness="0" FontSize="12"
                           Cursor="Hand" Click="CancelButton_Click">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}"
                                       CornerRadius="5"
                                       Padding="{TemplateBinding Padding}">
                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                            </ControlTemplate>
                        </Button.Template>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="❌" FontSize="14" Margin="0,0,5,0"/>
                            <TextBlock Text="إلغاء"/>
                        </StackPanel>
                    </Button>

                    <Button Name="MinimizeButton" Background="#FF9800" Foreground="White"
                           Padding="15,8" BorderThickness="0" FontSize="12"
                           Cursor="Hand" Margin="10,0,0,0" Click="MinimizeButton_Click">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}"
                                       CornerRadius="5"
                                       Padding="{TemplateBinding Padding}">
                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                            </ControlTemplate>
                        </Button.Template>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔽" FontSize="14" Margin="0,0,5,0"/>
                            <TextBlock Text="تصغير"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Border>
        </Grid>
    </Border>
</Window>
