<?php
// Simple PHP test to check if everything works
if (!file_exists(__DIR__.'/../vendor/autoload.php')) {
    // If Laravel is not installed, show a simple welcome page
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>منصة المحامين الذكية</title>
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <style>
            body {
                font-family: 'Cairo', sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                direction: rtl;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
            }
            .container {
                text-align: center;
                max-width: 800px;
                padding: 2rem;
            }
            .hero-title {
                font-size: 3rem;
                font-weight: 700;
                margin-bottom: 1rem;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
            .status-badge {
                background: rgba(40, 167, 69, 0.2);
                border: 1px solid rgba(40, 167, 69, 0.5);
                color: #28a745;
                padding: 0.5rem 1rem;
                border-radius: 25px;
                font-weight: 600;
                margin-bottom: 2rem;
                display: inline-block;
            }
            .btn-custom {
                background: rgba(255, 255, 255, 0.2);
                border: 2px solid rgba(255, 255, 255, 0.3);
                color: white;
                padding: 1rem 2rem;
                border-radius: 50px;
                font-weight: 600;
                text-decoration: none;
                display: inline-block;
                margin: 0.5rem;
                transition: all 0.3s ease;
            }
            .btn-custom:hover {
                background: rgba(255, 255, 255, 0.3);
                color: white;
                transform: translateY(-2px);
            }
            .feature-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1rem;
                margin: 2rem 0;
            }
            .feature-card {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border-radius: 20px;
                padding: 1.5rem;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
            .feature-icon {
                font-size: 2rem;
                margin-bottom: 1rem;
                color: #ffd700;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="status-badge">
                <i class="fas fa-check-circle me-2"></i>
                PHP يعمل بنجاح - الإصدار <?php echo PHP_VERSION; ?>
            </div>

            <h1 class="hero-title">
                <i class="fas fa-gavel me-3"></i>
                منصة المحامين الذكية
            </h1>

            <p class="lead mb-4">
                منصة ويب متكاملة لإدارة مكاتب المحاماة مع تكامل كامل مع الخدمات المغربية
            </p>

            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-users"></i></div>
                    <h5>إدارة العملاء</h5>
                    <p>نظام شامل لإدارة بيانات العملاء</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-balance-scale"></i></div>
                    <h5>إدارة القضايا</h5>
                    <p>متابعة شاملة للقضايا والجلسات</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="fab fa-whatsapp"></i></div>
                    <h5>تكامل WhatsApp</h5>
                    <p>تواصل مباشر مع العملاء</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-landmark"></i></div>
                    <h5>المحاكم المغربية</h5>
                    <p>تكامل مع mahakim.ma</p>
                </div>
            </div>

            <div class="mt-4">
                <a href="install.php" class="btn-custom">
                    <i class="fas fa-download me-2"></i>
                    بدء التثبيت
                </a>
                <a href="?info=1" class="btn-custom">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات النظام
                </a>
            </div>

            <?php if (isset($_GET['info'])): ?>
            <div class="mt-4 p-3" style="background: rgba(255,255,255,0.1); border-radius: 10px; text-align: right;">
                <h5>معلومات النظام:</h5>
                <p><strong>إصدار PHP:</strong> <?php echo PHP_VERSION; ?></p>
                <p><strong>نظام التشغيل:</strong> <?php echo PHP_OS; ?></p>
                <p><strong>الخادم:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد'; ?></p>
                <p><strong>المجلد الحالي:</strong> <?php echo __DIR__; ?></p>
                <p><strong>الوقت:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>

                <h6 class="mt-3">الإضافات المطلوبة:</h6>
                <ul class="list-unstyled">
                    <li><?php echo extension_loaded('pdo') ? '✅' : '❌'; ?> PDO</li>
                    <li><?php echo extension_loaded('mbstring') ? '✅' : '❌'; ?> Mbstring</li>
                    <li><?php echo extension_loaded('openssl') ? '✅' : '❌'; ?> OpenSSL</li>
                    <li><?php echo extension_loaded('tokenizer') ? '✅' : '❌'; ?> Tokenizer</li>
                    <li><?php echo extension_loaded('xml') ? '✅' : '❌'; ?> XML</li>
                    <li><?php echo extension_loaded('ctype') ? '✅' : '❌'; ?> Ctype</li>
                    <li><?php echo extension_loaded('json') ? '✅' : '❌'; ?> JSON</li>
                    <li><?php echo extension_loaded('curl') ? '✅' : '❌'; ?> cURL</li>
                </ul>
            </div>
            <?php endif; ?>

            <div class="mt-4">
                <small class="text-white-50">
                    الإصدار 1.0.0 |
                    <i class="fas fa-code me-1"></i>
                    Laravel + Bootstrap |
                    <i class="fas fa-heart me-1"></i>
                    صنع بحب في المغرب
                </small>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// If Laravel is installed, continue with normal Laravel bootstrap
try {
    define('LARAVEL_START', microtime(true));

    // Check if maintenance mode
    if (file_exists($maintenance = __DIR__.'/../storage/framework/maintenance.php')) {
        require $maintenance;
    }

    // Register autoloader
    require __DIR__.'/../vendor/autoload.php';

    // Bootstrap Laravel
    $app = require_once __DIR__.'/../bootstrap/app.php';

    $kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

    $response = $kernel->handle(
        $request = Illuminate\Http\Request::capture()
    )->send();

    $kernel->terminate($request, $response);

} catch (Exception $e) {
    // If Laravel fails, show error page
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="utf-8">
        <title>خطأ في التطبيق</title>
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600&display=swap" rel="stylesheet">
        <style>
            body { font-family: 'Cairo', sans-serif; background: #f8f9fa; padding: 2rem; direction: rtl; }
            .error-container { max-width: 600px; margin: 0 auto; background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
            .error-title { color: #dc3545; margin-bottom: 1rem; }
            .error-message { background: #f8d7da; color: #721c24; padding: 1rem; border-radius: 5px; margin: 1rem 0; }
            .btn { background: #007bff; color: white; padding: 0.5rem 1rem; text-decoration: none; border-radius: 5px; display: inline-block; }
        </style>
    </head>
    <body>
        <div class="error-container">
            <h1 class="error-title">⚠️ خطأ في التطبيق</h1>
            <div class="error-message">
                <strong>رسالة الخطأ:</strong><br>
                <?php echo htmlspecialchars($e->getMessage()); ?>
            </div>
            <p>يرجى التأكد من:</p>
            <ul>
                <li>تثبيت Composer وتشغيل <code>composer install</code></li>
                <li>وجود ملف <code>.env</code> مع الإعدادات الصحيحة</li>
                <li>صلاحيات الكتابة على مجلدات storage و bootstrap/cache</li>
            </ul>
            <a href="install.php" class="btn">🔧 بدء التثبيت</a>
        </div>
    </body>
    </html>
    <?php
}
