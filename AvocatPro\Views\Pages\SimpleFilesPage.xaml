<Page x:Class="AvocatPro.Views.Pages.SimpleFilesPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="إدارة الملفات" Background="#F8FAFC"
      FlowDirection="RightToLeft">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Background="White" CornerRadius="12" Padding="24" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Text="إدارة الملفات" FontSize="24" FontWeight="Bold" Foreground="#1F2937"/>
                    <TextBlock Text="نظام إدارة الملفات القانونية" FontSize="14" Foreground="#6B7280"/>
                </StackPanel>
                
                <Button Grid.Column="1" Content="➕ إضافة ملف جديد"
                        Background="#6366F1" Foreground="White" BorderThickness="0"
                        Padding="16,8" FontSize="14" FontWeight="SemiBold"
                        Cursor="Hand" Click="AddFile_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" 
                                                CornerRadius="8" Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#5B5BD6"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
            </Grid>
        </Border>

        <!-- الإحصائيات -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- إجمالي الملفات -->
            <Border Grid.Column="0" Background="White" CornerRadius="12" Padding="20" Margin="0,0,10,0">
                <Border.Effect>
                    <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <StackPanel Grid.Column="0">
                        <TextBlock x:Name="TotalFilesCount" Text="0" FontSize="28" FontWeight="Bold" Foreground="#1F2937"/>
                        <TextBlock Text="إجمالي الملفات" FontSize="12" Foreground="#6B7280"/>
                    </StackPanel>
                    <Border Grid.Column="1" Background="#6366F1" CornerRadius="8" Padding="8">
                        <TextBlock Text="📁" FontSize="16" Foreground="White"/>
                    </Border>
                </Grid>
            </Border>

            <!-- ملفات نشطة -->
            <Border Grid.Column="1" Background="White" CornerRadius="12" Padding="20" Margin="5,0">
                <Border.Effect>
                    <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <StackPanel Grid.Column="0">
                        <TextBlock x:Name="ActiveFilesCount" Text="0" FontSize="28" FontWeight="Bold" Foreground="#10B981"/>
                        <TextBlock Text="ملفات نشطة" FontSize="12" Foreground="#6B7280"/>
                    </StackPanel>
                    <Border Grid.Column="1" Background="#10B981" CornerRadius="8" Padding="8">
                        <TextBlock Text="✅" FontSize="16" Foreground="White"/>
                    </Border>
                </Grid>
            </Border>

            <!-- في الجلسات -->
            <Border Grid.Column="2" Background="White" CornerRadius="12" Padding="20" Margin="5,0">
                <Border.Effect>
                    <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <StackPanel Grid.Column="0">
                        <TextBlock x:Name="InSessionsCount" Text="0" FontSize="28" FontWeight="Bold" Foreground="#F59E0B"/>
                        <TextBlock Text="في الجلسات" FontSize="12" Foreground="#6B7280"/>
                    </StackPanel>
                    <Border Grid.Column="1" Background="#F59E0B" CornerRadius="8" Padding="8">
                        <TextBlock Text="⚖️" FontSize="16" Foreground="White"/>
                    </Border>
                </Grid>
            </Border>

            <!-- مؤرشفة -->
            <Border Grid.Column="3" Background="White" CornerRadius="12" Padding="20" Margin="10,0,0,0">
                <Border.Effect>
                    <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <StackPanel Grid.Column="0">
                        <TextBlock x:Name="ArchivedFilesCount" Text="0" FontSize="28" FontWeight="Bold" Foreground="#6B7280"/>
                        <TextBlock Text="مؤرشفة" FontSize="12" Foreground="#6B7280"/>
                    </StackPanel>
                    <Border Grid.Column="1" Background="#6B7280" CornerRadius="8" Padding="8">
                        <TextBlock Text="📦" FontSize="16" Foreground="White"/>
                    </Border>
                </Grid>
            </Border>
        </Grid>

        <!-- جدول الملفات -->
        <Border Grid.Row="2" Background="White" CornerRadius="16" Padding="0">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="15" ShadowDepth="0" Opacity="0.3"/>
            </Border.Effect>
            
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- شريط البحث والتصفية -->
                <Border Grid.Row="0" Background="#F8FAFC" CornerRadius="16,16,0,0" Padding="20">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- البحث -->
                        <Border Grid.Column="0" Background="White" CornerRadius="8" Margin="0,0,12,0">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="🔍" FontSize="16" Foreground="#6B7280"
                                           Margin="12,0,8,0" VerticalAlignment="Center"/>
                                <TextBox x:Name="SearchTextBox" Grid.Column="1" Background="Transparent" BorderThickness="0"
                                         Padding="0,12,12,12" FontSize="14"
                                         Text="البحث في الملفات..."
                                         Foreground="#9CA3AF" GotFocus="SearchTextBox_GotFocus"
                                         LostFocus="SearchTextBox_LostFocus" TextChanged="SearchTextBox_TextChanged"/>
                            </Grid>
                        </Border>

                        <!-- تصفية حسب الحالة -->
                        <ComboBox x:Name="StatusFilterComboBox" Grid.Column="1" Background="#F9FAFB"
                                  BorderBrush="#D1D5DB" BorderThickness="1" Padding="12" FontSize="14"
                                  Margin="0,0,12,0" SelectionChanged="StatusFilter_SelectionChanged">
                            <ComboBoxItem Content="جميع الحالات" IsSelected="True"/>
                            <ComboBoxItem Content="نشط"/>
                            <ComboBoxItem Content="في الجلسات"/>
                            <ComboBoxItem Content="مؤرشف"/>
                            <ComboBoxItem Content="مغلق"/>
                        </ComboBox>

                        <!-- تصفية حسب نوع القضية -->
                        <ComboBox x:Name="CaseTypeFilterComboBox" Grid.Column="2" Background="#F9FAFB"
                                  BorderBrush="#D1D5DB" BorderThickness="1" Padding="12" FontSize="14"
                                  Margin="0,0,12,0" SelectionChanged="CaseTypeFilter_SelectionChanged">
                            <ComboBoxItem Content="جميع أنواع القضايا" IsSelected="True"/>
                            <ComboBoxItem Content="مدني"/>
                            <ComboBoxItem Content="تجاري"/>
                            <ComboBoxItem Content="جنائي"/>
                            <ComboBoxItem Content="عائلي"/>
                            <ComboBoxItem Content="إداري"/>
                        </ComboBox>

                        <!-- أزرار الإجراءات -->
                        <StackPanel Grid.Column="3" Orientation="Horizontal">
                            <Button x:Name="ExportButton" Content="📤" Background="#10B981" Foreground="White"
                                    BorderThickness="0" Width="40" Height="40" FontSize="16"
                                    Cursor="Hand" Margin="0,0,8,0" Click="Export_Click"
                                    ToolTip="تصدير البيانات"/>

                            <Button x:Name="PrintButton" Content="🖨️" Background="#6366F1" Foreground="White"
                                    BorderThickness="0" Width="40" Height="40" FontSize="16"
                                    Cursor="Hand" Click="Print_Click"
                                    ToolTip="طباعة القائمة"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- الجدول -->
                <DataGrid x:Name="FilesDataGrid" Grid.Row="1" Background="Transparent" BorderThickness="0"
                          GridLinesVisibility="None" HeadersVisibility="Column"
                          AutoGenerateColumns="False" CanUserAddRows="False"
                          CanUserDeleteRows="False" IsReadOnly="True"
                          SelectionMode="Single" SelectionUnit="FullRow">

                    <DataGrid.ColumnHeaderStyle>
                        <Style TargetType="DataGridColumnHeader">
                            <Setter Property="Background" Value="#F8FAFC"/>
                            <Setter Property="Foreground" Value="#374151"/>
                            <Setter Property="FontWeight" Value="SemiBold"/>
                            <Setter Property="FontSize" Value="14"/>
                            <Setter Property="Padding" Value="16,12"/>
                            <Setter Property="BorderThickness" Value="0,0,0,1"/>
                            <Setter Property="BorderBrush" Value="#E5E7EB"/>
                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                        </Style>
                    </DataGrid.ColumnHeaderStyle>

                    <DataGrid.Columns>
                        <!-- رقم الملف -->
                        <DataGridTextColumn Header="رقم الملف" Binding="{Binding FileNumber}" Width="120"/>
                        
                        <!-- الموكل -->
                        <DataGridTextColumn Header="الموكل" Binding="{Binding Client}" Width="150"/>
                        
                        <!-- نوع القضية -->
                        <DataGridTextColumn Header="نوع القضية" Binding="{Binding CaseType}" Width="120"/>
                        
                        <!-- المحكمة -->
                        <DataGridTextColumn Header="المحكمة" Binding="{Binding Court}" Width="150"/>
                        
                        <!-- الموضوع -->
                        <DataGridTextColumn Header="الموضوع" Binding="{Binding Subject}" Width="200"/>
                        
                        <!-- الحالة -->
                        <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100"/>
                        
                        <!-- تاريخ الإنشاء -->
                        <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedDate, StringFormat=dd/MM/yyyy}" Width="120"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
    </Grid>
</Page>
