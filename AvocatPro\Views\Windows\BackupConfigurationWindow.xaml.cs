using System;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using Microsoft.EntityFrameworkCore;
using AvocatPro.Data;
using AvocatPro.Models.Backup;
using AvocatPro.Services;

namespace AvocatPro.Views.Windows
{
    /// <summary>
    /// نافذة إعداد النسخة الاحتياطية
    /// </summary>
    public partial class BackupConfigurationWindow : Window
    {
        private readonly AvocatProDbContext _context;
        private readonly BackupService _backupService;
        private BackupConfiguration? _configuration;
        private readonly int? _configurationId;

        public bool DialogResult { get; set; }

        public BackupConfigurationWindow(int? configurationId = null)
        {
            InitializeComponent();
            
            _context = new AvocatProDbContext();
            _backupService = new BackupService(_context, null!, null!); // سيتم حقن التبعيات لاحقاً
            _configurationId = configurationId;

            InitializeControls();
            Loaded += BackupConfigurationWindow_Loaded;
        }

        /// <summary>
        /// تهيئة العناصر
        /// </summary>
        private void InitializeControls()
        {
            // تهيئة ساعات اليوم
            for (int i = 0; i < 24; i++)
            {
                HourComboBox.Items.Add(i.ToString("00"));
            }
            HourComboBox.SelectedIndex = 2; // 2 صباحاً افتراضياً

            // تهيئة دقائق الساعة
            for (int i = 0; i < 60; i += 15)
            {
                MinuteComboBox.Items.Add(i.ToString("00"));
            }
            MinuteComboBox.SelectedIndex = 0; // 00 دقيقة افتراضياً

            // تهيئة أيام الشهر
            for (int i = 1; i <= 31; i++)
            {
                MonthlyDayComboBox.Items.Add(i.ToString());
            }
            MonthlyDayComboBox.SelectedIndex = 0; // اليوم الأول افتراضياً

            // تعيين المسار الافتراضي
            LocalPathTextBox.Text = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "AvocatPro Backups");
        }

        /// <summary>
        /// تحميل البيانات عند فتح النافذة
        /// </summary>
        private async void BackupConfigurationWindow_Loaded(object sender, RoutedEventArgs e)
        {
            if (_configurationId.HasValue)
            {
                await LoadConfigurationAsync(_configurationId.Value);
            }
        }

        /// <summary>
        /// تحميل إعداد موجود
        /// </summary>
        private async Task LoadConfigurationAsync(int configurationId)
        {
            try
            {
                _configuration = await _context.BackupConfigurations
                    .FirstOrDefaultAsync(c => c.Id == configurationId);

                if (_configuration != null)
                {
                    // تحميل البيانات الأساسية
                    NameTextBox.Text = _configuration.Name;
                    DescriptionTextBox.Text = _configuration.Description ?? string.Empty;
                    BackupTypeComboBox.SelectedIndex = (int)_configuration.BackupType - 1;
                    IsEnabledCheckBox.IsChecked = _configuration.IsEnabled;

                    // تحميل إعدادات الجدولة
                    FrequencyComboBox.SelectedIndex = (int)_configuration.Frequency;
                    HourComboBox.SelectedIndex = _configuration.ScheduledTime.Hours;
                    MinuteComboBox.SelectedIndex = _configuration.ScheduledTime.Minutes / 15;

                    // تحميل أيام الأسبوع
                    if (_configuration.WeeklyDays.HasValue)
                    {
                        var days = _configuration.WeeklyDays.Value;
                        SundayCheckBox.IsChecked = days.HasFlag(DaysOfWeek.Sunday);
                        MondayCheckBox.IsChecked = days.HasFlag(DaysOfWeek.Monday);
                        TuesdayCheckBox.IsChecked = days.HasFlag(DaysOfWeek.Tuesday);
                        WednesdayCheckBox.IsChecked = days.HasFlag(DaysOfWeek.Wednesday);
                        ThursdayCheckBox.IsChecked = days.HasFlag(DaysOfWeek.Thursday);
                        FridayCheckBox.IsChecked = days.HasFlag(DaysOfWeek.Friday);
                        SaturdayCheckBox.IsChecked = days.HasFlag(DaysOfWeek.Saturday);
                    }

                    // تحميل يوم الشهر
                    if (_configuration.MonthlyDay.HasValue)
                    {
                        MonthlyDayComboBox.SelectedIndex = _configuration.MonthlyDay.Value - 1;
                    }

                    // تحميل إعدادات الوجهة
                    DestinationComboBox.SelectedIndex = (int)_configuration.Destination - 1;
                    LocalPathTextBox.Text = _configuration.LocalPath ?? string.Empty;
                    GoogleDriveFolderTextBox.Text = _configuration.GoogleDriveFolderId ?? string.Empty;

                    // تحميل محتوى النسخة الاحتياطية
                    IncludeDatabaseCheckBox.IsChecked = _configuration.IncludeDatabase;
                    IncludeAttachmentsCheckBox.IsChecked = _configuration.IncludeAttachments;
                    IncludeImagesCheckBox.IsChecked = _configuration.IncludeImages;
                    IncludeReportsCheckBox.IsChecked = _configuration.IncludeReports;
                    IncludeSettingsCheckBox.IsChecked = _configuration.IncludeSettings;

                    // تحميل الإعدادات المتقدمة
                    CompressBackupCheckBox.IsChecked = _configuration.CompressBackup;
                    EncryptBackupCheckBox.IsChecked = _configuration.EncryptBackup;
                    AutoDeleteOldBackupsCheckBox.IsChecked = _configuration.AutoDeleteOldBackups;
                    SendNotificationOnCompleteCheckBox.IsChecked = _configuration.SendNotificationOnComplete;
                    MaxBackupsToKeepTextBox.Text = _configuration.MaxBackupsToKeep.ToString();
                    NotificationEmailTextBox.Text = _configuration.NotificationEmail ?? string.Empty;

                    // إظهار/إخفاء حقل كلمة مرور التشفير
                    EncryptionPasswordPanel.Visibility = _configuration.EncryptBackup ? Visibility.Visible : Visibility.Collapsed;

                    // تحديث عرض الجدولة
                    UpdateScheduleVisibility();
                    UpdateDestinationVisibility();

                    Title = $"تعديل إعداد النسخة الاحتياطية - {_configuration.Name}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعداد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تغيير تكرار النسخة الاحتياطية
        /// </summary>
        private void FrequencyComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateScheduleVisibility();
        }

        /// <summary>
        /// تحديث عرض إعدادات الجدولة
        /// </summary>
        private void UpdateScheduleVisibility()
        {
            var frequency = (BackupFrequency)FrequencyComboBox.SelectedIndex;

            // إخفاء جميع الإعدادات أولاً
            WeeklyDaysLabel.Visibility = Visibility.Collapsed;
            WeeklyDaysPanel.Visibility = Visibility.Collapsed;
            MonthlyDayLabel.Visibility = Visibility.Collapsed;
            MonthlyDayComboBox.Visibility = Visibility.Collapsed;

            // إظهار الإعدادات المناسبة
            switch (frequency)
            {
                case BackupFrequency.Weekly:
                    WeeklyDaysLabel.Visibility = Visibility.Visible;
                    WeeklyDaysPanel.Visibility = Visibility.Visible;
                    break;
                case BackupFrequency.Monthly:
                    MonthlyDayLabel.Visibility = Visibility.Visible;
                    MonthlyDayComboBox.Visibility = Visibility.Visible;
                    break;
            }
        }

        /// <summary>
        /// تغيير وجهة النسخة الاحتياطية
        /// </summary>
        private void DestinationComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateDestinationVisibility();
        }

        /// <summary>
        /// تحديث عرض إعدادات الوجهة
        /// </summary>
        private void UpdateDestinationVisibility()
        {
            var destination = (BackupDestination)(DestinationComboBox.SelectedIndex + 1);

            // إظهار/إخفاء المسار المحلي
            LocalPathLabel.Visibility = (destination == BackupDestination.Local || destination == BackupDestination.Both) 
                ? Visibility.Visible : Visibility.Collapsed;
            LocalPathPanel.Visibility = (destination == BackupDestination.Local || destination == BackupDestination.Both) 
                ? Visibility.Visible : Visibility.Collapsed;

            // إظهار/إخفاء مجلد Google Drive
            GoogleDriveFolderLabel.Visibility = (destination == BackupDestination.GoogleDrive || destination == BackupDestination.Both) 
                ? Visibility.Visible : Visibility.Collapsed;
            GoogleDriveFolderPanel.Visibility = (destination == BackupDestination.GoogleDrive || destination == BackupDestination.Both) 
                ? Visibility.Visible : Visibility.Collapsed;
        }

        /// <summary>
        /// تفعيل/إلغاء تفعيل التشفير
        /// </summary>
        private void EncryptBackupCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            EncryptionPasswordPanel.Visibility = Visibility.Visible;
        }

        private void EncryptBackupCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            EncryptionPasswordPanel.Visibility = Visibility.Collapsed;
            EncryptionPasswordBox.Password = string.Empty;
        }

        /// <summary>
        /// تصفح المجلد المحلي
        /// </summary>
        private void BrowseButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new System.Windows.Forms.FolderBrowserDialog
            {
                Description = "اختر مجلد حفظ النسخ الاحتياطية",
                SelectedPath = LocalPathTextBox.Text
            };

            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                LocalPathTextBox.Text = dialog.SelectedPath;
            }
        }

        /// <summary>
        /// اختيار مجلد Google Drive
        /// </summary>
        private void SelectGoogleDriveFolderButton_Click(object sender, RoutedEventArgs e)
        {
            // سيتم تنفيذ اختيار مجلد Google Drive لاحقاً
            MessageBox.Show("سيتم تنفيذ اختيار مجلد Google Drive قريباً", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// حفظ الإعداد
        /// </summary>
        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                {
                    return;
                }

                var isNew = _configuration == null;
                if (isNew)
                {
                    _configuration = new BackupConfiguration();
                    _context.BackupConfigurations.Add(_configuration);
                }

                // حفظ البيانات الأساسية
                _configuration.Name = NameTextBox.Text.Trim();
                _configuration.Description = string.IsNullOrWhiteSpace(DescriptionTextBox.Text) ? null : DescriptionTextBox.Text.Trim();
                _configuration.BackupType = (BackupType)(BackupTypeComboBox.SelectedIndex + 1);
                _configuration.IsEnabled = IsEnabledCheckBox.IsChecked == true;

                // حفظ إعدادات الجدولة
                _configuration.Frequency = (BackupFrequency)FrequencyComboBox.SelectedIndex;
                _configuration.ScheduledTime = new TimeSpan(HourComboBox.SelectedIndex, MinuteComboBox.SelectedIndex * 15, 0);

                // حفظ أيام الأسبوع
                if (_configuration.Frequency == BackupFrequency.Weekly)
                {
                    var days = DaysOfWeek.Sunday; // قيمة افتراضية
                    days = 0; // إعادة تعيين

                    if (SundayCheckBox.IsChecked == true) days |= DaysOfWeek.Sunday;
                    if (MondayCheckBox.IsChecked == true) days |= DaysOfWeek.Monday;
                    if (TuesdayCheckBox.IsChecked == true) days |= DaysOfWeek.Tuesday;
                    if (WednesdayCheckBox.IsChecked == true) days |= DaysOfWeek.Wednesday;
                    if (ThursdayCheckBox.IsChecked == true) days |= DaysOfWeek.Thursday;
                    if (FridayCheckBox.IsChecked == true) days |= DaysOfWeek.Friday;
                    if (SaturdayCheckBox.IsChecked == true) days |= DaysOfWeek.Saturday;

                    _configuration.WeeklyDays = days;
                }
                else
                {
                    _configuration.WeeklyDays = null;
                }

                // حفظ يوم الشهر
                if (_configuration.Frequency == BackupFrequency.Monthly)
                {
                    _configuration.MonthlyDay = MonthlyDayComboBox.SelectedIndex + 1;
                }
                else
                {
                    _configuration.MonthlyDay = null;
                }

                // حفظ إعدادات الوجهة
                _configuration.Destination = (BackupDestination)(DestinationComboBox.SelectedIndex + 1);
                _configuration.LocalPath = string.IsNullOrWhiteSpace(LocalPathTextBox.Text) ? null : LocalPathTextBox.Text.Trim();
                _configuration.GoogleDriveFolderId = string.IsNullOrWhiteSpace(GoogleDriveFolderTextBox.Text) ? null : GoogleDriveFolderTextBox.Text.Trim();

                // حفظ محتوى النسخة الاحتياطية
                _configuration.IncludeDatabase = IncludeDatabaseCheckBox.IsChecked == true;
                _configuration.IncludeAttachments = IncludeAttachmentsCheckBox.IsChecked == true;
                _configuration.IncludeImages = IncludeImagesCheckBox.IsChecked == true;
                _configuration.IncludeReports = IncludeReportsCheckBox.IsChecked == true;
                _configuration.IncludeSettings = IncludeSettingsCheckBox.IsChecked == true;

                // حفظ الإعدادات المتقدمة
                _configuration.CompressBackup = CompressBackupCheckBox.IsChecked == true;
                _configuration.EncryptBackup = EncryptBackupCheckBox.IsChecked == true;
                _configuration.AutoDeleteOldBackups = AutoDeleteOldBackupsCheckBox.IsChecked == true;
                _configuration.SendNotificationOnComplete = SendNotificationOnCompleteCheckBox.IsChecked == true;
                
                if (int.TryParse(MaxBackupsToKeepTextBox.Text, out int maxBackups))
                {
                    _configuration.MaxBackupsToKeep = maxBackups;
                }

                _configuration.NotificationEmail = string.IsNullOrWhiteSpace(NotificationEmailTextBox.Text) ? null : NotificationEmailTextBox.Text.Trim();

                // حفظ كلمة مرور التشفير
                if (_configuration.EncryptBackup && !string.IsNullOrEmpty(EncryptionPasswordBox.Password))
                {
                    _configuration.EncryptionPassword = EncryptString(EncryptionPasswordBox.Password);
                }

                // تحديث التواريخ
                if (isNew)
                {
                    _configuration.CreatedAt = DateTime.Now;
                    _configuration.CreatedBy = 1; // معرف المستخدم الحالي
                }
                _configuration.UpdatedAt = DateTime.Now;
                _configuration.UpdatedBy = 1; // معرف المستخدم الحالي

                // حساب تاريخ النسخة التالية
                _configuration.NextBackupDate = _configuration.CalculateNextBackupDate();

                await _context.SaveChangesAsync();

                MessageBox.Show("تم حفظ الإعداد بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعداد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// اختبار الإعداد
        /// </summary>
        private async void TestButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                {
                    return;
                }

                MessageBox.Show("سيتم تنفيذ اختبار الإعداد قريباً", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار الإعداد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// التحقق من صحة البيانات المدخلة
        /// </summary>
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الإعداد", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                return false;
            }

            var frequency = (BackupFrequency)FrequencyComboBox.SelectedIndex;
            if (frequency == BackupFrequency.Weekly)
            {
                var hasSelectedDay = SundayCheckBox.IsChecked == true ||
                                   MondayCheckBox.IsChecked == true ||
                                   TuesdayCheckBox.IsChecked == true ||
                                   WednesdayCheckBox.IsChecked == true ||
                                   ThursdayCheckBox.IsChecked == true ||
                                   FridayCheckBox.IsChecked == true ||
                                   SaturdayCheckBox.IsChecked == true;

                if (!hasSelectedDay)
                {
                    MessageBox.Show("يرجى اختيار يوم واحد على الأقل للنسخ الأسبوعي", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }
            }

            var destination = (BackupDestination)(DestinationComboBox.SelectedIndex + 1);
            if ((destination == BackupDestination.Local || destination == BackupDestination.Both) &&
                string.IsNullOrWhiteSpace(LocalPathTextBox.Text))
            {
                MessageBox.Show("يرجى تحديد المسار المحلي", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                LocalPathTextBox.Focus();
                return false;
            }

            if (!int.TryParse(MaxBackupsToKeepTextBox.Text, out int maxBackups) || maxBackups < 1)
            {
                MessageBox.Show("يرجى إدخال عدد صحيح أكبر من 0 لعدد النسخ المحفوظة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                MaxBackupsToKeepTextBox.Focus();
                return false;
            }

            if (EncryptBackupCheckBox.IsChecked == true && string.IsNullOrEmpty(EncryptionPasswordBox.Password))
            {
                MessageBox.Show("يرجى إدخال كلمة مرور التشفير", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                EncryptionPasswordBox.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// تشفير النص (تنفيذ بسيط)
        /// </summary>
        private string EncryptString(string text)
        {
            // تنفيذ بسيط للتشفير - يجب استخدام تشفير أقوى في الإنتاج
            var bytes = System.Text.Encoding.UTF8.GetBytes(text);
            return Convert.ToBase64String(bytes);
        }
    }
}
