<Page x:Class="AvocatPro.Views.Pages.ModernClientsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="إدارة الموكلين"
      Background="#F8FAFC"
      FlowDirection="RightToLeft">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- عنوان الصفحة -->
        <Border Grid.Row="0" Background="White" Padding="25" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="👥" FontSize="28" Margin="0,0,15,0"/>
                    <StackPanel>
                        <TextBlock Text="إدارة الموكلين" FontSize="24" FontWeight="Bold" 
                                   Foreground="#1F2937" Margin="0,0,0,5"/>
                        <TextBlock Text="إدارة قاعدة بيانات الموكلين والعملاء" FontSize="14" 
                                   Foreground="#6B7280"/>
                    </StackPanel>
                </StackPanel>

                <Button Grid.Column="1" Background="#6366F1" Foreground="White" 
                        BorderThickness="0" Padding="20,12" FontSize="14" FontWeight="SemiBold"
                        Cursor="Hand" Click="AddClient_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="➕" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="إضافة موكل جديد"/>
                    </StackPanel>
                </Button>
            </Grid>
        </Border>

        <!-- شريط البحث والتصفية -->
        <Border Grid.Row="1" Background="White" Padding="20" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- مربع البحث -->
                <Border Grid.Column="0" Background="#F9FAFB" BorderBrush="#E5E7EB" 
                        BorderThickness="1" Padding="15,10" Margin="0,0,15,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="🔍" FontSize="16" 
                                   Foreground="#9CA3AF" Margin="0,0,10,0"/>
                        <TextBox Grid.Column="1" Name="SearchTextBox" Background="Transparent" 
                                 BorderThickness="0" FontSize="14" 
                                 Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                 TextChanged="SearchTextBox_TextChanged"/>
                        <TextBlock Grid.Column="1" Text="البحث في الموكلين..."
                                   FontSize="14" Foreground="#9CA3AF"
                                   IsHitTestVisible="False"/>
                    </Grid>
                </Border>

                <!-- تصفية حسب النوع -->
                <ComboBox Grid.Column="1" Name="TypeFilterComboBox" Width="150" 
                          Padding="15,10" FontSize="14" Margin="0,0,15,0"
                          SelectionChanged="TypeFilter_SelectionChanged">
                    <ComboBoxItem Content="جميع الأنواع" IsSelected="True"/>
                    <ComboBoxItem Content="أفراد"/>
                    <ComboBoxItem Content="شركات"/>
                </ComboBox>

                <!-- زر التصدير -->
                <Button Grid.Column="2" Background="#10B981" Foreground="White" 
                        BorderThickness="0" Padding="15,10" FontSize="14"
                        Cursor="Hand" Click="Export_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📊" FontSize="14" Margin="0,0,8,0"/>
                        <TextBlock Text="تصدير"/>
                    </StackPanel>
                </Button>
            </Grid>
        </Border>

        <!-- جدول الموكلين -->
        <Border Grid.Row="2" Background="White" Padding="0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- رأس الجدول -->
                <Border Grid.Row="0" Background="#F8FAFC" Padding="20,15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="50"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="120"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="#" FontWeight="SemiBold" 
                                   Foreground="#374151" FontSize="14"/>
                        <TextBlock Grid.Column="1" Text="اسم الموكل" FontWeight="SemiBold" 
                                   Foreground="#374151" FontSize="14"/>
                        <TextBlock Grid.Column="2" Text="النوع" FontWeight="SemiBold" 
                                   Foreground="#374151" FontSize="14"/>
                        <TextBlock Grid.Column="3" Text="رقم الهاتف" FontWeight="SemiBold" 
                                   Foreground="#374151" FontSize="14"/>
                        <TextBlock Grid.Column="4" Text="البريد الإلكتروني" FontWeight="SemiBold" 
                                   Foreground="#374151" FontSize="14"/>
                        <TextBlock Grid.Column="5" Text="الملفات" FontWeight="SemiBold" 
                                   Foreground="#374151" FontSize="14"/>
                        <TextBlock Grid.Column="6" Text="الإجراءات" FontWeight="SemiBold" 
                                   Foreground="#374151" FontSize="14"/>
                    </Grid>
                </Border>

                <!-- محتوى الجدول -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <ItemsControl Name="ClientsItemsControl">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border BorderBrush="#E5E7EB" BorderThickness="0,0,0,1" 
                                        Padding="20,15" Background="White">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="50"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="120"/>
                                            <ColumnDefinition Width="150"/>
                                            <ColumnDefinition Width="150"/>
                                            <ColumnDefinition Width="100"/>
                                            <ColumnDefinition Width="120"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Column="0" Text="{Binding Id}" 
                                                   Foreground="#6B7280" FontSize="14"/>
                                        <TextBlock Grid.Column="1" Text="{Binding Name}" 
                                                   Foreground="#1F2937" FontSize="14" FontWeight="Medium"/>
                                        <Border Grid.Column="2" Background="{Binding TypeColor}" 
                                                Padding="8,4" HorizontalAlignment="Center">
                                            <TextBlock Text="{Binding Type}" Foreground="White" 
                                                       FontSize="12" FontWeight="SemiBold"/>
                                        </Border>
                                        <TextBlock Grid.Column="3" Text="{Binding Phone}" 
                                                   Foreground="#6B7280" FontSize="14"/>
                                        <TextBlock Grid.Column="4" Text="{Binding Email}" 
                                                   Foreground="#6B7280" FontSize="14"/>
                                        <TextBlock Grid.Column="5" Text="{Binding FilesCount}" 
                                                   Foreground="#6366F1" FontSize="14" FontWeight="SemiBold"
                                                   HorizontalAlignment="Center"/>
                                        
                                        <StackPanel Grid.Column="6" Orientation="Horizontal" 
                                                    HorizontalAlignment="Center">
                                            <Button Background="#3B82F6" Foreground="White" 
                                                    BorderThickness="0" Padding="8,4" Margin="2"
                                                    FontSize="12" Cursor="Hand"
                                                    Click="ViewClient_Click" Tag="{Binding Id}">
                                                <TextBlock Text="👁️"/>
                                            </Button>
                                            <Button Background="#10B981" Foreground="White" 
                                                    BorderThickness="0" Padding="8,4" Margin="2"
                                                    FontSize="12" Cursor="Hand"
                                                    Click="EditClient_Click" Tag="{Binding Id}">
                                                <TextBlock Text="✏️"/>
                                            </Button>
                                            <Button Background="#EF4444" Foreground="White" 
                                                    BorderThickness="0" Padding="8,4" Margin="2"
                                                    FontSize="12" Cursor="Hand"
                                                    Click="DeleteClient_Click" Tag="{Binding Id}">
                                                <TextBlock Text="🗑️"/>
                                            </Button>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>

                <!-- تذييل الجدول -->
                <Border Grid.Row="2" Background="#F8FAFC" Padding="20,15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Name="ClientsCountLabel" 
                                   Text="إجمالي الموكلين: 0" 
                                   Foreground="#6B7280" FontSize="14"/>

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <Button Content="السابق" Background="#E5E7EB" Foreground="#374151"
                                    BorderThickness="0" Padding="15,8" Margin="5,0"
                                    FontSize="12" Cursor="Hand" Click="PreviousPage_Click"/>
                            <TextBlock Text="صفحة 1 من 1" Foreground="#6B7280" 
                                       FontSize="14" VerticalAlignment="Center" Margin="10,0"/>
                            <Button Content="التالي" Background="#E5E7EB" Foreground="#374151"
                                    BorderThickness="0" Padding="15,8" Margin="5,0"
                                    FontSize="12" Cursor="Hand" Click="NextPage_Click"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </Grid>
        </Border>
    </Grid>
</Page>
