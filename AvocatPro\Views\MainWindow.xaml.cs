using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Effects;
using AvocatPro.Models;
using AvocatPro.Views.Pages;

namespace AvocatPro.Views;

public partial class MainWindow : Window
{
    private readonly User _currentUser;
    private readonly Dictionary<string, Page> _pages;
    private Button _activeButton;

    public MainWindow(User currentUser)
    {
        InitializeComponent();
        
        _currentUser = currentUser;
        _pages = new Dictionary<string, Page>();
        
        // تعيين معلومات المستخدم
        UserInfoTextBlock.Text = $"{currentUser.FullName} - {GetRoleDisplayName(currentUser.Role)}";
        
        // تعيين الزر النشط
        _activeButton = DashboardButton;
        
        // تحميل الصفحة الافتراضية
        NavigateToPage("Dashboard");
        
        // تعيين النص الافتراضي لمربع البحث
        SetupSearchBox();
    }

    private void NavigationButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is string pageKey)
        {
            NavigateToPage(pageKey);
            SetActiveButton(button);
        }
    }

    private void NavigateToPage(string pageKey)
    {
        try
        {
            Page page = GetOrCreatePage(pageKey);
            if (page != null)
            {
                MainContentFrame.Navigate(page);
                UpdatePageTitle(pageKey);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في التنقل إلى الصفحة: {ex.Message}", "خطأ", 
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private Page GetOrCreatePage(string pageKey)
    {
        if (_pages.ContainsKey(pageKey))
        {
            return _pages[pageKey];
        }

        Page? page = null;

        try
        {
            page = pageKey switch
            {
                "Dashboard" => new DashboardPage(_currentUser),
                "Clients" => new ClientsPage(_currentUser),
                "Cases" => new CasesPageNew(_currentUser),
                "Sessions" => new ComprehensiveSessionsPage(),
                "Appointments" => new ComprehensiveAppointmentsPage(),
                "Finance" => new FinancialPageNew(_currentUser),
                "Reports" => new ReportsPage(_currentUser),
                "UserManagement" => new UserManagementPage(_currentUser),
                "SmartAssistant" => new SmartAssistantPage(_currentUser),
                "InteractiveDashboard" => new InteractiveDashboardPage(_currentUser),
                "Settings" => CreateSettingsPage(),
                _ => null
            };
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إنشاء الصفحة: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
            return null;
        }

        if (page != null)
        {
            _pages[pageKey] = page;
        }

        return page;
    }

    private Page CreateSettingsPage()
    {
        var page = new Page
        {
            Title = "الإعدادات",
            FlowDirection = FlowDirection.RightToLeft,
            Background = new SolidColorBrush(Color.FromRgb(248, 249, 250))
        };

        var mainGrid = new Grid();

        // شريط العنوان
        var headerBorder = new Border
        {
            Background = Brushes.White,
            Height = 80,
            VerticalAlignment = VerticalAlignment.Top
        };

        var headerGrid = new Grid { Margin = new Thickness(30, 0, 30, 0) };

        var headerStack = new StackPanel
        {
            Orientation = Orientation.Horizontal,
            VerticalAlignment = VerticalAlignment.Center
        };

        var icon = new FontAwesome.WPF.ImageAwesome
        {
            Icon = FontAwesome.WPF.FontAwesomeIcon.Cogs,
            Width = 32,
            Height = 32,
            Foreground = new SolidColorBrush(Color.FromRgb(0, 123, 255)),
            Margin = new Thickness(0, 0, 15, 0)
        };

        var titleText = new TextBlock
        {
            Text = "إعدادات النظام",
            FontSize = 24,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush(Color.FromRgb(44, 62, 80)),
            VerticalAlignment = VerticalAlignment.Center
        };

        headerStack.Children.Add(icon);
        headerStack.Children.Add(titleText);
        headerGrid.Children.Add(headerStack);
        headerBorder.Child = headerGrid;

        // محتوى الصفحة
        var scrollViewer = new ScrollViewer
        {
            Margin = new Thickness(0, 80, 0, 0),
            VerticalScrollBarVisibility = ScrollBarVisibility.Auto
        };

        var contentStack = new StackPanel { Margin = new Thickness(30, 20, 30, 30) };

        // بطاقة إعدادات المكتب
        var officeCard = CreateAdvancedOfficeCard();
        contentStack.Children.Add(officeCard);

        // بطاقة إعدادات المظهر
        var appearanceCard = CreateAdvancedAppearanceCard();
        contentStack.Children.Add(appearanceCard);

        // بطاقة إعدادات السيرفر
        var serverCard = CreateServerSettingsCard();
        contentStack.Children.Add(serverCard);

        // بطاقة النسخ الاحتياطي
        var backupCard = CreateAdvancedBackupCard();
        contentStack.Children.Add(backupCard);

        // بطاقة الإشعارات
        var notificationCard = CreateAdvancedNotificationCard();
        contentStack.Children.Add(notificationCard);

        // بطاقة الأمان والخصوصية
        var securityCard = CreateSecurityCard();
        contentStack.Children.Add(securityCard);

        // بطاقة إعدادات البريد الإلكتروني
        var emailCard = CreateEmailSettingsCard();
        contentStack.Children.Add(emailCard);

        scrollViewer.Content = contentStack;

        mainGrid.Children.Add(headerBorder);
        mainGrid.Children.Add(scrollViewer);

        page.Content = mainGrid;

        return page;
    }

    private Border CreateSettingsCard(string title, FontAwesome.WPF.FontAwesomeIcon iconType)
    {
        var card = new Border
        {
            Background = Brushes.White,
            CornerRadius = new CornerRadius(12),
            Padding = new Thickness(20),
            Margin = new Thickness(0, 0, 0, 15),
            Effect = new DropShadowEffect
            {
                Color = Color.FromRgb(224, 224, 224),
                Direction = 270,
                ShadowDepth = 2,
                BlurRadius = 8,
                Opacity = 0.3
            }
        };

        var cardStack = new StackPanel();

        var headerStack = new StackPanel
        {
            Orientation = Orientation.Horizontal,
            Margin = new Thickness(0, 0, 0, 20)
        };

        var icon = new FontAwesome.WPF.ImageAwesome
        {
            Icon = iconType,
            Width = 24,
            Height = 24,
            Foreground = new SolidColorBrush(Color.FromRgb(0, 123, 255)),
            Margin = new Thickness(0, 0, 10, 0)
        };

        var titleText = new TextBlock
        {
            Text = title,
            FontSize = 18,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush(Color.FromRgb(44, 62, 80))
        };

        headerStack.Children.Add(icon);
        headerStack.Children.Add(titleText);

        var contentText = new TextBlock
        {
            Text = $"إعدادات {title} - قيد التطوير",
            FontSize = 14,
            Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
            Margin = new Thickness(0, 10, 0, 0)
        };

        cardStack.Children.Add(headerStack);
        cardStack.Children.Add(contentText);

        card.Child = cardStack;

        return card;
    }

    private Border CreateAdvancedOfficeCard()
    {
        var card = CreateBaseCard("إعدادات المكتب", FontAwesome.WPF.FontAwesomeIcon.Building);
        var cardStack = (StackPanel)card.Child;

        // إضافة محتوى متقدم
        var grid = new Grid();
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(20) });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

        // اسم المكتب
        var officeNameStack = new StackPanel { Margin = new Thickness(0, 10, 0, 0) };
        Grid.SetColumn(officeNameStack, 0);
        officeNameStack.Children.Add(new TextBlock { Text = "اسم المكتب", FontWeight = FontWeights.SemiBold, Margin = new Thickness(0, 0, 0, 5) });
        var officeNameTxt = new TextBox { Text = "مكتب المحامي أحمد محمد", Padding = new Thickness(10), FontSize = 14 };
        officeNameStack.Children.Add(officeNameTxt);

        // رقم الترخيص
        var licenseStack = new StackPanel { Margin = new Thickness(0, 10, 0, 0) };
        Grid.SetColumn(licenseStack, 2);
        licenseStack.Children.Add(new TextBlock { Text = "رقم الترخيص", FontWeight = FontWeights.SemiBold, Margin = new Thickness(0, 0, 0, 5) });
        var licenseTxt = new TextBox { Text = "LAW-2024-001", Padding = new Thickness(10), FontSize = 14 };
        licenseStack.Children.Add(licenseTxt);

        grid.Children.Add(officeNameStack);
        grid.Children.Add(licenseStack);

        // إضافة أزرار الحفظ
        var buttonStack = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 15, 0, 0) };
        var saveBtn = CreateStyledButton("حفظ", "#28A745");
        var resetBtn = CreateStyledButton("إعادة تعيين", "#6C757D");
        buttonStack.Children.Add(saveBtn);
        buttonStack.Children.Add(resetBtn);

        cardStack.Children.Add(grid);
        cardStack.Children.Add(buttonStack);

        return card;
    }

    private Border CreateAdvancedAppearanceCard()
    {
        var card = CreateBaseCard("إعدادات المظهر والواجهة", FontAwesome.WPF.FontAwesomeIcon.Desktop);
        var cardStack = (StackPanel)card.Child;

        var grid = new Grid();
        for (int i = 0; i < 3; i++)
        {
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            if (i < 2) grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(15) });
        }

        // اللغة
        var langStack = new StackPanel { Margin = new Thickness(0, 10, 0, 0) };
        Grid.SetColumn(langStack, 0);
        langStack.Children.Add(new TextBlock { Text = "اللغة", FontWeight = FontWeights.SemiBold, Margin = new Thickness(0, 0, 0, 5) });
        var langCombo = new ComboBox { Padding = new Thickness(10), FontSize = 14 };
        langCombo.Items.Add(new ComboBoxItem { Content = "العربية", IsSelected = true });
        langCombo.Items.Add(new ComboBoxItem { Content = "English" });
        langCombo.Items.Add(new ComboBoxItem { Content = "Français" });
        langStack.Children.Add(langCombo);

        // النمط
        var themeStack = new StackPanel { Margin = new Thickness(0, 10, 0, 0) };
        Grid.SetColumn(themeStack, 2);
        themeStack.Children.Add(new TextBlock { Text = "نمط الألوان", FontWeight = FontWeights.SemiBold, Margin = new Thickness(0, 0, 0, 5) });
        var themeCombo = new ComboBox { Padding = new Thickness(10), FontSize = 14 };
        themeCombo.Items.Add(new ComboBoxItem { Content = "الوضع الفاتح", IsSelected = true });
        themeCombo.Items.Add(new ComboBoxItem { Content = "الوضع الداكن" });
        themeCombo.Items.Add(new ComboBoxItem { Content = "تلقائي" });
        themeStack.Children.Add(themeCombo);

        // اللون الأساسي
        var colorStack = new StackPanel { Margin = new Thickness(0, 10, 0, 0) };
        Grid.SetColumn(colorStack, 4);
        colorStack.Children.Add(new TextBlock { Text = "اللون الأساسي", FontWeight = FontWeights.SemiBold, Margin = new Thickness(0, 0, 0, 5) });
        var colorCombo = new ComboBox { Padding = new Thickness(10), FontSize = 14 };
        colorCombo.Items.Add(new ComboBoxItem { Content = "أزرق", IsSelected = true });
        colorCombo.Items.Add(new ComboBoxItem { Content = "أخضر" });
        colorCombo.Items.Add(new ComboBoxItem { Content = "أحمر" });
        colorStack.Children.Add(colorCombo);

        grid.Children.Add(langStack);
        grid.Children.Add(themeStack);
        grid.Children.Add(colorStack);

        // خيارات إضافية
        var optionsStack = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 15, 0, 0) };
        var animChk = new CheckBox { Content = "تفعيل المؤثرات", IsChecked = true, Margin = new Thickness(0, 0, 20, 0) };
        var soundChk = new CheckBox { Content = "تفعيل الأصوات", IsChecked = true };
        optionsStack.Children.Add(animChk);
        optionsStack.Children.Add(soundChk);

        cardStack.Children.Add(grid);
        cardStack.Children.Add(optionsStack);

        return card;
    }

    private Border CreateServerSettingsCard()
    {
        var card = CreateBaseCard("إعدادات السيرفر والاتصال", FontAwesome.WPF.FontAwesomeIcon.Server);
        var cardStack = (StackPanel)card.Child;

        var grid = new Grid();
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(20) });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

        grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(15) });
        grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(15) });
        grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

        // عنوان السيرفر
        var serverStack = new StackPanel { Margin = new Thickness(0, 10, 0, 0) };
        Grid.SetColumn(serverStack, 0);
        Grid.SetRow(serverStack, 0);
        serverStack.Children.Add(new TextBlock { Text = "عنوان السيرفر", FontWeight = FontWeights.SemiBold, Margin = new Thickness(0, 0, 0, 5) });
        var serverTxt = new TextBox { Text = "localhost", Padding = new Thickness(10), FontSize = 14 };
        serverStack.Children.Add(serverTxt);

        // المنفذ
        var portStack = new StackPanel { Margin = new Thickness(0, 10, 0, 0) };
        Grid.SetColumn(portStack, 2);
        Grid.SetRow(portStack, 0);
        portStack.Children.Add(new TextBlock { Text = "المنفذ", FontWeight = FontWeights.SemiBold, Margin = new Thickness(0, 0, 0, 5) });
        var portTxt = new TextBox { Text = "5432", Padding = new Thickness(10), FontSize = 14 };
        portStack.Children.Add(portTxt);

        // اسم قاعدة البيانات
        var dbStack = new StackPanel { Margin = new Thickness(0, 10, 0, 0) };
        Grid.SetColumn(dbStack, 0);
        Grid.SetRow(dbStack, 2);
        dbStack.Children.Add(new TextBlock { Text = "اسم قاعدة البيانات", FontWeight = FontWeights.SemiBold, Margin = new Thickness(0, 0, 0, 5) });
        var dbTxt = new TextBox { Text = "AvocatPro", Padding = new Thickness(10), FontSize = 14 };
        dbStack.Children.Add(dbTxt);

        // اسم المستخدم
        var userStack = new StackPanel { Margin = new Thickness(0, 10, 0, 0) };
        Grid.SetColumn(userStack, 2);
        Grid.SetRow(userStack, 2);
        userStack.Children.Add(new TextBlock { Text = "اسم المستخدم", FontWeight = FontWeights.SemiBold, Margin = new Thickness(0, 0, 0, 5) });
        var userTxt = new TextBox { Text = "admin", Padding = new Thickness(10), FontSize = 14 };
        userStack.Children.Add(userTxt);

        grid.Children.Add(serverStack);
        grid.Children.Add(portStack);
        grid.Children.Add(dbStack);
        grid.Children.Add(userStack);

        // أزرار الاختبار والحفظ
        var buttonStack = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 15, 0, 0) };
        var testBtn = CreateStyledButton("اختبار الاتصال", "#17A2B8");
        var saveBtn = CreateStyledButton("حفظ", "#28A745");
        buttonStack.Children.Add(testBtn);
        buttonStack.Children.Add(saveBtn);

        cardStack.Children.Add(grid);
        cardStack.Children.Add(buttonStack);

        return card;
    }

    private Border CreateBaseCard(string title, FontAwesome.WPF.FontAwesomeIcon iconType)
    {
        var card = new Border
        {
            Background = Brushes.White,
            CornerRadius = new CornerRadius(12),
            Padding = new Thickness(20),
            Margin = new Thickness(0, 0, 0, 15),
            Effect = new DropShadowEffect
            {
                Color = Color.FromRgb(224, 224, 224),
                Direction = 270,
                ShadowDepth = 2,
                BlurRadius = 8,
                Opacity = 0.3
            }
        };

        var cardStack = new StackPanel();

        var headerStack = new StackPanel
        {
            Orientation = Orientation.Horizontal,
            Margin = new Thickness(0, 0, 0, 20)
        };

        var icon = new FontAwesome.WPF.ImageAwesome
        {
            Icon = iconType,
            Width = 24,
            Height = 24,
            Foreground = new SolidColorBrush(Color.FromRgb(0, 123, 255)),
            Margin = new Thickness(0, 0, 10, 0)
        };

        var titleText = new TextBlock
        {
            Text = title,
            FontSize = 18,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush(Color.FromRgb(44, 62, 80))
        };

        headerStack.Children.Add(icon);
        headerStack.Children.Add(titleText);
        cardStack.Children.Add(headerStack);

        card.Child = cardStack;
        return card;
    }

    private Button CreateStyledButton(string content, string colorHex)
    {
        var color = (Color)ColorConverter.ConvertFromString(colorHex);
        return new Button
        {
            Content = content,
            Background = new SolidColorBrush(color),
            Foreground = Brushes.White,
            Padding = new Thickness(15, 8, 15, 8),
            Margin = new Thickness(0, 0, 10, 0),
            FontSize = 14,
            FontWeight = FontWeights.SemiBold,
            BorderThickness = new Thickness(0),
            Cursor = Cursors.Hand
        };
    }

    private Border CreateAdvancedBackupCard()
    {
        var card = CreateBaseCard("النسخ الاحتياطي والاستعادة", FontAwesome.WPF.FontAwesomeIcon.Database);
        var cardStack = (StackPanel)card.Child;

        var grid = new Grid();
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(20) });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(20) });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

        // تفعيل النسخ التلقائي
        var autoStack = new StackPanel { Margin = new Thickness(0, 10, 0, 0) };
        Grid.SetColumn(autoStack, 0);
        var autoChk = new CheckBox { Content = "نسخ احتياطي تلقائي", IsChecked = true, FontWeight = FontWeights.SemiBold };
        autoStack.Children.Add(autoChk);

        // تكرار النسخ
        var freqStack = new StackPanel { Margin = new Thickness(0, 10, 0, 0) };
        Grid.SetColumn(freqStack, 2);
        freqStack.Children.Add(new TextBlock { Text = "تكرار النسخ", FontWeight = FontWeights.SemiBold, Margin = new Thickness(0, 0, 0, 5) });
        var freqCombo = new ComboBox { Padding = new Thickness(10), FontSize = 14 };
        freqCombo.Items.Add(new ComboBoxItem { Content = "يومياً", IsSelected = true });
        freqCombo.Items.Add(new ComboBoxItem { Content = "أسبوعياً" });
        freqCombo.Items.Add(new ComboBoxItem { Content = "شهرياً" });
        freqStack.Children.Add(freqCombo);

        // وقت النسخ
        var timeStack = new StackPanel { Margin = new Thickness(0, 10, 0, 0) };
        Grid.SetColumn(timeStack, 4);
        timeStack.Children.Add(new TextBlock { Text = "وقت النسخ", FontWeight = FontWeights.SemiBold, Margin = new Thickness(0, 0, 0, 5) });
        var timeCombo = new ComboBox { Padding = new Thickness(10), FontSize = 14 };
        timeCombo.Items.Add(new ComboBoxItem { Content = "02:00 ص", IsSelected = true });
        timeCombo.Items.Add(new ComboBoxItem { Content = "04:00 ص" });
        timeCombo.Items.Add(new ComboBoxItem { Content = "06:00 ص" });
        timeStack.Children.Add(timeCombo);

        grid.Children.Add(autoStack);
        grid.Children.Add(freqStack);
        grid.Children.Add(timeStack);

        // أزرار العمليات
        var buttonStack = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 15, 0, 0) };
        var createBtn = CreateStyledButton("إنشاء نسخة الآن", "#28A745");
        var restoreBtn = CreateStyledButton("استعادة نسخة", "#FFC107");
        var optimizeBtn = CreateStyledButton("تحسين قاعدة البيانات", "#6F42C1");
        buttonStack.Children.Add(createBtn);
        buttonStack.Children.Add(restoreBtn);
        buttonStack.Children.Add(optimizeBtn);

        cardStack.Children.Add(grid);
        cardStack.Children.Add(buttonStack);

        return card;
    }

    private Border CreateAdvancedNotificationCard()
    {
        var card = CreateBaseCard("إعدادات الإشعارات والتنبيهات", FontAwesome.WPF.FontAwesomeIcon.Bell);
        var cardStack = (StackPanel)card.Child;

        var grid = new Grid();
        for (int i = 0; i < 3; i++)
        {
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            if (i < 2) grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(15) });
        }
        grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(15) });
        grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

        // أنواع الإشعارات
        var desktopStack = new StackPanel { Margin = new Thickness(0, 10, 0, 0) };
        Grid.SetColumn(desktopStack, 0);
        Grid.SetRow(desktopStack, 0);
        var desktopChk = new CheckBox { Content = "إشعارات سطح المكتب", IsChecked = true };
        desktopStack.Children.Add(desktopChk);

        var soundStack = new StackPanel { Margin = new Thickness(0, 10, 0, 0) };
        Grid.SetColumn(soundStack, 2);
        Grid.SetRow(soundStack, 0);
        var soundChk = new CheckBox { Content = "الإشعارات الصوتية", IsChecked = true };
        soundStack.Children.Add(soundChk);

        var emailStack = new StackPanel { Margin = new Thickness(0, 10, 0, 0) };
        Grid.SetColumn(emailStack, 4);
        Grid.SetRow(emailStack, 0);
        var emailChk = new CheckBox { Content = "إشعارات البريد", IsChecked = false };
        emailStack.Children.Add(emailChk);

        // إعدادات التوقيت
        var alertTimeStack = new StackPanel { Margin = new Thickness(0, 10, 0, 0) };
        Grid.SetColumn(alertTimeStack, 0);
        Grid.SetRow(alertTimeStack, 2);
        alertTimeStack.Children.Add(new TextBlock { Text = "وقت التنبيه المسبق", FontWeight = FontWeights.SemiBold, Margin = new Thickness(0, 0, 0, 5) });
        var alertCombo = new ComboBox { Padding = new Thickness(10), FontSize = 14 };
        alertCombo.Items.Add(new ComboBoxItem { Content = "15 دقيقة" });
        alertCombo.Items.Add(new ComboBoxItem { Content = "30 دقيقة", IsSelected = true });
        alertCombo.Items.Add(new ComboBoxItem { Content = "ساعة واحدة" });
        alertTimeStack.Children.Add(alertCombo);

        var soundTypeStack = new StackPanel { Margin = new Thickness(0, 10, 0, 0) };
        Grid.SetColumn(soundTypeStack, 2);
        Grid.SetRow(soundTypeStack, 2);
        soundTypeStack.Children.Add(new TextBlock { Text = "نغمة التنبيه", FontWeight = FontWeights.SemiBold, Margin = new Thickness(0, 0, 0, 5) });
        var soundCombo = new ComboBox { Padding = new Thickness(10), FontSize = 14 };
        soundCombo.Items.Add(new ComboBoxItem { Content = "افتراضي", IsSelected = true });
        soundCombo.Items.Add(new ComboBoxItem { Content = "جرس" });
        soundCombo.Items.Add(new ComboBoxItem { Content = "تنبيه" });
        soundTypeStack.Children.Add(soundCombo);

        grid.Children.Add(desktopStack);
        grid.Children.Add(soundStack);
        grid.Children.Add(emailStack);
        grid.Children.Add(alertTimeStack);
        grid.Children.Add(soundTypeStack);

        cardStack.Children.Add(grid);

        return card;
    }

    private Border CreateSecurityCard()
    {
        var card = CreateBaseCard("الأمان والخصوصية", FontAwesome.WPF.FontAwesomeIcon.Shield);
        var cardStack = (StackPanel)card.Child;

        var grid = new Grid();
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(20) });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

        // إعدادات كلمة المرور
        var passStack = new StackPanel { Margin = new Thickness(0, 10, 0, 0) };
        Grid.SetColumn(passStack, 0);
        var passChk = new CheckBox { Content = "تفعيل انتهاء صلاحية كلمة المرور", IsChecked = false };
        passStack.Children.Add(passChk);
        passStack.Children.Add(new TextBlock { Text = "مدة الصلاحية: 90 يوم", FontSize = 12, Foreground = Brushes.Gray, Margin = new Thickness(0, 5, 0, 0) });

        // إعدادات الجلسة
        var sessionStack = new StackPanel { Margin = new Thickness(0, 10, 0, 0) };
        Grid.SetColumn(sessionStack, 2);
        var sessionChk = new CheckBox { Content = "انتهاء الجلسة التلقائي", IsChecked = true };
        sessionStack.Children.Add(sessionChk);
        sessionStack.Children.Add(new TextBlock { Text = "مدة الجلسة: 30 دقيقة", FontSize = 12, Foreground = Brushes.Gray, Margin = new Thickness(0, 5, 0, 0) });

        grid.Children.Add(passStack);
        grid.Children.Add(sessionStack);

        // أزرار الأمان
        var secButtonStack = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 15, 0, 0) };
        var changePassBtn = CreateStyledButton("تغيير كلمة المرور", "#DC3545");
        var auditBtn = CreateStyledButton("سجل العمليات", "#17A2B8");
        secButtonStack.Children.Add(changePassBtn);
        secButtonStack.Children.Add(auditBtn);

        cardStack.Children.Add(grid);
        cardStack.Children.Add(secButtonStack);

        return card;
    }

    private Border CreateEmailSettingsCard()
    {
        var card = CreateBaseCard("إعدادات البريد الإلكتروني", FontAwesome.WPF.FontAwesomeIcon.Envelope);
        var cardStack = (StackPanel)card.Child;

        var grid = new Grid();
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(20) });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

        grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(15) });
        grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

        // خادم SMTP
        var smtpStack = new StackPanel { Margin = new Thickness(0, 10, 0, 0) };
        Grid.SetColumn(smtpStack, 0);
        Grid.SetRow(smtpStack, 0);
        smtpStack.Children.Add(new TextBlock { Text = "خادم SMTP", FontWeight = FontWeights.SemiBold, Margin = new Thickness(0, 0, 0, 5) });
        var smtpTxt = new TextBox { Text = "smtp.gmail.com", Padding = new Thickness(10), FontSize = 14 };
        smtpStack.Children.Add(smtpTxt);

        // المنفذ
        var portStack = new StackPanel { Margin = new Thickness(0, 10, 0, 0) };
        Grid.SetColumn(portStack, 2);
        Grid.SetRow(portStack, 0);
        portStack.Children.Add(new TextBlock { Text = "المنفذ", FontWeight = FontWeights.SemiBold, Margin = new Thickness(0, 0, 0, 5) });
        var portTxt = new TextBox { Text = "587", Padding = new Thickness(10), FontSize = 14 };
        portStack.Children.Add(portTxt);

        // اسم المستخدم
        var userStack = new StackPanel { Margin = new Thickness(0, 10, 0, 0) };
        Grid.SetColumn(userStack, 0);
        Grid.SetRow(userStack, 2);
        userStack.Children.Add(new TextBlock { Text = "اسم المستخدم", FontWeight = FontWeights.SemiBold, Margin = new Thickness(0, 0, 0, 5) });
        var userTxt = new TextBox { Text = "<EMAIL>", Padding = new Thickness(10), FontSize = 14 };
        userStack.Children.Add(userTxt);

        // SSL
        var sslStack = new StackPanel { Margin = new Thickness(0, 10, 0, 0) };
        Grid.SetColumn(sslStack, 2);
        Grid.SetRow(sslStack, 2);
        var sslChk = new CheckBox { Content = "تفعيل SSL", IsChecked = true, FontWeight = FontWeights.SemiBold };
        sslStack.Children.Add(sslChk);

        grid.Children.Add(smtpStack);
        grid.Children.Add(portStack);
        grid.Children.Add(userStack);
        grid.Children.Add(sslStack);

        // أزرار البريد
        var emailButtonStack = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 15, 0, 0) };
        var testEmailBtn = CreateStyledButton("اختبار الاتصال", "#17A2B8");
        var sendTestBtn = CreateStyledButton("إرسال بريد تجريبي", "#28A745");
        emailButtonStack.Children.Add(testEmailBtn);
        emailButtonStack.Children.Add(sendTestBtn);

        cardStack.Children.Add(grid);
        cardStack.Children.Add(emailButtonStack);

        return card;
    }

    private Page CreateTempPage(string title, string description)
    {
        var page = new Page();
        var stackPanel = new StackPanel
        {
            Margin = new Thickness(50),
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center
        };

        var titleBlock = new TextBlock
        {
            Text = title,
            FontSize = 24,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush(Color.FromRgb(25, 118, 210)),
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 20)
        };

        var descBlock = new TextBlock
        {
            Text = description,
            FontSize = 16,
            Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102)),
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 30)
        };

        var statusBlock = new TextBlock
        {
            Text = "🚧 هذا القسم قيد التطوير",
            FontSize = 18,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush(Color.FromRgb(255, 152, 0)),
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 20)
        };

        var infoBlock = new TextBlock
        {
            Text = "سيتم إضافة هذا القسم في التحديثات القادمة",
            FontSize = 14,
            Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102)),
            HorizontalAlignment = HorizontalAlignment.Center
        };

        stackPanel.Children.Add(titleBlock);
        stackPanel.Children.Add(descBlock);
        stackPanel.Children.Add(statusBlock);
        stackPanel.Children.Add(infoBlock);

        page.Content = stackPanel;
        return page;
    }

    private void SetActiveButton(Button button)
    {
        // إزالة النمط النشط من الزر السابق
        if (_activeButton != null)
        {
            _activeButton.Style = (Style)FindResource("ModernSidebarButtonStyle");
        }

        // تطبيق النمط النشط على الزر الجديد
        button.Style = (Style)FindResource("ActiveSidebarButtonStyle");
        _activeButton = button;
    }

    private void UpdatePageTitle(string pageKey)
    {
        var (title, subtitle) = pageKey switch
        {
            "Dashboard" => ("لوحة التحكم", "نظرة عامة على أنشطة المكتب"),
            "Clients" => ("إدارة الموكلين", "إضافة وإدارة بيانات الموكلين"),
            "Cases" => ("إدارة الملفات", "إدارة ملفات القضايا والدعاوى"),
            "Sessions" => ("إدارة الجلسات", "جدولة ومتابعة جلسات المحكمة"),
            "Appointments" => ("إدارة المواعيد", "تنظيم المواعيد واللقاءات"),
            "Finance" => ("الإدارة المالية", "إدارة الأتعاب والمصروفات"),
            "Reports" => ("التقارير والإحصائيات", "تقارير شاملة عن أداء المكتب"),
            "UserManagement" => ("إدارة المستخدمين", "إدارة المستخدمين والصلاحيات"),
            "SmartAssistant" => ("المساعد الذكي المتطور", "ذكاء اصطناعي متقدم للاستشارات والتحليل"),
            "InteractiveDashboard" => ("لوحة التحكم التفاعلية", "تحليلات حية ومؤشرات أداء متقدمة مع رسوم بيانية تفاعلية"),
            "Settings" => ("الإعدادات", "إعدادات النظام والتخصيص"),
            _ => ("AvocatPro", "نظام إدارة مكتب المحاماة")
        };

        PageTitleTextBlock.Text = title;
        PageSubtitleTextBlock.Text = subtitle;
    }

    private string GetRoleDisplayName(UserRole role)
    {
        return role switch
        {
            UserRole.Admin => "مدير النظام",
            UserRole.Lawyer => "محامي",
            _ => "مستخدم"
        };
    }

    private void SetupSearchBox()
    {
        QuickSearchTextBox.GotFocus += (s, e) =>
        {
            if (QuickSearchTextBox.Text == "البحث السريع...")
            {
                QuickSearchTextBox.Text = "";
                QuickSearchTextBox.Foreground = new SolidColorBrush(Colors.Black);
            }
        };

        QuickSearchTextBox.LostFocus += (s, e) =>
        {
            if (string.IsNullOrWhiteSpace(QuickSearchTextBox.Text))
            {
                QuickSearchTextBox.Text = "البحث السريع...";
                QuickSearchTextBox.Foreground = new SolidColorBrush(Color.FromRgb(153, 153, 153));
            }
        };
    }

    private void QuickSearchTextBox_GotFocus(object sender, RoutedEventArgs e)
    {
        if (QuickSearchTextBox.Text == "البحث السريع...")
        {
            QuickSearchTextBox.Text = "";
            QuickSearchTextBox.Foreground = new SolidColorBrush(Colors.Black);
        }
    }

    private void QuickSearchTextBox_LostFocus(object sender, RoutedEventArgs e)
    {
        if (string.IsNullOrWhiteSpace(QuickSearchTextBox.Text))
        {
            QuickSearchTextBox.Text = "البحث السريع...";
            QuickSearchTextBox.Foreground = new SolidColorBrush(Color.FromRgb(153, 153, 153));
        }
    }

    private void QuickSearchTextBox_KeyDown(object sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter)
        {
            var searchTerm = QuickSearchTextBox.Text?.Trim();
            if (!string.IsNullOrEmpty(searchTerm) && searchTerm != "البحث السريع...")
            {
                PerformQuickSearch(searchTerm);
            }
        }
    }

    private void PerformQuickSearch(string searchTerm)
    {
        MessageBox.Show($"البحث عن: {searchTerm}\n\nميزة البحث السريع قيد التطوير", 
                       "بحث", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void LogoutButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("هل أنت متأكد من تسجيل الخروج؟", "تأكيد", 
                                   MessageBoxButton.YesNo, MessageBoxImage.Question);
        
        if (result == MessageBoxResult.Yes)
        {
            Application.Current.Shutdown();
        }
    }
}
