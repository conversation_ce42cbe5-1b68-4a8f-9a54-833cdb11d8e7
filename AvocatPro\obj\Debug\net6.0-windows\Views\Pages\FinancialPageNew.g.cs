﻿#pragma checksum "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1ADC5686804896ADE5093DFF18FD33F47C3EC0C7"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Pages {
    
    
    /// <summary>
    /// FinancialPageNew
    /// </summary>
    public partial class FinancialPageNew : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 121 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddExpenseButton;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddRevenueButton;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button GenerateReportButton;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalRevenuesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalExpensesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 184 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NetProfitTextBlock;
        
        #line default
        #line hidden
        
        
        #line 191 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PendingRevenuesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OverdueRevenuesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 205 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReimbursableExpensesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PeriodFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 235 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker StartDatePicker;
        
        #line default
        #line hidden
        
        
        #line 239 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ToLabel;
        
        #line default
        #line hidden
        
        
        #line 241 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker EndDatePicker;
        
        #line default
        #line hidden
        
        
        #line 247 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CategoryFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 258 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 271 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 279 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintButton;
        
        #line default
        #line hidden
        
        
        #line 309 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ExpensesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 374 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ExpensesStatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 395 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid RevenuesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 460 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RevenuesStatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 490 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer MonthlyChart;
        
        #line default
        #line hidden
        
        
        #line 491 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel MonthlyChartPanel;
        
        #line default
        #line hidden
        
        
        #line 501 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer ExpenseCategoryChart;
        
        #line default
        #line hidden
        
        
        #line 502 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ExpenseCategoryChartPanel;
        
        #line default
        #line hidden
        
        
        #line 512 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer RevenueTypeChart;
        
        #line default
        #line hidden
        
        
        #line 513 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel RevenueTypeChartPanel;
        
        #line default
        #line hidden
        
        
        #line 523 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ProfitabilitySummaryPanel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/pages/financialpagenew.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AddExpenseButton = ((System.Windows.Controls.Button)(target));
            
            #line 122 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
            this.AddExpenseButton.Click += new System.Windows.RoutedEventHandler(this.AddExpenseButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.AddRevenueButton = ((System.Windows.Controls.Button)(target));
            
            #line 130 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
            this.AddRevenueButton.Click += new System.Windows.RoutedEventHandler(this.AddRevenueButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.GenerateReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 138 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
            this.GenerateReportButton.Click += new System.Windows.RoutedEventHandler(this.GenerateReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 146 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.TotalRevenuesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.TotalExpensesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.NetProfitTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.PendingRevenuesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.OverdueRevenuesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.ReimbursableExpensesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.PeriodFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 225 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
            this.PeriodFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PeriodFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 12:
            this.StartDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 236 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
            this.StartDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DatePicker_SelectedDateChanged);
            
            #line default
            #line hidden
            return;
            case 13:
            this.ToLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.EndDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 242 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
            this.EndDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DatePicker_SelectedDateChanged);
            
            #line default
            #line hidden
            return;
            case 15:
            this.CategoryFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 248 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
            this.CategoryFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 16:
            this.StatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 259 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
            this.StatusFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 17:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 272 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.PrintButton = ((System.Windows.Controls.Button)(target));
            
            #line 280 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
            this.PrintButton.Click += new System.Windows.RoutedEventHandler(this.PrintButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.ExpensesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 25:
            this.ExpensesStatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.RevenuesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 32:
            this.RevenuesStatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 33:
            this.MonthlyChart = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 34:
            this.MonthlyChartPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 35:
            this.ExpenseCategoryChart = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 36:
            this.ExpenseCategoryChartPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 37:
            this.RevenueTypeChart = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 38:
            this.RevenueTypeChartPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 39:
            this.ProfitabilitySummaryPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 20:
            
            #line 337 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewExpenseButton_Click);
            
            #line default
            #line hidden
            break;
            case 21:
            
            #line 343 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditExpenseButton_Click);
            
            #line default
            #line hidden
            break;
            case 22:
            
            #line 349 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewAttachmentsButton_Click);
            
            #line default
            #line hidden
            break;
            case 23:
            
            #line 355 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PayExpenseButton_Click);
            
            #line default
            #line hidden
            break;
            case 24:
            
            #line 361 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteExpenseButton_Click);
            
            #line default
            #line hidden
            break;
            case 27:
            
            #line 423 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewRevenueButton_Click);
            
            #line default
            #line hidden
            break;
            case 28:
            
            #line 429 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditRevenueButton_Click);
            
            #line default
            #line hidden
            break;
            case 29:
            
            #line 435 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SendInvoiceButton_Click);
            
            #line default
            #line hidden
            break;
            case 30:
            
            #line 441 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CollectRevenueButton_Click);
            
            #line default
            #line hidden
            break;
            case 31:
            
            #line 447 "..\..\..\..\..\Views\Pages\FinancialPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteRevenueButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

