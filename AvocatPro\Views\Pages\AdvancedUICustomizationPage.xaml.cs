using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Threading.Tasks;
using AvocatPro.Services;

namespace AvocatPro.Views.Pages
{
    public partial class AdvancedUICustomizationPage : Page
    {
        private const string DefaultTheme = "Light";
        private const string DefaultLayout = "Default";
        private const string DefaultFont = "Segoe UI";

        private readonly ThemeManager _themeManager;
        private readonly UICustomizationManager _uiManager;
        private readonly PerformanceOptimizer _performanceOptimizer;
        private readonly AnimationService _animationService;

        public AdvancedUICustomizationPage()
        {
            InitializeComponent();
            _themeManager = ThemeManager.Instance;
            _uiManager = UICustomizationManager.Instance;
            _performanceOptimizer = PerformanceOptimizer.Instance;
            _animationService = new AnimationService();
            
            InitializeControls();
            LoadCurrentSettings();
            SetupEventHandlers();
        }

        private void InitializeControls()
        {
            // تحسين أداء العناصر
            _performanceOptimizer.OptimizeUIElement(this);
            
            // تفعيل الافتراضية للعناصر
            _performanceOptimizer.EnableVirtualization(ThemeComboBox);
            _performanceOptimizer.EnableVirtualization(LayoutComboBox);
        }

        private void LoadCurrentSettings()
        {
            try
            {
                // تحميل إعدادات الثيم
                var currentTheme = _themeManager.GetCurrentTheme();
                ThemeComboBox.SelectedIndex = GetThemeIndex(currentTheme.Name);
                AutoThemeCheckBox.IsChecked = _themeManager.IsAutoMode;
                
                // تحميل إعدادات التخطيط
                var currentLayout = _uiManager.GetCurrentLayout();
                LayoutComboBox.SelectedIndex = GetLayoutIndex(currentLayout.Id);
                
                // تحميل إعدادات الخطوط
                LoadFontSettings();
                
                // تحميل إعدادات الرسوم المتحركة
                LoadAnimationSettings();
                
                // تحديث المعاينات
                UpdateThemePreview();
                UpdateLayoutPreview();
                UpdateFontPreview();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadFontSettings()
        {
            // تحميل إعدادات الخطوط من الإعدادات المحفوظة
            PrimaryFontComboBox.SelectedIndex = 0; // DefaultFont افتراضي
            HeaderFontComboBox.SelectedIndex = 0;
            PrimaryFontSizeSlider.Value = 14;
            HeaderFontSizeSlider.Value = 24;
        }

        private void LoadAnimationSettings()
        {
            // تحميل إعدادات الرسوم المتحركة
            EnableAnimationsCheckBox.IsChecked = true;
            EnableTransitionsCheckBox.IsChecked = true;
            EnableHoverEffectsCheckBox.IsChecked = true;
            AnimationSpeedComboBox.SelectedIndex = 1; // عادي
            TransitionTypeComboBox.SelectedIndex = 0; // انزلاق
        }

        private void SetupEventHandlers()
        {
            // معالجات أحداث الخطوط
            PrimaryFontSizeSlider.ValueChanged += (s, e) => 
            {
                PrimaryFontSizeLabel.Text = $"{(int)e.NewValue}px";
                UpdateFontPreview();
            };
            
            HeaderFontSizeSlider.ValueChanged += (s, e) => 
            {
                HeaderFontSizeLabel.Text = $"{(int)e.NewValue}px";
                UpdateFontPreview();
            };

            PrimaryFontComboBox.SelectionChanged += (s, e) => UpdateFontPreview();
            HeaderFontComboBox.SelectionChanged += (s, e) => UpdateFontPreview();

            // معالجات أحداث الثيم
            _themeManager.ThemeChanged += OnThemeChanged;
            
            // معالجات أحداث التخطيط
            _uiManager.LayoutChanged += OnLayoutChanged;
        }

        private void OnThemeChanged(object sender, ThemeChangedEventArgs e)
        {
            UpdateThemePreview();
        }

        private void OnLayoutChanged(object sender, LayoutChangedEventArgs e)
        {
            UpdateLayoutPreview();
        }

        #region معالجات الأحداث

        private async void Theme_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ThemeComboBox.SelectedItem is ComboBoxItem)
            {
                var themeName = GetThemeNameFromIndex(ThemeComboBox.SelectedIndex);
                await _themeManager.ApplyThemeAsync(themeName, true);
            }
        }

        private async void Layout_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (LayoutComboBox.SelectedItem is ComboBoxItem)
            {
                var layoutId = GetLayoutIdFromIndex(LayoutComboBox.SelectedIndex);
                await _uiManager.ApplyLayoutAsync(layoutId);
            }
        }

        private async void SaveChanges_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SaveChangesButton.IsEnabled = false;
                SaveChangesButton.Content = "⏳ جاري الحفظ...";

                // حفظ إعدادات الثيم
                await _themeManager.SetAutoMode(AutoThemeCheckBox.IsChecked ?? false);
                
                // حفظ إعدادات الخطوط
                await SaveFontSettings();
                
                // حفظ إعدادات الرسوم المتحركة
                await SaveAnimationSettings();

                MessageBox.Show("تم حفظ جميع التغييرات بنجاح!", "نجح الحفظ", 
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التغييرات: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                SaveChangesButton.IsEnabled = true;
                SaveChangesButton.Content = "💾 حفظ التغييرات";
            }
        }

        private async void ResetToDefault_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد استعادة جميع إعدادات الواجهة إلى القيم الافتراضية؟", 
                                       "تأكيد الاستعادة", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    ResetToDefaultButton.IsEnabled = false;
                    ResetToDefaultButton.Content = "⏳ جاري الاستعادة...";

                    // استعادة الثيم الافتراضي
                    await _themeManager.ApplyThemeAsync(DefaultTheme);

                    // استعادة التخطيط الافتراضي
                    await _uiManager.ApplyLayoutAsync(DefaultLayout);
                    
                    // إعادة تحميل الإعدادات
                    LoadCurrentSettings();

                    MessageBox.Show("تم استعادة الإعدادات الافتراضية بنجاح!", "تمت الاستعادة", 
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في استعادة الإعدادات: {ex.Message}", "خطأ", 
                                   MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    ResetToDefaultButton.IsEnabled = true;
                    ResetToDefaultButton.Content = "🔄 استعادة الافتراضي";
                }
            }
        }

        private void CreateCustomTheme_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var customThemeWindow = new CustomThemeCreatorWindow();
                customThemeWindow.Owner = Window.GetWindow(this);
                customThemeWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح منشئ الثيم المخصص: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CustomizeLayout_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var layoutCustomizerWindow = new LayoutCustomizerWindow();
                layoutCustomizerWindow.Owner = Window.GetWindow(this);
                layoutCustomizerWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح مخصص التخطيط: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void PreviewTransition_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var animationType = GetSelectedAnimationType();
                
                // إنشاء عنصر تجريبي للمعاينة
                var previewElement = new Border
                {
                    Width = 200,
                    Height = 100,
                    Background = new SolidColorBrush(Colors.LightBlue),
                    CornerRadius = new CornerRadius(8),
                    Child = new TextBlock
                    {
                        Text = "معاينة الانتقال",
                        HorizontalAlignment = HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Center,
                        FontSize = 14,
                        FontWeight = FontWeights.Bold
                    }
                };

                // إضافة العنصر مؤقتاً للمعاينة
                var parentGrid = (Grid)this.Parent;
                parentGrid.Children.Add(previewElement);
                
                // تشغيل الرسم المتحرك
                await _animationService.AnimatePageTransition(null, previewElement, animationType);
                
                // إزالة العنصر بعد المعاينة
                await Task.Delay(2000);
                parentGrid.Children.Remove(previewElement);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معاينة الانتقال: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region الوظائف المساعدة

        private void UpdateThemePreview()
        {
            var currentTheme = _themeManager.GetCurrentTheme();
            
            // تحديث معاينة الألوان
            var colorBorders = LayoutPreviewGrid.Children.OfType<Border>().ToArray();
            if (colorBorders.Length >= 4)
            {
                colorBorders[0].Background = new SolidColorBrush(currentTheme.PrimaryColor);
                colorBorders[1].Background = new SolidColorBrush(currentTheme.SuccessColor);
                colorBorders[2].Background = new SolidColorBrush(currentTheme.WarningColor);
                colorBorders[3].Background = new SolidColorBrush(currentTheme.ErrorColor);
            }
        }

        private void UpdateLayoutPreview()
        {
            // تحديث معاينة التخطيط
            LayoutPreviewGrid.Children.Clear();
            
            var currentLayout = _uiManager.GetCurrentLayout();
            var maxX = currentLayout.Widgets.Max(w => w.X + w.Width);
            var maxY = currentLayout.Widgets.Max(w => w.Y + w.Height);
            
            // إنشاء شبكة للمعاينة
            for (int i = 0; i < maxX; i++)
                LayoutPreviewGrid.ColumnDefinitions.Add(new ColumnDefinition());
            for (int i = 0; i < maxY; i++)
                LayoutPreviewGrid.RowDefinitions.Add(new RowDefinition());
            
            // إضافة الويدجت للمعاينة
            foreach (var widget in currentLayout.Widgets)
            {
                var previewWidget = new Border
                {
                    Background = new SolidColorBrush(Color.FromRgb(59, 130, 246)),
                    CornerRadius = new CornerRadius(2),
                    Margin = new Thickness(1),
                    Child = new TextBlock
                    {
                        Text = widget.WidgetId.Substring(0, Math.Min(widget.WidgetId.Length, 3)),
                        HorizontalAlignment = HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Center,
                        Foreground = Brushes.White,
                        FontSize = 8
                    }
                };
                
                Grid.SetColumn(previewWidget, widget.X);
                Grid.SetRow(previewWidget, widget.Y);
                Grid.SetColumnSpan(previewWidget, widget.Width);
                Grid.SetRowSpan(previewWidget, widget.Height);
                
                LayoutPreviewGrid.Children.Add(previewWidget);
            }
        }

        private void UpdateFontPreview()
        {
            try
            {
                var primaryFont = (PrimaryFontComboBox.SelectedItem as ComboBoxItem)?.Content.ToString() ?? DefaultFont;
                var headerFont = (HeaderFontComboBox.SelectedItem as ComboBoxItem)?.Content.ToString() ?? DefaultFont;
                var primarySize = (int)PrimaryFontSizeSlider.Value;
                var headerSize = (int)HeaderFontSizeSlider.Value;

                TextPreview.FontFamily = new FontFamily(primaryFont);
                TextPreview.FontSize = primarySize;
                
                HeaderPreview.FontFamily = new FontFamily(headerFont);
                HeaderPreview.FontSize = headerSize;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث معاينة الخط: {ex.Message}");
            }
        }

        private async Task SaveFontSettings()
        {
            // حفظ إعدادات الخطوط
            await Task.Delay(100); // محاكاة حفظ
        }

        private async Task SaveAnimationSettings()
        {
            // حفظ إعدادات الرسوم المتحركة
            await Task.Delay(100); // محاكاة حفظ
        }

        private int GetThemeIndex(string themeName)
        {
            return themeName switch
            {
                var name when name == DefaultTheme => 0,
                "Dark" => 1,
                "Professional" => 2,
                "Elegant" => 3,
                _ => 0
            };
        }

        private string GetThemeNameFromIndex(int index)
        {
            return index switch
            {
                0 => DefaultTheme,
                1 => "Dark",
                2 => "Professional",
                3 => "Elegant",
                _ => DefaultTheme
            };
        }

        private int GetLayoutIndex(string layoutId)
        {
            return layoutId switch
            {
                var id when id == DefaultLayout => 0,
                "Compact" => 1,
                "Expanded" => 2,
                _ => 0
            };
        }

        private string GetLayoutIdFromIndex(int index)
        {
            return index switch
            {
                0 => DefaultLayout,
                1 => "Compact",
                2 => "Expanded",
                _ => DefaultLayout
            };
        }

        private AnimationType GetSelectedAnimationType()
        {
            return TransitionTypeComboBox.SelectedIndex switch
            {
                0 => AnimationType.Slide,
                1 => AnimationType.Fade,
                2 => AnimationType.Scale,
                3 => AnimationType.Flip,
                4 => AnimationType.Bounce,
                _ => AnimationType.Slide
            };
        }

        #endregion
    }

    // نوافذ مؤقتة للتخصيص المتقدم
    public class CustomThemeCreatorWindow : Window
    {
        public CustomThemeCreatorWindow()
        {
            Title = "إنشاء ثيم مخصص";
            Width = 600;
            Height = 400;
            WindowStartupLocation = WindowStartupLocation.CenterOwner;
            Content = new TextBlock
            {
                Text = "منشئ الثيم المخصص - قيد التطوير",
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                FontSize = 16
            };
        }
    }

    public class LayoutCustomizerWindow : Window
    {
        public LayoutCustomizerWindow()
        {
            Title = "تخصيص التخطيط";
            Width = 800;
            Height = 600;
            WindowStartupLocation = WindowStartupLocation.CenterOwner;
            Content = new TextBlock
            {
                Text = "مخصص التخطيط - قيد التطوير",
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                FontSize = 16
            };
        }
    }
}
