using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using AvocatPro.Models;

namespace AvocatPro.Views.Windows;

public partial class AddSessionWindow : Window
{
    public Session? NewSession { get; private set; }
    public bool SaveAndAddAnother { get; private set; }

    public AddSessionWindow()
    {
        InitializeComponent();
        InitializeForm();
    }

    private void InitializeForm()
    {
        LoadCases();
        LoadTimeComboBoxes();
        SetDefaultValues();
    }

    private void LoadCases()
    {
        // تحميل قائمة القضايا - في التطبيق الحقيقي من قاعدة البيانات
        CaseComboBox.Items.Clear();
        CaseComboBox.Items.Add(new ComboBoxItem { Content = "دعوى مطالبة مالية - CASE-20241201-001", Tag = "1" });
        CaseComboBox.Items.Add(new ComboBoxItem { Content = "قضية عمالية - فصل تعسفي - CASE-20241215-002", Tag = "2" });
        CaseComboBox.Items.Add(new ComboBoxItem { Content = "قضية تجارية - نزاع شراكة - CASE-20241101-003", Tag = "3" });
        CaseComboBox.Items.Add(new ComboBoxItem { Content = "قضية أسرة - نفقة - CASE-20241120-004", Tag = "4" });
        CaseComboBox.Items.Add(new ComboBoxItem { Content = "قضية عقارية - منازعة ملكية - CASE-20241210-005", Tag = "5" });
        CaseComboBox.Items.Add(new ComboBoxItem { Content = "قضية جنائية - اختلاس - CASE-20241205-006", Tag = "6" });
        
        if (CaseComboBox.Items.Count > 0)
            CaseComboBox.SelectedIndex = 0;
    }

    private void LoadTimeComboBoxes()
    {
        // تحميل الساعات (8 صباحاً إلى 6 مساءً)
        HourComboBox.Items.Clear();
        NextHourComboBox.Items.Clear();
        for (int hour = 8; hour <= 18; hour++)
        {
            var hourText = hour.ToString("00");
            HourComboBox.Items.Add(new ComboBoxItem { Content = hourText, Tag = hour });
            NextHourComboBox.Items.Add(new ComboBoxItem { Content = hourText, Tag = hour });
        }

        // تحميل الدقائق (كل 15 دقيقة)
        MinuteComboBox.Items.Clear();
        NextMinuteComboBox.Items.Clear();
        for (int minute = 0; minute < 60; minute += 15)
        {
            var minuteText = minute.ToString("00");
            MinuteComboBox.Items.Add(new ComboBoxItem { Content = minuteText, Tag = minute });
            NextMinuteComboBox.Items.Add(new ComboBoxItem { Content = minuteText, Tag = minute });
        }

        // تعيين القيم الافتراضية
        HourComboBox.SelectedIndex = 1; // 9 صباحاً
        MinuteComboBox.SelectedIndex = 0; // 00 دقيقة
        NextHourComboBox.SelectedIndex = 1;
        NextMinuteComboBox.SelectedIndex = 0;
    }

    private void SetDefaultValues()
    {
        SessionDatePicker.SelectedDate = DateTime.Now.AddDays(7); // الأسبوع القادم
        NextSessionDatePicker.SelectedDate = DateTime.Now.AddDays(14); // بعد أسبوعين
    }

    private void SaveButton_Click(object sender, RoutedEventArgs e)
    {
        if (ValidateForm())
        {
            CreateSession();
            SaveAndAddAnother = false;
            DialogResult = true;
            Close();
        }
    }

    private void SaveAndNewButton_Click(object sender, RoutedEventArgs e)
    {
        if (ValidateForm())
        {
            CreateSession();
            SaveAndAddAnother = true;
            MessageBox.Show("تم حفظ الجلسة بنجاح!", "نجح الحفظ", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
            
            // مسح النموذج لإضافة جلسة جديدة
            ClearForm();
            SetDefaultValues();
        }
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("هل تريد إلغاء العملية؟\nسيتم فقدان جميع البيانات المدخلة.", 
                                   "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Question);
        
        if (result == MessageBoxResult.Yes)
        {
            DialogResult = false;
            Close();
        }
    }

    private bool ValidateForm()
    {
        var errors = new System.Collections.Generic.List<string>();

        if (CaseComboBox.SelectedItem == null)
            errors.Add("يجب اختيار القضية المرتبطة");

        if (!SessionDatePicker.SelectedDate.HasValue)
            errors.Add("يجب تحديد تاريخ الجلسة");

        if (HourComboBox.SelectedItem == null || MinuteComboBox.SelectedItem == null)
            errors.Add("يجب تحديد وقت الجلسة");

        // التحقق من صحة المدة
        if (!string.IsNullOrWhiteSpace(DurationTextBox.Text))
        {
            if (!int.TryParse(DurationTextBox.Text, out var duration) || duration <= 0)
                errors.Add("مدة الجلسة يجب أن تكون رقماً صحيحاً أكبر من صفر");
        }

        // التحقق من صحة المصاريف
        if (!string.IsNullOrWhiteSpace(ExpensesTextBox.Text))
        {
            if (!decimal.TryParse(ExpensesTextBox.Text, out var expenses) || expenses < 0)
                errors.Add("المصاريف يجب أن تكون رقماً صحيحاً أكبر من أو يساوي صفر");
        }

        if (errors.Count > 0)
        {
            MessageBox.Show($"يرجى تصحيح الأخطاء التالية:\n\n{string.Join("\n", errors)}", 
                           "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
            return false;
        }

        return true;
    }

    private void CreateSession()
    {
        var sessionDate = SessionDatePicker.SelectedDate!.Value;
        var hour = (int)((ComboBoxItem)HourComboBox.SelectedItem).Tag;
        var minute = (int)((ComboBoxItem)MinuteComboBox.SelectedItem).Tag;
        var sessionTime = new TimeSpan(hour, minute, 0);

        TimeSpan? nextSessionTime = null;
        if (NextHourComboBox.SelectedItem != null && NextMinuteComboBox.SelectedItem != null)
        {
            var nextHour = (int)((ComboBoxItem)NextHourComboBox.SelectedItem).Tag;
            var nextMinute = (int)((ComboBoxItem)NextMinuteComboBox.SelectedItem).Tag;
            nextSessionTime = new TimeSpan(nextHour, nextMinute, 0);
        }

        NewSession = new Session
        {
            CaseId = int.Parse((string)((ComboBoxItem)CaseComboBox.SelectedItem).Tag),
            SessionDate = sessionDate,
            SessionTime = sessionTime,
            Type = Enum.Parse<SessionType>((string)((ComboBoxItem)SessionTypeComboBox.SelectedItem).Tag),
            ProcedureType = ((ComboBoxItem)ProcedureTypeComboBox.SelectedItem).Content.ToString(),
            Decision = DecisionTextBox.Text.Trim(),
            NextSessionDate = NextSessionDatePicker.SelectedDate,
            NextSessionTime = nextSessionTime,
            Status = Enum.Parse<SessionStatus>((string)((ComboBoxItem)StatusComboBox.SelectedItem).Tag),
            AssignedLawyerId = (LawyerComboBox.SelectedItem as ComboBoxItem)?.Tag != null ? 
                              int.Parse((string)((ComboBoxItem)LawyerComboBox.SelectedItem).Tag) : null,
            Judge = JudgeTextBox.Text.Trim(),
            CourtRoom = CourtRoomTextBox.Text.Trim(),
            SessionNotes = NotesTextBox.Text.Trim(),
            Attendance = AttendanceTextBox.Text.Trim(),
            Arguments = ArgumentsTextBox.Text.Trim(),
            Evidence = EvidenceTextBox.Text.Trim(),
            Duration = int.TryParse(DurationTextBox.Text, out var duration) ? duration : null,
            Expenses = decimal.TryParse(ExpensesTextBox.Text, out var expenses) ? expenses : null,
            CreatedAt = DateTime.Now,
            UpdatedAt = DateTime.Now
        };
    }

    private void ClearForm()
    {
        DecisionTextBox.Clear();
        JudgeTextBox.Clear();
        CourtRoomTextBox.Clear();
        NotesTextBox.Clear();
        AttendanceTextBox.Clear();
        ArgumentsTextBox.Clear();
        EvidenceTextBox.Clear();
        DurationTextBox.Clear();
        ExpensesTextBox.Clear();
        
        SessionTypeComboBox.SelectedIndex = 0;
        ProcedureTypeComboBox.SelectedIndex = 0;
        StatusComboBox.SelectedIndex = 0;
        CaseComboBox.SelectedIndex = 0;
        LawyerComboBox.SelectedIndex = -1;
        
        NextSessionDatePicker.SelectedDate = null;
        NextHourComboBox.SelectedIndex = -1;
        NextMinuteComboBox.SelectedIndex = -1;
    }
}
