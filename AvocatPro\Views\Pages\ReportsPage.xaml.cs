using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using AvocatPro.Models;
using AvocatPro.Data;
using Microsoft.EntityFrameworkCore;
using System.Collections.ObjectModel;

namespace AvocatPro.Views.Pages;

public partial class ReportsPage : Page
{
    private readonly User _currentUser;
    private DateTime _periodStart;
    private DateTime _periodEnd;
    private ComprehensiveReportModel _currentReport;

    public ReportsPage(User currentUser)
    {
        InitializeComponent();
        _currentUser = currentUser;

        // تهيئة التقرير مع جميع الخصائص
        _currentReport = new ComprehensiveReportModel();

        SetCurrentPeriod();
        LoadReportsData();
        BuildDashboard();
    }

    private void SetCurrentPeriod()
    {
        var now = DateTime.Now;
        _periodStart = new DateTime(now.Year, now.Month, 1);
        _periodEnd = _periodStart.AddMonths(1).AddDays(-1);
    }

    private void LoadReportsData()
    {
        try
        {
            // التأكد من تهيئة التقرير
            if (_currentReport == null)
            {
                _currentReport = new ComprehensiveReportModel();
            }

            // تهيئة التقرير أولاً
            _currentReport.PeriodStart = _periodStart;
            _currentReport.PeriodEnd = _periodEnd;

            // استخدام بيانات تجريبية بدلاً من قاعدة البيانات
            LoadSampleFinancialData();
            LoadSampleCasesData();
            LoadSampleClientsData();
            LoadSampleAppointmentsData();
            CalculatePerformanceMetrics();
            UpdateKPIs();
            BuildCharts();
            LoadAlerts();
            LoadQuickStats();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل بيانات التقارير: {ex.Message}\n\nStack Trace: {ex.StackTrace}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void LoadSampleFinancialData()
    {
        try
        {
            // بيانات مالية تجريبية
            _currentReport.Financial = new FinancialSummary
            {
                TotalRevenues = 285000m,
                TotalExpenses = 165000m,
                PendingRevenues = 45000m,
                OverdueRevenues = 12000m,
                ReimbursableExpenses = 8500m,
                TotalTransactions = 156,
                AverageTransactionValue = 2885m,
                LargestTransaction = 25000m,
                SmallestTransaction = 150m
            };

            _currentReport.Financial.NetProfit = _currentReport.Financial.TotalRevenues - _currentReport.Financial.TotalExpenses;
            _currentReport.Financial.ProfitMargin = _currentReport.Financial.TotalRevenues > 0
                ? (_currentReport.Financial.NetProfit / _currentReport.Financial.TotalRevenues) * 100
                : 0;
        }
        catch (Exception ex)
        {
            throw new Exception($"خطأ في تحميل البيانات المالية: {ex.Message}", ex);
        }
    }

    private void LoadSampleCasesData()
    {
        // بيانات قضايا تجريبية
        _currentReport.Cases = new CasesSummary
        {
            TotalCases = 127,
            ActiveCases = 45,
            ClosedCases = 82,
            WonCases = 68,
            LostCases = 14,
            PendingCases = 12,
            NewCasesThisPeriod = 18,
            ClosedCasesThisPeriod = 22,
            TotalCaseValue = 1850000m,
            AverageCaseDuration = 145m
        };

        var completedCases = _currentReport.Cases.WonCases + _currentReport.Cases.LostCases;
        _currentReport.Cases.SuccessRate = completedCases > 0
            ? (_currentReport.Cases.WonCases / (decimal)completedCases) * 100
            : 0;
    }

    private void LoadSampleClientsData()
    {
        // بيانات موكلين تجريبية
        _currentReport.Clients = new ClientsSummary
        {
            TotalClients = 89,
            ActiveClients = 76,
            NewClients = 15,
            IndividualClients = 62,
            CorporateClients = 27,
            ClientsWithMultipleCases = 34,
            ClientRetentionRate = 87.5m,
            ClientSatisfactionRate = 94.2m,
            AverageClientValue = 20750m,
            TopClientValue = 125000m
        };
    }

    private void LoadSampleAppointmentsData()
    {
        // بيانات مواعيد تجريبية
        _currentReport.Appointments = new AppointmentsSummary
        {
            TotalAppointments = 156,
            CompletedAppointments = 142,
            CancelledAppointments = 8,
            UpcomingAppointments = 6,
            OverdueAppointments = 2,
            ConsultationAppointments = 89,
            CourtAppointments = 45,
            ClientMeetings = 22,
            AverageAppointmentDuration = 75
        };

        var totalScheduled = _currentReport.Appointments.CompletedAppointments + _currentReport.Appointments.CancelledAppointments;
        _currentReport.Appointments.AttendanceRate = totalScheduled > 0
            ? (_currentReport.Appointments.CompletedAppointments / (decimal)totalScheduled) * 100
            : 0;
    }

    private void CalculatePerformanceMetrics()
    {
        _currentReport.Performance = new PerformanceMetrics
        {
            ProductivityIndex = CalculateProductivityIndex(),
            EfficiencyRating = CalculateEfficiencyRating(),
            QualityScore = CalculateQualityScore(),
            ClientSatisfactionIndex = _currentReport.Clients.ClientSatisfactionRate,
            FinancialHealthScore = CalculateFinancialHealthScore()
        };

        // حساب النتيجة الإجمالية
        _currentReport.Performance.OverallPerformanceScore = (
            _currentReport.Performance.ProductivityIndex +
            _currentReport.Performance.EfficiencyRating +
            _currentReport.Performance.QualityScore +
            _currentReport.Performance.ClientSatisfactionIndex +
            _currentReport.Performance.FinancialHealthScore
        ) / 5;

        // تحديد الدرجة
        _currentReport.Performance.PerformanceGrade = _currentReport.Performance.OverallPerformanceScore switch
        {
            >= 90 => "ممتاز",
            >= 80 => "جيد جداً",
            >= 70 => "جيد",
            >= 60 => "مقبول",
            _ => "يحتاج تحسين"
        };

        // إضافة نقاط القوة ومجالات التحسين
        AddStrengthsAndImprovements();
    }

    private decimal CalculateProductivityIndex()
    {
        var casesPerMonth = _currentReport.Cases.NewCasesThisPeriod;
        var appointmentsPerMonth = _currentReport.Appointments.TotalAppointments;
        var revenuePerMonth = _currentReport.Financial.TotalRevenues;

        // معادلة مبسطة للإنتاجية
        var productivityScore = (casesPerMonth * 10) + (appointmentsPerMonth * 5) + (revenuePerMonth / 1000);
        return Math.Min(100, productivityScore);
    }

    private decimal CalculateEfficiencyRating()
    {
        var attendanceRate = _currentReport.Appointments.AttendanceRate;
        var profitMargin = _currentReport.Financial.ProfitMargin;
        var caseSuccessRate = _currentReport.Cases.SuccessRate;

        return (attendanceRate + Math.Min(100, profitMargin * 2) + caseSuccessRate) / 3;
    }

    private decimal CalculateQualityScore()
    {
        var successRate = _currentReport.Cases.SuccessRate;
        var clientSatisfaction = _currentReport.Clients.ClientSatisfactionRate;
        var retentionRate = _currentReport.Clients.ClientRetentionRate;

        return (successRate + clientSatisfaction + retentionRate) / 3;
    }

    private decimal CalculateFinancialHealthScore()
    {
        var profitMargin = _currentReport.Financial.ProfitMargin;
        var revenueGrowth = 15m; // يمكن حسابها من البيانات التاريخية
        var expenseControl = Math.Max(0, 100 - ((_currentReport.Financial.TotalExpenses / Math.Max(1, _currentReport.Financial.TotalRevenues)) * 100));

        return (Math.Min(100, profitMargin * 2) + Math.Min(100, revenueGrowth * 2) + expenseControl) / 3;
    }

    private void AddStrengthsAndImprovements()
    {
        _currentReport.Performance.Strengths.Clear();
        _currentReport.Performance.ImprovementAreas.Clear();
        _currentReport.Performance.Recommendations.Clear();

        // نقاط القوة
        if (_currentReport.Cases.SuccessRate >= 80)
            _currentReport.Performance.Strengths.Add("معدل نجاح عالي في القضايا");
        
        if (_currentReport.Financial.ProfitMargin >= 20)
            _currentReport.Performance.Strengths.Add("هامش ربح ممتاز");
        
        if (_currentReport.Clients.ClientRetentionRate >= 90)
            _currentReport.Performance.Strengths.Add("معدل احتفاظ عالي بالموكلين");

        // مجالات التحسين
        if (_currentReport.Appointments.AttendanceRate < 80)
            _currentReport.Performance.ImprovementAreas.Add("تحسين معدل حضور المواعيد");
        
        if (_currentReport.Financial.ProfitMargin < 15)
            _currentReport.Performance.ImprovementAreas.Add("تحسين هامش الربح");
        
        if (_currentReport.Cases.SuccessRate < 70)
            _currentReport.Performance.ImprovementAreas.Add("تحسين معدل نجاح القضايا");

        // التوصيات
        _currentReport.Performance.Recommendations.Add("تطوير استراتيجيات تسويقية لجذب موكلين جدد");
        _currentReport.Performance.Recommendations.Add("تحسين كفاءة إدارة الوقت والمواعيد");
        _currentReport.Performance.Recommendations.Add("الاستثمار في التدريب المهني المستمر");
    }

    private void UpdateKPIs()
    {
        try
        {
            if (_currentReport?.Financial != null)
            {
                if (TotalRevenuesKPI != null) TotalRevenuesKPI.Text = _currentReport.Financial.TotalRevenuesDisplay;
                if (TotalExpensesKPI != null) TotalExpensesKPI.Text = _currentReport.Financial.TotalExpensesDisplay;
                if (NetProfitKPI != null) NetProfitKPI.Text = _currentReport.Financial.NetProfitDisplay;
            }

            if (_currentReport?.Cases != null)
            {
                if (TotalCasesKPI != null) TotalCasesKPI.Text = _currentReport.Cases.TotalCases.ToString();
                if (SuccessRateKPI != null) SuccessRateKPI.Text = _currentReport.Cases.SuccessRateDisplay;
            }

            if (_currentReport?.Clients != null)
            {
                if (TotalClientsKPI != null) TotalClientsKPI.Text = _currentReport.Clients.TotalClients.ToString();
            }

            // تحديث الاتجاهات (مؤقتاً بقيم عشوائية)
            UpdateTrendIndicators();
        }
        catch (Exception ex)
        {
            // تسجيل الخطأ بدلاً من رفعه
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث المؤشرات: {ex.Message}");
        }
    }

    private void UpdateTrendIndicators()
    {
        try
        {
            // التحقق من وجود العناصر قبل التحديث
            if (RevenuesTrendText != null) RevenuesTrendText.Text = "+12.5%";
            if (ExpensesTrendText != null) ExpensesTrendText.Text = "-5.2%";
            if (ProfitTrendText != null) ProfitTrendText.Text = "+18.7%";
            if (CasesTrendText != null) CasesTrendText.Text = "****%";
            if (ClientsTrendText != null) ClientsTrendText.Text = "+15.1%";
            if (SuccessTrendText != null) SuccessTrendText.Text = "****%";
        }
        catch (Exception ex)
        {
            // تجاهل أخطاء التحديث المؤقتة
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الاتجاهات: {ex.Message}");
        }
    }

    private void BuildDashboard()
    {
        BuildCharts();
        LoadAlerts();
        LoadQuickStats();
        LoadPerformanceIndicators();
    }

    private void BuildCharts()
    {
        try
        {
            // تعطيل الرسوم البيانية مؤقتاً لحل مشكلة null reference
            // BuildFinancialTrendsChart();
            // BuildCasesPerformanceChart();
            // BuildClientsDistributionChart();

            // إضافة رسالة بسيطة بدلاً من الرسوم البيانية
            AddSimpleChartPlaceholders();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في بناء الرسوم البيانية: {ex.Message}");
        }
    }

    private void AddSimpleChartPlaceholders()
    {
        try
        {
            // إضافة نص بسيط للرسوم البيانية
            if (FinancialTrendsChartPanel != null)
            {
                FinancialTrendsChartPanel.Children.Clear();
                var placeholder = new TextBlock
                {
                    Text = "📈 الرسوم البيانية للاتجاهات المالية\n\nالإيرادات: 285,000 ريال\nالمصاريف: 165,000 ريال\nصافي الربح: 120,000 ريال",
                    FontSize = 14,
                    TextAlignment = TextAlignment.Center,
                    Margin = new Thickness(20),
                    Foreground = new SolidColorBrush(Colors.Gray)
                };
                FinancialTrendsChartPanel.Children.Add(placeholder);
            }

            if (CasesPerformanceChartPanel != null)
            {
                CasesPerformanceChartPanel.Children.Clear();
                var placeholder = new TextBlock
                {
                    Text = "⚖️ أداء القضايا\n\nإجمالي القضايا: 127\nالقضايا النشطة: 45\nمعدل النجاح: 82.9%",
                    FontSize = 14,
                    TextAlignment = TextAlignment.Center,
                    Margin = new Thickness(20),
                    Foreground = new SolidColorBrush(Colors.Gray)
                };
                CasesPerformanceChartPanel.Children.Add(placeholder);
            }

            if (ClientsDistributionChartPanel != null)
            {
                ClientsDistributionChartPanel.Children.Clear();
                var placeholder = new TextBlock
                {
                    Text = "👥 توزيع الموكلين\n\nإجمالي الموكلين: 89\nالأفراد: 62\nالشركات: 27",
                    FontSize = 14,
                    TextAlignment = TextAlignment.Center,
                    Margin = new Thickness(20),
                    Foreground = new SolidColorBrush(Colors.Gray)
                };
                ClientsDistributionChartPanel.Children.Add(placeholder);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في إضافة النصوص البديلة: {ex.Message}");
        }
    }

    private void BuildFinancialTrendsChart()
    {
        try
        {
            if (FinancialTrendsChartPanel == null) return;

            FinancialTrendsChartPanel.Children.Clear();

        // إنشاء رسم بياني بسيط للاتجاهات المالية
        var chartData = new[]
        {
            new { Month = "يناير", Revenue = 45000m, Expense = 32000m },
            new { Month = "فبراير", Revenue = 52000m, Expense = 35000m },
            new { Month = "مارس", Revenue = 48000m, Expense = 33000m },
            new { Month = "أبريل", Revenue = 58000m, Expense = 38000m },
            new { Month = "مايو", Revenue = 62000m, Expense = 41000m },
            new { Month = "يونيو", Revenue = _currentReport.Financial.TotalRevenues, Expense = _currentReport.Financial.TotalExpenses }
        };

        foreach (var data in chartData)
        {
            var monthPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 5, 0, 5) };

            var monthLabel = new TextBlock
            {
                Text = data.Month,
                Width = 80,
                FontWeight = FontWeights.Bold,
                VerticalAlignment = VerticalAlignment.Center
            };

            var revenueBar = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(76, 175, 80)),
                Height = 20,
                Width = (double)(data.Revenue / 1000),
                Margin = new Thickness(5, 0, 5, 0),
                CornerRadius = new CornerRadius(3)
            };

            var expenseBar = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(244, 67, 54)),
                Height = 20,
                Width = (double)(data.Expense / 1000),
                Margin = new Thickness(5, 0, 5, 0),
                CornerRadius = new CornerRadius(3)
            };

            var revenueText = new TextBlock
            {
                Text = $"{data.Revenue:N0} ريال",
                FontSize = 11,
                Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80)),
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(5, 0, 0, 0)
            };

            var expenseText = new TextBlock
            {
                Text = $"{data.Expense:N0} ريال",
                FontSize = 11,
                Foreground = new SolidColorBrush(Color.FromRgb(244, 67, 54)),
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(5, 0, 0, 0)
            };

            monthPanel.Children.Add(monthLabel);
            monthPanel.Children.Add(revenueBar);
            monthPanel.Children.Add(revenueText);
            monthPanel.Children.Add(expenseBar);
            monthPanel.Children.Add(expenseText);

            FinancialTrendsChartPanel.Children.Add(monthPanel);
        }

        // إضافة مفتاح الرسم البياني
        var legendPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 10, 0, 0) };

        var revenueLegend = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 20, 0) };
        revenueLegend.Children.Add(new Border { Background = new SolidColorBrush(Color.FromRgb(76, 175, 80)), Width = 15, Height = 15, CornerRadius = new CornerRadius(3) });
        revenueLegend.Children.Add(new TextBlock { Text = "الإيرادات", Margin = new Thickness(5, 0, 0, 0), FontSize = 12 });

        var expenseLegend = new StackPanel { Orientation = Orientation.Horizontal };
        expenseLegend.Children.Add(new Border { Background = new SolidColorBrush(Color.FromRgb(244, 67, 54)), Width = 15, Height = 15, CornerRadius = new CornerRadius(3) });
        expenseLegend.Children.Add(new TextBlock { Text = "المصاريف", Margin = new Thickness(5, 0, 0, 0), FontSize = 12 });

            legendPanel.Children.Add(revenueLegend);
            legendPanel.Children.Add(expenseLegend);

            FinancialTrendsChartPanel.Children.Add(legendPanel);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في بناء رسم الاتجاهات المالية: {ex.Message}");
        }
    }

    private void BuildCasesPerformanceChart()
    {
        try
        {
            if (CasesPerformanceChartPanel == null) return;

            CasesPerformanceChartPanel.Children.Clear();

        var caseData = new[]
        {
            new { Status = "نشطة", Count = _currentReport.Cases.ActiveCases, Color = Color.FromRgb(33, 150, 243) },
            new { Status = "مغلقة", Count = _currentReport.Cases.ClosedCases, Color = Color.FromRgb(156, 39, 176) },
            new { Status = "مكسوبة", Count = _currentReport.Cases.WonCases, Color = Color.FromRgb(76, 175, 80) },
            new { Status = "مخسورة", Count = _currentReport.Cases.LostCases, Color = Color.FromRgb(244, 67, 54) },
            new { Status = "معلقة", Count = _currentReport.Cases.PendingCases, Color = Color.FromRgb(255, 152, 0) }
        };

        var maxCount = caseData.Max(d => d.Count);
        if (maxCount == 0) maxCount = 1;

        foreach (var data in caseData)
        {
            var casePanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 8, 0, 8) };

            var statusLabel = new TextBlock
            {
                Text = data.Status,
                Width = 80,
                FontWeight = FontWeights.Bold,
                VerticalAlignment = VerticalAlignment.Center
            };

            var progressBar = new Border
            {
                Background = new SolidColorBrush(data.Color),
                Height = 25,
                Width = (double)(data.Count * 200 / maxCount),
                Margin = new Thickness(10, 0, 10, 0),
                CornerRadius = new CornerRadius(5)
            };

            var countText = new TextBlock
            {
                Text = data.Count.ToString(),
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(data.Color),
                VerticalAlignment = VerticalAlignment.Center
            };

            casePanel.Children.Add(statusLabel);
            casePanel.Children.Add(progressBar);
            casePanel.Children.Add(countText);

            CasesPerformanceChartPanel.Children.Add(casePanel);
        }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في بناء رسم أداء القضايا: {ex.Message}");
        }
    }

    private void BuildClientsDistributionChart()
    {
        try
        {
            if (ClientsDistributionChartPanel == null) return;

            ClientsDistributionChartPanel.Children.Clear();

        var clientData = new[]
        {
            new { Type = "أفراد", Count = _currentReport.Clients.IndividualClients, Color = Color.FromRgb(33, 150, 243) },
            new { Type = "شركات", Count = _currentReport.Clients.CorporateClients, Color = Color.FromRgb(156, 39, 176) },
            new { Type = "نشطين", Count = _currentReport.Clients.ActiveClients, Color = Color.FromRgb(76, 175, 80) },
            new { Type = "جدد", Count = _currentReport.Clients.NewClients, Color = Color.FromRgb(255, 152, 0) }
        };

        var totalClients = Math.Max(1, _currentReport.Clients.TotalClients);

        foreach (var data in clientData)
        {
            var clientPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 5, 0, 5) };

            var typeLabel = new TextBlock
            {
                Text = data.Type,
                Width = 60,
                FontWeight = FontWeights.Bold,
                VerticalAlignment = VerticalAlignment.Center,
                FontSize = 12
            };

            var percentage = (double)(data.Count * 100 / totalClients);
            var percentageBar = new Border
            {
                Background = new SolidColorBrush(data.Color),
                Height = 18,
                Width = percentage * 2,
                Margin = new Thickness(10, 0, 10, 0),
                CornerRadius = new CornerRadius(3)
            };

            var percentageText = new TextBlock
            {
                Text = $"{data.Count} ({percentage:F1}%)",
                FontSize = 11,
                Foreground = new SolidColorBrush(data.Color),
                VerticalAlignment = VerticalAlignment.Center
            };

            clientPanel.Children.Add(typeLabel);
            clientPanel.Children.Add(percentageBar);
            clientPanel.Children.Add(percentageText);

            ClientsDistributionChartPanel.Children.Add(clientPanel);
        }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في بناء رسم توزيع الموكلين: {ex.Message}");
        }
    }

    private void LoadAlerts()
    {
        try
        {
            if (AlertsPanel == null) return;

            AlertsPanel.Children.Clear();

        var alerts = new[]
        {
            new { Type = "Warning", Icon = "⚠️", Title = "إيرادات متأخرة", Message = $"لديك {_currentReport.Financial.OverdueRevenuesDisplay} من الإيرادات المتأخرة", Color = "#FF9800" },
            new { Type = "Info", Icon = "ℹ️", Title = "مواعيد قادمة", Message = $"{_currentReport.Appointments.UpcomingAppointments} موعد خلال الأسبوع القادم", Color = "#2196F3" },
            new { Type = "Success", Icon = "✅", Title = "أداء ممتاز", Message = $"معدل النجاح {_currentReport.Cases.SuccessRateDisplay}", Color = "#4CAF50" }
        };

        foreach (var alert in alerts)
        {
            var alertCard = new Border
            {
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FFF3E0")),
                BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString(alert.Color)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(15),
                Margin = new Thickness(0, 5, 0, 5)
            };

            var alertPanel = new StackPanel();

            var headerPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 5) };
            headerPanel.Children.Add(new TextBlock { Text = alert.Icon, FontSize = 16, Margin = new Thickness(0, 0, 8, 0) });
            headerPanel.Children.Add(new TextBlock { Text = alert.Title, FontWeight = FontWeights.Bold, FontSize = 13 });

            var messageText = new TextBlock
            {
                Text = alert.Message,
                FontSize = 11,
                Foreground = new SolidColorBrush(Colors.Gray),
                TextWrapping = TextWrapping.Wrap
            };

            alertPanel.Children.Add(headerPanel);
            alertPanel.Children.Add(messageText);
            alertCard.Child = alertPanel;

            AlertsPanel.Children.Add(alertCard);
        }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحميل التنبيهات: {ex.Message}");
        }
    }

    private void LoadQuickStats()
    {
        try
        {
            if (QuickStatsPanel == null) return;

            QuickStatsPanel.Children.Clear();

        var stats = new[]
        {
            new { Icon = "💼", Title = "متوسط قيمة القضية", Value = (_currentReport.Cases.TotalCaseValue / Math.Max(1, _currentReport.Cases.TotalCases)).ToString("N0") + " ريال", Color = "#9C27B0" },
            new { Icon = "⏱️", Title = "متوسط مدة القضية", Value = _currentReport.Cases.AverageCaseDurationDisplay, Color = "#FF5722" },
            new { Icon = "📅", Title = "معدل الحضور", Value = _currentReport.Appointments.AttendanceRateDisplay, Color = "#607D8B" },
            new { Icon = "💎", Title = "أعلى قيمة موكل", Value = _currentReport.Clients.TopClientValueDisplay, Color = "#795548" }
        };

        foreach (var stat in stats)
        {
            var statCard = new Border
            {
                Background = new SolidColorBrush(Colors.White),
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(15),
                Margin = new Thickness(0, 5, 0, 5),
                Effect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    Color = Colors.Gray,
                    Direction = 270,
                    ShadowDepth = 2,
                    Opacity = 0.2,
                    BlurRadius = 4
                }
            };

            var statPanel = new StackPanel();

            var iconText = new TextBlock
            {
                Text = stat.Icon,
                FontSize = 20,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 5)
            };

            var titleText = new TextBlock
            {
                Text = stat.Title,
                FontSize = 11,
                Foreground = new SolidColorBrush(Colors.Gray),
                HorizontalAlignment = HorizontalAlignment.Center,
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 0, 0, 5)
            };

            var valueText = new TextBlock
            {
                Text = stat.Value,
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(stat.Color)),
                HorizontalAlignment = HorizontalAlignment.Center
            };

            statPanel.Children.Add(iconText);
            statPanel.Children.Add(titleText);
            statPanel.Children.Add(valueText);
            statCard.Child = statPanel;

            QuickStatsPanel.Children.Add(statCard);
        }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الإحصائيات السريعة: {ex.Message}");
        }
    }

    private void LoadPerformanceIndicators()
    {
        PerformanceIndicatorsPanel.Children.Clear();

        var indicators = new[]
        {
            new { Title = "الإنتاجية", Value = _currentReport.Performance.ProductivityIndexDisplay, Color = GetPerformanceColor(_currentReport.Performance.ProductivityIndex) },
            new { Title = "الكفاءة", Value = _currentReport.Performance.EfficiencyRatingDisplay, Color = GetPerformanceColor(_currentReport.Performance.EfficiencyRating) },
            new { Title = "الجودة", Value = _currentReport.Performance.QualityScoreDisplay, Color = GetPerformanceColor(_currentReport.Performance.QualityScore) },
            new { Title = "الصحة المالية", Value = _currentReport.Performance.FinancialHealthScoreDisplay, Color = GetPerformanceColor(_currentReport.Performance.FinancialHealthScore) }
        };

        foreach (var indicator in indicators)
        {
            var indicatorPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 8, 0, 8) };

            var titleText = new TextBlock
            {
                Text = indicator.Title,
                Width = 80,
                FontSize = 12,
                VerticalAlignment = VerticalAlignment.Center
            };

            var valueText = new TextBlock
            {
                Text = indicator.Value,
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(indicator.Color)),
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(10, 0, 0, 0)
            };

            indicatorPanel.Children.Add(titleText);
            indicatorPanel.Children.Add(valueText);

            PerformanceIndicatorsPanel.Children.Add(indicatorPanel);
        }

        // إضافة النتيجة الإجمالية
        var overallPanel = new StackPanel { Margin = new Thickness(0, 15, 0, 0) };

        var overallTitle = new TextBlock
        {
            Text = "التقييم الإجمالي",
            FontSize = 14,
            FontWeight = FontWeights.Bold,
            HorizontalAlignment = HorizontalAlignment.Center
        };

        var overallScore = new TextBlock
        {
            Text = _currentReport.Performance.OverallPerformanceScoreDisplay,
            FontSize = 20,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(_currentReport.Performance.PerformanceColor)),
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 5, 0, 0)
        };

        var overallGrade = new TextBlock
        {
            Text = _currentReport.Performance.PerformanceGrade + " " + _currentReport.Performance.PerformanceIcon,
            FontSize = 16,
            FontWeight = FontWeights.Bold,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 5, 0, 0)
        };

        overallPanel.Children.Add(overallTitle);
        overallPanel.Children.Add(overallScore);
        overallPanel.Children.Add(overallGrade);

        PerformanceIndicatorsPanel.Children.Add(overallPanel);
    }

    private string GetPerformanceColor(decimal score)
    {
        return score switch
        {
            >= 90 => "#4CAF50",
            >= 75 => "#2196F3",
            >= 60 => "#FF9800",
            _ => "#F44336"
        };
    }

    // معالجات الأحداث
    private void PeriodFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (PeriodFilterComboBox.SelectedItem is ComboBoxItem item && item.Tag is string tag)
        {
            var now = DateTime.Now;

            switch (tag)
            {
                case "ThisMonth":
                    _periodStart = new DateTime(now.Year, now.Month, 1);
                    _periodEnd = _periodStart.AddMonths(1).AddDays(-1);
                    break;
                case "LastMonth":
                    _periodStart = new DateTime(now.Year, now.Month, 1).AddMonths(-1);
                    _periodEnd = _periodStart.AddMonths(1).AddDays(-1);
                    break;
                case "Last3Months":
                    _periodStart = new DateTime(now.Year, now.Month, 1).AddMonths(-3);
                    _periodEnd = new DateTime(now.Year, now.Month, 1).AddDays(-1);
                    break;
                case "Last6Months":
                    _periodStart = new DateTime(now.Year, now.Month, 1).AddMonths(-6);
                    _periodEnd = new DateTime(now.Year, now.Month, 1).AddDays(-1);
                    break;
                case "ThisYear":
                    _periodStart = new DateTime(now.Year, 1, 1);
                    _periodEnd = new DateTime(now.Year, 12, 31);
                    break;
                case "LastYear":
                    _periodStart = new DateTime(now.Year - 1, 1, 1);
                    _periodEnd = new DateTime(now.Year - 1, 12, 31);
                    break;
                case "Custom":
                    // يمكن إضافة نافذة لاختيار فترة مخصصة
                    MessageBox.Show("ميزة الفترة المخصصة قيد التطوير", "معلومات",
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
            }

            LoadReportsData();
        }
    }

    private void RefreshButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            LoadReportsData();
            MessageBox.Show("تم تحديث التقارير بنجاح!", "تحديث",
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحديث التقارير: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ExportButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var exportWindow = new Windows.ReportExportWindow(_currentReport);
            exportWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void CustomReportButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // var customReportWindow = new CustomReportBuilderWindow(_currentUser);
            // customReportWindow.ShowDialog();
            MessageBox.Show("ميزة التقارير المخصصة قيد التطوير", "معلومات",
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح منشئ التقارير المخصصة: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void PrintDashboardButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var printDialog = new System.Windows.Controls.PrintDialog();
            if (printDialog.ShowDialog() == true)
            {
                // إنشاء نسخة للطباعة من لوحة المعلومات
                var printDocument = CreatePrintDocument();
                printDialog.PrintVisual(printDocument, "تقرير لوحة المعلومات - AvocatPro");

                MessageBox.Show("تم إرسال التقرير للطابعة بنجاح!", "طباعة",
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private FrameworkElement CreatePrintDocument()
    {
        var printPanel = new StackPanel
        {
            Background = new SolidColorBrush(Colors.White),
            Margin = new Thickness(20)
        };

        // رأس التقرير
        var header = new StackPanel { Margin = new Thickness(0, 0, 0, 30) };
        header.Children.Add(new TextBlock
        {
            Text = "تقرير لوحة المعلومات - AvocatPro",
            FontSize = 24,
            FontWeight = FontWeights.Bold,
            HorizontalAlignment = HorizontalAlignment.Center
        });
        header.Children.Add(new TextBlock
        {
            Text = $"الفترة: {_currentReport.PeriodDisplay}",
            FontSize = 14,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 5, 0, 0)
        });
        header.Children.Add(new TextBlock
        {
            Text = $"تاريخ الإنشاء: {_currentReport.GeneratedAt}",
            FontSize = 12,
            Foreground = new SolidColorBrush(Colors.Gray),
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 5, 0, 0)
        });

        printPanel.Children.Add(header);

        // المؤشرات الرئيسية
        var kpiSection = new StackPanel { Margin = new Thickness(0, 0, 0, 20) };
        kpiSection.Children.Add(new TextBlock
        {
            Text = "المؤشرات الرئيسية",
            FontSize = 18,
            FontWeight = FontWeights.Bold,
            Margin = new Thickness(0, 0, 0, 10)
        });

        var kpiGrid = new Grid();
        kpiGrid.ColumnDefinitions.Add(new ColumnDefinition());
        kpiGrid.ColumnDefinitions.Add(new ColumnDefinition());
        kpiGrid.ColumnDefinitions.Add(new ColumnDefinition());

        var kpiData = new[]
        {
            new { Title = "إجمالي الإيرادات", Value = _currentReport.Financial.TotalRevenuesDisplay },
            new { Title = "إجمالي المصاريف", Value = _currentReport.Financial.TotalExpensesDisplay },
            new { Title = "صافي الربح", Value = _currentReport.Financial.NetProfitDisplay },
            new { Title = "عدد القضايا", Value = _currentReport.Cases.TotalCases.ToString() },
            new { Title = "عدد الموكلين", Value = _currentReport.Clients.TotalClients.ToString() },
            new { Title = "معدل النجاح", Value = _currentReport.Cases.SuccessRateDisplay }
        };

        for (int i = 0; i < kpiData.Length; i++)
        {
            var kpiItem = new StackPanel { Margin = new Thickness(10) };
            kpiItem.Children.Add(new TextBlock
            {
                Text = kpiData[i].Title,
                FontWeight = FontWeights.Bold,
                FontSize = 12
            });
            kpiItem.Children.Add(new TextBlock
            {
                Text = kpiData[i].Value,
                FontSize = 16,
                Margin = new Thickness(0, 5, 0, 0)
            });

            Grid.SetColumn(kpiItem, i % 3);
            Grid.SetRow(kpiItem, i / 3);

            if (i / 3 >= kpiGrid.RowDefinitions.Count)
                kpiGrid.RowDefinitions.Add(new RowDefinition());

            kpiGrid.Children.Add(kpiItem);
        }

        kpiSection.Children.Add(kpiGrid);
        printPanel.Children.Add(kpiSection);

        return printPanel;
    }


}
