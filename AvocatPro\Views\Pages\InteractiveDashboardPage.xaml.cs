using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Shapes;
using AvocatPro.Models;
using AvocatPro.Services;

namespace AvocatPro.Views.Pages
{
    public partial class InteractiveDashboardPage : Page
    {
        private InteractiveDashboard _currentDashboard;
        private bool _isLoading = false;

        public InteractiveDashboardPage(User? currentUser = null)
        {
            InitializeComponent();

            // إنشاء مستخدم افتراضي إذا لم يتم تمرير مستخدم
            if (currentUser == null)
            {
                currentUser = new User
                {
                    Id = 1,
                    Username = "admin",
                    FullName = "المحامي الرئيسي",
                    Email = "<EMAIL>"
                };
            }

            Loaded += InteractiveDashboardPage_Loaded;
        }

        private async void InteractiveDashboardPage_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadDashboardData();
        }

        private async Task LoadDashboardData()
        {
            if (_isLoading) return;

            _isLoading = true;
            ShowLoading(true);

            try
            {
                await Task.Delay(500); // محاكاة تحميل البيانات

                // تحميل البيانات التفاعلية (نسخة مبسطة)
                _currentDashboard = new InteractiveDashboard
                {
                    LiveKPIs = new List<LiveKPI>
                    {
                        new LiveKPI { Name = "إجمالي الإيرادات", Value = 125000, Unit = "درهم", Icon = "💰", Color = "#10B981", Trend = 5.2, LastUpdated = DateTime.Now },
                        new LiveKPI { Name = "القضايا النشطة", Value = 45, Unit = "قضية", Icon = "⚖️", Color = "#3B82F6", Trend = 2.1, LastUpdated = DateTime.Now },
                        new LiveKPI { Name = "معدل النجاح", Value = 87.5, Unit = "%", Icon = "🎯", Color = "#F59E0B", Trend = 1.8, LastUpdated = DateTime.Now },
                        new LiveKPI { Name = "رضا العملاء", Value = 92.3, Unit = "%", Icon = "😊", Color = "#8B5CF6", Trend = 3.2, LastUpdated = DateTime.Now }
                    },
                    InteractiveCharts = new List<InteractiveChart>()
                };

                // عرض البيانات بشكل آمن
                try { DisplayLiveKPIs(); } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"خطأ في DisplayLiveKPIs: {ex.Message}"); }
                try { DisplayInteractiveCharts(); } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"خطأ في DisplayInteractiveCharts: {ex.Message}"); }
                try { DisplayFinancialAnalytics(); } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"خطأ في DisplayFinancialAnalytics: {ex.Message}"); }
                try { DisplayClientStatistics(); } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"خطأ في DisplayClientStatistics: {ex.Message}"); }
                try { DisplaySuccessRates(); } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"خطأ في DisplaySuccessRates: {ex.Message}"); }

                // تشغيل أنيميشن التحديث (إذا كان متوفراً)
                try
                {
                    var storyboard = (Storyboard)FindResource("DataUpdateAnimation");
                    storyboard?.Begin(this);
                }
                catch
                {
                    // تجاهل خطأ الأنيميشن
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}\n\nتفاصيل: {ex.StackTrace}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                ShowLoading(false);
                _isLoading = false;
            }
        }

        private void DisplayLiveKPIs()
        {
            try
            {
                var liveKPIGrid = FindName("LiveKPIGrid") as Panel;
                if (liveKPIGrid != null && _currentDashboard?.LiveKPIs != null)
                {
                    liveKPIGrid.Children.Clear();

                    foreach (var kpi in _currentDashboard.LiveKPIs)
                    {
                        var kpiCard = CreateLiveKPICard(kpi);
                        liveKPIGrid.Children.Add(kpiCard);
                    }
                }
            }
            catch (Exception ex)
            {
                // تجاهل الخطأ وتسجيله
                System.Diagnostics.Debug.WriteLine($"خطأ في عرض KPIs: {ex.Message}");
            }
        }

        private Border CreateLiveKPICard(LiveKPI kpi)
        {
            var card = new Border
            {
                Background = new SolidColorBrush(Colors.White),
                CornerRadius = new CornerRadius(15),
                Padding = new Thickness(20),
                Margin = new Thickness(5),
                Effect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    Color = Colors.Black,
                    Opacity = 0.1,
                    BlurRadius = 10,
                    ShadowDepth = 2
                }
            };

            var stackPanel = new StackPanel();

            // الأيقونة والعنوان
            var headerPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 10)
            };

            var iconText = new TextBlock
            {
                Text = kpi.Icon,
                FontSize = 24,
                Margin = new Thickness(0, 0, 10, 0)
            };

            var titleText = new TextBlock
            {
                Text = kpi.Name,
                FontSize = 14,
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#374151"))
            };

            headerPanel.Children.Add(iconText);
            headerPanel.Children.Add(titleText);
            stackPanel.Children.Add(headerPanel);

            // القيمة المباشرة
            var valueText = new TextBlock
            {
                Text = $"{kpi.Value:N0} {kpi.Unit}",
                FontSize = 28,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(kpi.Color)),
                Margin = new Thickness(0, 0, 0, 5)
            };
            stackPanel.Children.Add(valueText);

            // الاتجاه مع أنيميشن
            var trendPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal
            };

            var trendIcon = new TextBlock
            {
                Text = kpi.Trend >= 0 ? "📈" : "📉",
                FontSize = 16,
                Margin = new Thickness(0, 0, 5, 0)
            };

            var trendText = new TextBlock
            {
                Text = $"{Math.Abs(kpi.Trend):F1}%",
                FontSize = 14,
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush(kpi.Trend >= 0 ? Colors.Green : Colors.Red)
            };

            trendPanel.Children.Add(trendIcon);
            trendPanel.Children.Add(trendText);
            stackPanel.Children.Add(trendPanel);

            // وقت آخر تحديث
            var updateText = new TextBlock
            {
                Text = $"آخر تحديث: {kpi.LastUpdated:HH:mm}",
                FontSize = 11,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#9CA3AF")),
                Margin = new Thickness(0, 5, 0, 0)
            };
            stackPanel.Children.Add(updateText);

            card.Child = stackPanel;

            // تأثير النبض للبيانات المباشرة
            var pulseAnimation = new DoubleAnimation
            {
                From = 1.0,
                To = 0.8,
                Duration = TimeSpan.FromSeconds(1),
                AutoReverse = true,
                RepeatBehavior = RepeatBehavior.Forever
            };
            card.BeginAnimation(OpacityProperty, pulseAnimation);

            return card;
        }

        private void DisplayInteractiveCharts()
        {
            try
            {
                // رسم الرسم البياني الرئيسي
                DrawMainChart();

                // رسم الرسم البياني الدائري
                DrawPieChart();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في عرض الرسوم البيانية: {ex.Message}");
            }
        }

        private void DrawMainChart()
        {
            try
            {
                var mainChart = FindName("MainChart") as Panel;
                if (mainChart != null)
                {
                    mainChart.Children.Clear();

                    if (_currentDashboard?.InteractiveCharts == null || !_currentDashboard.InteractiveCharts.Any())
                        return;

            var revenueChart = _currentDashboard.InteractiveCharts.FirstOrDefault(c => c.Id == "monthly-revenue");
            if (revenueChart?.Data == null || !revenueChart.Data.Any())
                return;

            var data = revenueChart.Data;
            var maxValue = data.Max(d => d.Value);
            var chartWidth = MainChart.ActualWidth > 0 ? MainChart.ActualWidth : 600;
            var chartHeight = MainChart.Height;

            // رسم الخطوط والنقاط
            var pointsCollection = new PointCollection();
            
            for (int i = 0; i < data.Count; i++)
            {
                var x = (chartWidth / (data.Count - 1)) * i;
                var y = chartHeight - (data[i].Value / maxValue * (chartHeight - 50)) - 25;
                pointsCollection.Add(new Point(x, y));

                // إضافة نقطة تفاعلية
                var point = new Ellipse
                {
                    Width = 10,
                    Height = 10,
                    Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#3B82F6")),
                    Stroke = Brushes.White,
                    StrokeThickness = 2
                };
                Canvas.SetLeft(point, x - 5);
                Canvas.SetTop(point, y - 5);
                
                // إضافة تفاعل
                point.MouseEnter += (s, e) =>
                {
                    point.Width = point.Height = 14;
                    Canvas.SetLeft(point, x - 7);
                    Canvas.SetTop(point, y - 7);
                };
                point.MouseLeave += (s, e) =>
                {
                    point.Width = point.Height = 10;
                    Canvas.SetLeft(point, x - 5);
                    Canvas.SetTop(point, y - 5);
                };
                
                MainChart.Children.Add(point);

                // إضافة تسمية
                var label = new TextBlock
                {
                    Text = data[i].Label,
                    FontSize = 10,
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#6B7280"))
                };
                Canvas.SetLeft(label, x - 20);
                Canvas.SetTop(label, chartHeight - 20);
                MainChart.Children.Add(label);
            }

            // رسم الخط مع تدرج
            var polyline = new Polyline
            {
                Points = pointsCollection,
                Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#3B82F6")),
                StrokeThickness = 3,
                Fill = new LinearGradientBrush
                {
                    StartPoint = new Point(0, 0),
                    EndPoint = new Point(0, 1),
                    GradientStops = new GradientStopCollection
                    {
                        new GradientStop { Color = (Color)ColorConverter.ConvertFromString("#3B82F6"), Offset = 0 },
                        new GradientStop { Color = Colors.Transparent, Offset = 1 }
                    }
                }
            };
                    mainChart.Children.Add(polyline);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في رسم الرسم البياني: {ex.Message}");
            }
        }

        private void DrawPieChart()
        {
            try
            {
                var pieChart = FindName("PieChart") as Panel;
                var pieChartLegend = FindName("PieChartLegend") as Panel;

                if (pieChart != null)
                    pieChart.Children.Clear();
                if (pieChartLegend != null)
                    pieChartLegend.Children.Clear();
            
            if (_currentDashboard?.InteractiveCharts == null)
                return;

            var caseChart = _currentDashboard.InteractiveCharts.FirstOrDefault(c => c.Id == "case-distribution");
            if (caseChart?.Data == null || !caseChart.Data.Any())
                return;

            var data = caseChart.Data;
            var total = data.Sum(d => d.Value);
            var centerX = PieChart.ActualWidth > 0 ? PieChart.ActualWidth / 2 : 150;
            var centerY = PieChart.Height / 2;
            var radius = Math.Min(centerX, centerY) - 30;

            var colors = new[]
            {
                "#10B981", "#3B82F6", "#F59E0B", "#EF4444", "#8B5CF6", "#06B6D4"
            };

            double startAngle = 0;
            int colorIndex = 0;

            foreach (var item in data)
            {
                var percentage = item.Value / total;
                var angle = percentage * 360;

                // رسم قطاع الدائرة
                var path = CreateInteractivePieSlice(centerX, centerY, radius, startAngle, angle,
                                                   colors[colorIndex % colors.Length]);
                PieChart.Children.Add(path);

                // إضافة وسيلة إيضاح
                var legendItem = CreateLegendItem(item.Label, colors[colorIndex % colors.Length], percentage);
                if (pieChartLegend != null)
                    pieChartLegend.Children.Add(legendItem);

                startAngle += angle;
                colorIndex++;
            }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في رسم الرسم الدائري: {ex.Message}");
            }
        }

        private Path CreateInteractivePieSlice(double centerX, double centerY, double radius,
                                             double startAngle, double angle, string color)
        {
            var startAngleRad = startAngle * Math.PI / 180;
            var endAngleRad = (startAngle + angle) * Math.PI / 180;

            var x1 = centerX + radius * Math.Cos(startAngleRad);
            var y1 = centerY + radius * Math.Sin(startAngleRad);
            var x2 = centerX + radius * Math.Cos(endAngleRad);
            var y2 = centerY + radius * Math.Sin(endAngleRad);

            var largeArc = angle > 180 ? 1 : 0;
            var pathData = $"M {centerX},{centerY} L {x1},{y1} A {radius},{radius} 0 {largeArc},1 {x2},{y2} Z";

            var path = new Path
            {
                Data = Geometry.Parse(pathData),
                Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
                Stroke = Brushes.White,
                StrokeThickness = 2
            };

            // إضافة تفاعل
            path.MouseEnter += (s, e) =>
            {
                path.Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)) { Opacity = 0.8 };
                path.StrokeThickness = 3;
            };
            path.MouseLeave += (s, e) =>
            {
                path.Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color));
                path.StrokeThickness = 2;
            };

            return path;
        }

        private Border CreateLegendItem(string label, string color, double percentage)
        {
            var item = new Border
            {
                Background = new SolidColorBrush(Colors.Transparent),
                Padding = new Thickness(0, 5, 0, 5),
                Margin = new Thickness(0, 2, 0, 2)
            };

            var panel = new StackPanel
            {
                Orientation = Orientation.Horizontal
            };

            var colorBox = new Border
            {
                Width = 16,
                Height = 16,
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
                CornerRadius = new CornerRadius(3),
                Margin = new Thickness(0, 0, 10, 0)
            };

            var labelText = new TextBlock
            {
                Text = $"{label} ({percentage:P1})",
                FontSize = 12,
                VerticalAlignment = VerticalAlignment.Center
            };

            panel.Children.Add(colorBox);
            panel.Children.Add(labelText);
            item.Child = panel;

            return item;
        }

        private void DisplayFinancialAnalytics()
        {
            // عرض تحليل التدفق النقدي
            DisplayCashFlowAnalysis();
            
            // عرض تحليل الربحية
            DisplayProfitabilityAnalysis();
            
            // عرض التوقعات
            DisplayForecasts();
        }

        private void DisplayCashFlowAnalysis()
        {
            try
            {
                var cashFlowPanel = FindName("CashFlowPanel") as Panel;
                if (cashFlowPanel != null)
                {
                    cashFlowPanel.Children.Clear();

                    // محاكاة بيانات التدفق النقدي
                    var cashFlow = new { TotalInflow = 150000m, TotalOutflow = 120000m, NetCashFlow = 30000m };

                    var inflowItem = CreateFinancialItem("التدفق الداخل", cashFlow.TotalInflow, "#10B981", "💰");
                    var outflowItem = CreateFinancialItem("التدفق الخارج", cashFlow.TotalOutflow, "#EF4444", "💸");
                    var netFlowItem = CreateFinancialItem("صافي التدفق", cashFlow.NetCashFlow,
                                                        cashFlow.NetCashFlow >= 0 ? "#10B981" : "#EF4444", "📊");

                    cashFlowPanel.Children.Add(inflowItem);
                    cashFlowPanel.Children.Add(outflowItem);
                    cashFlowPanel.Children.Add(netFlowItem);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في عرض تحليل التدفق النقدي: {ex.Message}");
            }
        }

        private void DisplayProfitabilityAnalysis()
        {
            try
            {
                var profitabilityPanel = FindName("ProfitabilityPanel") as Panel;
                if (profitabilityPanel != null)
                {
                    profitabilityPanel.Children.Clear();

                    // محاكاة بيانات الربحية
                    var grossProfit = 150000m;
                    var netProfit = 120000m;
                    var profitMargin = 25.5;

                    var grossItem = CreateFinancialItem("إجمالي الربح", grossProfit, "#3B82F6", "📈");
                    var netItem = CreateFinancialItem("صافي الربح", netProfit, "#10B981", "💎");
                    var marginItem = CreateFinancialItem("هامش الربح", (decimal)profitMargin, "#F59E0B", "📊", "%");

                    profitabilityPanel.Children.Add(grossItem);
                    profitabilityPanel.Children.Add(netItem);
                    profitabilityPanel.Children.Add(marginItem);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في عرض تحليل الربحية: {ex.Message}");
            }
        }

        private void DisplayForecasts()
        {
            try
            {
                var forecastPanel = FindName("ForecastPanel") as Panel;
                if (forecastPanel != null)
                {
                    forecastPanel.Children.Clear();

            // محاكاة التوقعات
            var forecasts = new[]
            {
                new { Period = "الشهر القادم", Value = 85000m, Confidence = 92.0 },
                new { Period = "الربع القادم", Value = 250000m, Confidence = 78.0 },
                new { Period = "السنة القادمة", Value = 1200000m, Confidence = 65.0 }
            };

                    foreach (var forecast in forecasts)
                    {
                        var forecastItem = CreateForecastItem(forecast.Period, forecast.Value, forecast.Confidence);
                        forecastPanel.Children.Add(forecastItem);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في عرض التوقعات: {ex.Message}");
            }
        }

        private StackPanel CreateFinancialItem(string label, decimal value, string color, string icon, string unit = "درهم")
        {
            var panel = new StackPanel
            {
                Margin = new Thickness(0, 0, 0, 10)
            };

            var headerPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 5)
            };

            var iconText = new TextBlock
            {
                Text = icon,
                FontSize = 16,
                Margin = new Thickness(0, 0, 8, 0)
            };

            var labelText = new TextBlock
            {
                Text = label,
                FontSize = 13,
                FontWeight = FontWeights.SemiBold
            };

            headerPanel.Children.Add(iconText);
            headerPanel.Children.Add(labelText);

            var valueText = new TextBlock
            {
                Text = unit == "%" ? $"{value:F1}%" : $"{value:N0} {unit}",
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color))
            };

            panel.Children.Add(headerPanel);
            panel.Children.Add(valueText);

            return panel;
        }

        private StackPanel CreateForecastItem(string period, decimal value, double confidence)
        {
            var panel = new StackPanel
            {
                Margin = new Thickness(0, 0, 0, 10)
            };

            var periodText = new TextBlock
            {
                Text = period,
                FontSize = 13,
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 0, 5)
            };

            var valueText = new TextBlock
            {
                Text = $"{value:N0} درهم",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F59E0B"))
            };

            var confidenceText = new TextBlock
            {
                Text = $"الثقة: {confidence:F0}%",
                FontSize = 11,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#6B7280"))
            };

            panel.Children.Add(periodText);
            panel.Children.Add(valueText);
            panel.Children.Add(confidenceText);

            return panel;
        }

        private void DisplayClientStatistics()
        {
            try
            {
                var clientStatsPanel = FindName("ClientStatsPanel") as Panel;
                if (clientStatsPanel != null)
                {
                    clientStatsPanel.Children.Clear();

            var stats = new[]
            {
                new { Label = "إجمالي العملاء", Value = "156", Icon = "👥", Color = "#3B82F6" },
                new { Label = "عملاء نشطون", Value = "89", Icon = "🔥", Color = "#10B981" },
                new { Label = "عملاء جدد", Value = "12", Icon = "🆕", Color = "#F59E0B" },
                new { Label = "معدل الاحتفاظ", Value = "94%", Icon = "💎", Color = "#8B5CF6" }
            };

                    foreach (var stat in stats)
                    {
                        var statItem = CreateStatItem(stat.Label, stat.Value, stat.Icon, stat.Color);
                        clientStatsPanel.Children.Add(statItem);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في عرض إحصائيات العملاء: {ex.Message}");
            }
        }

        private void DisplaySuccessRates()
        {
            try
            {
                var successRatesPanel = FindName("SuccessRatesPanel") as Panel;
                if (successRatesPanel != null)
                {
                    successRatesPanel.Children.Clear();

            var rates = new[]
            {
                new { Type = "قضايا مدنية", Rate = 87.5, Cases = 45 },
                new { Type = "قضايا تجارية", Rate = 92.3, Cases = 32 },
                new { Type = "قضايا أسرة", Rate = 78.9, Cases = 28 },
                new { Type = "قضايا عمالية", Rate = 85.2, Cases = 19 }
            };

                    foreach (var rate in rates)
                    {
                        var rateItem = CreateSuccessRateItem(rate.Type, rate.Rate, rate.Cases);
                        successRatesPanel.Children.Add(rateItem);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في عرض معدلات النجاح: {ex.Message}");
            }
        }

        private StackPanel CreateStatItem(string label, string value, string icon, string color)
        {
            var panel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 15)
            };

            var iconText = new TextBlock
            {
                Text = icon,
                FontSize = 20,
                Margin = new Thickness(0, 0, 15, 0),
                VerticalAlignment = VerticalAlignment.Center
            };

            var textPanel = new StackPanel();

            var valueText = new TextBlock
            {
                Text = value,
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color))
            };

            var labelText = new TextBlock
            {
                Text = label,
                FontSize = 12,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#6B7280"))
            };

            textPanel.Children.Add(valueText);
            textPanel.Children.Add(labelText);

            panel.Children.Add(iconText);
            panel.Children.Add(textPanel);

            return panel;
        }

        private StackPanel CreateSuccessRateItem(string type, double rate, int cases)
        {
            var panel = new StackPanel
            {
                Margin = new Thickness(0, 0, 0, 15)
            };

            var headerPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 5)
            };

            var typeText = new TextBlock
            {
                Text = type,
                FontSize = 13,
                FontWeight = FontWeights.SemiBold
            };

            var casesText = new TextBlock
            {
                Text = $"({cases} قضية)",
                FontSize = 11,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#6B7280")),
                Margin = new Thickness(10, 0, 0, 0)
            };

            headerPanel.Children.Add(typeText);
            headerPanel.Children.Add(casesText);

            var progressBar = new ProgressBar
            {
                Value = rate,
                Maximum = 100,
                Height = 8,
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E5E7EB")),
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#10B981")),
                BorderThickness = new Thickness(0, 0, 0, 0)
            };

            var rateText = new TextBlock
            {
                Text = $"{rate:F1}%",
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#10B981")),
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 5, 0, 0)
            };

            panel.Children.Add(headerPanel);
            panel.Children.Add(progressBar);
            panel.Children.Add(rateText);

            return panel;
        }

        private void ShowLoading(bool show)
        {
            try
            {
                var loadingOverlay = FindName("LoadingOverlay") as FrameworkElement;
                if (loadingOverlay != null)
                    loadingOverlay.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
            }
            catch
            {
                // تجاهل الخطأ إذا لم يوجد العنصر
            }
        }

        private async void Refresh_Click(object sender, RoutedEventArgs e)
        {
            // تشغيل أنيميشن الدوران
            var refreshAnimation = (Storyboard)FindResource("RefreshAnimation");
            refreshAnimation.Begin();
            
            await LoadDashboardData();
        }

        private void Export_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "Excel files (*.xlsx)|*.xlsx|PDF files (*.pdf)|*.pdf",
                    FileName = $"لوحة_التحكم_التفاعلية_{DateTime.Now:yyyy-MM-dd}.xlsx"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    MessageBox.Show("تم تصدير لوحة التحكم بنجاح!", "نجح", 
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void TimeRange_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isLoading) return;
            await LoadDashboardData();
        }

        private void ChartType_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            DrawMainChart();
        }

        private void ShowTrendLine_Changed(object sender, RoutedEventArgs e)
        {
            DrawMainChart();
        }

        private void ShowDataPoints_Changed(object sender, RoutedEventArgs e)
        {
            DrawMainChart();
        }

        private void EnableZoom_Changed(object sender, RoutedEventArgs e)
        {
            // تفعيل/إلغاء تفعيل التكبير التفاعلي
            DrawMainChart();
        }
    }
}
