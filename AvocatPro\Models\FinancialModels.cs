using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AvocatPro.Models;

/// <summary>
/// نموذج المصاريف
/// </summary>
public class Expense : BaseEntity
{
    /// <summary>
    /// رقم المرجع
    /// </summary>
    [Required]
    [StringLength(20)]
    public string Reference { get; set; } = string.Empty;
    
    /// <summary>
    /// وصف المصروف
    /// </summary>
    [Required]
    [StringLength(200)]
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// المبلغ
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,2)")]
    public decimal Amount { get; set; }
    
    /// <summary>
    /// تاريخ المصروف
    /// </summary>
    [Required]
    public DateTime ExpenseDate { get; set; }
    
    /// <summary>
    /// فئة المصروف
    /// </summary>
    public ExpenseCategory Category { get; set; }
    
    /// <summary>
    /// نوع المصروف
    /// </summary>
    public ExpenseType Type { get; set; }
    
    /// <summary>
    /// معرف القضية (اختياري)
    /// </summary>
    public int? CaseId { get; set; }
    
    /// <summary>
    /// معرف الموكل (اختياري)
    /// </summary>
    public int? ClientId { get; set; }
    
    /// <summary>
    /// المورد/المستفيد
    /// </summary>
    [StringLength(100)]
    public string? Vendor { get; set; }
    
    /// <summary>
    /// رقم الفاتورة
    /// </summary>
    [StringLength(50)]
    public string? InvoiceNumber { get; set; }
    
    /// <summary>
    /// طريقة الدفع
    /// </summary>
    public PaymentMethod PaymentMethod { get; set; }
    
    /// <summary>
    /// حالة الدفع
    /// </summary>
    public PaymentStatus PaymentStatus { get; set; } = PaymentStatus.Pending;
    
    /// <summary>
    /// تاريخ الدفع
    /// </summary>
    public DateTime? PaymentDate { get; set; }
    
    /// <summary>
    /// المرفقات (JSON)
    /// </summary>
    public string? Attachments { get; set; }
    
    /// <summary>
    /// قابل للاسترداد من الموكل
    /// </summary>
    public bool IsReimbursable { get; set; } = false;
    
    /// <summary>
    /// تم الاسترداد
    /// </summary>
    public bool IsReimbursed { get; set; } = false;
    
    /// <summary>
    /// تاريخ الاسترداد
    /// </summary>
    public DateTime? ReimbursementDate { get; set; }
    
    // Navigation Properties
    [ForeignKey("CaseId")]
    public virtual Case? Case { get; set; }
    
    [ForeignKey("ClientId")]
    public virtual Client? Client { get; set; }
}

/// <summary>
/// نموذج الإيرادات
/// </summary>
public class Revenue : BaseEntity
{
    /// <summary>
    /// رقم المرجع
    /// </summary>
    [Required]
    [StringLength(20)]
    public string Reference { get; set; } = string.Empty;
    
    /// <summary>
    /// وصف الإيراد
    /// </summary>
    [Required]
    [StringLength(200)]
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// المبلغ
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,2)")]
    public decimal Amount { get; set; }
    
    /// <summary>
    /// تاريخ الإيراد
    /// </summary>
    [Required]
    public DateTime RevenueDate { get; set; }
    
    /// <summary>
    /// نوع الإيراد
    /// </summary>
    public RevenueType Type { get; set; }
    
    /// <summary>
    /// معرف القضية (اختياري)
    /// </summary>
    public int? CaseId { get; set; }
    
    /// <summary>
    /// معرف الموكل
    /// </summary>
    [Required]
    public int ClientId { get; set; }
    
    /// <summary>
    /// رقم الفاتورة
    /// </summary>
    [StringLength(50)]
    public string? InvoiceNumber { get; set; }
    
    /// <summary>
    /// طريقة الدفع
    /// </summary>
    public PaymentMethod PaymentMethod { get; set; }
    
    /// <summary>
    /// حالة الدفع
    /// </summary>
    public PaymentStatus PaymentStatus { get; set; } = PaymentStatus.Pending;
    
    /// <summary>
    /// تاريخ الدفع
    /// </summary>
    public DateTime? PaymentDate { get; set; }
    
    /// <summary>
    /// تاريخ الاستحقاق
    /// </summary>
    public DateTime? DueDate { get; set; }
    
    /// <summary>
    /// المرفقات (JSON)
    /// </summary>
    public string? Attachments { get; set; }
    
    // Navigation Properties
    [ForeignKey("CaseId")]
    public virtual Case? Case { get; set; }
    
    [ForeignKey("ClientId")]
    public virtual Client Client { get; set; } = null!;
}

/// <summary>
/// مصاريف القضية
/// </summary>
public class CaseExpense : BaseEntity
{
    /// <summary>
    /// معرف القضية
    /// </summary>
    [Required]
    public int CaseId { get; set; }
    
    /// <summary>
    /// وصف المصروف
    /// </summary>
    [Required]
    [StringLength(200)]
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// المبلغ
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,2)")]
    public decimal Amount { get; set; }
    
    /// <summary>
    /// تاريخ المصروف
    /// </summary>
    [Required]
    public DateTime ExpenseDate { get; set; }
    
    /// <summary>
    /// نوع المصروف
    /// </summary>
    public CaseExpenseType Type { get; set; }
    
    // Navigation Properties
    [ForeignKey("CaseId")]
    public virtual Case Case { get; set; } = null!;
}

/// <summary>
/// فئات المصاريف
/// </summary>
public enum ExpenseCategory
{
    /// <summary>
    /// مصاريف إدارية
    /// </summary>
    Administrative = 1,
    
    /// <summary>
    /// مصاريف قانونية
    /// </summary>
    Legal = 2,
    
    /// <summary>
    /// مصاريف تشغيلية
    /// </summary>
    Operational = 3,
    
    /// <summary>
    /// مصاريف تقنية
    /// </summary>
    Technology = 4,
    
    /// <summary>
    /// مصاريف تسويق
    /// </summary>
    Marketing = 5,
    
    /// <summary>
    /// مصاريف سفر
    /// </summary>
    Travel = 6
}

/// <summary>
/// أنواع المصاريف
/// </summary>
public enum ExpenseType
{
    /// <summary>
    /// رسوم محكمة
    /// </summary>
    CourtFees = 1,
    
    /// <summary>
    /// رسوم خبرة
    /// </summary>
    ExpertFees = 2,
    
    /// <summary>
    /// مصاريف انتقال
    /// </summary>
    Transportation = 3,
    
    /// <summary>
    /// مصاريف اتصالات
    /// </summary>
    Communication = 4,
    
    /// <summary>
    /// مصاريف طباعة
    /// </summary>
    Printing = 5,
    
    /// <summary>
    /// مصاريف ترجمة
    /// </summary>
    Translation = 6,
    
    /// <summary>
    /// أخرى
    /// </summary>
    Other = 7
}

/// <summary>
/// أنواع الإيرادات
/// </summary>
public enum RevenueType
{
    /// <summary>
    /// أتعاب محاماة
    /// </summary>
    LawyerFees = 1,
    
    /// <summary>
    /// أتعاب استشارة
    /// </summary>
    ConsultationFees = 2,
    
    /// <summary>
    /// أتعاب عقود
    /// </summary>
    ContractFees = 3,
    
    /// <summary>
    /// أتعاب تحكيم
    /// </summary>
    ArbitrationFees = 4,
    
    /// <summary>
    /// أخرى
    /// </summary>
    Other = 5
}

/// <summary>
/// أنواع مصاريف القضية
/// </summary>
public enum CaseExpenseType
{
    /// <summary>
    /// رسوم محكمة
    /// </summary>
    CourtFees = 1,
    
    /// <summary>
    /// رسوم خبرة
    /// </summary>
    ExpertFees = 2,
    
    /// <summary>
    /// مصاريف انتقال
    /// </summary>
    Transportation = 3,
    
    /// <summary>
    /// مصاريف ترجمة
    /// </summary>
    Translation = 4,
    
    /// <summary>
    /// أخرى
    /// </summary>
    Other = 5
}

/// <summary>
/// طرق الدفع
/// </summary>
public enum PaymentMethod
{
    /// <summary>
    /// نقدي
    /// </summary>
    Cash = 1,
    
    /// <summary>
    /// شيك
    /// </summary>
    Check = 2,
    
    /// <summary>
    /// تحويل بنكي
    /// </summary>
    BankTransfer = 3,
    
    /// <summary>
    /// بطاقة ائتمان
    /// </summary>
    CreditCard = 4,
    
    /// <summary>
    /// محفظة إلكترونية
    /// </summary>
    EWallet = 5
}

/// <summary>
/// حالة الدفع
/// </summary>
public enum PaymentStatus
{
    /// <summary>
    /// في الانتظار
    /// </summary>
    Pending = 1,
    
    /// <summary>
    /// مدفوع
    /// </summary>
    Paid = 2,
    
    /// <summary>
    /// مدفوع جزئياً
    /// </summary>
    PartiallyPaid = 3,
    
    /// <summary>
    /// متأخر
    /// </summary>
    Overdue = 4,
    
    /// <summary>
    /// ملغي
    /// </summary>
    Cancelled = 5
}
