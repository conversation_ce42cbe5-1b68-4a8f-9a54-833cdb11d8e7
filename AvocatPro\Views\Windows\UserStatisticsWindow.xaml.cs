using System;
using System.Windows;

namespace AvocatPro.Views.Windows
{

public partial class UserStatisticsWindow : Window
{
    private readonly int _userId;

    public UserStatisticsWindow(int userId)
    {
        InitializeComponent();
        _userId = userId;
        InitializeControls();
        LoadUserStatistics();
    }

    private void InitializeControls()
    {
        // تهيئة فلاتر التاريخ
        FromDatePicker.SelectedDate = DateTime.Now.AddDays(-30);
        ToDatePicker.SelectedDate = DateTime.Now;

        // تهيئة فلاتر الأنشطة
        ActivityTypeFilter.ItemsSource = new[]
        {
            "جميع الأنواع",
            "تسجيل دخول",
            "إنشاء",
            "تعديل",
            "حذف",
            "طباعة",
            "تصدير"
        };
        ActivityTypeFilter.SelectedIndex = 0;

        ActivityLevelFilter.ItemsSource = new[]
        {
            "جميع المستويات",
            "معلومات",
            "تحذير",
            "خطأ",
            "حرج"
        };
        ActivityLevelFilter.SelectedIndex = 0;
    }

    private void LoadUserStatistics()
    {
        UserNameText.Text = $"إحصائيات المستخدم رقم: {_userId}";
        
        // تحميل الإحصائيات (مبسط)
        LoginCountText.Text = "45";
        SessionTimeText.Text = "120";
        ActionsCountText.Text = "234";
        ErrorCountText.Text = "3";

        // تحميل التقييمات
        ProductivityRatingText.Text = "8/10";
        QualityRatingText.Text = "9/10";
        PunctualityRatingText.Text = "7/10";
        CollaborationRatingText.Text = "8/10";

        MessageBox.Show($"تم تحميل إحصائيات المستخدم رقم: {_userId}", "معلومات", 
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void RefreshButton_Click(object sender, RoutedEventArgs e)
    {
        LoadUserStatistics();
        MessageBox.Show("تم تحديث الإحصائيات بنجاح!", "نجح", 
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void AddRatingButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("سيتم إضافة نافذة تقييم الأداء في التحديث القادم", "قريباً",
            MessageBoxButton.OK, MessageBoxImage.Information);
    }
}
}
