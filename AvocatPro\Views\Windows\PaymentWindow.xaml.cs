using System;
using System.Windows;
using System.Windows.Controls;
using AvocatPro.Models;

namespace AvocatPro.Views.Windows;

public partial class PaymentWindow : Window
{
    private readonly decimal _totalAmount;
    private readonly decimal _paidAmount;
    private readonly string _reference;
    private readonly string _description;
    
    public PaymentInfo? PaymentResult { get; private set; }
    public bool IsPaymentProcessed { get; private set; }

    public PaymentWindow(string reference, string description, decimal totalAmount, decimal paidAmount = 0)
    {
        InitializeComponent();
        
        _reference = reference;
        _description = description;
        _totalAmount = totalAmount;
        _paidAmount = paidAmount;
        
        InitializeForm();
        SetupEventHandlers();
    }

    private void InitializeForm()
    {
        // تعيين معلومات المعاملة
        ReferenceTextBlock.Text = _reference;
        DescriptionTextBlock.Text = _description;
        TotalAmountTextBlock.Text = _totalAmount.ToString("N2") + " ريال";
        PaidAmountTextBlock.Text = _paidAmount.ToString("N2") + " ريال";
        
        var remainingAmount = _totalAmount - _paidAmount;
        RemainingAmountTextBlock.Text = remainingAmount.ToString("N2") + " ريال";
        
        // تعيين القيم الافتراضية
        PaymentDatePicker.SelectedDate = DateTime.Now;
        PaymentAmountTextBox.Text = remainingAmount.ToString("F2");
        TransferFeesTextBox.Text = "0";
        
        UpdateSummary();
    }

    private void SetupEventHandlers()
    {
        PaymentAmountTextBox.TextChanged += PaymentAmountTextBox_TextChanged;
        TransferFeesTextBox.TextChanged += TransferFeesTextBox_TextChanged;
    }

    private void PaymentAmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
    {
        UpdateSummary();
    }

    private void TransferFeesTextBox_TextChanged(object sender, TextChangedEventArgs e)
    {
        UpdateSummary();
    }

    private void UpdateSummary()
    {
        try
        {
            var paymentAmount = decimal.TryParse(PaymentAmountTextBox.Text, out var amount) ? amount : 0;
            var fees = decimal.TryParse(TransferFeesTextBox.Text, out var feeAmount) ? feeAmount : 0;
            var total = paymentAmount + fees;
            var remainingAfterPayment = _totalAmount - _paidAmount - paymentAmount;

            SummaryPaymentAmountTextBlock.Text = paymentAmount.ToString("N2") + " ريال";
            SummaryFeesTextBlock.Text = fees.ToString("N2") + " ريال";
            SummaryTotalTextBlock.Text = total.ToString("N2") + " ريال";
            SummaryRemainingTextBlock.Text = Math.Max(0, remainingAfterPayment).ToString("N2") + " ريال";

            // تحديث لون المبلغ المتبقي
            if (remainingAfterPayment <= 0)
            {
                SummaryRemainingTextBlock.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Green);
                SummaryRemainingTextBlock.Text = "مدفوع بالكامل ✅";
            }
            else
            {
                SummaryRemainingTextBlock.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(244, 67, 54));
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الملخص: {ex.Message}");
        }
    }

    private void ProcessPaymentButton_Click(object sender, RoutedEventArgs e)
    {
        if (ValidatePayment())
        {
            var paymentAmount = decimal.Parse(PaymentAmountTextBox.Text);
            var remainingAmount = _totalAmount - _paidAmount;

            if (paymentAmount > remainingAmount)
            {
                var result = MessageBox.Show($"المبلغ المدخل ({paymentAmount:N2} ريال) أكبر من المبلغ المتبقي ({remainingAmount:N2} ريال).\n\nهل تريد المتابعة؟", 
                                           "تأكيد المبلغ", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.No)
                    return;
            }

            ProcessPayment(PaymentStatus.Paid);
        }
    }

    private void PartialPaymentButton_Click(object sender, RoutedEventArgs e)
    {
        if (ValidatePayment())
        {
            var paymentAmount = decimal.Parse(PaymentAmountTextBox.Text);
            var remainingAmount = _totalAmount - _paidAmount;

            if (paymentAmount >= remainingAmount)
            {
                MessageBox.Show("للدفع الجزئي، يجب أن يكون المبلغ أقل من المبلغ المتبقي.\n\nاستخدم 'تنفيذ الدفع' للدفع الكامل.", 
                               "دفع جزئي", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            ProcessPayment(PaymentStatus.PartiallyPaid);
        }
    }

    private void SchedulePaymentButton_Click(object sender, RoutedEventArgs e)
    {
        if (ValidatePayment())
        {
            var result = MessageBox.Show("هل تريد جدولة هذا الدفع لتاريخ لاحق؟\n\nسيتم حفظ تفاصيل الدفع وتذكيرك في التاريخ المحدد.", 
                                       "جدولة الدفع", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                ProcessPayment(PaymentStatus.Pending, true);
            }
        }
    }

    private void ProcessPayment(PaymentStatus status, bool isScheduled = false)
    {
        try
        {
            var paymentAmount = decimal.Parse(PaymentAmountTextBox.Text);
            var fees = decimal.TryParse(TransferFeesTextBox.Text, out var feeAmount) ? feeAmount : 0;
            
            PaymentResult = new PaymentInfo
            {
                Amount = paymentAmount,
                Fees = fees,
                PaymentMethod = GetSelectedPaymentMethod(),
                PaymentDate = PaymentDatePicker.SelectedDate ?? DateTime.Now,
                ReferenceNumber = ReferenceNumberTextBox.Text.Trim(),
                Bank = GetSelectedBank(),
                Notes = NotesTextBox.Text.Trim(),
                Status = status,
                IsScheduled = isScheduled,
                SendReceipt = SendReceiptCheckBox.IsChecked == true,
                UpdateStatus = UpdateStatusCheckBox.IsChecked == true,
                PrintReceipt = PrintReceiptCheckBox.IsChecked == true,
                AddToAccounting = AddToAccountingCheckBox.IsChecked == true
            };

            IsPaymentProcessed = true;

            var statusText = status switch
            {
                PaymentStatus.Paid => "تم تنفيذ الدفع بنجاح!",
                PaymentStatus.PartiallyPaid => "تم تسجيل الدفع الجزئي بنجاح!",
                PaymentStatus.Pending => isScheduled ? "تم جدولة الدفع بنجاح!" : "تم تسجيل الدفع بنجاح!",
                _ => "تم تسجيل الدفع بنجاح!"
            };

            var detailsText = $"\nالمبلغ: {paymentAmount:N2} ريال";
            if (fees > 0)
                detailsText += $"\nالرسوم: {fees:N2} ريال";
            detailsText += $"\nطريقة الدفع: {GetPaymentMethodDisplay()}";
            detailsText += $"\nتاريخ الدفع: {PaymentDatePicker.SelectedDate:dd/MM/yyyy}";

            MessageBox.Show(statusText + detailsText, "نجح الدفع", 
                           MessageBoxButton.OK, MessageBoxImage.Information);

            // تنفيذ الإجراءات الإضافية
            if (PaymentResult.PrintReceipt)
            {
                PrintReceipt();
            }

            if (PaymentResult.SendReceipt)
            {
                SendReceipt();
            }

            DialogResult = true;
            Close();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في معالجة الدفع: {ex.Message}", "خطأ", 
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private bool ValidatePayment()
    {
        var errors = new System.Collections.Generic.List<string>();

        if (string.IsNullOrWhiteSpace(PaymentAmountTextBox.Text))
            errors.Add("يجب إدخال مبلغ الدفع");
        else if (!decimal.TryParse(PaymentAmountTextBox.Text, out var amount) || amount <= 0)
            errors.Add("مبلغ الدفع يجب أن يكون رقماً صحيحاً أكبر من صفر");

        if (!PaymentDatePicker.SelectedDate.HasValue)
            errors.Add("يجب تحديد تاريخ الدفع");

        if (PaymentMethodComboBox.SelectedItem == null)
            errors.Add("يجب اختيار طريقة الدفع");

        // التحقق من الرسوم
        if (!string.IsNullOrWhiteSpace(TransferFeesTextBox.Text))
        {
            if (!decimal.TryParse(TransferFeesTextBox.Text, out var fees) || fees < 0)
                errors.Add("رسوم التحويل يجب أن تكون رقماً صحيحاً أكبر من أو يساوي صفر");
        }

        if (errors.Count > 0)
        {
            MessageBox.Show($"يرجى تصحيح الأخطاء التالية:\n\n{string.Join("\n", errors)}", 
                           "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
            return false;
        }

        return true;
    }

    private PaymentMethod GetSelectedPaymentMethod()
    {
        if (PaymentMethodComboBox.SelectedItem is ComboBoxItem item && item.Tag is string tag)
        {
            return Enum.Parse<PaymentMethod>(tag);
        }
        return PaymentMethod.Cash;
    }

    private string GetSelectedBank()
    {
        if (BankComboBox.SelectedItem is ComboBoxItem item)
        {
            return item.Content?.ToString() ?? "";
        }
        return "";
    }

    private string GetPaymentMethodDisplay()
    {
        if (PaymentMethodComboBox.SelectedItem is ComboBoxItem item)
        {
            return item.Content?.ToString() ?? "";
        }
        return "";
    }

    private void PrintReceipt()
    {
        try
        {
            MessageBox.Show("تم إرسال إيصال الدفع للطابعة بنجاح!", "طباعة الإيصال", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة الإيصال: {ex.Message}", "خطأ", 
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void SendReceipt()
    {
        try
        {
            MessageBox.Show("تم إرسال إيصال الدفع للموكل عبر البريد الإلكتروني والرسائل النصية!", "إرسال الإيصال", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إرسال الإيصال: {ex.Message}", "خطأ", 
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("هل تريد إلغاء عملية الدفع؟\nسيتم فقدان جميع البيانات المدخلة.", 
                                   "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Question);
        
        if (result == MessageBoxResult.Yes)
        {
            DialogResult = false;
            Close();
        }
    }
}

// فئة معلومات الدفع
public class PaymentInfo
{
    public decimal Amount { get; set; }
    public decimal Fees { get; set; }
    public PaymentMethod PaymentMethod { get; set; }
    public DateTime PaymentDate { get; set; }
    public string ReferenceNumber { get; set; } = "";
    public string Bank { get; set; } = "";
    public string Notes { get; set; } = "";
    public PaymentStatus Status { get; set; }
    public bool IsScheduled { get; set; }
    public bool SendReceipt { get; set; }
    public bool UpdateStatus { get; set; }
    public bool PrintReceipt { get; set; }
    public bool AddToAccounting { get; set; }
}
