<Page x:Class="AvocatPro.Views.Pages.AdvancedUISettingsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="إعدادات الواجهة المتقدمة" FlowDirection="RightToLeft"
      Background="{DynamicResource BackgroundColor}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="{DynamicResource SurfaceColor}" CornerRadius="15" Padding="20" Margin="20,20,20,10">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="{DynamicResource ShadowOpacity}" BlurRadius="10" ShadowDepth="2"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border CornerRadius="50" Width="60" Height="60" Margin="0,0,20,0">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#8B5CF6" Offset="0"/>
                                <GradientStop Color="#7C3AED" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <Border.Effect>
                            <DropShadowEffect Color="#8B5CF6" Opacity="0.4" BlurRadius="15" ShadowDepth="0"/>
                        </Border.Effect>
                        <TextBlock Text="🎨" FontSize="30" Foreground="White" 
                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <StackPanel>
                        <TextBlock Text="إعدادات الواجهة المتقدمة" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource TextColor}"/>
                        <TextBlock Text="تخصيص الثيمات، الألوان، الأنيميشن والاختصارات" FontSize="14" Foreground="{DynamicResource TextSecondaryColor}"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="ResetBtn" Background="{DynamicResource WarningColor}" Foreground="White" Padding="12,8" 
                            Margin="0,0,10,0" Click="Reset_Click" BorderThickness="0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔄" FontSize="14" Margin="0,0,8,0"/>
                            <TextBlock Text="إعادة تعيين"/>
                        </StackPanel>
                    </Button>
                    <Button x:Name="SaveBtn" Background="{DynamicResource SuccessColor}" Foreground="White" Padding="12,8" 
                            Click="Save_Click" BorderThickness="0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="💾" FontSize="14" Margin="0,0,8,0"/>
                            <TextBlock Text="حفظ"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="20,0,20,20">
            <StackPanel>
                <!-- إعدادات الثيم -->
                <Border Background="{DynamicResource SurfaceColor}" CornerRadius="15" Padding="20" Margin="0,0,0,20">
                    <Border.Effect>
                        <DropShadowEffect Color="Black" Opacity="{DynamicResource ShadowOpacity}" BlurRadius="10" ShadowDepth="2"/>
                    </Border.Effect>
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                            <TextBlock Text="🌓" FontSize="24" Margin="0,0,10,0"/>
                            <TextBlock Text="إعدادات الثيم" FontSize="18" FontWeight="SemiBold" Foreground="{DynamicResource TextColor}"/>
                        </StackPanel>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- وضع الثيم -->
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="وضع الثيم" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,10" Foreground="{DynamicResource TextColor}"/>
                                <ComboBox x:Name="ThemeModeComboBox" Padding="12,8" FontSize="14" 
                                          SelectionChanged="ThemeMode_SelectionChanged">
                                    <ComboBoxItem Content="فاتح" Tag="Light"/>
                                    <ComboBoxItem Content="داكن" Tag="Dark"/>
                                    <ComboBoxItem Content="تلقائي" Tag="Auto"/>
                                    <ComboBoxItem Content="مخصص" Tag="Custom"/>
                                </ComboBox>
                            </StackPanel>

                            <!-- الثيمات المحددة مسبقاً -->
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="الثيمات الجاهزة" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,10" Foreground="{DynamicResource TextColor}"/>
                                <ComboBox x:Name="PredefinedThemeComboBox" Padding="12,8" FontSize="14" 
                                          SelectionChanged="PredefinedTheme_SelectionChanged">
                                    <ComboBoxItem Content="افتراضي" Tag="Light"/>
                                    <ComboBoxItem Content="أزرق" Tag="Blue"/>
                                    <ComboBoxItem Content="أخضر" Tag="Green"/>
                                    <ComboBoxItem Content="بنفسجي" Tag="Purple"/>
                                    <ComboBoxItem Content="برتقالي" Tag="Orange"/>
                                    <ComboBoxItem Content="مهني" Tag="Professional"/>
                                    <ComboBoxItem Content="أنيق" Tag="Elegant"/>
                                </ComboBox>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- تخصيص الألوان -->
                <Border Background="{DynamicResource SurfaceColor}" CornerRadius="15" Padding="20" Margin="0,0,0,20">
                    <Border.Effect>
                        <DropShadowEffect Color="Black" Opacity="{DynamicResource ShadowOpacity}" BlurRadius="10" ShadowDepth="2"/>
                    </Border.Effect>
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                            <TextBlock Text="🎨" FontSize="24" Margin="0,0,10,0"/>
                            <TextBlock Text="تخصيص الألوان" FontSize="18" FontWeight="SemiBold" Foreground="{DynamicResource TextColor}"/>
                        </StackPanel>

                        <UniformGrid Columns="3" Margin="0,10,0,0">
                            <!-- اللون الأساسي -->
                            <StackPanel Margin="0,0,10,0">
                                <TextBlock Text="اللون الأساسي" FontSize="14" FontWeight="SemiBold" Margin="0,0,0,8" Foreground="{DynamicResource TextColor}"/>
                                <Border x:Name="PrimaryColorPreview" Height="40" CornerRadius="8" Background="{DynamicResource PrimaryColor}" 
                                        BorderBrush="{DynamicResource TextSecondaryColor}" BorderThickness="1" Cursor="Hand" 
                                        MouseLeftButtonDown="ColorPreview_Click" Tag="Primary"/>
                                <TextBox x:Name="PrimaryColorTextBox" Margin="0,5,0,0"
                                         Style="{StaticResource ModernTextBoxStyle}"
                                         Text="#FFFFFF" TextChanged="ColorTextBox_TextChanged" Tag="Primary"/>
                            </StackPanel>

                            <!-- اللون الثانوي -->
                            <StackPanel Margin="5,0">
                                <TextBlock Text="اللون الثانوي" FontSize="14" FontWeight="SemiBold" Margin="0,0,0,8" Foreground="{DynamicResource TextColor}"/>
                                <Border x:Name="SecondaryColorPreview" Height="40" CornerRadius="8" Background="{DynamicResource SecondaryColor}" 
                                        BorderBrush="{DynamicResource TextSecondaryColor}" BorderThickness="1" Cursor="Hand" 
                                        MouseLeftButtonDown="ColorPreview_Click" Tag="Secondary"/>
                                <TextBox x:Name="SecondaryColorTextBox" Margin="0,5,0,0"
                                         Style="{StaticResource ModernTextBoxStyle}"
                                         Text="#F8FAFC" TextChanged="ColorTextBox_TextChanged" Tag="Secondary"/>
                            </StackPanel>

                            <!-- لون التمييز -->
                            <StackPanel Margin="10,0,0,0">
                                <TextBlock Text="لون التمييز" FontSize="14" FontWeight="SemiBold" Margin="0,0,0,8" Foreground="{DynamicResource TextColor}"/>
                                <Border x:Name="AccentColorPreview" Height="40" CornerRadius="8" Background="{DynamicResource AccentColor}" 
                                        BorderBrush="{DynamicResource TextSecondaryColor}" BorderThickness="1" Cursor="Hand" 
                                        MouseLeftButtonDown="ColorPreview_Click" Tag="Accent"/>
                                <TextBox x:Name="AccentColorTextBox" Margin="0,5,0,0"
                                         Style="{StaticResource ModernTextBoxStyle}"
                                         Text="#3B82F6" TextChanged="ColorTextBox_TextChanged" Tag="Accent"/>
                            </StackPanel>
                        </UniformGrid>

                        <!-- ألوان إضافية -->
                        <UniformGrid Columns="4" Margin="0,20,0,0">
                            <!-- لون النجاح -->
                            <StackPanel Margin="0,0,5,0">
                                <TextBlock Text="النجاح" FontSize="12" FontWeight="SemiBold" Margin="0,0,0,5" Foreground="{DynamicResource TextColor}"/>
                                <Border x:Name="SuccessColorPreview" Height="30" CornerRadius="6" Background="{DynamicResource SuccessColor}" 
                                        BorderBrush="{DynamicResource TextSecondaryColor}" BorderThickness="1" Cursor="Hand" 
                                        MouseLeftButtonDown="ColorPreview_Click" Tag="Success"/>
                            </StackPanel>

                            <!-- لون التحذير -->
                            <StackPanel Margin="5,0">
                                <TextBlock Text="التحذير" FontSize="12" FontWeight="SemiBold" Margin="0,0,0,5" Foreground="{DynamicResource TextColor}"/>
                                <Border x:Name="WarningColorPreview" Height="30" CornerRadius="6" Background="{DynamicResource WarningColor}" 
                                        BorderBrush="{DynamicResource TextSecondaryColor}" BorderThickness="1" Cursor="Hand" 
                                        MouseLeftButtonDown="ColorPreview_Click" Tag="Warning"/>
                            </StackPanel>

                            <!-- لون الخطأ -->
                            <StackPanel Margin="5,0">
                                <TextBlock Text="الخطأ" FontSize="12" FontWeight="SemiBold" Margin="0,0,0,5" Foreground="{DynamicResource TextColor}"/>
                                <Border x:Name="ErrorColorPreview" Height="30" CornerRadius="6" Background="{DynamicResource ErrorColor}" 
                                        BorderBrush="{DynamicResource TextSecondaryColor}" BorderThickness="1" Cursor="Hand" 
                                        MouseLeftButtonDown="ColorPreview_Click" Tag="Error"/>
                            </StackPanel>

                            <!-- لون المعلومات -->
                            <StackPanel Margin="5,0,0,0">
                                <TextBlock Text="المعلومات" FontSize="12" FontWeight="SemiBold" Margin="0,0,0,5" Foreground="{DynamicResource TextColor}"/>
                                <Border x:Name="InfoColorPreview" Height="30" CornerRadius="6" Background="{DynamicResource InfoColor}" 
                                        BorderBrush="{DynamicResource TextSecondaryColor}" BorderThickness="1" Cursor="Hand" 
                                        MouseLeftButtonDown="ColorPreview_Click" Tag="Info"/>
                            </StackPanel>
                        </UniformGrid>
                    </StackPanel>
                </Border>

                <!-- إعدادات الأنيميشن -->
                <Border Background="{DynamicResource SurfaceColor}" CornerRadius="15" Padding="20" Margin="0,0,0,20">
                    <Border.Effect>
                        <DropShadowEffect Color="Black" Opacity="{DynamicResource ShadowOpacity}" BlurRadius="10" ShadowDepth="2"/>
                    </Border.Effect>
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                            <TextBlock Text="🎬" FontSize="24" Margin="0,0,10,0"/>
                            <TextBlock Text="إعدادات الأنيميشن" FontSize="18" FontWeight="SemiBold" Foreground="{DynamicResource TextColor}"/>
                        </StackPanel>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- تفعيل الأنيميشن -->
                            <StackPanel Grid.Column="0">
                                <CheckBox x:Name="EnableAnimationsCheckBox" Content="تفعيل الأنيميشن" FontSize="14" 
                                          Margin="0,0,0,15" IsChecked="True" Checked="AnimationSettings_Changed" Unchecked="AnimationSettings_Changed"/>
                                <CheckBox x:Name="EnableTransitionsCheckBox" Content="انتقالات الصفحات" FontSize="14" 
                                          Margin="0,0,0,15" IsChecked="True" Checked="AnimationSettings_Changed" Unchecked="AnimationSettings_Changed"/>
                                <CheckBox x:Name="EnableHoverEffectsCheckBox" Content="تأثيرات التمرير" FontSize="14" 
                                          Margin="0,0,0,15" IsChecked="True" Checked="AnimationSettings_Changed" Unchecked="AnimationSettings_Changed"/>
                            </StackPanel>

                            <!-- نوع الانتقال -->
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="نوع انتقال الصفحات" FontSize="14" FontWeight="SemiBold" Margin="0,0,0,10" Foreground="{DynamicResource TextColor}"/>
                                <ComboBox x:Name="TransitionTypeComboBox" Padding="12,8" FontSize="14" 
                                          SelectionChanged="TransitionType_SelectionChanged">
                                    <ComboBoxItem Content="انزلاق يسار" Tag="SlideLeft" IsSelected="True"/>
                                    <ComboBoxItem Content="انزلاق يمين" Tag="SlideRight"/>
                                    <ComboBoxItem Content="انزلاق أعلى" Tag="SlideUp"/>
                                    <ComboBoxItem Content="انزلاق أسفل" Tag="SlideDown"/>
                                    <ComboBoxItem Content="تلاشي" Tag="Fade"/>
                                    <ComboBoxItem Content="تكبير/تصغير" Tag="Scale"/>
                                    <ComboBoxItem Content="قلب" Tag="Flip"/>
                                </ComboBox>

                                <Button x:Name="TestTransitionBtn" Background="{DynamicResource AccentColor}" Foreground="White" 
                                        Padding="12,8" Margin="0,10,0,0" Click="TestTransition_Click" BorderThickness="0">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="🎭" FontSize="14" Margin="0,0,8,0"/>
                                        <TextBlock Text="اختبار الانتقال"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- اختصارات لوحة المفاتيح -->
                <Border Background="{DynamicResource SurfaceColor}" CornerRadius="15" Padding="20">
                    <Border.Effect>
                        <DropShadowEffect Color="Black" Opacity="{DynamicResource ShadowOpacity}" BlurRadius="10" ShadowDepth="2"/>
                    </Border.Effect>
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                            <TextBlock Text="⌨️" FontSize="24" Margin="0,0,10,0"/>
                            <TextBlock Text="اختصارات لوحة المفاتيح" FontSize="18" FontWeight="SemiBold" Foreground="{DynamicResource TextColor}"/>
                            <Button x:Name="ViewAllShortcutsBtn" Background="Transparent" Foreground="{DynamicResource AccentColor}" 
                                    Padding="8" Margin="20,0,0,0" Click="ViewAllShortcuts_Click" BorderThickness="0">
                                <TextBlock Text="عرض جميع الاختصارات"/>
                            </Button>
                        </StackPanel>

                        <CheckBox x:Name="EnableShortcutsCheckBox" Content="تفعيل اختصارات لوحة المفاتيح" FontSize="14" 
                                  Margin="0,0,0,15" IsChecked="True" Checked="ShortcutSettings_Changed" Unchecked="ShortcutSettings_Changed"/>

                        <!-- أهم الاختصارات -->
                        <TextBlock Text="الاختصارات الأساسية:" FontSize="14" FontWeight="SemiBold" Margin="0,0,0,10" Foreground="{DynamicResource TextColor}"/>
                        <StackPanel x:Name="ShortcutsPanel">
                            <!-- سيتم إضافة الاختصارات ديناميكياً -->
                        </StackPanel>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- مؤشر التحميل -->
        <Border x:Name="LoadingOverlay" Grid.RowSpan="2" Background="Black" Opacity="0.5" Visibility="Collapsed">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="300" Height="6" 
                             Foreground="{DynamicResource AccentColor}" Background="Transparent"/>
                <TextBlock Text="جاري تطبيق الإعدادات..." Foreground="White" 
                           HorizontalAlignment="Center" Margin="0,20,0,0" FontSize="18"/>
            </StackPanel>
        </Border>
    </Grid>
</Page>
