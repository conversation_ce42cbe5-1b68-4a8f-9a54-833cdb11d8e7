using Microsoft.EntityFrameworkCore;
using AvocatPro.Models;

namespace AvocatPro.Data;

/// <summary>
/// مُهيئ قاعدة البيانات
/// </summary>
public static class DatabaseInitializer
{
    private const string SystemUser = "System";
    /// <summary>
    /// تهيئة قاعدة البيانات مع البيانات الأولية
    /// </summary>
    /// <param name="context">سياق قاعدة البيانات</param>
    public static async Task InitializeAsync(AvocatProDbContext context)
    {
        try
        {
            // إنشاء قاعدة البيانات إذا لم تكن موجودة
            await context.Database.EnsureCreatedAsync();

            // إضافة البيانات الأولية
            await SeedDataAsync(context);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// إضافة البيانات الأولية
    /// </summary>
    /// <param name="context">سياق قاعدة البيانات</param>
    private static async Task SeedDataAsync(AvocatProDbContext context)
    {
        // إضافة المستخدم الافتراضي
        await SeedDefaultUserAsync(context);

        // إضافة إعدادات المكتب الافتراضية
        await SeedOfficeSettingsAsync(context);

        // إضافة بيانات تجريبية (اختيارية)
        if (await context.Clients.CountAsync() == 0)
        {
            await SeedSampleDataAsync(context);
        }

        await context.SaveChangesAsync();
    }

    /// <summary>
    /// إضافة المستخدم الافتراضي
    /// </summary>
    private static async Task SeedDefaultUserAsync(AvocatProDbContext context)
    {
        if (!await context.Users.AnyAsync(u => u.Username == "admin"))
        {
            var adminUser = new User
            {
                Username = "admin",
                FullName = "مدير النظام",
                Email = "<EMAIL>",
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("Admin@123"),
                Role = UserRole.Admin,
                IsActive = true,
                CreatedAt = DateTime.Now,
                CreatedBy = SystemUser
            };

            context.Users.Add(adminUser);
        }
    }

    /// <summary>
    /// إضافة إعدادات المكتب الافتراضية
    /// </summary>
    private static async Task SeedOfficeSettingsAsync(AvocatProDbContext context)
    {
        if (!await context.OfficeSettings.AnyAsync())
        {
            var officeSettings = new OfficeSettings
            {
                OfficeName = "مكتب المحاماة",
                OfficeAddress = "الرباط، المغرب",
                OfficePhone = "+212 5 37 XX XX XX",
                OfficeEmail = "<EMAIL>",
                OfficeWebsite = "www.lawfirm.ma",
                LicenseNumber = "LIC-2025-001",
                CreatedAt = DateTime.Now,
                CreatedBy = SystemUser
            };

            context.OfficeSettings.Add(officeSettings);
        }
    }

    /// <summary>
    /// إضافة بيانات تجريبية
    /// </summary>
    private static async Task SeedSampleDataAsync(AvocatProDbContext context)
    {
        // إضافة موكلين تجريبيين
        var sampleClients = new[]
        {
            new Client
            {
                OfficeReference = "**********",
                FullName = "أحمد محمد الحسني",
                Type = ClientType.Individual,
                IdentityNumber = "AB123456",
                Phone = "+212 6 12 34 56 78",
                Email = "<EMAIL>",
                Address = "شارع محمد الخامس، الرباط",
                City = "الرباط",
                Region = "الرباط-سلا-القنيطرة",
                PostalCode = "10000",
                Nationality = "مغربية",
                DateOfBirth = new DateTime(1985, 5, 15, 0, 0, 0, DateTimeKind.Local),
                Gender = Gender.Male,
                Profession = "مهندس",
                MaritalStatus = MaritalStatus.Married,
                Status = ClientStatus.Active,
                Source = "إحالة",
                Rating = 5,
                CreatedAt = DateTime.Now,
                CreatedBy = SystemUser
            },
            new Client
            {
                OfficeReference = "CL20250002",
                FullName = "شركة التقنيات المتقدمة",
                Type = ClientType.Company,
                CompanyName = "شركة التقنيات المتقدمة ش.م.م",
                CommercialRegister = "RC123456",
                TaxNumber = "TAX789012",
                LegalRepresentative = "سعيد الإدريسي",
                Phone = "+212 5 22 33 44 55",
                Email = "<EMAIL>",
                Address = "المنطقة الصناعية، الدار البيضاء",
                City = "الدار البيضاء",
                Region = "الدار البيضاء-سطات",
                PostalCode = "20000",
                Status = ClientStatus.Active,
                Source = "موقع إلكتروني",
                Rating = 4,
                CreatedAt = DateTime.Now,
                CreatedBy = SystemUser
            }
        };

        context.Clients.AddRange(sampleClients);
        await context.SaveChangesAsync();

        // إضافة قضايا تجريبية
        var sampleCases = new[]
        {
            new Case
            {
                OfficeReference = "CA20250001",
                CourtReference = "CT2025/001",
                Title = "قضية عقد عمل",
                Description = "نزاع حول عقد عمل وتعويضات",
                Type = CaseType.Labor,
                Category = CaseCategory.Lawsuit,
                Court = "المحكمة الابتدائية بالرباط",
                CourtType = CourtType.FirstInstance,
                Opponent = "شركة البناء الحديث",
                Subject = "تعويضات عن فصل تعسفي",
                StartDate = DateTime.Now.AddDays(-30),
                Status = CaseStatus.Active,
                Priority = CasePriority.High,
                FinancialValue = 50000,
                LawyerFees = 10000,
                ClientId = 1, // أحمد محمد الحسني
                Stage = "مرحلة المرافعات",
                LitigationLevel = LitigationLevel.FirstInstance,
                CreatedAt = DateTime.Now,
                CreatedBy = SystemUser
            },
            new Case
            {
                OfficeReference = "CA20250002",
                Title = "استشارة قانونية - عقد شراكة",
                Description = "مراجعة وصياغة عقد شراكة تجارية",
                Type = CaseType.Commercial,
                Category = CaseCategory.Contract,
                Court = "غير محدد",
                CourtType = CourtType.FirstInstance,
                Subject = "عقد شراكة تجارية",
                StartDate = DateTime.Now.AddDays(-15),
                Status = CaseStatus.Active,
                Priority = CasePriority.Medium,
                LawyerFees = 5000,
                ClientId = 2, // شركة التقنيات المتقدمة
                Stage = "مرحلة الصياغة",
                LitigationLevel = LitigationLevel.FirstInstance,
                CreatedAt = DateTime.Now,
                CreatedBy = SystemUser
            }
        };

        context.Cases.AddRange(sampleCases);
        await context.SaveChangesAsync();

        // إضافة جلسات تجريبية
        var sampleSessions = new[]
        {
            new Session
            {
                CaseId = 1,
                SessionDate = DateTime.Now.AddDays(7),
                SessionTime = new TimeSpan(10, 0, 0),
                Type = SessionType.Regular,
                ProcedureType = "جلسة مرافعة",
                Status = SessionStatus.Scheduled,
                Judge = "القاضي محمد العلوي",
                CourtRoom = "قاعة 3",
                SessionNotes = "جلسة للاستماع للمرافعات",
                CreatedAt = DateTime.Now,
                CreatedBy = SystemUser
            }
        };

        context.Sessions.AddRange(sampleSessions);

        // إضافة مواعيد تجريبية
        var sampleAppointments = new[]
        {
            new Appointment
            {
                Title = "استشارة قانونية",
                Description = "استشارة حول قضية عمالية",
                AppointmentDate = DateTime.Now.AddDays(2),
                StartTime = new TimeSpan(14, 0, 0),
                EndTime = new TimeSpan(15, 0, 0),
                Type = AppointmentType.Consultation,
                Status = AppointmentStatus.Scheduled,
                Priority = AppointmentPriority.Medium,
                ClientId = 1,
                Location = "مكتب المحاماة",
                Cost = 500,
                CreatedByUserId = 1,
                CreatedAt = DateTime.Now,
                CreatedBy = SystemUser
            }
        };

        context.Appointments.AddRange(sampleAppointments);
    }

    /// <summary>
    /// إعادة تعيين قاعدة البيانات (للتطوير فقط)
    /// </summary>
    public static async Task ResetDatabaseAsync(AvocatProDbContext context)
    {
        await context.Database.EnsureDeletedAsync();
        await context.Database.EnsureCreatedAsync();
        await SeedDataAsync(context);
    }

    /// <summary>
    /// التحقق من سلامة قاعدة البيانات
    /// </summary>
    public static async Task<bool> ValidateDatabaseAsync(AvocatProDbContext context)
    {
        try
        {
            // التحقق من الاتصال
            await context.Database.CanConnectAsync();

            // التحقق من وجود الجداول الأساسية
            var hasUsers = await context.Users.AnyAsync();
            var hasOfficeSettings = await context.OfficeSettings.AnyAsync();

            return hasUsers && hasOfficeSettings;
        }
        catch
        {
            return false;
        }
    }
}
