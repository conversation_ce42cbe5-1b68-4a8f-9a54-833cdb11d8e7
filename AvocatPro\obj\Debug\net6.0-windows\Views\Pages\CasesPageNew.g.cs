﻿#pragma checksum "..\..\..\..\..\Views\Pages\CasesPageNew.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "62B6E763F0D3F94A3931E458473F34A929DBFDE0"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Pages {
    
    
    /// <summary>
    /// CasesPageNew
    /// </summary>
    public partial class CasesPageNew : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 108 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddCaseButton;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalCasesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActiveCasesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ClosedCasesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ArchivedCasesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CaseTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CaseStatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 205 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CasePriorityComboBox;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearFiltersButton;
        
        #line default
        #line hidden
        
        
        #line 238 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 246 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintButton;
        
        #line default
        #line hidden
        
        
        #line 254 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ArchiveButton;
        
        #line default
        #line hidden
        
        
        #line 265 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid CasesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 330 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/pages/casespagenew.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AddCaseButton = ((System.Windows.Controls.Button)(target));
            
            #line 108 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
            this.AddCaseButton.Click += new System.Windows.RoutedEventHandler(this.AddCaseButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 116 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.TotalCasesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.ActiveCasesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.ClosedCasesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.ArchivedCasesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 178 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.CaseTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 182 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
            this.CaseTypeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.CaseStatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 196 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
            this.CaseStatusComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.CasePriorityComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 207 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
            this.CasePriorityComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ClearFiltersButton = ((System.Windows.Controls.Button)(target));
            
            #line 217 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
            this.ClearFiltersButton.Click += new System.Windows.RoutedEventHandler(this.ClearFiltersButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 239 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.PrintButton = ((System.Windows.Controls.Button)(target));
            
            #line 247 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
            this.PrintButton.Click += new System.Windows.RoutedEventHandler(this.PrintButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.ArchiveButton = ((System.Windows.Controls.Button)(target));
            
            #line 255 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
            this.ArchiveButton.Click += new System.Windows.RoutedEventHandler(this.ArchiveButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.CasesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 21:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 16:
            
            #line 293 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewCaseButton_Click);
            
            #line default
            #line hidden
            break;
            case 17:
            
            #line 299 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditCaseButton_Click);
            
            #line default
            #line hidden
            break;
            case 18:
            
            #line 305 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SessionsButton_Click);
            
            #line default
            #line hidden
            break;
            case 19:
            
            #line 311 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DocumentsButton_Click);
            
            #line default
            #line hidden
            break;
            case 20:
            
            #line 317 "..\..\..\..\..\Views\Pages\CasesPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteCaseButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

