using Microsoft.EntityFrameworkCore;
using AvocatPro.Data;
using AvocatPro.Models;

namespace AvocatPro.Services;

/// <summary>
/// خدمة إدارة المستخدمين
/// </summary>
public class UserService : IUserService
{
    private readonly AvocatProDbContext _context;
    private readonly IAuthenticationService _authService;

    public UserService(AvocatProDbContext context, IAuthenticationService authService)
    {
        _context = context;
        _authService = authService;
    }

    public async Task<List<User>> GetAllUsersAsync()
    {
        return await _context.Users
            .Where(u => !u.IsDeleted)
            .OrderBy(u => u.FullName)
            .ToListAsync();
    }

    public async Task<List<User>> GetActiveUsersAsync()
    {
        return await _context.Users
            .Where(u => !u.IsDeleted && u.IsActive)
            .OrderBy(u => u.FullName)
            .ToListAsync();
    }

    public async Task<User?> GetUserByIdAsync(int id)
    {
        return await _context.Users
            .FirstOrDefaultAsync(u => u.Id == id && !u.IsDeleted);
    }

    public async Task<User?> GetUserByUsernameAsync(string username)
    {
        return await _context.Users
            .FirstOrDefaultAsync(u => u.Username == username && !u.IsDeleted);
    }

    public async Task<User?> GetUserByEmailAsync(string email)
    {
        return await _context.Users
            .FirstOrDefaultAsync(u => u.Email == email && !u.IsDeleted);
    }

    public async Task<User> CreateUserAsync(User user, string password)
    {
        // التحقق من صحة كلمة المرور
        var passwordValidation = _authService.ValidatePasswordStrength(password);
        if (!passwordValidation.IsValid)
        {
            throw new ArgumentException($"كلمة المرور غير صالحة: {string.Join(", ", passwordValidation.Errors)}");
        }

        // التحقق من توفر اسم المستخدم
        if (!await IsUsernameAvailableAsync(user.Username))
        {
            throw new ArgumentException("اسم المستخدم غير متاح");
        }

        // التحقق من توفر البريد الإلكتروني
        if (!await IsEmailAvailableAsync(user.Email))
        {
            throw new ArgumentException("البريد الإلكتروني غير متاح");
        }

        // تشفير كلمة المرور
        user.PasswordHash = _authService.HashPassword(password);
        user.CreatedAt = DateTime.Now;

        _context.Users.Add(user);
        await _context.SaveChangesAsync();

        return user;
    }

    public async Task<User> UpdateUserAsync(User user)
    {
        var existingUser = await GetUserByIdAsync(user.Id);
        if (existingUser == null)
        {
            throw new ArgumentException("المستخدم غير موجود");
        }

        // التحقق من توفر اسم المستخدم
        if (!await IsUsernameAvailableAsync(user.Username, user.Id))
        {
            throw new ArgumentException("اسم المستخدم غير متاح");
        }

        // التحقق من توفر البريد الإلكتروني
        if (!await IsEmailAvailableAsync(user.Email, user.Id))
        {
            throw new ArgumentException("البريد الإلكتروني غير متاح");
        }

        // تحديث البيانات
        existingUser.Username = user.Username;
        existingUser.FullName = user.FullName;
        existingUser.Email = user.Email;
        existingUser.Phone = user.Phone;
        existingUser.Role = user.Role;
        existingUser.IsActive = user.IsActive;
        existingUser.UpdatedAt = DateTime.Now;
        existingUser.UpdatedBy = user.UpdatedBy;

        await _context.SaveChangesAsync();
        return existingUser;
    }

    public async Task<bool> DeleteUserAsync(int id, string deletedBy)
    {
        var user = await GetUserByIdAsync(id);
        if (user == null)
        {
            return false;
        }

        // منع حذف المدير الوحيد
        var adminCount = await _context.Users
            .CountAsync(u => u.Role == UserRole.Admin && !u.IsDeleted && u.Id != id);
        
        if (user.Role == UserRole.Admin && adminCount == 0)
        {
            throw new InvalidOperationException("لا يمكن حذف المدير الوحيد في النظام");
        }

        user.IsDeleted = true;
        user.DeletedAt = DateTime.Now;
        user.DeletedBy = deletedBy;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> SetUserActiveStatusAsync(int id, bool isActive)
    {
        var user = await GetUserByIdAsync(id);
        if (user == null)
        {
            return false;
        }

        // منع إلغاء تفعيل المدير الوحيد
        if (!isActive && user.Role == UserRole.Admin)
        {
            var activeAdminCount = await _context.Users
                .CountAsync(u => u.Role == UserRole.Admin && u.IsActive && !u.IsDeleted && u.Id != id);
            
            if (activeAdminCount == 0)
            {
                throw new InvalidOperationException("لا يمكن إلغاء تفعيل المدير الوحيد النشط في النظام");
            }
        }

        user.IsActive = isActive;
        user.UpdatedAt = DateTime.Now;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> ChangeUserRoleAsync(int id, UserRole newRole)
    {
        var user = await GetUserByIdAsync(id);
        if (user == null)
        {
            return false;
        }

        // منع تغيير دور المدير الوحيد
        if (user.Role == UserRole.Admin && newRole != UserRole.Admin)
        {
            var adminCount = await _context.Users
                .CountAsync(u => u.Role == UserRole.Admin && !u.IsDeleted && u.Id != id);
            
            if (adminCount == 0)
            {
                throw new InvalidOperationException("لا يمكن تغيير دور المدير الوحيد في النظام");
            }
        }

        user.Role = newRole;
        user.UpdatedAt = DateTime.Now;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<List<User>> SearchUsersAsync(string searchTerm)
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
        {
            return await GetAllUsersAsync();
        }

        searchTerm = searchTerm.ToLower();

        return await _context.Users
            .Where(u => !u.IsDeleted && 
                       (u.FullName.ToLower().Contains(searchTerm) ||
                        u.Username.ToLower().Contains(searchTerm) ||
                        u.Email.ToLower().Contains(searchTerm) ||
                        (u.Phone != null && u.Phone.Contains(searchTerm))))
            .OrderBy(u => u.FullName)
            .ToListAsync();
    }

    public async Task<List<User>> GetUsersByRoleAsync(UserRole role)
    {
        return await _context.Users
            .Where(u => !u.IsDeleted && u.Role == role)
            .OrderBy(u => u.FullName)
            .ToListAsync();
    }

    public async Task<bool> IsUsernameAvailableAsync(string username, int? excludeUserId = null)
    {
        var query = _context.Users.Where(u => u.Username == username && !u.IsDeleted);
        
        if (excludeUserId.HasValue)
        {
            query = query.Where(u => u.Id != excludeUserId.Value);
        }

        return !await query.AnyAsync();
    }

    public async Task<bool> IsEmailAvailableAsync(string email, int? excludeUserId = null)
    {
        var query = _context.Users.Where(u => u.Email == email && !u.IsDeleted);
        
        if (excludeUserId.HasValue)
        {
            query = query.Where(u => u.Id != excludeUserId.Value);
        }

        return !await query.AnyAsync();
    }

    public async Task<bool> UpdateProfilePictureAsync(int userId, string profilePicturePath)
    {
        var user = await GetUserByIdAsync(userId);
        if (user == null)
        {
            return false;
        }

        user.ProfilePicture = profilePicturePath;
        user.UpdatedAt = DateTime.Now;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> UpdateUserSettingsAsync(int userId, string settings)
    {
        var user = await GetUserByIdAsync(userId);
        if (user == null)
        {
            return false;
        }

        user.UserSettings = settings;
        user.UpdatedAt = DateTime.Now;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<UserStatistics> GetUserStatisticsAsync()
    {
        var today = DateTime.Today;
        var weekStart = today.AddDays(-(int)today.DayOfWeek);
        var monthStart = new DateTime(today.Year, today.Month, 1);

        var statistics = new UserStatistics
        {
            TotalUsers = await _context.Users.CountAsync(u => !u.IsDeleted),
            ActiveUsers = await _context.Users.CountAsync(u => !u.IsDeleted && u.IsActive),
            InactiveUsers = await _context.Users.CountAsync(u => !u.IsDeleted && !u.IsActive),
            LockedUsers = await _context.Users.CountAsync(u => !u.IsDeleted && u.LockedUntil > DateTime.Now),
            UsersLoggedInToday = await _context.Users.CountAsync(u => !u.IsDeleted && u.LastLoginAt >= today),
            UsersLoggedInThisWeek = await _context.Users.CountAsync(u => !u.IsDeleted && u.LastLoginAt >= weekStart),
            UsersLoggedInThisMonth = await _context.Users.CountAsync(u => !u.IsDeleted && u.LastLoginAt >= monthStart)
        };

        // إحصائيات حسب الدور
        var roleStats = await _context.Users
            .Where(u => !u.IsDeleted)
            .GroupBy(u => u.Role)
            .Select(g => new { Role = g.Key, Count = g.Count() })
            .ToListAsync();

        foreach (var roleStat in roleStats)
        {
            statistics.UsersByRole[roleStat.Role] = roleStat.Count;
        }

        return statistics;
    }

    public async Task<bool> CreateDefaultUserIfNotExistsAsync()
    {
        var adminExists = await _context.Users
            .AnyAsync(u => u.Role == UserRole.Admin && !u.IsDeleted);

        if (!adminExists)
        {
            var defaultAdmin = new User
            {
                Username = "admin",
                FullName = "مدير النظام",
                Email = "<EMAIL>",
                Role = UserRole.Admin,
                IsActive = true,
                CreatedBy = "System"
            };

            await CreateUserAsync(defaultAdmin, "Admin@123");
            return true;
        }

        return false;
    }
}
