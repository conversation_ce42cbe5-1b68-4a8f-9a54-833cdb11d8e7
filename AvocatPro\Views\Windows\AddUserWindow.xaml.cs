using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using AvocatPro.Models;

namespace AvocatPro.Views.Windows
{
    public partial class AddUserWindow : Window
    {
        private readonly User _currentUser;
        private bool _saveAndAddAnother = false;

        public AddUserWindow(User currentUser)
        {
            InitializeComponent();
            _currentUser = currentUser;
            
            // تعيين القيم الافتراضية
            RequirePasswordChangeCheckBox.IsChecked = true;
            IsActiveCheckBox.IsChecked = true;
            EmailNotificationsCheckBox.IsChecked = true;
            SystemNotificationsCheckBox.IsChecked = true;
            SessionTimeoutTextBox.Text = "30";
        }

        private void AddRole_Click(object sender, RoutedEventArgs e)
        {
            var selectedItems = AvailableRolesListBox.SelectedItems.Cast<ListBoxItem>().ToList();
            
            foreach (var item in selectedItems)
            {
                // التحقق من عدم وجود الدور مسبقاً
                bool exists = false;
                foreach (ListBoxItem existingItem in SelectedRolesListBox.Items)
                {
                    if (existingItem.Content.ToString() == item.Content.ToString())
                    {
                        exists = true;
                        break;
                    }
                }
                
                if (!exists)
                {
                    var newItem = new ListBoxItem { Content = item.Content };
                    SelectedRolesListBox.Items.Add(newItem);
                }
            }
        }

        private void RemoveRole_Click(object sender, RoutedEventArgs e)
        {
            var selectedItems = SelectedRolesListBox.SelectedItems.Cast<ListBoxItem>().ToList();
            
            foreach (var item in selectedItems)
            {
                SelectedRolesListBox.Items.Remove(item);
            }
        }

        private void SaveBtn_Click(object sender, RoutedEventArgs e)
        {
            _saveAndAddAnother = false;
            SaveUser();
        }

        private void SaveAndAddBtn_Click(object sender, RoutedEventArgs e)
        {
            _saveAndAddAnother = true;
            SaveUser();
        }

        private void SaveUser()
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                    return;

                // إنشاء المستخدم الجديد
                var newUser = new User
                {
                    Username = UsernameTextBox.Text.Trim(),
                    Email = EmailTextBox.Text.Trim(),
                    FullName = $"{FirstNameTextBox.Text.Trim()} {LastNameTextBox.Text.Trim()}",
                    Phone = PhoneTextBox.Text.Trim(),
                    IsActive = IsActiveCheckBox.IsChecked == true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = _currentUser.Username
                };

                // حفظ المستخدم في قاعدة البيانات (محاكاة)
                SaveUserToDatabase(newUser);

                // حفظ الأدوار
                SaveUserRoles(newUser.Id);

                // حفظ الإعدادات
                SaveUserSettings(newUser.Id);

                // تسجيل النشاط
                LogUserActivity("إضافة مستخدم جديد", $"تم إضافة المستخدم: {newUser.FullName}");

                MessageBox.Show("تم حفظ المستخدم بنجاح!", "نجح الحفظ", 
                              MessageBoxButton.OK, MessageBoxImage.Information);

                if (_saveAndAddAnother)
                {
                    ClearForm();
                }
                else
                {
                    DialogResult = true;
                    Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ المستخدم:\n{ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            var errors = new List<string>();

            // التحقق من الحقول المطلوبة
            if (string.IsNullOrWhiteSpace(UsernameTextBox.Text))
                errors.Add("اسم المستخدم مطلوب");

            if (string.IsNullOrWhiteSpace(EmailTextBox.Text))
                errors.Add("البريد الإلكتروني مطلوب");

            if (string.IsNullOrWhiteSpace(FirstNameTextBox.Text))
                errors.Add("الاسم الأول مطلوب");

            if (string.IsNullOrWhiteSpace(LastNameTextBox.Text))
                errors.Add("اسم العائلة مطلوب");

            if (string.IsNullOrWhiteSpace(PasswordBox.Password))
                errors.Add("كلمة المرور مطلوبة");

            // التحقق من صحة البريد الإلكتروني
            if (!string.IsNullOrWhiteSpace(EmailTextBox.Text))
            {
                var emailRegex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$");
                if (!emailRegex.IsMatch(EmailTextBox.Text))
                    errors.Add("البريد الإلكتروني غير صحيح");
            }

            // التحقق من اسم المستخدم
            if (!string.IsNullOrWhiteSpace(UsernameTextBox.Text))
            {
                if (UsernameTextBox.Text.Length < 3)
                    errors.Add("اسم المستخدم يجب أن يكون 3 أحرف على الأقل");

                if (!Regex.IsMatch(UsernameTextBox.Text, @"^[a-zA-Z0-9_]+$"))
                    errors.Add("اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط");
            }

            // التحقق من كلمة المرور
            if (!string.IsNullOrWhiteSpace(PasswordBox.Password))
            {
                if (PasswordBox.Password.Length < 6)
                    errors.Add("كلمة المرور يجب أن تكون 6 أحرف على الأقل");

                if (PasswordBox.Password != ConfirmPasswordBox.Password)
                    errors.Add("كلمة المرور وتأكيدها غير متطابقتين");
            }

            // التحقق من رقم الهاتف
            if (!string.IsNullOrWhiteSpace(PhoneTextBox.Text))
            {
                if (!Regex.IsMatch(PhoneTextBox.Text, @"^[0-9+\-\s()]+$"))
                    errors.Add("رقم الهاتف غير صحيح");
            }

            // التحقق من مهلة الجلسة
            if (!string.IsNullOrWhiteSpace(SessionTimeoutTextBox.Text))
            {
                if (!int.TryParse(SessionTimeoutTextBox.Text, out int timeout) || timeout < 5 || timeout > 480)
                    errors.Add("مهلة الجلسة يجب أن تكون بين 5 و 480 دقيقة");
            }

            // التحقق من وجود دور واحد على الأقل
            if (SelectedRolesListBox.Items.Count == 0)
                errors.Add("يجب تحديد دور واحد على الأقل للمستخدم");

            if (errors.Any())
            {
                MessageBox.Show($"يرجى تصحيح الأخطاء التالية:\n\n{string.Join("\n", errors)}", 
                              "أخطاء في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        private void SaveUserToDatabase(User user)
        {
            // محاكاة حفظ المستخدم في قاعدة البيانات
            // في التطبيق الحقيقي، سيتم استخدام Entity Framework أو أي ORM آخر
            
            // تعيين معرف فريد للمستخدم
            user.Id = new Random().Next(1000, 9999);
            
            // هنا يتم حفظ المستخدم في قاعدة البيانات
            System.Diagnostics.Debug.WriteLine($"تم حفظ المستخدم: {user.FullName} (ID: {user.Id})");
        }

        private void SaveUserRoles(int userId)
        {
            // حفظ الأدوار المحددة للمستخدم
            foreach (ListBoxItem item in SelectedRolesListBox.Items)
            {
                var roleName = item.Content.ToString();
                
                // محاكاة حفظ الدور
                System.Diagnostics.Debug.WriteLine($"تم ربط المستخدم {userId} بالدور: {roleName}");
            }
        }

        private void SaveUserSettings(int userId)
        {
            // حفظ إعدادات المستخدم
            var settings = new UserSettings
            {
                UserId = userId,
                EmailNotifications = EmailNotificationsCheckBox.IsChecked == true,
                SystemNotifications = SystemNotificationsCheckBox.IsChecked == true,
                SessionTimeout = int.TryParse(SessionTimeoutTextBox.Text, out int timeout) ? timeout : 30,
                TwoFactorEnabled = TwoFactorCheckBox.IsChecked == true,
                CreatedAt = DateTime.Now
            };

            // محاكاة حفظ الإعدادات
            System.Diagnostics.Debug.WriteLine($"تم حفظ إعدادات المستخدم {userId}");
        }

        private void LogUserActivity(string action, string details)
        {
            // تسجيل نشاط المستخدم
            var activity = new UserActivity
            {
                UserId = _currentUser.Id,
                Action = action,
                Module = "إدارة المستخدمين",
                Details = details,
                Type = ActivityType.Create,
                Level = ActivityLevel.Info,
                CreatedAt = DateTime.Now
            };

            // محاكاة حفظ النشاط
            System.Diagnostics.Debug.WriteLine($"تم تسجيل النشاط: {action} - {details}");
        }

        private void ClearForm()
        {
            // مسح جميع الحقول
            UsernameTextBox.Clear();
            EmailTextBox.Clear();
            FirstNameTextBox.Clear();
            LastNameTextBox.Clear();
            PhoneTextBox.Clear();
            PasswordBox.Clear();
            ConfirmPasswordBox.Clear();
            NotesTextBox.Clear();
            
            DepartmentComboBox.SelectedIndex = -1;
            SelectedRolesListBox.Items.Clear();
            
            // إعادة تعيين القيم الافتراضية
            RequirePasswordChangeCheckBox.IsChecked = true;
            IsActiveCheckBox.IsChecked = true;
            EmailNotificationsCheckBox.IsChecked = true;
            SystemNotificationsCheckBox.IsChecked = true;
            TwoFactorCheckBox.IsChecked = false;
            SessionTimeoutTextBox.Text = "30";
            
            // التركيز على أول حقل
            UsernameTextBox.Focus();
        }

        private void CancelBtn_Click(object sender, RoutedEventArgs e)
        {
            if (HasUnsavedChanges())
            {
                var result = MessageBox.Show("هل تريد إغلاق النافذة بدون حفظ التغييرات؟", 
                                           "تأكيد الإغلاق", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.No)
                    return;
            }

            DialogResult = false;
            Close();
        }

        private void CloseBtn_Click(object sender, RoutedEventArgs e)
        {
            CancelBtn_Click(sender, e);
        }

        private bool HasUnsavedChanges()
        {
            // التحقق من وجود تغييرات غير محفوظة
            return !string.IsNullOrWhiteSpace(UsernameTextBox.Text) ||
                   !string.IsNullOrWhiteSpace(EmailTextBox.Text) ||
                   !string.IsNullOrWhiteSpace(FirstNameTextBox.Text) ||
                   !string.IsNullOrWhiteSpace(LastNameTextBox.Text) ||
                   !string.IsNullOrWhiteSpace(PhoneTextBox.Text) ||
                   !string.IsNullOrWhiteSpace(PasswordBox.Password) ||
                   SelectedRolesListBox.Items.Count > 0;
        }

        protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
        {
            if (HasUnsavedChanges() && DialogResult != true)
            {
                var result = MessageBox.Show("هل تريد إغلاق النافذة بدون حفظ التغييرات؟", 
                                           "تأكيد الإغلاق", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.No)
                {
                    e.Cancel = true;
                    return;
                }
            }

            base.OnClosing(e);
        }
    }
}
