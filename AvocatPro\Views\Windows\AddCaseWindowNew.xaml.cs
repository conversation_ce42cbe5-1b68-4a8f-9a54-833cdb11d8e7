using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using AvocatPro.Models;

namespace AvocatPro.Views.Windows;

public partial class AddCaseWindowNew : Window
{
    public Case? NewCase { get; private set; }
    public bool SaveAndAddAnother { get; private set; }

    public AddCaseWindowNew()
    {
        InitializeComponent();
        GenerateOfficeReference();
        LoadClients();
    }

    private void GenerateOfficeReference()
    {
        // توليد مرجع تلقائي للمكتب
        var reference = $"CASE-{DateTime.Now:yyyyMMdd}-{new Random().Next(1000, 9999)}";
        OfficeReferenceTextBox.Text = reference;
    }

    private void LoadClients()
    {
        // تحميل قائمة الموكلين - في التطبيق الحقيقي من قاعدة البيانات
        ClientComboBox.Items.Clear();
        ClientComboBox.Items.Add(new ComboBoxItem { Content = "أحمد محمد علي السعيد", Tag = "1" });
        ClientComboBox.Items.Add(new ComboBoxItem { Content = "شركة النور للتجارة والاستثمار", Tag = "2" });
        ClientComboBox.Items.Add(new ComboBoxItem { Content = "فاطمة أحمد سالم", Tag = "3" });
        ClientComboBox.Items.Add(new ComboBoxItem { Content = "وزارة التربية والتعليم", Tag = "4" });
        ClientComboBox.Items.Add(new ComboBoxItem { Content = "جمعية البر الخيرية", Tag = "5" });
        
        if (ClientComboBox.Items.Count > 0)
            ClientComboBox.SelectedIndex = 0;
    }

    private void SaveButton_Click(object sender, RoutedEventArgs e)
    {
        if (ValidateForm())
        {
            CreateCase();
            SaveAndAddAnother = false;
            DialogResult = true;
            Close();
        }
    }

    private void SaveAndNewButton_Click(object sender, RoutedEventArgs e)
    {
        if (ValidateForm())
        {
            CreateCase();
            SaveAndAddAnother = true;
            MessageBox.Show("تم حفظ الملف بنجاح!", "نجح الحفظ", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
            
            // مسح النموذج لإضافة ملف جديد
            ClearForm();
            GenerateOfficeReference();
        }
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("هل تريد إلغاء العملية؟\nسيتم فقدان جميع البيانات المدخلة.", 
                                   "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Question);
        
        if (result == MessageBoxResult.Yes)
        {
            DialogResult = false;
            Close();
        }
    }

    private bool ValidateForm()
    {
        var errors = new System.Collections.Generic.List<string>();

        if (string.IsNullOrWhiteSpace(OfficeReferenceTextBox.Text))
            errors.Add("مرجع المكتب مطلوب");

        if (string.IsNullOrWhiteSpace(TitleTextBox.Text))
            errors.Add("عنوان القضية مطلوب");

        if (ClientComboBox.SelectedItem == null)
            errors.Add("يجب اختيار الموكل");

        if (CourtComboBox.SelectedItem == null)
            errors.Add("يجب اختيار المحكمة");

        // التحقق من القيم المالية
        if (!string.IsNullOrWhiteSpace(FinancialValueTextBox.Text))
        {
            if (!decimal.TryParse(FinancialValueTextBox.Text, out _))
                errors.Add("القيمة المالية يجب أن تكون رقماً صحيحاً");
        }

        if (!string.IsNullOrWhiteSpace(LawyerFeesTextBox.Text))
        {
            if (!decimal.TryParse(LawyerFeesTextBox.Text, out _))
                errors.Add("أتعاب المحاماة يجب أن تكون رقماً صحيحاً");
        }

        if (!string.IsNullOrWhiteSpace(AdditionalCostsTextBox.Text))
        {
            if (!decimal.TryParse(AdditionalCostsTextBox.Text, out _))
                errors.Add("المصاريف الإضافية يجب أن تكون رقماً صحيحاً");
        }

        if (errors.Count > 0)
        {
            MessageBox.Show($"يرجى تصحيح الأخطاء التالية:\n\n{string.Join("\n", errors)}", 
                           "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
            return false;
        }

        return true;
    }

    private void CreateCase()
    {
        NewCase = new Case
        {
            OfficeReference = OfficeReferenceTextBox.Text.Trim(),
            Title = TitleTextBox.Text.Trim(),
            Description = DescriptionTextBox.Text.Trim(),
            Subject = SubjectTextBox.Text.Trim(),
            Court = (CourtComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString() ?? "",
            CourtReference = CourtReferenceTextBox.Text.Trim(),
            Opponent = OpponentTextBox.Text.Trim(),
            Type = Enum.Parse<CaseType>((CaseTypeComboBox.SelectedItem as ComboBoxItem)?.Tag?.ToString() ?? "Civil"),
            Category = CaseCategory.Lawsuit, // افتراضي
            Status = Enum.Parse<CaseStatus>((StatusComboBox.SelectedItem as ComboBoxItem)?.Tag?.ToString() ?? "Active"),
            Priority = Enum.Parse<CasePriority>((PriorityComboBox.SelectedItem as ComboBoxItem)?.Tag?.ToString() ?? "Medium"),
            CourtType = CourtType.FirstInstance, // افتراضي
            LitigationLevel = LitigationLevel.FirstInstance, // افتراضي
            ClientId = int.Parse((ClientComboBox.SelectedItem as ComboBoxItem)?.Tag?.ToString() ?? "1"),
            AssignedLawyerId = (LawyerComboBox.SelectedItem as ComboBoxItem)?.Tag != null ? 
                              int.Parse((LawyerComboBox.SelectedItem as ComboBoxItem).Tag.ToString()) : null,
            StartDate = DateTime.Now,
            Stage = "مرحلة ابتدائية",
            FinancialValue = decimal.TryParse(FinancialValueTextBox.Text, out var fv) ? fv : null,
            LawyerFees = decimal.TryParse(LawyerFeesTextBox.Text, out var lf) ? lf : null,
            AdditionalCosts = decimal.TryParse(AdditionalCostsTextBox.Text, out var ac) ? ac : null,
            CreatedAt = DateTime.Now,
            UpdatedAt = DateTime.Now
        };
    }

    private void ClearForm()
    {
        TitleTextBox.Clear();
        DescriptionTextBox.Clear();
        SubjectTextBox.Clear();
        CourtReferenceTextBox.Clear();
        OpponentTextBox.Clear();
        FinancialValueTextBox.Clear();
        LawyerFeesTextBox.Clear();
        AdditionalCostsTextBox.Clear();
        NotesTextBox.Clear();
        
        CaseTypeComboBox.SelectedIndex = 0;
        StatusComboBox.SelectedIndex = 0;
        PriorityComboBox.SelectedIndex = 1;
        CourtComboBox.SelectedIndex = 0;
        ClientComboBox.SelectedIndex = 0;
        LawyerComboBox.SelectedIndex = -1;
    }
}
