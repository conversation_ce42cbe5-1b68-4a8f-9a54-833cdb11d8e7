﻿#pragma checksum "..\..\..\..\..\Views\Windows\UserChatWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "5B1556F9BF1CF07DDF9EA173669CA20E36F7DD73"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using FontAwesome.WPF;
using FontAwesome.WPF.Converters;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Windows {
    
    
    /// <summary>
    /// UserChatWindow
    /// </summary>
    public partial class UserChatWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 63 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserAvatarText;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserNameText;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse StatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CallBtn;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button VideoCallBtn;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button UserInfoBtn;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseBtn;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer MessagesScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel MessagesPanel;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border FilesSidebar;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel SharedFilesPanel;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border FilePreviewPanel;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal FontAwesome.WPF.ImageAwesome FilePreviewIcon;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FilePreviewName;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FilePreviewSize;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RemoveFileBtn;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AttachFileBtn;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MessageTextBox;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EmojiBtn;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SendBtn;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/windows/userchatwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.UserAvatarText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.UserNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.StatusIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 4:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.CallBtn = ((System.Windows.Controls.Button)(target));
            
            #line 83 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
            this.CallBtn.Click += new System.Windows.RoutedEventHandler(this.CallBtn_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.VideoCallBtn = ((System.Windows.Controls.Button)(target));
            
            #line 89 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
            this.VideoCallBtn.Click += new System.Windows.RoutedEventHandler(this.VideoCallBtn_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.UserInfoBtn = ((System.Windows.Controls.Button)(target));
            
            #line 95 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
            this.UserInfoBtn.Click += new System.Windows.RoutedEventHandler(this.UserInfoBtn_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.CloseBtn = ((System.Windows.Controls.Button)(target));
            
            #line 101 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
            this.CloseBtn.Click += new System.Windows.RoutedEventHandler(this.CloseBtn_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.MessagesScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 10:
            this.MessagesPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 11:
            this.FilesSidebar = ((System.Windows.Controls.Border)(target));
            return;
            case 12:
            this.SharedFilesPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 13:
            this.FilePreviewPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 14:
            this.FilePreviewIcon = ((FontAwesome.WPF.ImageAwesome)(target));
            return;
            case 15:
            this.FilePreviewName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.FilePreviewSize = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.RemoveFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 180 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
            this.RemoveFileBtn.Click += new System.Windows.RoutedEventHandler(this.RemoveFileBtn_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.AttachFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 196 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
            this.AttachFileBtn.Click += new System.Windows.RoutedEventHandler(this.AttachFileBtn_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.MessageTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 206 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
            this.MessageTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.MessageTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 20:
            this.EmojiBtn = ((System.Windows.Controls.Button)(target));
            
            #line 213 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
            this.EmojiBtn.Click += new System.Windows.RoutedEventHandler(this.EmojiBtn_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.SendBtn = ((System.Windows.Controls.Button)(target));
            
            #line 220 "..\..\..\..\..\Views\Windows\UserChatWindow.xaml"
            this.SendBtn.Click += new System.Windows.RoutedEventHandler(this.SendBtn_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

