<Window x:Class="AvocatPro.Views.Windows.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="AvocatPro" Height="300" Width="400"
        WindowStartupLocation="CenterScreen">

    <StackPanel Margin="50" VerticalAlignment="Center">
        <TextBlock Text="AvocatPro" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,20"/>
        <TextBlock Text="اسم المستخدم:" Margin="0,0,0,5"/>
        <TextBox x:Name="UsernameTextBox" Height="25" Text="admin" Margin="0,0,0,10"/>
        <TextBlock Text="كلمة المرور:" Margin="0,0,0,5"/>
        <PasswordBox x:Name="PasswordBox" Height="25" Margin="0,0,0,10"/>
        <CheckBox x:Name="RememberMeCheckBox" Content="تذكرني" Margin="0,0,0,15"/>
        <Button x:Name="LoginButton" Content="دخول" Height="30" Click="Login_Click" Margin="0,0,0,10"/>
        <Button x:Name="CloseButton" Content="إغلاق" Height="25" Click="Close_Click"/>

        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15,0,0">
            <Ellipse x:Name="InternetStatusIndicator" Width="8" Height="8" Fill="Green" Margin="0,0,5,0"/>
            <TextBlock x:Name="InternetStatusText" Text="متصل" FontSize="10"/>
            <Ellipse x:Name="ServerStatusIndicator" Width="8" Height="8" Fill="Green" Margin="10,0,5,0"/>
            <TextBlock x:Name="ServerStatusText" Text="سيرفر" FontSize="10"/>
        </StackPanel>
    </StackPanel>
</Window>
