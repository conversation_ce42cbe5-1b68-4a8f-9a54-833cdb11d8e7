<Window x:Class="AvocatPro.Views.Windows.AdvancedAddSessionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة/تعديل جلسة" Height="700" Width="900"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize"
        Background="#F8FAFC" FlowDirection="RightToLeft">

    <Window.Resources>
        <Style TargetType="TextBox">
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#D1D5DB"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Style.Triggers>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="BorderBrush" Value="#6366F1"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style TargetType="ComboBox">
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#D1D5DB"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style TargetType="DatePicker">
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#D1D5DB"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style TargetType="Label">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#374151"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>

        <Style TargetType="Button" x:Key="ActionButtonStyle">
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="8" Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Background="White" CornerRadius="12" Padding="24" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
            </Border.Effect>
            
            <StackPanel>
                <TextBlock x:Name="WindowTitleText" Text="إضافة جلسة جديدة" FontSize="24" FontWeight="Bold" Foreground="#1F2937"/>
                <TextBlock Text="أدخل جميع تفاصيل الجلسة بدقة" FontSize="14" Foreground="#6B7280"/>
            </StackPanel>
        </Border>

        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <TabControl Background="Transparent" BorderThickness="0">
                
                <!-- تبويب المعلومات الأساسية -->
                <TabItem Header="📋 المعلومات الأساسية" FontSize="14" FontWeight="SemiBold">
                    <Border Background="White" CornerRadius="12" Padding="24" Margin="0,10">
                        <Border.Effect>
                            <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                        </Border.Effect>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- العمود الأول -->
                            <StackPanel Grid.Column="0">
                                <Label Content="رقم الجلسة *"/>
                                <TextBox x:Name="SessionNumberTextBox"/>
                                
                                <Label Content="رقم الملف *"/>
                                <ComboBox x:Name="FileNumberComboBox" IsEditable="True"/>
                                
                                <Label Content="الموكل *"/>
                                <ComboBox x:Name="ClientComboBox" IsEditable="True"/>
                                
                                <Label Content="نوع القضية"/>
                                <ComboBox x:Name="CaseTypeComboBox"/>
                                
                                <Label Content="المحكمة *"/>
                                <ComboBox x:Name="CourtComboBox"/>
                                
                                <Label Content="نوع الإجراء"/>
                                <ComboBox x:Name="ProcedureTypeComboBox"/>
                            </StackPanel>
                            
                            <!-- العمود الثاني -->
                            <StackPanel Grid.Column="2">
                                <Label Content="تاريخ الإجراء"/>
                                <DatePicker x:Name="ProcedureDatePicker"/>
                                
                                <Label Content="القرار"/>
                                <TextBox x:Name="DecisionTextBox"/>
                                
                                <Label Content="تاريخ الجلسة *"/>
                                <DatePicker x:Name="SessionDatePicker"/>
                                
                                <Label Content="وقت الجلسة *"/>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <ComboBox x:Name="SessionHourComboBox" Grid.Column="0" Margin="0,0,5,15"/>
                                    <ComboBox x:Name="SessionMinuteComboBox" Grid.Column="1" Margin="5,0,0,15"/>
                                </Grid>
                                
                                <Label Content="نوع الجلسة *"/>
                                <ComboBox x:Name="SessionTypeComboBox"/>
                                
                                <Label Content="المحامي المكلف *"/>
                                <ComboBox x:Name="AssignedLawyerComboBox"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                </TabItem>
                
                <!-- تبويب تفاصيل المحكمة -->
                <TabItem Header="⚖️ تفاصيل المحكمة" FontSize="14" FontWeight="SemiBold">
                    <Border Background="White" CornerRadius="12" Padding="24" Margin="0,10">
                        <Border.Effect>
                            <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                        </Border.Effect>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- العمود الأول -->
                            <StackPanel Grid.Column="0">
                                <Label Content="القاضي"/>
                                <ComboBox x:Name="JudgeComboBox"/>
                                
                                <Label Content="كاتب الضبط"/>
                                <ComboBox x:Name="ClerkComboBox"/>
                                
                                <Label Content="قاعة المحكمة"/>
                                <ComboBox x:Name="CourtRoomComboBox"/>
                                
                                <Label Content="الحالة *"/>
                                <ComboBox x:Name="StatusComboBox"/>
                                
                                <Label Content="الأولوية"/>
                                <ComboBox x:Name="PriorityComboBox"/>
                            </StackPanel>
                            
                            <!-- العمود الثاني -->
                            <StackPanel Grid.Column="2">
                                <Label Content="المصروفات (درهم)"/>
                                <TextBox x:Name="ExpensesTextBox" Text="0"/>
                                
                                <Label Content="الوثائق المطلوبة"/>
                                <TextBox x:Name="DocumentsTextBox" Height="60" TextWrapping="Wrap" AcceptsReturn="True"/>
                                
                                <Label Content="الملاحظات"/>
                                <TextBox x:Name="NotesTextBox" Height="80" TextWrapping="Wrap" AcceptsReturn="True"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                </TabItem>
                
                <!-- تبويب النتائج والمتابعة -->
                <TabItem Header="📊 النتائج والمتابعة" FontSize="14" FontWeight="SemiBold">
                    <Border Background="White" CornerRadius="12" Padding="24" Margin="0,10">
                        <Border.Effect>
                            <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                        </Border.Effect>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- العمود الأول -->
                            <StackPanel Grid.Column="0">
                                <Label Content="نتيجة الجلسة"/>
                                <TextBox x:Name="OutcomeTextBox" Height="80" TextWrapping="Wrap" AcceptsReturn="True"/>
                                
                                <Label Content="تاريخ الجلسة المقبلة"/>
                                <DatePicker x:Name="NextSessionDatePicker"/>
                                
                                <Label Content="وقت الجلسة المقبلة"/>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <ComboBox x:Name="NextSessionHourComboBox" Grid.Column="0" Margin="0,0,5,15"/>
                                    <ComboBox x:Name="NextSessionMinuteComboBox" Grid.Column="1" Margin="5,0,0,15"/>
                                </Grid>
                            </StackPanel>
                            
                            <!-- العمود الثاني -->
                            <StackPanel Grid.Column="2">
                                <Label Content="إعدادات التذكير"/>
                                <CheckBox x:Name="ReminderSentCheckBox" Content="تم إرسال التذكير" Margin="0,0,0,15"/>
                                
                                <Border Background="#F3F4F6" CornerRadius="8" Padding="15" Margin="0,10,0,0">
                                    <StackPanel>
                                        <TextBlock Text="معلومات إضافية" FontWeight="SemiBold" Margin="0,0,0,10"/>
                                        <TextBlock x:Name="CreatedByLabel" Text="تم الإنشاء بواسطة: المستخدم" FontSize="12" Foreground="#6B7280" Margin="0,0,0,5"/>
                                        <TextBlock x:Name="CreatedDateLabel" Text="تاريخ الإنشاء: اليوم" FontSize="12" Foreground="#6B7280" Margin="0,0,0,5"/>
                                        <TextBlock x:Name="LastUpdatedLabel" Text="آخر تحديث: لم يتم التحديث" FontSize="12" Foreground="#6B7280"/>
                                    </StackPanel>
                                </Border>
                            </StackPanel>
                        </Grid>
                    </Border>
                </TabItem>
            </TabControl>
        </ScrollViewer>

        <!-- أزرار الحفظ -->
        <Border Grid.Row="2" Background="White" CornerRadius="12" Padding="20" Margin="0,20,0,0">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
            </Border.Effect>
            
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="SaveButton" Content="💾 حفظ الجلسة" Background="#10B981" Foreground="White" 
                        Style="{StaticResource ActionButtonStyle}" Click="SaveButton_Click"/>
                <Button x:Name="SaveAndNewButton" Content="💾➕ حفظ وإضافة جديد" Background="#6366F1" Foreground="White" 
                        Style="{StaticResource ActionButtonStyle}" Click="SaveAndNewButton_Click"/>
                <Button x:Name="CancelButton" Content="❌ إلغاء" Background="#6B7280" Foreground="White" 
                        Style="{StaticResource ActionButtonStyle}" Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
