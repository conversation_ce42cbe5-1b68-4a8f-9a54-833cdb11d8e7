using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using AvocatPro.Models;
using AvocatPro.Services;

namespace AvocatPro.Views.Windows
{
    /// <summary>
    /// نافذة إضافة/تعديل الجلسة المتقدمة
    /// </summary>
    public partial class AdvancedAddSessionWindow : Window
    {
        #region Fields

        private readonly SessionsService _sessionsService;
        private readonly MoroccanCourtsService _courtsService;
        private AdvancedSessionModel? _existingSession;
        private bool _isEditMode;

        #endregion

        #region Properties

        public AdvancedSessionModel NewSession { get; private set; }
        public bool DialogResult { get; private set; }

        #endregion

        #region Constructors

        /// <summary>
        /// إنشاء نافذة إضافة جلسة جديدة
        /// </summary>
        public AdvancedAddSessionWindow()
        {
            InitializeComponent();
            
            _sessionsService = new SessionsService();
            _courtsService = new MoroccanCourtsService();
            _isEditMode = false;
            
            NewSession = new AdvancedSessionModel
            {
                SessionDate = DateTime.Now.AddDays(1),
                SessionTime = new TimeSpan(9, 0, 0),
                ProcedureDate = DateTime.Now,
                Status = "مجدولة",
                Priority = "عادي",
                CreatedDate = DateTime.Now,
                CreatedBy = "المستخدم الحالي"
            };

            InitializeControls();
            GenerateSessionNumber();
        }

        /// <summary>
        /// إنشاء نافذة تعديل جلسة موجودة
        /// </summary>
        public AdvancedAddSessionWindow(AdvancedSessionModel existingSession) : this()
        {
            _existingSession = existingSession;
            _isEditMode = true;
            
            WindowTitleText.Text = "تعديل الجلسة";
            Title = "تعديل الجلسة";
            
            LoadSessionData();
        }

        #endregion

        #region Initialization

        private void InitializeControls()
        {
            // إعداد قوائم الساعات والدقائق
            SetupTimeControls();
            
            // إعداد القوائم المنسدلة
            SetupComboBoxes();
            
            // إعداد التواريخ الافتراضية
            SessionDatePicker.SelectedDate = NewSession.SessionDate;
            ProcedureDatePicker.SelectedDate = NewSession.ProcedureDate;
        }

        private void SetupTimeControls()
        {
            // إعداد الساعات (من 8 صباحاً إلى 6 مساءً)
            for (int hour = 8; hour <= 18; hour++)
            {
                SessionHourComboBox.Items.Add(hour.ToString("00"));
                NextSessionHourComboBox.Items.Add(hour.ToString("00"));
            }

            // إعداد الدقائق (كل 15 دقيقة)
            for (int minute = 0; minute < 60; minute += 15)
            {
                SessionMinuteComboBox.Items.Add(minute.ToString("00"));
                NextSessionMinuteComboBox.Items.Add(minute.ToString("00"));
            }

            // تعيين القيم الافتراضية
            SessionHourComboBox.SelectedItem = "09";
            SessionMinuteComboBox.SelectedItem = "00";
        }

        private void SetupComboBoxes()
        {
            // إعداد أنواع القضايا
            var caseTypes = _courtsService.GetCaseTypes();
            foreach (var type in caseTypes)
            {
                CaseTypeComboBox.Items.Add(type);
            }

            // إعداد المحاكم
            var courts = _courtsService.GetMoroccanCourts();
            foreach (var court in courts)
            {
                CourtComboBox.Items.Add(court);
            }

            // إعداد أنواع الإجراءات
            var procedureTypes = _sessionsService.GetProcedureTypes();
            foreach (var type in procedureTypes)
            {
                ProcedureTypeComboBox.Items.Add(type);
            }

            // إعداد أنواع الجلسات
            var sessionTypes = _sessionsService.GetSessionTypes();
            foreach (var type in sessionTypes)
            {
                SessionTypeComboBox.Items.Add(type);
            }

            // إعداد المحامين
            var lawyers = _sessionsService.GetLawyers();
            foreach (var lawyer in lawyers)
            {
                AssignedLawyerComboBox.Items.Add(lawyer);
            }

            // إعداد القضاة
            var judges = _sessionsService.GetJudges();
            foreach (var judge in judges)
            {
                JudgeComboBox.Items.Add(judge);
            }

            // إعداد كتاب الضبط
            var clerks = _sessionsService.GetClerks();
            foreach (var clerk in clerks)
            {
                ClerkComboBox.Items.Add(clerk);
            }

            // إعداد قاعات المحكمة
            var courtRooms = _sessionsService.GetCourtRooms();
            foreach (var room in courtRooms)
            {
                CourtRoomComboBox.Items.Add(room);
            }

            // إعداد حالات الجلسات
            var statuses = _sessionsService.GetSessionStatuses();
            foreach (var status in statuses)
            {
                StatusComboBox.Items.Add(status);
            }

            // إعداد مستويات الأولوية
            var priorities = _sessionsService.GetPriorityLevels();
            foreach (var priority in priorities)
            {
                PriorityComboBox.Items.Add(priority);
            }

            // تعيين القيم الافتراضية
            StatusComboBox.SelectedItem = "مجدولة";
            PriorityComboBox.SelectedItem = "عادي";
        }

        private void GenerateSessionNumber()
        {
            if (!_isEditMode)
            {
                var year = DateTime.Now.Year;
                var sessionNumber = $"S{year}{DateTime.Now:MMdd}{DateTime.Now:HHmmss}";
                SessionNumberTextBox.Text = sessionNumber;
                NewSession.SessionNumber = sessionNumber;
            }
        }

        private void LoadSessionData()
        {
            if (_existingSession == null) return;

            // تحميل البيانات الأساسية
            SessionNumberTextBox.Text = _existingSession.SessionNumber;
            FileNumberComboBox.Text = _existingSession.FileNumber;
            ClientComboBox.Text = _existingSession.Client;
            CaseTypeComboBox.SelectedItem = _existingSession.CaseType;
            CourtComboBox.SelectedItem = _existingSession.Court;
            ProcedureTypeComboBox.SelectedItem = _existingSession.ProcedureType;
            ProcedureDatePicker.SelectedDate = _existingSession.ProcedureDate;
            DecisionTextBox.Text = _existingSession.Decision;
            SessionDatePicker.SelectedDate = _existingSession.SessionDate;
            SessionTypeComboBox.SelectedItem = _existingSession.SessionType;
            AssignedLawyerComboBox.SelectedItem = _existingSession.AssignedLawyer;

            // تحميل وقت الجلسة
            SessionHourComboBox.SelectedItem = _existingSession.SessionTime.Hours.ToString("00");
            SessionMinuteComboBox.SelectedItem = _existingSession.SessionTime.Minutes.ToString("00");

            // تحميل تفاصيل المحكمة
            JudgeComboBox.SelectedItem = _existingSession.Judge;
            ClerkComboBox.SelectedItem = _existingSession.Clerk;
            CourtRoomComboBox.SelectedItem = _existingSession.CourtRoom;
            StatusComboBox.SelectedItem = _existingSession.Status;
            PriorityComboBox.SelectedItem = _existingSession.Priority;
            ExpensesTextBox.Text = _existingSession.Expenses.ToString();
            DocumentsTextBox.Text = _existingSession.Documents;
            NotesTextBox.Text = _existingSession.Notes;

            // تحميل النتائج والمتابعة
            OutcomeTextBox.Text = _existingSession.Outcome;
            NextSessionDatePicker.SelectedDate = _existingSession.NextSessionDate;
            
            if (_existingSession.NextSessionTime.HasValue)
            {
                NextSessionHourComboBox.SelectedItem = _existingSession.NextSessionTime.Value.Hours.ToString("00");
                NextSessionMinuteComboBox.SelectedItem = _existingSession.NextSessionTime.Value.Minutes.ToString("00");
            }

            ReminderSentCheckBox.IsChecked = _existingSession.ReminderSent;

            // تحديث معلومات الإنشاء والتعديل
            CreatedByLabel.Text = $"تم الإنشاء بواسطة: {_existingSession.CreatedBy}";
            CreatedDateLabel.Text = $"تاريخ الإنشاء: {_existingSession.CreatedDate:dd/MM/yyyy HH:mm}";
            
            if (_existingSession.LastUpdated.HasValue)
            {
                LastUpdatedLabel.Text = $"آخر تحديث: {_existingSession.LastUpdated:dd/MM/yyyy HH:mm}";
            }

            // نسخ البيانات إلى NewSession
            NewSession = new AdvancedSessionModel
            {
                Id = _existingSession.Id,
                SessionNumber = _existingSession.SessionNumber,
                FileNumber = _existingSession.FileNumber,
                Client = _existingSession.Client,
                CaseType = _existingSession.CaseType,
                Court = _existingSession.Court,
                ProcedureType = _existingSession.ProcedureType,
                ProcedureDate = _existingSession.ProcedureDate,
                Decision = _existingSession.Decision,
                SessionDate = _existingSession.SessionDate,
                SessionTime = _existingSession.SessionTime,
                SessionType = _existingSession.SessionType,
                AssignedLawyer = _existingSession.AssignedLawyer,
                Judge = _existingSession.Judge,
                Clerk = _existingSession.Clerk,
                CourtRoom = _existingSession.CourtRoom,
                Status = _existingSession.Status,
                Priority = _existingSession.Priority,
                Expenses = _existingSession.Expenses,
                Documents = _existingSession.Documents,
                Notes = _existingSession.Notes,
                Outcome = _existingSession.Outcome,
                NextSessionDate = _existingSession.NextSessionDate,
                NextSessionTime = _existingSession.NextSessionTime,
                ReminderSent = _existingSession.ReminderSent,
                CreatedDate = _existingSession.CreatedDate,
                CreatedBy = _existingSession.CreatedBy,
                LastUpdated = _existingSession.LastUpdated
            };
        }

        #endregion

        #region Validation

        private bool ValidateInput()
        {
            var errors = new System.Collections.Generic.List<string>();

            // التحقق من الحقول المطلوبة
            if (string.IsNullOrWhiteSpace(SessionNumberTextBox.Text))
                errors.Add("رقم الجلسة مطلوب");

            if (string.IsNullOrWhiteSpace(FileNumberComboBox.Text))
                errors.Add("رقم الملف مطلوب");

            if (string.IsNullOrWhiteSpace(ClientComboBox.Text))
                errors.Add("اسم الموكل مطلوب");

            if (CourtComboBox.SelectedItem == null)
                errors.Add("المحكمة مطلوبة");

            if (SessionDatePicker.SelectedDate == null)
                errors.Add("تاريخ الجلسة مطلوب");

            if (SessionHourComboBox.SelectedItem == null || SessionMinuteComboBox.SelectedItem == null)
                errors.Add("وقت الجلسة مطلوب");

            if (SessionTypeComboBox.SelectedItem == null)
                errors.Add("نوع الجلسة مطلوب");

            if (AssignedLawyerComboBox.SelectedItem == null)
                errors.Add("المحامي المكلف مطلوب");

            if (StatusComboBox.SelectedItem == null)
                errors.Add("حالة الجلسة مطلوبة");

            // التحقق من صحة المصروفات
            if (!decimal.TryParse(ExpensesTextBox.Text, out decimal expenses) || expenses < 0)
                errors.Add("المصروفات يجب أن تكون رقماً صحيحاً وغير سالب");

            // التحقق من التاريخ
            if (SessionDatePicker.SelectedDate < DateTime.Now.Date && 
                StatusComboBox.SelectedItem?.ToString() == "مجدولة")
                errors.Add("لا يمكن جدولة جلسة في تاريخ سابق");

            if (errors.Any())
            {
                MessageBox.Show(string.Join("\n", errors), "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        #endregion

        #region Data Collection

        private void CollectData()
        {
            NewSession.SessionNumber = SessionNumberTextBox.Text.Trim();
            NewSession.FileNumber = FileNumberComboBox.Text.Trim();
            NewSession.Client = ClientComboBox.Text.Trim();
            NewSession.CaseType = CaseTypeComboBox.SelectedItem?.ToString() ?? "";
            NewSession.Court = CourtComboBox.SelectedItem?.ToString() ?? "";
            NewSession.ProcedureType = ProcedureTypeComboBox.SelectedItem?.ToString() ?? "";
            NewSession.ProcedureDate = ProcedureDatePicker.SelectedDate ?? DateTime.Now;
            NewSession.Decision = DecisionTextBox.Text.Trim();
            NewSession.SessionDate = SessionDatePicker.SelectedDate ?? DateTime.Now;
            
            // تجميع وقت الجلسة
            if (int.TryParse(SessionHourComboBox.SelectedItem?.ToString(), out int hour) &&
                int.TryParse(SessionMinuteComboBox.SelectedItem?.ToString(), out int minute))
            {
                NewSession.SessionTime = new TimeSpan(hour, minute, 0);
            }

            NewSession.SessionType = SessionTypeComboBox.SelectedItem?.ToString() ?? "";
            NewSession.AssignedLawyer = AssignedLawyerComboBox.SelectedItem?.ToString() ?? "";
            NewSession.Judge = JudgeComboBox.SelectedItem?.ToString() ?? "";
            NewSession.Clerk = ClerkComboBox.SelectedItem?.ToString() ?? "";
            NewSession.CourtRoom = CourtRoomComboBox.SelectedItem?.ToString() ?? "";
            NewSession.Status = StatusComboBox.SelectedItem?.ToString() ?? "";
            NewSession.Priority = PriorityComboBox.SelectedItem?.ToString() ?? "";
            
            if (decimal.TryParse(ExpensesTextBox.Text, out decimal expenses))
            {
                NewSession.Expenses = expenses;
            }

            NewSession.Documents = DocumentsTextBox.Text.Trim();
            NewSession.Notes = NotesTextBox.Text.Trim();
            NewSession.Outcome = OutcomeTextBox.Text.Trim();
            NewSession.NextSessionDate = NextSessionDatePicker.SelectedDate;
            
            // تجميع وقت الجلسة المقبلة
            if (int.TryParse(NextSessionHourComboBox.SelectedItem?.ToString(), out int nextHour) &&
                int.TryParse(NextSessionMinuteComboBox.SelectedItem?.ToString(), out int nextMinute))
            {
                NewSession.NextSessionTime = new TimeSpan(nextHour, nextMinute, 0);
            }

            NewSession.ReminderSent = ReminderSentCheckBox.IsChecked ?? false;

            // تحديث معلومات التعديل
            if (_isEditMode)
            {
                NewSession.LastUpdated = DateTime.Now;
            }
        }

        #endregion

        #region Event Handlers

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInput())
            {
                CollectData();
                DialogResult = true;
                Close();
            }
        }

        private void SaveAndNewButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInput())
            {
                CollectData();
                DialogResult = true;
                
                // إعادة تعيين النموذج لجلسة جديدة
                var newSessionWindow = new AdvancedAddSessionWindow();
                newSessionWindow.Show();
                
                Close();
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        #endregion
    }
}
