using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using AvocatPro.Models;

namespace AvocatPro.Views.Windows;

public partial class AddRevenueWindow : Window
{
    public Revenue? NewRevenue { get; private set; }
    public bool SaveAndAddAnother { get; private set; }
    public bool SaveAndInvoice { get; private set; }
    
    private readonly ObservableCollection<string> _attachments;

    public AddRevenueWindow()
    {
        InitializeComponent();
        _attachments = new ObservableCollection<string>();
        AttachmentsListBox.ItemsSource = _attachments;
        InitializeForm();
    }

    private void InitializeForm()
    {
        GenerateReference();
        SetDefaultValues();
    }

    private void GenerateReference()
    {
        var reference = $"REV-{DateTime.Now:yyyyMMdd}-{DateTime.Now:HHmmss}";
        ReferenceTextBox.Text = reference;
    }

    private void SetDefaultValues()
    {
        RevenueDatePicker.SelectedDate = DateTime.Now;
        TypeComboBox.SelectedIndex = 0; // أتعاب محاماة
        PaymentMethodComboBox.SelectedIndex = 0; // تحويل بنكي
        PaymentStatusComboBox.SelectedIndex = 0; // في الانتظار
        ClientComboBox.SelectedIndex = 0;
        CaseComboBox.SelectedIndex = 0; // بدون قضية
        PaymentTermsComboBox.SelectedIndex = 3; // 30 يوم
        
        // حساب تاريخ الاستحقاق الافتراضي
        CalculateDueDate();
    }

    private void CalculateDueDateButton_Click(object sender, RoutedEventArgs e)
    {
        CalculateDueDate();
    }

    private void CalculateDueDate()
    {
        if (RevenueDatePicker.SelectedDate.HasValue && PaymentTermsComboBox.SelectedItem != null)
        {
            var days = int.Parse((string)((ComboBoxItem)PaymentTermsComboBox.SelectedItem).Tag);
            DueDatePicker.SelectedDate = RevenueDatePicker.SelectedDate.Value.AddDays(days);
        }
    }

    private void AddAttachmentButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Title = "اختيار ملف للإرفاق",
                Filter = "جميع الملفات (*.*)|*.*|ملفات PDF (*.pdf)|*.pdf|صور (*.jpg;*.jpeg;*.png)|*.jpg;*.jpeg;*.png|مستندات Word (*.doc;*.docx)|*.doc;*.docx|جداول Excel (*.xls;*.xlsx)|*.xls;*.xlsx",
                Multiselect = true
            };

            if (openFileDialog.ShowDialog() == true)
            {
                foreach (var fileName in openFileDialog.FileNames)
                {
                    var fileInfo = new System.IO.FileInfo(fileName);
                    if (!_attachments.Contains(fileInfo.Name))
                    {
                        _attachments.Add(fileInfo.Name);
                    }
                }
                
                if (openFileDialog.FileNames.Length > 0)
                {
                    MessageBox.Show($"تم إرفاق {openFileDialog.FileNames.Length} ملف بنجاح!", "إرفاق الملفات", 
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إرفاق الملف: {ex.Message}", "خطأ", 
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void GenerateInvoiceButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (ValidateBasicInfo())
            {
                var invoiceNumber = $"INV-{DateTime.Now:yyyyMMdd}-{DateTime.Now:HHmmss}";
                InvoiceNumberTextBox.Text = invoiceNumber;
                
                var invoiceFileName = $"فاتورة_{invoiceNumber}.pdf";
                _attachments.Add(invoiceFileName);
                
                MessageBox.Show($"تم إنشاء الفاتورة بنجاح!\n\nرقم الفاتورة: {invoiceNumber}\nاسم الملف: {invoiceFileName}", 
                               "إنشاء الفاتورة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إنشاء الفاتورة: {ex.Message}", "خطأ", 
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void SendInvoiceButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(InvoiceNumberTextBox.Text))
            {
                MessageBox.Show("يجب إنشاء الفاتورة أولاً قبل الإرسال.", "تنبيه", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show("هل تريد إرسال الفاتورة للموكل؟\n\nسيتم الإرسال عبر:\n• البريد الإلكتروني\n• الرسائل النصية\n• واتساب", 
                                       "إرسال الفاتورة", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                MessageBox.Show("تم إرسال الفاتورة بنجاح للموكل!", "نجح الإرسال", 
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إرسال الفاتورة: {ex.Message}", "خطأ", 
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void RemoveAttachmentButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is string fileName)
        {
            var result = MessageBox.Show($"هل تريد حذف المرفق: {fileName}؟", "تأكيد الحذف", 
                                       MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                _attachments.Remove(fileName);
            }
        }
    }

    private void SaveButton_Click(object sender, RoutedEventArgs e)
    {
        if (ValidateForm())
        {
            CreateRevenue();
            SaveAndAddAnother = false;
            SaveAndInvoice = false;
            DialogResult = true;
            Close();
        }
    }

    private void SaveAndNewButton_Click(object sender, RoutedEventArgs e)
    {
        if (ValidateForm())
        {
            CreateRevenue();
            SaveAndAddAnother = true;
            SaveAndInvoice = false;
            MessageBox.Show("تم حفظ الإيراد بنجاح!", "نجح الحفظ", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
            
            // مسح النموذج لإضافة إيراد جديد
            ClearForm();
            GenerateReference();
            SetDefaultValues();
        }
    }

    private void SaveAndInvoiceButton_Click(object sender, RoutedEventArgs e)
    {
        if (ValidateForm())
        {
            CreateRevenue();
            SaveAndAddAnother = false;
            SaveAndInvoice = true;
            DialogResult = true;
            Close();
        }
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("هل تريد إلغاء العملية؟\nسيتم فقدان جميع البيانات المدخلة.", 
                                   "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Question);
        
        if (result == MessageBoxResult.Yes)
        {
            DialogResult = false;
            Close();
        }
    }

    private bool ValidateBasicInfo()
    {
        return !string.IsNullOrWhiteSpace(DescriptionTextBox.Text) &&
               !string.IsNullOrWhiteSpace(AmountTextBox.Text) &&
               decimal.TryParse(AmountTextBox.Text, out var amount) && amount > 0 &&
               ClientComboBox.SelectedItem != null;
    }

    private bool ValidateForm()
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(DescriptionTextBox.Text))
            errors.Add("يجب إدخال وصف الإيراد");

        if (string.IsNullOrWhiteSpace(AmountTextBox.Text))
            errors.Add("يجب إدخال مبلغ الإيراد");
        else if (!decimal.TryParse(AmountTextBox.Text, out var amount) || amount <= 0)
            errors.Add("مبلغ الإيراد يجب أن يكون رقماً صحيحاً أكبر من صفر");

        if (!RevenueDatePicker.SelectedDate.HasValue)
            errors.Add("يجب تحديد تاريخ الإيراد");

        if (TypeComboBox.SelectedItem == null)
            errors.Add("يجب اختيار نوع الإيراد");

        if (ClientComboBox.SelectedItem == null)
            errors.Add("يجب اختيار الموكل");

        if (PaymentMethodComboBox.SelectedItem == null)
            errors.Add("يجب اختيار طريقة الدفع");

        if (PaymentStatusComboBox.SelectedItem == null)
            errors.Add("يجب اختيار حالة الدفع");

        // التحقق من تاريخ الدفع إذا كانت الحالة مدفوع
        var selectedStatus = (PaymentStatusComboBox.SelectedItem as ComboBoxItem)?.Tag?.ToString();
        if (selectedStatus == "Paid" && !PaymentDatePicker.SelectedDate.HasValue)
            errors.Add("يجب تحديد تاريخ الدفع عند اختيار حالة 'مدفوع'");

        if (errors.Count > 0)
        {
            MessageBox.Show($"يرجى تصحيح الأخطاء التالية:\n\n{string.Join("\n", errors)}", 
                           "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
            return false;
        }

        return true;
    }

    private void CreateRevenue()
    {
        var attachmentsJson = _attachments.Count > 0 ? 
            System.Text.Json.JsonSerializer.Serialize(_attachments.ToList()) : null;

        NewRevenue = new Revenue
        {
            Reference = ReferenceTextBox.Text.Trim(),
            Description = DescriptionTextBox.Text.Trim(),
            Amount = decimal.Parse(AmountTextBox.Text),
            RevenueDate = RevenueDatePicker.SelectedDate!.Value,
            Type = Enum.Parse<RevenueType>((string)((ComboBoxItem)TypeComboBox.SelectedItem).Tag),
            ClientId = int.Parse((string)((ComboBoxItem)ClientComboBox.SelectedItem).Tag),
            CaseId = GetSelectedId(CaseComboBox),
            InvoiceNumber = InvoiceNumberTextBox.Text.Trim(),
            PaymentMethod = Enum.Parse<PaymentMethod>((string)((ComboBoxItem)PaymentMethodComboBox.SelectedItem).Tag),
            PaymentStatus = Enum.Parse<PaymentStatus>((string)((ComboBoxItem)PaymentStatusComboBox.SelectedItem).Tag),
            PaymentDate = PaymentDatePicker.SelectedDate,
            DueDate = DueDatePicker.SelectedDate,
            Attachments = attachmentsJson,
            CreatedAt = DateTime.Now,
            UpdatedAt = DateTime.Now
        };
    }

    private int? GetSelectedId(ComboBox comboBox)
    {
        if (comboBox.SelectedItem is ComboBoxItem item && item.Tag is string tag)
        {
            if (int.TryParse(tag, out var id) && id > 0)
                return id;
        }
        return null;
    }

    private void ClearForm()
    {
        DescriptionTextBox.Clear();
        AmountTextBox.Clear();
        InvoiceNumberTextBox.Clear();
        NotesTextBox.Clear();
        
        TypeComboBox.SelectedIndex = 0;
        PaymentMethodComboBox.SelectedIndex = 0;
        PaymentStatusComboBox.SelectedIndex = 0;
        ClientComboBox.SelectedIndex = 0;
        CaseComboBox.SelectedIndex = 0;
        PaymentTermsComboBox.SelectedIndex = 3;
        
        PaymentDatePicker.SelectedDate = null;
        DueDatePicker.SelectedDate = null;
        
        _attachments.Clear();
    }
}
