<Page x:Class="AvocatPro.Views.Pages.AppointmentsPageNew"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="إدارة المواعيد"
      FlowDirection="RightToLeft">

    <Page.Resources>
        <!-- أنماط الصفحة -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1976D2"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>

        <Style x:Key="StatCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#1976D2"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5,0"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1565C0"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="FontSize" Value="14"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F0F0F0"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="CalendarDayStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Margin" Value="1"/>
            <Setter Property="Padding" Value="5"/>
            <Setter Property="Height" Value="90"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F5F5F5"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="AppointmentItemStyle" TargetType="Border">
            <Setter Property="CornerRadius" Value="3"/>
            <Setter Property="Padding" Value="3,1"/>
            <Setter Property="Margin" Value="0,1"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>

        <Style x:Key="FilterComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Margin" Value="5,0"/>
        </Style>

        <Style x:Key="NotificationBadgeStyle" TargetType="Border">
            <Setter Property="Background" Value="#F44336"/>
            <Setter Property="CornerRadius" Value="10"/>
            <Setter Property="Padding" Value="5,2"/>
            <Setter Property="Margin" Value="5,0"/>
        </Style>
    </Page.Resources>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- رأس الصفحة -->
        <Border Grid.Row="0" Background="White" Padding="30,20" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="📅" FontSize="32" Margin="0,0,15,0"/>
                    <StackPanel>
                        <TextBlock Text="إدارة المواعيد والتنبيهات" Style="{StaticResource HeaderTextStyle}"/>
                        <TextBlock Text="تقويم ذكي مع تنبيهات فورية ومتابعة احترافية للمواعيد" FontSize="14" Foreground="#666"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <!-- تنبيهات فورية -->
                    <Button Name="NotificationsButton" Style="{StaticResource PrimaryButtonStyle}" 
                           Background="#FF9800" Click="NotificationsButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔔" FontSize="16" Margin="0,0,5,0"/>
                            <TextBlock Text="التنبيهات"/>
                            <Border Name="NotificationBadge" Style="{StaticResource NotificationBadgeStyle}" Visibility="Collapsed">
                                <TextBlock Name="NotificationCount" Text="0" FontSize="10" Foreground="White" FontWeight="Bold"/>
                            </Border>
                        </StackPanel>
                    </Button>
                    
                    <Button Name="AddAppointmentButton" Style="{StaticResource PrimaryButtonStyle}" Click="AddAppointmentButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="➕" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="إضافة موعد جديد"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Name="RefreshButton" Style="{StaticResource PrimaryButtonStyle}" 
                           Background="#4CAF50" Click="RefreshButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔄" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- بطاقات الإحصائيات -->
        <Grid Grid.Row="1" Margin="20,0,20,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Border Grid.Column="0" Style="{StaticResource StatCardStyle}">
                <StackPanel>
                    <TextBlock Text="📊 إجمالي المواعيد" FontSize="12" Foreground="#666" Margin="0,0,0,8"/>
                    <TextBlock Name="TotalAppointmentsTextBlock" Text="0" FontSize="24" FontWeight="Bold" Foreground="#1976D2"/>
                </StackPanel>
            </Border>

            <Border Grid.Column="1" Style="{StaticResource StatCardStyle}">
                <StackPanel>
                    <TextBlock Text="📅 مواعيد اليوم" FontSize="12" Foreground="#666" Margin="0,0,0,8"/>
                    <TextBlock Name="TodayAppointmentsTextBlock" Text="0" FontSize="24" FontWeight="Bold" Foreground="#4CAF50"/>
                </StackPanel>
            </Border>

            <Border Grid.Column="2" Style="{StaticResource StatCardStyle}">
                <StackPanel>
                    <TextBlock Text="⏰ مواعيد الأسبوع" FontSize="12" Foreground="#666" Margin="0,0,0,8"/>
                    <TextBlock Name="WeekAppointmentsTextBlock" Text="0" FontSize="24" FontWeight="Bold" Foreground="#FF9800"/>
                </StackPanel>
            </Border>

            <Border Grid.Column="3" Style="{StaticResource StatCardStyle}">
                <StackPanel>
                    <TextBlock Text="✅ مواعيد مؤكدة" FontSize="12" Foreground="#666" Margin="0,0,0,8"/>
                    <TextBlock Name="ConfirmedAppointmentsTextBlock" Text="0" FontSize="24" FontWeight="Bold" Foreground="#9C27B0"/>
                </StackPanel>
            </Border>

            <Border Grid.Column="4" Style="{StaticResource StatCardStyle}">
                <StackPanel>
                    <TextBlock Text="🔔 تنبيهات معلقة" FontSize="12" Foreground="#666" Margin="0,0,0,8"/>
                    <TextBlock Name="PendingNotificationsTextBlock" Text="0" FontSize="24" FontWeight="Bold" Foreground="#F44336"/>
                </StackPanel>
            </Border>

            <Border Grid.Column="5" Style="{StaticResource StatCardStyle}">
                <StackPanel>
                    <TextBlock Text="💰 إجمالي الإيرادات" FontSize="12" Foreground="#666" Margin="0,0,0,8"/>
                    <TextBlock Name="TotalRevenueTextBlock" Text="0" FontSize="24" FontWeight="Bold" Foreground="#2E7D32"/>
                </StackPanel>
            </Border>
        </Grid>

        <!-- شريط التحكم والتصفية -->
        <Border Grid.Row="2" Background="White" Padding="20" Margin="20,0,20,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- أزرار التنقل في التقويم -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Button Name="PrevMonthButton" Style="{StaticResource PrimaryButtonStyle}" 
                           Background="#607D8B" Click="PrevMonthButton_Click">
                        <TextBlock Text="◀ الشهر السابق"/>
                    </Button>
                    
                    <TextBlock Name="CurrentMonthTextBlock" Text="ديسمبر 2024" 
                              FontSize="18" FontWeight="Bold" Foreground="#1976D2"
                              VerticalAlignment="Center" Margin="20,0"/>
                    
                    <Button Name="NextMonthButton" Style="{StaticResource PrimaryButtonStyle}" 
                           Background="#607D8B" Click="NextMonthButton_Click">
                        <TextBlock Text="الشهر التالي ▶"/>
                    </Button>
                </StackPanel>

                <!-- مرشحات -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                    <ComboBox Name="AppointmentTypeFilterComboBox" Style="{StaticResource FilterComboBoxStyle}"
                             SelectionChanged="FilterComboBox_SelectionChanged">
                        <ComboBoxItem Content="جميع الأنواع" Tag="All" IsSelected="True"/>
                        <ComboBoxItem Content="💼 استشارة" Tag="Consultation"/>
                        <ComboBoxItem Content="🤝 اجتماع" Tag="Meeting"/>
                        <ComboBoxItem Content="📞 مكالمة هاتفية" Tag="PhoneCall"/>
                        <ComboBoxItem Content="🚗 زيارة ميدانية" Tag="FieldVisit"/>
                        <ComboBoxItem Content="📝 توقيع عقد" Tag="ContractSigning"/>
                        <ComboBoxItem Content="📋 متابعة قضية" Tag="CaseFollowUp"/>
                        <ComboBoxItem Content="👤 موعد شخصي" Tag="Personal"/>
                        <ComboBoxItem Content="🎓 تدريب" Tag="Training"/>
                    </ComboBox>

                    <ComboBox Name="AppointmentStatusFilterComboBox" Style="{StaticResource FilterComboBoxStyle}"
                             SelectionChanged="FilterComboBox_SelectionChanged">
                        <ComboBoxItem Content="جميع الحالات" Tag="All" IsSelected="True"/>
                        <ComboBoxItem Content="📅 مجدول" Tag="Scheduled"/>
                        <ComboBoxItem Content="✅ مؤكد" Tag="Confirmed"/>
                        <ComboBoxItem Content="⏳ جاري" Tag="InProgress"/>
                        <ComboBoxItem Content="✔️ مكتمل" Tag="Completed"/>
                        <ComboBoxItem Content="⏰ مؤجل" Tag="Postponed"/>
                        <ComboBoxItem Content="❌ ملغي" Tag="Cancelled"/>
                        <ComboBoxItem Content="❓ لم يحضر" Tag="NoShow"/>
                    </ComboBox>

                    <ComboBox Name="PriorityFilterComboBox" Style="{StaticResource FilterComboBoxStyle}"
                             SelectionChanged="FilterComboBox_SelectionChanged">
                        <ComboBoxItem Content="جميع الأولويات" Tag="All" IsSelected="True"/>
                        <ComboBoxItem Content="🟢 منخفضة" Tag="Low"/>
                        <ComboBoxItem Content="🟡 متوسطة" Tag="Medium"/>
                        <ComboBoxItem Content="🟠 عالية" Tag="High"/>
                        <ComboBoxItem Content="🔴 عاجلة" Tag="Urgent"/>
                    </ComboBox>
                </StackPanel>

                <!-- أزرار الإجراءات -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Name="SendNotificationsButton" Style="{StaticResource PrimaryButtonStyle}" 
                           Background="#9C27B0" Click="SendNotificationsButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📧" FontSize="14" Margin="0,0,5,0"/>
                            <TextBlock Text="إرسال تنبيهات"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Name="ExportButton" Style="{StaticResource PrimaryButtonStyle}" 
                           Background="#FF9800" Click="ExportButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📤" FontSize="14" Margin="0,0,5,0"/>
                            <TextBlock Text="تصدير"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Name="ViewModeButton" Style="{StaticResource PrimaryButtonStyle}" 
                           Background="#795548" Click="ViewModeButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Name="ViewModeIcon" Text="📋" FontSize="14" Margin="0,0,5,0"/>
                            <TextBlock Name="ViewModeText" Text="عرض الجدول"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="3" Margin="20,0,20,20">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- التقويم -->
            <Border Name="CalendarView" Background="White" CornerRadius="8" Padding="20">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- رؤوس أيام الأسبوع -->
                    <Grid Grid.Row="0" Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="السبت" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#1976D2"/>
                        <TextBlock Grid.Column="1" Text="الأحد" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#1976D2"/>
                        <TextBlock Grid.Column="2" Text="الاثنين" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#1976D2"/>
                        <TextBlock Grid.Column="3" Text="الثلاثاء" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#1976D2"/>
                        <TextBlock Grid.Column="4" Text="الأربعاء" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#1976D2"/>
                        <TextBlock Grid.Column="5" Text="الخميس" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#1976D2"/>
                        <TextBlock Grid.Column="6" Text="الجمعة" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#F44336"/>
                    </Grid>

                    <!-- أيام التقويم -->
                    <UniformGrid Name="CalendarGrid" Grid.Row="1" Columns="7" Rows="6"/>
                </Grid>
            </Border>

            <!-- عرض الجدول -->
            <Border Name="TableView" Background="White" CornerRadius="8" Visibility="Collapsed">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- شريط الأدوات -->
                    <Border Grid.Row="0" Background="#F8F9FA" Padding="15" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
                        <TextBlock Text="📋 جدول المواعيد" FontSize="16" FontWeight="Bold" Foreground="#1976D2"/>
                    </Border>

                    <!-- الجدول -->
                    <DataGrid Name="AppointmentsDataGrid" Grid.Row="1" 
                             AutoGenerateColumns="False" 
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             AlternatingRowBackground="#F9F9F9"
                             RowHeight="45"
                             FontSize="12">
                        
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="60"/>
                            <DataGridTextColumn Header="العنوان" Binding="{Binding Title}" Width="150"/>
                            <DataGridTextColumn Header="التاريخ والوقت" Binding="{Binding FullDateTimeDisplay}" Width="140"/>
                            <DataGridTextColumn Header="النوع" Binding="{Binding TypeDisplay}" Width="100"/>
                            <DataGridTextColumn Header="الحالة" Binding="{Binding StatusDisplay}" Width="80"/>
                            <DataGridTextColumn Header="الأولوية" Binding="{Binding PriorityDisplay}" Width="80"/>
                            <DataGridTextColumn Header="الموكل" Binding="{Binding ClientDisplay}" Width="120"/>
                            <DataGridTextColumn Header="المكان" Binding="{Binding LocationDisplay}" Width="120"/>
                            <DataGridTextColumn Header="التكلفة" Binding="{Binding CostDisplay}" Width="100"/>
                            <DataGridTextColumn Header="حالة الدفع" Binding="{Binding PaymentStatusDisplay}" Width="100"/>
                            
                            <DataGridTemplateColumn Header="الإجراءات" Width="250">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Button Style="{StaticResource ActionButtonStyle}" 
                                                   ToolTip="عرض التفاصيل" 
                                                   Click="ViewAppointmentButton_Click" 
                                                   Tag="{Binding Id}">
                                                <TextBlock Text="👁️" FontSize="14"/>
                                            </Button>
                                            <Button Style="{StaticResource ActionButtonStyle}" 
                                                   ToolTip="تعديل" 
                                                   Click="EditAppointmentButton_Click" 
                                                   Tag="{Binding Id}">
                                                <TextBlock Text="✏️" FontSize="14"/>
                                            </Button>
                                            <Button Style="{StaticResource ActionButtonStyle}" 
                                                   ToolTip="تأكيد الموعد" 
                                                   Click="ConfirmAppointmentButton_Click" 
                                                   Tag="{Binding Id}">
                                                <TextBlock Text="✅" FontSize="14"/>
                                            </Button>
                                            <Button Style="{StaticResource ActionButtonStyle}" 
                                                   ToolTip="إرسال تنبيه" 
                                                   Click="NotifyAppointmentButton_Click" 
                                                   Tag="{Binding Id}">
                                                <TextBlock Text="🔔" FontSize="14"/>
                                            </Button>
                                            <Button Style="{StaticResource ActionButtonStyle}" 
                                                   ToolTip="تأجيل" 
                                                   Click="PostponeAppointmentButton_Click" 
                                                   Tag="{Binding Id}">
                                                <TextBlock Text="⏰" FontSize="14"/>
                                            </Button>
                                            <Button Style="{StaticResource ActionButtonStyle}" 
                                                   ToolTip="حذف" 
                                                   Click="DeleteAppointmentButton_Click" 
                                                   Tag="{Binding Id}">
                                                <TextBlock Text="🗑️" FontSize="14" Foreground="Red"/>
                                            </Button>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- تذييل الجدول -->
                    <Border Grid.Row="2" Background="#F8F9FA" Padding="15" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
                        <TextBlock Name="StatusTextBlock" Text="جاري التحميل..." FontSize="12" Foreground="#666"/>
                    </Border>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</Page>
