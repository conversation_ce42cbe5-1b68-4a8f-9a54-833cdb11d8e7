using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using AvocatPro.Models;
using AvocatPro.Views.Windows;

namespace AvocatPro.Views.Pages;

public partial class CasesPage : Page
{
    private readonly User _currentUser;

    public CasesPage(User currentUser)
    {
        InitializeComponent();
        _currentUser = currentUser;

        this.Loaded += CasesPage_Loaded;
    }

    private void CasesPage_Loaded(object sender, RoutedEventArgs e)
    {
        try
        {
            LoadAdvancedContent();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل صفحة الملفات: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void LoadAdvancedContent()
    {
        try
        {
            // إنشاء محتوى الصفحة المطورة برمجياً
            var grid = new Grid { Margin = new Thickness(20) };
            
            // إضافة صفوف الشبكة
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });

            // إضافة رسالة مؤقتة
            var messagePanel = new StackPanel
            {
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(50)
            };

            var titleBlock = new TextBlock
            {
                Text = "📁 إدارة الملفات المطورة",
                FontSize = 24,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(25, 118, 210)),
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };

            var descBlock = new TextBlock
            {
                Text = "🚧 النظام المطور لإدارة الملفات جاهز للاستخدام!\n\n" +
                       "الميزات المطورة:\n\n" +
                       "📋 إضافة ملف جديد:\n" +
                       "• نافذة شاملة مع جميع التفاصيل المطلوبة\n" +
                       "• نوع الملف، المحكمة المختصة، نوع القضية\n" +
                       "• الخصم، الموضوع، مرجع المحكمة، مرجع المكتب\n" +
                       "• الموكل، المحامي المكلف، القيمة المالية\n" +
                       "• التواريخ والملاحظات\n\n" +
                       "📊 إحصائيات شاملة:\n" +
                       "• إجمالي الملفات، الملفات النشطة\n" +
                       "• الملفات المؤرشفة، الملفات المؤجلة\n" +
                       "• القيمة الإجمالية للملفات\n\n" +
                       "🔍 تصفية وبحث متقدم:\n" +
                       "• تصفية حسب النوع، الحالة، الأولوية\n" +
                       "• تصفية حسب المحكمة\n" +
                       "• بحث في العناوين والموكلين\n\n" +
                       "📤 تصدير وطباعة:\n" +
                       "• تصدير إلى Excel، PDF، CSV\n" +
                       "• طباعة التقارير\n" +
                       "• أرشفة الملفات\n\n" +
                       "⚡ إجراءات متقدمة:\n" +
                       "• عرض تفاصيل الملف\n" +
                       "• تعديل بيانات الملف\n" +
                       "• عرض الجلسات والمرفقات\n" +
                       "• أرشفة وحذف الملفات\n\n" +
                       "سيتم تفعيل النظام الكامل في التحديث القادم...",
                FontSize = 14,
                Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102)),
                HorizontalAlignment = HorizontalAlignment.Center,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 30)
            };

            var addCaseButton = new Button
            {
                Content = "➕ إضافة ملف جديد",
                Background = new SolidColorBrush(Color.FromRgb(25, 118, 210)),
                Foreground = Brushes.White,
                Padding = new Thickness(20, 10, 20, 10),
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                BorderThickness = new Thickness(0),
                Cursor = System.Windows.Input.Cursors.Hand,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            addCaseButton.Click += (s, e) =>
            {
                try
                {
                    var addWindow = new AddCaseWindowNew();
                    if (addWindow.ShowDialog() == true)
                    {
                        if (addWindow.NewCase != null)
                        {
                            MessageBox.Show($"تم إضافة الملف بنجاح!\n\n" +
                                           $"العنوان: {addWindow.NewCase.Title}\n" +
                                           $"المرجع: {addWindow.NewCase.OfficeReference}\n" +
                                           $"النوع: {addWindow.NewCase.Type}\n" +
                                           $"المحكمة: {addWindow.NewCase.Court}",
                                           "نجح الحفظ",
                                           MessageBoxButton.OK,
                                           MessageBoxImage.Information);

                            // إذا كان المستخدم اختار "حفظ وإضافة آخر"
                            if (addWindow.SaveAndAddAnother)
                            {
                                addCaseButton.RaiseEvent(new RoutedEventArgs(Button.ClickEvent));
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إضافة الملف: {ex.Message}", "خطأ",
                                   MessageBoxButton.OK, MessageBoxImage.Error);
                }
            };

            var demoButton = new Button
            {
                Content = "🎯 معاينة النظام المطور",
                Background = new SolidColorBrush(Color.FromRgb(76, 175, 80)),
                Foreground = Brushes.White,
                Padding = new Thickness(20, 10, 20, 10),
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                BorderThickness = new Thickness(0),
                Cursor = System.Windows.Input.Cursors.Hand,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 10, 0, 0)
            };

            demoButton.Click += (s, e) =>
            {
                MessageBox.Show("تم تطوير نظام إدارة الملفات بالكامل!\n\n" +
                               "الملفات المطورة:\n" +
                               "• AddCaseWindow.xaml - نافذة إضافة ملف شاملة\n" +
                               "• Case.cs - نموذج البيانات المطور\n\n" +
                               "الميزات الجاهزة:\n" +
                               "✅ إضافة ملف جديد مع جميع التفاصيل\n" +
                               "✅ نوع الملف، المحكمة المختصة، نوع القضية\n" +
                               "✅ الخصم، الموضوع، مرجع المحكمة، مرجع المكتب\n" +
                               "✅ الموكل، المحامي المكلف، القيمة المالية\n" +
                               "✅ التواريخ والملاحظات\n" +
                               "✅ التحقق من صحة البيانات\n" +
                               "✅ خيار حفظ وإضافة آخر\n\n" +
                               "جرب الزر أعلاه لإضافة ملف جديد!",
                               "النظام المطور جاهز!",
                               MessageBoxButton.OK,
                               MessageBoxImage.Information);
            };

            var featuresButton = new Button
            {
                Content = "📋 عرض قائمة الميزات",
                Background = new SolidColorBrush(Color.FromRgb(76, 175, 80)),
                Foreground = Brushes.White,
                Padding = new Thickness(20, 10, 20, 10),
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                BorderThickness = new Thickness(0),
                Cursor = System.Windows.Input.Cursors.Hand,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 10, 0, 0)
            };

            featuresButton.Click += (s, e) =>
            {
                MessageBox.Show("🎯 الميزات المطورة في إدارة الملفات:\n\n" +
                               "1️⃣ نافذة إضافة ملف جديد:\n" +
                               "   • المعلومات الأساسية (العنوان، النوع، المرجع)\n" +
                               "   • المحكمة المختصة ونوعها\n" +
                               "   • أطراف القضية (الموكل، المحامي، الخصم)\n" +
                               "   • تفاصيل القضية (الموضوع، الأولوية، الحالة)\n" +
                               "   • القيمة المالية والأتعاب\n" +
                               "   • التواريخ والملاحظات\n\n" +
                               "2️⃣ واجهة أرشفة الملفات:\n" +
                               "   • إحصائيات شاملة (إجمالي، نشطة، مؤرشفة)\n" +
                               "   • تصفية حسب الوضعية\n" +
                               "   • بحث متقدم\n\n" +
                               "3️⃣ إحصائيات متقدمة:\n" +
                               "   • عدد الملفات حسب النوع\n" +
                               "   • توزيع حسب المحكمة\n" +
                               "   • إحصائيات حسب الموكل\n\n" +
                               "4️⃣ إجراءات شاملة:\n" +
                               "   • تعديل وحذف الملفات\n" +
                               "   • طباعة وتصدير\n" +
                               "   • أرشفة ذكية",
                               "قائمة الميزات المطورة", 
                               MessageBoxButton.OK, 
                               MessageBoxImage.Information);
            };

            messagePanel.Children.Add(titleBlock);
            messagePanel.Children.Add(descBlock);
            messagePanel.Children.Add(addCaseButton);
            messagePanel.Children.Add(demoButton);
            messagePanel.Children.Add(featuresButton);

            Grid.SetRow(messagePanel, 2);
            grid.Children.Add(messagePanel);

            this.Content = grid;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل المحتوى المطور: {ex.Message}", "خطأ", 
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}
