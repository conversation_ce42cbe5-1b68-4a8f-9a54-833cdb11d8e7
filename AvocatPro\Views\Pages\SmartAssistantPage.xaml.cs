using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using AvocatPro.Models;
using AvocatPro.Services;
using AvocatPro.Data;

namespace AvocatPro.Views.Pages
{
    public partial class SmartAssistantPage : Page
    {
        private readonly User _currentUser;
        private readonly SimpleAIService _aiService;
        private readonly List<ChatMessage> _chatHistory;
        private bool _isProcessing = false;

        public SmartAssistantPage(User? currentUser = null)
        {
            InitializeComponent();
            _currentUser = currentUser ?? new User
            {
                Id = 1,
                Username = "admin",
                FullName = "المحامي الرئيسي",
                Email = "<EMAIL>"
            };
            _aiService = new SimpleAIService();
            _chatHistory = new List<ChatMessage>();

            Loaded += SmartAssistantPage_Loaded;
        }

        private async void SmartAssistantPage_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                LoadQuickSuggestions();
                LoadAIStatistics();
                LoadRecentAnalysis();
                SetupPlaceholderText();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الصفحة: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SetupPlaceholderText()
        {
            QueryTextBox.GotFocus += QueryTextBox_GotFocus;
            QueryTextBox.LostFocus += QueryTextBox_LostFocus;
        }

        private void QueryTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (QueryTextBox.Text == "اكتب استفسارك هنا...")
            {
                QueryTextBox.Text = "";
                QueryTextBox.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#1F2937"));
            }
        }

        private void QueryTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(QueryTextBox.Text))
            {
                QueryTextBox.Text = "اكتب استفسارك هنا...";
                QueryTextBox.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#9CA3AF"));
            }
        }

        private void QueryTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && !_isProcessing)
            {
                if (Keyboard.Modifiers == ModifierKeys.Control)
                {
                    SendMessage();
                }
                else
                {
                    // السماح بالسطر الجديد
                }
            }
        }

        private async void SendBtn_Click(object sender, RoutedEventArgs e)
        {
            if (!_isProcessing)
            {
                await SendMessage();
            }
        }

        private async Task SendMessage()
        {
            try
            {
                var queryTextBox = FindName("QueryTextBox") as TextBox;
                if (queryTextBox == null) return;

                var query = queryTextBox.Text.Trim();
                if (string.IsNullOrEmpty(query) || query == "اكتب استفسارك هنا...")
                    return;

            _isProcessing = true;
            SendBtn.IsEnabled = false;
            
            // إضافة رسالة المستخدم
            AddUserMessage(query);
            QueryTextBox.Text = "";
            
            // إظهار مؤشر الكتابة
            ShowTypingIndicator();

            try
            {
                // معالجة الاستعلام بالذكاء الاصطناعي
                var response = await _aiService.ProcessLegalQueryAsync(query, _currentUser);
                
                // إخفاء مؤشر الكتابة
                HideTypingIndicator();
                
                // إضافة رد المساعد
                AddAssistantMessage(response);
                
                // تحديث الإحصائيات
                UpdateAIStatistics();
                
                // تحديث التحليلات الأخيرة
                UpdateRecentAnalysis(response);
            }
            catch (Exception ex)
            {
                HideTypingIndicator();
                AddAssistantMessage(new AIResponse
                {
                    Answer = $"عذراً، حدث خطأ: {ex.Message}",
                    Type = "خطأ",
                    Confidence = 0
                });
            }
            finally
            {
                _isProcessing = false;
                var sendBtn = FindName("SendBtn") as Button;
                if (sendBtn != null)
                    sendBtn.IsEnabled = true;
                queryTextBox?.Focus();
            }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إرسال الرسالة: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddUserMessage(string message)
        {
            try
            {
                var chatMessage = new ChatMessage
                {
                    Content = message,
                    IsUser = true,
                    Timestamp = DateTime.Now
                };

                _chatHistory.Add(chatMessage);

                var messageContainer = CreateMessageContainer(chatMessage);
                var chatPanel = FindName("ChatPanel") as Panel;
                if (chatPanel != null)
                    chatPanel.Children.Add(messageContainer);

                ScrollToBottom();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إضافة رسالة المستخدم: {ex.Message}");
            }
        }

        private void AddAssistantMessage(AIResponse response)
        {
            try
            {
                var chatMessage = new ChatMessage
                {
                    Content = response.Answer,
                    IsUser = false,
                    Timestamp = response.Timestamp,
                    Type = response.Type,
                    Confidence = response.Confidence
                };

                _chatHistory.Add(chatMessage);

                var messageContainer = CreateMessageContainer(chatMessage);
                var chatPanel = FindName("ChatPanel") as Panel;
                if (chatPanel != null)
                    chatPanel.Children.Add(messageContainer);

                ScrollToBottom();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إضافة رسالة المساعد: {ex.Message}");
            }
        }

        private Border CreateMessageContainer(ChatMessage message)
        {
            var border = new Border
            {
                CornerRadius = message.IsUser ? new CornerRadius(20, 20, 5, 20) : new CornerRadius(20, 20, 20, 5),
                Padding = new Thickness(20),
                Margin = message.IsUser ? new Thickness(80, 10, 0, 10) : new Thickness(0, 10, 80, 10),
                HorizontalAlignment = message.IsUser ? HorizontalAlignment.Left : HorizontalAlignment.Right
            };

            if (message.IsUser)
            {
                border.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F3F4F6"));
                border.BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E5E7EB"));
                border.BorderThickness = new Thickness(1, 1, 1, 1);
            }
            else
            {
                border.Background = new LinearGradientBrush(
                    (Color)ColorConverter.ConvertFromString("#3B82F6"),
                    (Color)ColorConverter.ConvertFromString("#1E3A8A"),
                    90);
                border.Effect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    Color = (Color)ColorConverter.ConvertFromString("#3B82F6"),
                    Opacity = 0.3,
                    BlurRadius = 10,
                    ShadowDepth = 3
                };
            }

            var stackPanel = new StackPanel();

            // محتوى الرسالة
            var contentText = new TextBlock
            {
                Text = message.Content,
                TextWrapping = TextWrapping.Wrap,
                FontSize = 14,
                LineHeight = 22,
                Foreground = message.IsUser ? 
                    new SolidColorBrush((Color)ColorConverter.ConvertFromString("#1F2937")) : 
                    Brushes.White
            };
            stackPanel.Children.Add(contentText);

            // معلومات إضافية للمساعد
            if (!message.IsUser)
            {
                var infoPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal,
                    Margin = new Thickness(0, 15, 0, 0)
                };

                if (!string.IsNullOrEmpty(message.Type))
                {
                    var typeChip = new Border
                    {
                        Background = new SolidColorBrush(Colors.White) { Opacity = 0.2 },
                        CornerRadius = new CornerRadius(12),
                        Padding = new Thickness(12, 6, 12, 6),
                        Margin = new Thickness(0, 0, 10, 0)
                    };
                    
                    var typeText = new TextBlock
                    {
                        Text = message.Type,
                        FontSize = 12,
                        Foreground = Brushes.White,
                        FontWeight = FontWeights.SemiBold
                    };
                    
                    typeChip.Child = typeText;
                    infoPanel.Children.Add(typeChip);
                }

                // مستوى الثقة
                if (message.Confidence > 0)
                {
                    var confidencePanel = new StackPanel
                    {
                        Orientation = Orientation.Horizontal
                    };

                    var confidenceIcon = new TextBlock
                    {
                        Text = message.Confidence > 0.8 ? "🎯" : message.Confidence > 0.5 ? "📊" : "⚠️",
                        FontSize = 12,
                        Margin = new Thickness(0, 0, 5, 0)
                    };

                    var confidenceText = new TextBlock
                    {
                        Text = $"الثقة: {message.Confidence:P0}",
                        FontSize = 11,
                        Foreground = Brushes.White,
                        Opacity = 0.9
                    };

                    confidencePanel.Children.Add(confidenceIcon);
                    confidencePanel.Children.Add(confidenceText);
                    infoPanel.Children.Add(confidencePanel);
                }

                if (infoPanel.Children.Count > 0)
                {
                    stackPanel.Children.Add(infoPanel);
                }
            }

            // الوقت
            var timeText = new TextBlock
            {
                Text = message.Timestamp.ToString("HH:mm"),
                FontSize = 11,
                HorizontalAlignment = message.IsUser ? HorizontalAlignment.Left : HorizontalAlignment.Right,
                Margin = new Thickness(0, 8, 0, 0),
                Foreground = message.IsUser ? 
                    new SolidColorBrush((Color)ColorConverter.ConvertFromString("#6B7280")) : 
                    Brushes.White,
                Opacity = 0.7
            };
            stackPanel.Children.Add(timeText);

            border.Child = stackPanel;

            // تأثير الظهور
            border.Opacity = 0;
            var fadeIn = new DoubleAnimation(0, 1, TimeSpan.FromMilliseconds(400));
            border.BeginAnimation(OpacityProperty, fadeIn);

            return border;
        }

        private void ShowTypingIndicator()
        {
            try
            {
                var typingIndicator = FindName("TypingIndicator") as FrameworkElement;
                if (typingIndicator != null)
                    typingIndicator.Visibility = Visibility.Visible;

                try
                {
                    var storyboard = (Storyboard)FindResource("TypingAnimation");
                    storyboard?.Begin();
                }
                catch
                {
                    // تجاهل خطأ الأنيميشن
                }

                ScrollToBottom();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إظهار مؤشر الكتابة: {ex.Message}");
            }
        }

        private void HideTypingIndicator()
        {
            try
            {
                var typingIndicator = FindName("TypingIndicator") as FrameworkElement;
                if (typingIndicator != null)
                    typingIndicator.Visibility = Visibility.Collapsed;

                try
                {
                    var storyboard = (Storyboard)FindResource("TypingAnimation");
                    storyboard?.Stop();
                }
                catch
                {
                    // تجاهل خطأ الأنيميشن
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إخفاء مؤشر الكتابة: {ex.Message}");
            }
        }

        private void ScrollToBottom()
        {
            try
            {
                var chatScrollViewer = FindName("ChatScrollViewer") as ScrollViewer;
                chatScrollViewer?.ScrollToEnd();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التمرير: {ex.Message}");
            }
        }

        private void LoadQuickSuggestions()
        {
            try
            {
                var suggestions = new[]
                {
                    "توقع نتيجة القضية رقم 123",
                    "حلل الوثيقة المرفقة",
                    "اقترح جدولة مثالية لهذا الأسبوع",
                    "ما هي مخاطر هذا العميل؟",
                    "أظهر القضايا المشابهة",
                    "احسب الرسوم القضائية",
                    "ما هي إجراءات الاستئناف؟",
                    "اقترح استراتيجية للقضية"
                };

                var quickSuggestionsPanel = FindName("QuickSuggestionsPanel") as Panel;
                if (quickSuggestionsPanel != null)
                {
                    quickSuggestionsPanel.Children.Clear();
                    foreach (var suggestion in suggestions.Take(6))
                    {
                        var button = new Button
                        {
                            Content = suggestion,
                            Background = new SolidColorBrush(Colors.White),
                            BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E5E7EB")),
                            BorderThickness = new Thickness(1),
                            Margin = new Thickness(0, 0, 0, 8),
                            Padding = new Thickness(12, 8, 12, 8),
                            FontSize = 12,
                            HorizontalAlignment = HorizontalAlignment.Stretch
                        };

                        button.Click += (s, e) =>
                        {
                            var queryTextBox = FindName("QueryTextBox") as TextBox;
                            if (queryTextBox != null)
                            {
                                queryTextBox.Text = suggestion;
                                queryTextBox.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#1F2937"));
                                SendMessage();
                            }
                        };

                        quickSuggestionsPanel.Children.Add(button);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الاقتراحات: {ex.Message}");
            }
        }

        private void LoadAIStatistics()
        {
            try
            {
                var aiStatsPanel = FindName("AIStatsPanel") as Panel;
                if (aiStatsPanel != null)
                {
                    aiStatsPanel.Children.Clear();

            var stats = new[]
            {
                new { Label = "الاستعلامات اليوم", Value = "47", Icon = "💬" },
                new { Label = "دقة التوقعات", Value = "89%", Icon = "🎯" },
                new { Label = "الوثائق المحللة", Value = "23", Icon = "📄" },
                new { Label = "الوقت المُوفر", Value = "3.2 ساعة", Icon = "⏱️" }
            };

            foreach (var stat in stats)
            {
                var statPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal,
                    Margin = new Thickness(0, 0, 0, 10)
                };

                var icon = new TextBlock
                {
                    Text = stat.Icon,
                    FontSize = 16,
                    Margin = new Thickness(0, 0, 10, 0),
                    VerticalAlignment = VerticalAlignment.Center
                };

                var textPanel = new StackPanel();

                var valueText = new TextBlock
                {
                    Text = stat.Value,
                    FontSize = 16,
                    FontWeight = FontWeights.Bold,
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#3B82F6"))
                };

                var labelText = new TextBlock
                {
                    Text = stat.Label,
                    FontSize = 11,
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#6B7280"))
                };

                textPanel.Children.Add(valueText);
                textPanel.Children.Add(labelText);

                statPanel.Children.Add(icon);
                statPanel.Children.Add(textPanel);

                aiStatsPanel.Children.Add(statPanel);
            }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل إحصائيات الذكاء الاصطناعي: {ex.Message}");
            }
        }

        private void LoadRecentAnalysis()
        {
            try
            {
                var recentAnalysis = new[]
                {
                    new { Type = "توقع قضية", Summary = "احتمالية نجاح 85% للقضية التجارية", Time = "منذ 5 دقائق" },
                    new { Type = "تحليل وثيقة", Summary = "عقد إيجار - تم اكتشاف 3 نقاط مهمة", Time = "منذ 15 دقيقة" },
                    new { Type = "تحسين جدولة", Summary = "اقتراح توفير 2 ساعة أسبوعياً", Time = "منذ ساعة" },
                    new { Type = "تقييم مخاطر", Summary = "عميل جديد - مخاطر منخفضة", Time = "منذ ساعتين" }
                };

                var recentAnalysisListView = FindName("RecentAnalysisListView") as ListView;
                if (recentAnalysisListView != null)
                {
                    recentAnalysisListView.ItemsSource = recentAnalysis;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل التحليلات الأخيرة: {ex.Message}");
            }
        }

        private void UpdateAIStatistics()
        {
            // تحديث الإحصائيات بعد كل استعلام
            LoadAIStatistics();
        }

        private void UpdateRecentAnalysis(AIResponse response)
        {
            // إضافة التحليل الجديد إلى القائمة
            LoadRecentAnalysis();
        }

        private void QueryType_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // تحديث الاقتراحات حسب نوع الاستعلام المختار
            LoadQuickSuggestions();
        }

        private void VoiceInput_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("ميزة الإدخال الصوتي ستكون متاحة قريباً!", "قريباً", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ClearChat_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من مسح جميع المحادثات؟", 
                                       "تأكيد المسح", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                ChatPanel.Children.Clear();
                _chatHistory.Clear();
                
                // إعادة إضافة رسالة الترحيب
                AddWelcomeMessage();
            }
        }

        private void AddWelcomeMessage()
        {
            // إعادة إضافة رسالة الترحيب الأصلية
            LoadQuickSuggestions();
        }

        private void ExportChat_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var chatContent = string.Join("\n\n", _chatHistory.Select(m => 
                    $"[{m.Timestamp:yyyy-MM-dd HH:mm}] {(m.IsUser ? "المستخدم" : "المساعد الذكي")}: {m.Content}"));
                
                var saveDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "Text files (*.txt)|*.txt|All files (*.*)|*.*",
                    FileName = $"محادثة_المساعد_الذكي_{DateTime.Now:yyyy-MM-dd_HH-mm}.txt"
                };
                
                if (saveDialog.ShowDialog() == true)
                {
                    System.IO.File.WriteAllText(saveDialog.FileName, chatContent);
                    MessageBox.Show("تم تصدير المحادثة بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير المحادثة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AttachFile_Click(object sender, RoutedEventArgs e)
        {
            var openDialog = new Microsoft.Win32.OpenFileDialog
            {
                Filter = "All files (*.*)|*.*|PDF files (*.pdf)|*.pdf|Word files (*.docx)|*.docx|Images (*.jpg;*.png)|*.jpg;*.png",
                Title = "اختر ملف للتحليل"
            };

            if (openDialog.ShowDialog() == true)
            {
                QueryTextBox.Text = $"حلل الملف: {openDialog.FileName}";
                QueryTextBox.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#1F2937"));
            }
        }

        private async void PredictCase_Click(object sender, RoutedEventArgs e)
        {
            QueryTextBox.Text = "توقع نتيجة القضية رقم ";
            QueryTextBox.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#1F2937"));
            QueryTextBox.Focus();
            QueryTextBox.CaretIndex = QueryTextBox.Text.Length;
        }

        private void AnalyzeDocument_Click(object sender, RoutedEventArgs e)
        {
            AttachFile_Click(sender, e);
        }

        private async void OptimizeSchedule_Click(object sender, RoutedEventArgs e)
        {
            QueryTextBox.Text = "اقترح جدولة مثالية لهذا الأسبوع";
            QueryTextBox.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#1F2937"));
            await SendMessage();
        }

        private async void AssessRisk_Click(object sender, RoutedEventArgs e)
        {
            QueryTextBox.Text = "قيم مخاطر العميل ";
            QueryTextBox.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#1F2937"));
            QueryTextBox.Focus();
            QueryTextBox.CaretIndex = QueryTextBox.Text.Length;
        }
    }

    public class ChatMessage
    {
        public string Content { get; set; } = string.Empty;
        public bool IsUser { get; set; }
        public DateTime Timestamp { get; set; }
        public string Type { get; set; } = string.Empty;
        public double Confidence { get; set; }
    }

}
