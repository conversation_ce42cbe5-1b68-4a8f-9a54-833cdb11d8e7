﻿#pragma checksum "..\..\..\..\..\Views\Windows\AppointmentsCalendarWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "B916B2600F929F519F48CE9F50D22FD72B36D86B"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using FontAwesome.WPF;
using FontAwesome.WPF.Converters;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Windows {
    
    
    /// <summary>
    /// AppointmentsCalendarWindow
    /// </summary>
    public partial class AppointmentsCalendarWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 101 "..\..\..\..\..\Views\Windows\AppointmentsCalendarWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PreviousMonthButton;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\..\Views\Windows\AppointmentsCalendarWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TodayButton;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\..\Views\Windows\AppointmentsCalendarWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NextMonthButton;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\..\Views\Windows\AppointmentsCalendarWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MonthYearTitle;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\..\Views\Windows\AppointmentsCalendarWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MonthStatsText;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\..\Views\Windows\AppointmentsCalendarWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddAppointmentButton;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\..\Views\Windows\AppointmentsCalendarWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewModeButton;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\..\..\Views\Windows\AppointmentsCalendarWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid CalendarGrid;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\..\..\Views\Windows\AppointmentsCalendarWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectedDateText;
        
        #line default
        #line hidden
        
        
        #line 220 "..\..\..\..\..\Views\Windows\AppointmentsCalendarWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DayAppointmentsPanel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/windows/appointmentscalendarwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\AppointmentsCalendarWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PreviousMonthButton = ((System.Windows.Controls.Button)(target));
            
            #line 101 "..\..\..\..\..\Views\Windows\AppointmentsCalendarWindow.xaml"
            this.PreviousMonthButton.Click += new System.Windows.RoutedEventHandler(this.PreviousMonthButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.TodayButton = ((System.Windows.Controls.Button)(target));
            
            #line 107 "..\..\..\..\..\Views\Windows\AppointmentsCalendarWindow.xaml"
            this.TodayButton.Click += new System.Windows.RoutedEventHandler(this.TodayButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.NextMonthButton = ((System.Windows.Controls.Button)(target));
            
            #line 113 "..\..\..\..\..\Views\Windows\AppointmentsCalendarWindow.xaml"
            this.NextMonthButton.Click += new System.Windows.RoutedEventHandler(this.NextMonthButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.MonthYearTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.MonthStatsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.AddAppointmentButton = ((System.Windows.Controls.Button)(target));
            
            #line 131 "..\..\..\..\..\Views\Windows\AppointmentsCalendarWindow.xaml"
            this.AddAppointmentButton.Click += new System.Windows.RoutedEventHandler(this.AddAppointmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ViewModeButton = ((System.Windows.Controls.Button)(target));
            
            #line 137 "..\..\..\..\..\Views\Windows\AppointmentsCalendarWindow.xaml"
            this.ViewModeButton.Click += new System.Windows.RoutedEventHandler(this.ViewModeButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.CalendarGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 9:
            this.SelectedDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.DayAppointmentsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

