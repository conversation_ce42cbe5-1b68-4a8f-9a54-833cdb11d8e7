#!/bin/bash

# Smart Lawyers Web Platform - Quick Installation Script
# منصة المحامين الذكية - سكريبت التثبيت السريع

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        print_warning "This script should not be run as root for security reasons."
        read -p "Do you want to continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# Check system requirements
check_requirements() {
    print_header "Checking System Requirements"
    
    # Check PHP version
    if command -v php &> /dev/null; then
        PHP_VERSION=$(php -r "echo PHP_VERSION;")
        print_status "PHP version: $PHP_VERSION"
        
        if php -r "exit(version_compare(PHP_VERSION, '8.1.0', '<') ? 1 : 0);"; then
            print_error "PHP 8.1 or higher is required. Current version: $PHP_VERSION"
            exit 1
        fi
        print_success "PHP version check passed"
    else
        print_error "PHP is not installed"
        exit 1
    fi
    
    # Check Composer
    if command -v composer &> /dev/null; then
        COMPOSER_VERSION=$(composer --version | cut -d' ' -f3)
        print_status "Composer version: $COMPOSER_VERSION"
        print_success "Composer check passed"
    else
        print_error "Composer is not installed"
        print_status "Installing Composer..."
        curl -sS https://getcomposer.org/installer | php
        sudo mv composer.phar /usr/local/bin/composer
        print_success "Composer installed successfully"
    fi
    
    # Check Node.js
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_status "Node.js version: $NODE_VERSION"
        print_success "Node.js check passed"
    else
        print_error "Node.js is not installed"
        print_status "Please install Node.js 16.0 or higher from https://nodejs.org/"
        exit 1
    fi
    
    # Check NPM
    if command -v npm &> /dev/null; then
        NPM_VERSION=$(npm --version)
        print_status "NPM version: $NPM_VERSION"
        print_success "NPM check passed"
    else
        print_error "NPM is not installed"
        exit 1
    fi
    
    # Check MySQL
    if command -v mysql &> /dev/null; then
        MYSQL_VERSION=$(mysql --version | cut -d' ' -f6 | cut -d',' -f1)
        print_status "MySQL version: $MYSQL_VERSION"
        print_success "MySQL check passed"
    else
        print_warning "MySQL client not found. Make sure MySQL server is running."
    fi
}

# Install dependencies
install_dependencies() {
    print_header "Installing Dependencies"
    
    print_status "Installing PHP dependencies..."
    composer install --no-dev --optimize-autoloader
    print_success "PHP dependencies installed"
    
    print_status "Installing JavaScript dependencies..."
    npm install --production
    print_success "JavaScript dependencies installed"
}

# Setup environment
setup_environment() {
    print_header "Setting Up Environment"
    
    if [ ! -f .env ]; then
        print_status "Creating .env file..."
        cp .env.example .env
        print_success ".env file created"
    else
        print_warning ".env file already exists"
    fi
    
    print_status "Generating application key..."
    php artisan key:generate --force
    print_success "Application key generated"
}

# Setup database
setup_database() {
    print_header "Setting Up Database"
    
    read -p "Enter database host (default: 127.0.0.1): " DB_HOST
    DB_HOST=${DB_HOST:-127.0.0.1}
    
    read -p "Enter database port (default: 3306): " DB_PORT
    DB_PORT=${DB_PORT:-3306}
    
    read -p "Enter database name (default: smartlawyers_web): " DB_NAME
    DB_NAME=${DB_NAME:-smartlawyers_web}
    
    read -p "Enter database username (default: root): " DB_USER
    DB_USER=${DB_USER:-root}
    
    read -s -p "Enter database password: " DB_PASS
    echo
    
    # Update .env file
    sed -i "s/DB_HOST=.*/DB_HOST=$DB_HOST/" .env
    sed -i "s/DB_PORT=.*/DB_PORT=$DB_PORT/" .env
    sed -i "s/DB_DATABASE=.*/DB_DATABASE=$DB_NAME/" .env
    sed -i "s/DB_USERNAME=.*/DB_USERNAME=$DB_USER/" .env
    sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=$DB_PASS/" .env
    
    print_status "Testing database connection..."
    if php artisan migrate:status &> /dev/null; then
        print_success "Database connection successful"
    else
        print_error "Database connection failed"
        print_status "Creating database..."
        mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS -e "CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    fi
    
    print_status "Running database migrations..."
    php artisan migrate --force
    print_success "Database migrations completed"
    
    print_status "Seeding database..."
    php artisan db:seed --force
    print_success "Database seeding completed"
}

# Build assets
build_assets() {
    print_header "Building Assets"
    
    print_status "Building frontend assets..."
    npm run build
    print_success "Assets built successfully"
}

# Set permissions
set_permissions() {
    print_header "Setting Permissions"
    
    print_status "Setting storage permissions..."
    chmod -R 755 storage
    chmod -R 755 bootstrap/cache
    print_success "Permissions set successfully"
    
    print_status "Creating storage link..."
    php artisan storage:link
    print_success "Storage link created"
}

# Create admin user
create_admin() {
    print_header "Creating Admin User"
    
    read -p "Enter admin name (default: مدير النظام): " ADMIN_NAME
    ADMIN_NAME=${ADMIN_NAME:-"مدير النظام"}
    
    read -p "Enter admin email (default: <EMAIL>): " ADMIN_EMAIL
    ADMIN_EMAIL=${ADMIN_EMAIL:-"<EMAIL>"}
    
    read -s -p "Enter admin password: " ADMIN_PASS
    echo
    
    print_status "Creating admin user..."
    php artisan make:admin "$ADMIN_NAME" "$ADMIN_EMAIL" "$ADMIN_PASS"
    print_success "Admin user created successfully"
}

# Optimize application
optimize_app() {
    print_header "Optimizing Application"
    
    print_status "Caching configuration..."
    php artisan config:cache
    
    print_status "Caching routes..."
    php artisan route:cache
    
    print_status "Caching views..."
    php artisan view:cache
    
    print_success "Application optimized"
}

# Final setup
final_setup() {
    print_header "Final Setup"
    
    # Get application URL
    read -p "Enter your application URL (e.g., http://smartlawyers.local): " APP_URL
    if [ ! -z "$APP_URL" ]; then
        sed -i "s|APP_URL=.*|APP_URL=$APP_URL|" .env
    fi
    
    print_status "Clearing caches..."
    php artisan cache:clear
    php artisan config:clear
    
    print_success "Setup completed successfully!"
}

# Main installation function
main() {
    print_header "Smart Lawyers Web Platform Installation"
    echo -e "${CYAN}منصة المحامين الذكية - التثبيت التلقائي${NC}"
    echo
    
    check_root
    check_requirements
    install_dependencies
    setup_environment
    setup_database
    build_assets
    set_permissions
    create_admin
    optimize_app
    final_setup
    
    echo
    print_header "Installation Complete!"
    echo -e "${GREEN}🎉 تم تثبيت منصة المحامين الذكية بنجاح!${NC}"
    echo
    echo -e "${CYAN}Next steps:${NC}"
    echo -e "1. Visit your application URL to access the platform"
    echo -e "2. Login with your admin credentials"
    echo -e "3. Configure integrations in Settings"
    echo -e "4. Start adding clients and cases"
    echo
    echo -e "${YELLOW}For support: <EMAIL>${NC}"
    echo -e "${YELLOW}Documentation: https://docs.smartlawyers.ma${NC}"
    echo
}

# Run main function
main "$@"
