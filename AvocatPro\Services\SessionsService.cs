using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AvocatPro.Models;

namespace AvocatPro.Services
{
    /// <summary>
    /// خدمة إدارة الجلسات
    /// </summary>
    public class SessionsService
    {
        #region Session Types and Procedures

        /// <summary>
        /// الحصول على أنواع الجلسات
        /// </summary>
        public List<string> GetSessionTypes()
        {
            return new List<string>
            {
                "جلسة مرافعة",
                "جلسة تحقيق",
                "جلسة خبرة",
                "جلسة صلح",
                "جلسة نطق بالحكم",
                "جلسة استئناف",
                "جلسة نقض",
                "جلسة تنفيذ",
                "جلسة معاينة",
                "جلسة سماع شهود",
                "جلسة استجواب",
                "جلسة مداولة",
                "جلسة إدارية",
                "جلسة طوارئ"
            };
        }

        /// <summary>
        /// الحصول على أنواع الإجراءات
        /// </summary>
        public List<string> GetProcedureTypes()
        {
            return new List<string>
            {
                "إيداع مذكرة",
                "إيداع مستندات",
                "طلب تأجيل",
                "طلب ضم ملفات",
                "طلب فصل ملفات",
                "طلب إحالة",
                "طلب رد",
                "طلب تنحي",
                "طلب معاينة",
                "طلب انتداب خبير",
                "طلب استجواب",
                "طلب سماع شهود",
                "طلب إنابة قضائية",
                "طلب مساعدة قضائية",
                "تقديم دفوع",
                "تقديم طعن",
                "تقديم اعتراض"
            };
        }

        /// <summary>
        /// الحصول على حالات الجلسات
        /// </summary>
        public List<string> GetSessionStatuses()
        {
            return new List<string>
            {
                "مجدولة",
                "جارية",
                "مكتملة",
                "مؤجلة",
                "ملغية"
            };
        }

        /// <summary>
        /// الحصول على مستويات الأولوية
        /// </summary>
        public List<string> GetPriorityLevels()
        {
            return new List<string>
            {
                "عاجل",
                "مهم",
                "عادي"
            };
        }

        /// <summary>
        /// الحصول على قائمة المحامين
        /// </summary>
        public List<string> GetLawyers()
        {
            return new List<string>
            {
                "الأستاذ محمد العلوي",
                "الأستاذة فاطمة الزهراء",
                "الأستاذ عبد الرحمن الفاسي",
                "الأستاذة خديجة الإدريسي",
                "الأستاذ يوسف الحسني",
                "الأستاذة عائشة المغربي",
                "الأستاذ أحمد الأندلسي"
            };
        }

        /// <summary>
        /// الحصول على قائمة القضاة
        /// </summary>
        public List<string> GetJudges()
        {
            return new List<string>
            {
                "القاضي محمد بن علي",
                "القاضية فاطمة الزهراء",
                "القاضي عبد الرحمن الفاسي",
                "القاضية خديجة الإدريسي",
                "القاضي يوسف الحسني",
                "القاضية عائشة المغربي",
                "القاضي أحمد الأندلسي"
            };
        }

        /// <summary>
        /// الحصول على قائمة كتاب الضبط
        /// </summary>
        public List<string> GetClerks()
        {
            return new List<string>
            {
                "كاتب الضبط محمد العلوي",
                "كاتبة الضبط فاطمة الزهراء",
                "كاتب الضبط عبد الرحمن الفاسي",
                "كاتبة الضبط خديجة الإدريسي",
                "كاتب الضبط يوسف الحسني"
            };
        }

        /// <summary>
        /// الحصول على قائمة قاعات المحكمة
        /// </summary>
        public List<string> GetCourtRooms()
        {
            return new List<string>
            {
                "القاعة الأولى",
                "القاعة الثانية",
                "القاعة الثالثة",
                "القاعة الرابعة",
                "القاعة الخامسة",
                "قاعة الاستئناف",
                "قاعة النقض",
                "قاعة التحكيم",
                "قاعة الصلح",
                "قاعة الطوارئ"
            };
        }

        #endregion

        #region Statistics

        /// <summary>
        /// حساب إحصائيات الجلسات
        /// </summary>
        public SessionStatistics CalculateStatistics(List<AdvancedSessionModel> sessions)
        {
            var now = DateTime.Now;
            var startOfWeek = now.Date.AddDays(-(int)now.DayOfWeek);
            var startOfMonth = new DateTime(now.Year, now.Month, 1);
            var startOfYear = new DateTime(now.Year, 1, 1);

            var stats = new SessionStatistics
            {
                TotalSessions = sessions.Count,
                ScheduledSessions = sessions.Count(s => s.Status == "مجدولة"),
                CompletedSessions = sessions.Count(s => s.Status == "مكتملة"),
                PostponedSessions = sessions.Count(s => s.Status == "مؤجلة"),
                CancelledSessions = sessions.Count(s => s.Status == "ملغية"),
                TodaySessions = sessions.Count(s => s.SessionDate.Date == now.Date),
                ThisWeekSessions = sessions.Count(s => s.SessionDate.Date >= startOfWeek),
                ThisMonthSessions = sessions.Count(s => s.SessionDate >= startOfMonth),
                ThisYearSessions = sessions.Count(s => s.SessionDate >= startOfYear),
                TotalExpenses = sessions.Sum(s => s.Expenses)
            };

            // حساب متوسط الجلسات الشهرية
            if (sessions.Any())
            {
                var monthsCount = sessions.GroupBy(s => new { s.SessionDate.Year, s.SessionDate.Month }).Count();
                stats.AverageSessionsPerMonth = monthsCount > 0 ? (double)sessions.Count / monthsCount : 0;
            }

            // أكثر المحاكم نشاطاً
            var courtGroups = sessions.GroupBy(s => s.Court).OrderByDescending(g => g.Count());
            stats.MostActiveCourt = courtGroups.FirstOrDefault()?.Key ?? "غير محدد";

            // أكثر أنواع الجلسات نشاطاً
            var sessionTypeGroups = sessions.GroupBy(s => s.SessionType).OrderByDescending(g => g.Count());
            stats.MostActiveSessionType = sessionTypeGroups.FirstOrDefault()?.Key ?? "غير محدد";

            return stats;
        }

        /// <summary>
        /// الحصول على إحصائيات شهرية
        /// </summary>
        public Dictionary<string, int> GetMonthlyStatistics(List<AdvancedSessionModel> sessions, int year)
        {
            var monthlyStats = new Dictionary<string, int>();
            var monthNames = new[]
            {
                "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
            };

            for (int i = 1; i <= 12; i++)
            {
                var count = sessions.Count(s => s.SessionDate.Year == year && s.SessionDate.Month == i);
                monthlyStats[monthNames[i - 1]] = count;
            }

            return monthlyStats;
        }

        /// <summary>
        /// الحصول على إحصائيات سنوية
        /// </summary>
        public Dictionary<int, int> GetYearlyStatistics(List<AdvancedSessionModel> sessions)
        {
            return sessions
                .GroupBy(s => s.SessionDate.Year)
                .ToDictionary(g => g.Key, g => g.Count())
                .OrderBy(kvp => kvp.Key)
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }

        /// <summary>
        /// الحصول على إحصائيات حسب نوع الجلسة
        /// </summary>
        public Dictionary<string, int> GetSessionTypeStatistics(List<AdvancedSessionModel> sessions)
        {
            return sessions
                .GroupBy(s => s.SessionType)
                .ToDictionary(g => g.Key, g => g.Count())
                .OrderByDescending(kvp => kvp.Value)
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }

        /// <summary>
        /// الحصول على إحصائيات حسب المحكمة
        /// </summary>
        public Dictionary<string, int> GetCourtStatistics(List<AdvancedSessionModel> sessions)
        {
            return sessions
                .GroupBy(s => s.Court)
                .ToDictionary(g => g.Key, g => g.Count())
                .OrderByDescending(kvp => kvp.Value)
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }

        /// <summary>
        /// الحصول على إحصائيات حسب المحامي
        /// </summary>
        public Dictionary<string, int> GetLawyerStatistics(List<AdvancedSessionModel> sessions)
        {
            return sessions
                .GroupBy(s => s.AssignedLawyer)
                .ToDictionary(g => g.Key, g => g.Count())
                .OrderByDescending(kvp => kvp.Value)
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }

        #endregion

        #region Calendar Helpers

        /// <summary>
        /// الحصول على جلسات شهر معين
        /// </summary>
        public List<AdvancedSessionModel> GetSessionsForMonth(List<AdvancedSessionModel> sessions, int year, int month)
        {
            return sessions.Where(s => s.SessionDate.Year == year && s.SessionDate.Month == month).ToList();
        }

        /// <summary>
        /// الحصول على جلسات يوم معين
        /// </summary>
        public List<AdvancedSessionModel> GetSessionsForDay(List<AdvancedSessionModel> sessions, DateTime date)
        {
            return sessions.Where(s => s.SessionDate.Date == date.Date).ToList();
        }

        /// <summary>
        /// الحصول على جلسات الأسبوع الحالي
        /// </summary>
        public List<AdvancedSessionModel> GetThisWeekSessions(List<AdvancedSessionModel> sessions)
        {
            var now = DateTime.Now;
            var startOfWeek = now.Date.AddDays(-(int)now.DayOfWeek);
            var endOfWeek = startOfWeek.AddDays(6);

            return sessions.Where(s => s.SessionDate.Date >= startOfWeek && s.SessionDate.Date <= endOfWeek).ToList();
        }

        /// <summary>
        /// الحصول على الجلسات القادمة
        /// </summary>
        public List<AdvancedSessionModel> GetUpcomingSessions(List<AdvancedSessionModel> sessions, int days = 7)
        {
            var now = DateTime.Now;
            var endDate = now.AddDays(days);

            return sessions
                .Where(s => s.SessionDate >= now && s.SessionDate <= endDate && s.Status == "مجدولة")
                .OrderBy(s => s.SessionDate)
                .ToList();
        }

        /// <summary>
        /// الحصول على الجلسات المتأخرة
        /// </summary>
        public List<AdvancedSessionModel> GetOverdueSessions(List<AdvancedSessionModel> sessions)
        {
            var now = DateTime.Now;
            return sessions
                .Where(s => s.SessionDate < now && s.Status == "مجدولة")
                .OrderBy(s => s.SessionDate)
                .ToList();
        }

        #endregion

        #region Validation

        /// <summary>
        /// التحقق من صحة بيانات الجلسة
        /// </summary>
        public (bool IsValid, List<string> Errors) ValidateSession(AdvancedSessionModel session)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(session.SessionNumber))
                errors.Add("رقم الجلسة مطلوب");

            if (string.IsNullOrWhiteSpace(session.FileNumber))
                errors.Add("رقم الملف مطلوب");

            if (string.IsNullOrWhiteSpace(session.Client))
                errors.Add("اسم الموكل مطلوب");

            if (string.IsNullOrWhiteSpace(session.Court))
                errors.Add("المحكمة مطلوبة");

            if (string.IsNullOrWhiteSpace(session.SessionType))
                errors.Add("نوع الجلسة مطلوب");

            if (string.IsNullOrWhiteSpace(session.AssignedLawyer))
                errors.Add("المحامي المكلف مطلوب");

            if (session.SessionDate < DateTime.Now.Date && session.Status == "مجدولة")
                errors.Add("لا يمكن جدولة جلسة في تاريخ سابق");

            if (session.Expenses < 0)
                errors.Add("المصروفات لا يمكن أن تكون سالبة");

            return (errors.Count == 0, errors);
        }

        #endregion

        #region Export

        /// <summary>
        /// تحضير بيانات التصدير
        /// </summary>
        public List<object> PrepareExportData(List<AdvancedSessionModel> sessions)
        {
            return sessions.Select(s => new
            {
                رقم_الجلسة = s.SessionNumber,
                رقم_الملف = s.FileNumber,
                الموكل = s.Client,
                نوع_القضية = s.CaseType,
                المحكمة = s.Court,
                نوع_الإجراء = s.ProcedureType,
                تاريخ_الإجراء = s.ProcedureDate.ToString("dd/MM/yyyy"),
                القرار = s.Decision,
                تاريخ_الجلسة = s.SessionDate.ToString("dd/MM/yyyy"),
                وقت_الجلسة = s.SessionTimeText,
                نوع_الجلسة = s.SessionType,
                المحامي_المكلف = s.AssignedLawyer,
                القاضي = s.Judge,
                كاتب_الضبط = s.Clerk,
                قاعة_المحكمة = s.CourtRoom,
                الحالة = s.Status,
                الأولوية = s.Priority,
                المصروفات = s.Expenses,
                الملاحظات = s.Notes,
                النتيجة = s.Outcome,
                الجلسة_المقبلة = s.NextSessionText,
                تاريخ_الإنشاء = s.CreatedDate.ToString("dd/MM/yyyy")
            }).Cast<object>().ToList();
        }

        #endregion
    }
}
