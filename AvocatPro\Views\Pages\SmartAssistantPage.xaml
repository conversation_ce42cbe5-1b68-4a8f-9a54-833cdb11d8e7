<Page x:Class="AvocatPro.Views.Pages.SmartAssistantPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:fa="http://schemas.fontawesome.io/icons/"
      Title="المساعد الذكي المتطور" FlowDirection="RightToLeft"
      Background="#F8FAFC">

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- منطقة المحادثة الرئيسية -->
        <Grid Grid.Column="0" Margin="20,20,10,20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- شريط العنوان -->
            <Border Grid.Row="0" Background="White" CornerRadius="15" Padding="20" Margin="0,0,0,20">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                </Border.Effect>
                <Grid>
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                        <Border CornerRadius="50" Width="60" Height="60" Margin="0,0,20,0">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                    <GradientStop Color="#3B82F6" Offset="0"/>
                                    <GradientStop Color="#1E3A8A" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.Background>
                            <Border.Effect>
                                <DropShadowEffect Color="#3B82F6" Opacity="0.4" BlurRadius="15" ShadowDepth="0"/>
                            </Border.Effect>
                            <TextBlock Text="🤖" FontSize="30" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <StackPanel>
                            <TextBlock Text="المساعد الذكي المتطور" FontSize="24" FontWeight="Bold" Foreground="#1F2937"/>
                            <TextBlock Text="ذكاء اصطناعي متقدم للاستشارات القانونية والتحليل الذكي" FontSize="14" Foreground="#6B7280"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- أزرار التحكم -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Center">
                        <Button x:Name="VoiceInputBtn" Background="#3B82F6" Foreground="White" Padding="12,8" Margin="0,0,10,0" Click="VoiceInput_Click" BorderThickness="0">
                            <StackPanel Orientation="Horizontal">
                                <fa:ImageAwesome Icon="Microphone" Width="14" Height="14" Margin="0,0,8,0"/>
                                <TextBlock Text="صوتي"/>
                            </StackPanel>
                        </Button>
                        <Button x:Name="ClearChatBtn" Background="#6B7280" Foreground="White" Padding="12,8" Margin="0,0,10,0" Click="ClearChat_Click" BorderThickness="0">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🗑️" FontSize="14" Margin="0,0,8,0"/>
                                <TextBlock Text="مسح"/>
                            </StackPanel>
                        </Button>
                        <Button x:Name="ExportChatBtn" Background="#10B981" Foreground="White" Padding="12,8" Click="ExportChat_Click" BorderThickness="0">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📥" FontSize="14" Margin="0,0,8,0"/>
                                <TextBlock Text="تصدير"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- منطقة المحادثة -->
            <Border Grid.Row="1" Background="White" CornerRadius="15" Padding="15" Margin="0,0,0,20">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                </Border.Effect>
                <Grid>
                    <ScrollViewer x:Name="ChatScrollViewer" VerticalScrollBarVisibility="Auto" 
                                  HorizontalScrollBarVisibility="Disabled">
                        <StackPanel x:Name="ChatPanel" Margin="15">
                            <!-- رسالة ترحيب متطورة -->
                            <Border CornerRadius="20,20,5,20" Padding="20" Margin="0,0,80,15" HorizontalAlignment="Right">
                                <Border.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#3B82F6" Offset="0"/>
                                        <GradientStop Color="#1E3A8A" Offset="1"/>
                                    </LinearGradientBrush>
                                </Border.Background>
                                <Border.Effect>
                                    <DropShadowEffect Color="#3B82F6" Opacity="0.3" BlurRadius="10" ShadowDepth="3"/>
                                </Border.Effect>
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                        <TextBlock Text="🤖" FontSize="20" Margin="0,0,10,0"/>
                                        <TextBlock Text="مرحباً! أنا مساعدك الذكي المتطور" FontWeight="Bold" 
                                                   Foreground="White" FontSize="16"/>
                                    </StackPanel>
                                    <TextBlock Text="يمكنني مساعدتك في:" Foreground="White" FontSize="14" Margin="0,0,0,10"/>
                                    <StackPanel>
                                        <TextBlock Text="🎯 توقع نتائج القضايا بناءً على البيانات التاريخية" 
                                                   Foreground="White" FontSize="13" Margin="0,2"/>
                                        <TextBlock Text="📄 تحليل الوثائق واستخراج المعلومات المهمة" 
                                                   Foreground="White" FontSize="13" Margin="0,2"/>
                                        <TextBlock Text="📅 الجدولة الذكية وتحسين المواعيد" 
                                                   Foreground="White" FontSize="13" Margin="0,2"/>
                                        <TextBlock Text="⚖️ الاستشارات القانونية المتخصصة" 
                                                   Foreground="White" FontSize="13" Margin="0,2"/>
                                        <TextBlock Text="📊 تحليل المخاطر وتقييم العملاء" 
                                                   Foreground="White" FontSize="13" Margin="0,2"/>
                                    </StackPanel>
                                    <TextBlock Text="اطرح سؤالك أو اختر من الاقتراحات السريعة!" 
                                               Foreground="White" FontStyle="Italic" Margin="0,10,0,0"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </ScrollViewer>

                    <!-- مؤشر الكتابة المتطور -->
                    <Border x:Name="TypingIndicator" Background="#F3F4F6"
                            CornerRadius="20" Padding="20" Margin="0,0,80,15"
                            HorizontalAlignment="Right" VerticalAlignment="Bottom"
                            Visibility="Collapsed">
                        <StackPanel Orientation="Horizontal">
                            <Border Background="#3B82F6" CornerRadius="15" Width="30" Height="30" Margin="0,0,15,0">
                                <TextBlock Text="🤖" FontSize="16" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel>
                                <TextBlock Text="المساعد الذكي يحلل..." FontWeight="SemiBold" 
                                           Foreground="#1F2937" FontSize="14"/>
                                <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                    <Ellipse x:Name="Dot1" Width="8" Height="8" Fill="#3B82F6" Margin="2"/>
                                    <Ellipse x:Name="Dot2" Width="8" Height="8" Fill="#3B82F6" Margin="2"/>
                                    <Ellipse x:Name="Dot3" Width="8" Height="8" Fill="#3B82F6" Margin="2"/>
                                </StackPanel>
                            </StackPanel>
                        </StackPanel>
                    </Border>
                </Grid>
            </Border>

            <!-- منطقة الإدخال المتطورة -->
            <Border Grid.Row="2" Background="White" CornerRadius="15" Padding="20">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                </Border.Effect>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- شريط نوع الاستعلام -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                        <TextBlock Text="نوع الاستعلام:" FontWeight="SemiBold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <ComboBox x:Name="QueryTypeComboBox" Width="200" Padding="10,8"
                                  SelectionChanged="QueryType_SelectionChanged">
                            <ComboBoxItem Content="استشارة قانونية عامة" IsSelected="True"/>
                            <ComboBoxItem Content="توقع نتيجة قضية"/>
                            <ComboBoxItem Content="تحليل وثيقة"/>
                            <ComboBoxItem Content="تحسين الجدولة"/>
                            <ComboBoxItem Content="تقييم مخاطر العميل"/>
                        </ComboBox>
                    </StackPanel>

                    <!-- منطقة الإدخال -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- حقل الإدخال المتطور -->
                        <Border Grid.Column="0" Background="#F9FAFB"
                                BorderBrush="#E5E7EB" BorderThickness="2"
                                CornerRadius="25" Margin="0,0,15,0">
                            <TextBox x:Name="QueryTextBox" Background="Transparent" BorderThickness="0" 
                                     Padding="20,15" FontSize="14" VerticalContentAlignment="Center"
                                     Text="اكتب استفسارك هنا..." Foreground="#9CA3AF"
                                     GotFocus="QueryTextBox_GotFocus" LostFocus="QueryTextBox_LostFocus"
                                     KeyDown="QueryTextBox_KeyDown" TextWrapping="Wrap" AcceptsReturn="True"
                                     MaxHeight="100" VerticalScrollBarVisibility="Auto"/>
                        </Border>

                        <!-- زر الإرسال -->
                        <Button x:Name="SendBtn" Grid.Column="1" Width="60" Height="60" Margin="0,0,15,0" Click="SendBtn_Click"
                                BorderThickness="0">
                            <Button.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                    <GradientStop Color="#3B82F6" Offset="0"/>
                                    <GradientStop Color="#1E3A8A" Offset="1"/>
                                </LinearGradientBrush>
                            </Button.Background>
                            <TextBlock Text="📤" FontSize="24" Foreground="White"/>
                        </Button>

                        <!-- زر الملفات -->
                        <Button x:Name="AttachFileBtn" Grid.Column="2" Background="#3B82F6" Foreground="White"
                                Width="60" Height="60" Click="AttachFile_Click" BorderThickness="0">
                            <TextBlock Text="📎" FontSize="24" Foreground="White"/>
                        </Button>
                    </Grid>
                </Grid>
            </Border>
        </Grid>

        <!-- الشريط الجانبي للميزات -->
        <Grid Grid.Column="1" Margin="10,20,20,20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- الاقتراحات السريعة -->
            <Border Grid.Row="0" Background="White" CornerRadius="15" Padding="20" Margin="0,0,0,15">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                </Border.Effect>
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                        <TextBlock Text="💡" FontSize="20" Margin="0,0,10,0"/>
                        <TextBlock Text="اقتراحات سريعة" FontSize="18" FontWeight="SemiBold" Foreground="#1F2937"/>
                    </StackPanel>
                    <StackPanel x:Name="QuickSuggestionsPanel">
                        <!-- سيتم إضافة الاقتراحات ديناميكياً -->
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- إحصائيات الذكاء الاصطناعي -->
            <Border Grid.Row="1" Background="White" CornerRadius="15" Padding="20" Margin="0,0,0,15">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                </Border.Effect>
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                        <TextBlock Text="📊" FontSize="20" Margin="0,0,10,0"/>
                        <TextBlock Text="إحصائيات الذكاء الاصطناعي" FontSize="18" FontWeight="SemiBold" Foreground="#1F2937"/>
                    </StackPanel>
                    <StackPanel x:Name="AIStatsPanel">
                        <!-- سيتم إضافة الإحصائيات ديناميكياً -->
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- التحليلات الأخيرة -->
            <Border Grid.Row="2" Background="White" CornerRadius="15" Padding="20" Margin="0,0,0,15">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                </Border.Effect>
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                        <TextBlock Text="📈" FontSize="20" Margin="0,0,10,0"/>
                        <TextBlock Text="التحليلات الأخيرة" FontSize="18" FontWeight="SemiBold" Foreground="#1F2937"/>
                    </StackPanel>
                    <ListView x:Name="RecentAnalysisListView" Height="150" BorderThickness="0" Background="Transparent">
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Border Background="#F3F4F6" CornerRadius="8" Padding="10" Margin="0,0,0,5">
                                    <StackPanel>
                                        <TextBlock Text="{Binding Type}" FontWeight="SemiBold" FontSize="12"/>
                                        <TextBlock Text="{Binding Summary}" FontSize="11"
                                                   Foreground="#6B7280" TextWrapping="Wrap"/>
                                        <TextBlock Text="{Binding Time}" FontSize="10"
                                                   Foreground="#9CA3AF" HorizontalAlignment="Left"/>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                </StackPanel>
            </Border>

            <!-- أدوات متقدمة -->
            <Border Grid.Row="3" Background="White" CornerRadius="15" Padding="20">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                </Border.Effect>
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                        <TextBlock Text="⚙️" FontSize="20" Margin="0,0,10,0"/>
                        <TextBlock Text="أدوات متقدمة" FontSize="18" FontWeight="SemiBold" Foreground="#1F2937"/>
                    </StackPanel>
                    <StackPanel>
                        <Button x:Name="PredictCaseBtn" Background="#3B82F6" Foreground="White" Padding="12,8" Margin="0,0,0,10" Click="PredictCase_Click" BorderThickness="0">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🔮" FontSize="16" Margin="0,0,8,0"/>
                                <TextBlock Text="توقع نتيجة قضية"/>
                            </StackPanel>
                        </Button>
                        <Button x:Name="AnalyzeDocumentBtn" Background="#3B82F6" Foreground="White" Padding="12,8" Margin="0,0,0,10" Click="AnalyzeDocument_Click" BorderThickness="0">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📄" FontSize="16" Margin="0,0,8,0"/>
                                <TextBlock Text="تحليل وثيقة"/>
                            </StackPanel>
                        </Button>
                        <Button x:Name="OptimizeScheduleBtn" Background="#10B981" Foreground="White" Padding="12,8" Margin="0,0,0,10" Click="OptimizeSchedule_Click" BorderThickness="0">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📅" FontSize="16" Margin="0,0,8,0"/>
                                <TextBlock Text="تحسين الجدولة"/>
                            </StackPanel>
                        </Button>
                        <Button x:Name="AssessRiskBtn" Background="#F59E0B" Foreground="White" Padding="12,8" Click="AssessRisk_Click" BorderThickness="0">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="⚠️" FontSize="16" Margin="0,0,8,0"/>
                                <TextBlock Text="تقييم المخاطر"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </StackPanel>
            </Border>
        </Grid>
    </Grid>

    <!-- الموارد والأنيميشن -->
    <Page.Resources>
        <Storyboard x:Key="TypingAnimation" RepeatBehavior="Forever">
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Dot1" Storyboard.TargetProperty="Opacity">
                <EasingDoubleKeyFrame KeyTime="0:0:0" Value="0.3"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.3" Value="1"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="0.3"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Dot2" Storyboard.TargetProperty="Opacity">
                <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="0.3"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.5" Value="1"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.8" Value="0.3"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Dot3" Storyboard.TargetProperty="Opacity">
                <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="0.3"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="1"/>
                <EasingDoubleKeyFrame KeyTime="0:0:1" Value="0.3"/>
            </DoubleAnimationUsingKeyFrames>
        </Storyboard>
    </Page.Resources>
</Page>
