using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Input;
using System.Linq;
using System.Text.Json;
using System.IO;
using System.Threading.Tasks;
using AvocatPro.Helpers;

namespace AvocatPro.Services
{
    public class KeyboardShortcutManager
    {
        private static KeyboardShortcutManager _instance;
        public static KeyboardShortcutManager Instance => _instance ??= new KeyboardShortcutManager();

        private readonly Dictionary<string, ShortcutAction> _shortcuts;
        private readonly Dictionary<string, KeyGesture> _keyGestures;
        private Window _mainWindow;

        public event EventHandler<ShortcutExecutedEventArgs> ShortcutExecuted;

        private KeyboardShortcutManager()
        {
            _shortcuts = new Dictionary<string, ShortcutAction>();
            _keyGestures = new Dictionary<string, KeyGesture>();
            InitializeDefaultShortcuts();
        }

        public void Initialize(Window mainWindow)
        {
            _mainWindow = mainWindow;
            RegisterKeyBindings();
            LoadCustomShortcuts();
        }

        private void InitializeDefaultShortcuts()
        {
            // اختصارات الملفات
            AddShortcut("NewFile", "إنشاء ملف جديد", Key.N, ModifierKeys.Control, () => ExecuteCommand("NewFile"));
            AddShortcut("OpenFile", "فتح ملف", Key.O, ModifierKeys.Control, () => ExecuteCommand("OpenFile"));
            AddShortcut("SaveFile", "حفظ الملف", Key.S, ModifierKeys.Control, () => ExecuteCommand("SaveFile"));
            AddShortcut("SaveAsFile", "حفظ باسم", Key.S, ModifierKeys.Control | ModifierKeys.Shift, () => ExecuteCommand("SaveAsFile"));
            AddShortcut("Print", "طباعة", Key.P, ModifierKeys.Control, () => ExecuteCommand("Print"));

            // اختصارات التحرير
            AddShortcut("Undo", "تراجع", Key.Z, ModifierKeys.Control, () => ExecuteCommand("Undo"));
            AddShortcut("Redo", "إعادة", Key.Y, ModifierKeys.Control, () => ExecuteCommand("Redo"));
            AddShortcut("Cut", "قص", Key.X, ModifierKeys.Control, () => ExecuteCommand("Cut"));
            AddShortcut("Copy", "نسخ", Key.C, ModifierKeys.Control, () => ExecuteCommand("Copy"));
            AddShortcut("Paste", "لصق", Key.V, ModifierKeys.Control, () => ExecuteCommand("Paste"));
            AddShortcut("SelectAll", "تحديد الكل", Key.A, ModifierKeys.Control, () => ExecuteCommand("SelectAll"));

            // اختصارات التنقل
            AddShortcut("Dashboard", "لوحة التحكم", Key.D, ModifierKeys.Control, () => NavigateToPage("Dashboard"));
            AddShortcut("Clients", "العملاء", Key.C, ModifierKeys.Control | ModifierKeys.Shift, () => NavigateToPage("Clients"));
            AddShortcut("Files", "الملفات", Key.F, ModifierKeys.Control | ModifierKeys.Shift, () => NavigateToPage("Files"));
            AddShortcut("Sessions", "الجلسات", Key.S, ModifierKeys.Control | ModifierKeys.Shift, () => NavigateToPage("Sessions"));
            AddShortcut("Appointments", "المواعيد", Key.A, ModifierKeys.Control | ModifierKeys.Shift, () => NavigateToPage("Appointments"));
            AddShortcut("Settings", "الإعدادات", Key.OemComma, ModifierKeys.Control, () => NavigateToPage("Settings"));

            // اختصارات البحث
            AddShortcut("Search", "بحث", Key.F, ModifierKeys.Control, () => ExecuteCommand("Search"));
            AddShortcut("AdvancedSearch", "بحث متقدم", Key.F, ModifierKeys.Control | ModifierKeys.Shift, () => ExecuteCommand("AdvancedSearch"));

            // اختصارات النوافذ
            AddShortcut("CloseWindow", "إغلاق النافذة", Key.W, ModifierKeys.Control, () => ExecuteCommand("CloseWindow"));
            AddShortcut("MinimizeWindow", "تصغير النافذة", Key.M, ModifierKeys.Control, () => ExecuteCommand("MinimizeWindow"));
            AddShortcut("MaximizeWindow", "تكبير النافذة", Key.M, ModifierKeys.Control | ModifierKeys.Shift, () => ExecuteCommand("MaximizeWindow"));
            AddShortcut("FullScreen", "ملء الشاشة", Key.F11, ModifierKeys.None, () => ExecuteCommand("FullScreen"));

            // اختصارات المساعدة
            AddShortcut("Help", "المساعدة", Key.F1, ModifierKeys.None, () => ExecuteCommand("Help"));
            AddShortcut("About", "حول البرنامج", Key.F1, ModifierKeys.Control, () => ExecuteCommand("About"));

            // اختصارات سريعة للإجراءات
            AddShortcut("QuickAddClient", "إضافة عميل سريع", Key.Q, ModifierKeys.Control | ModifierKeys.Alt, () => ExecuteCommand("QuickAddClient"));
            AddShortcut("QuickAddFile", "إضافة ملف سريع", Key.W, ModifierKeys.Control | ModifierKeys.Alt, () => ExecuteCommand("QuickAddFile"));
            AddShortcut("QuickAddAppointment", "إضافة موعد سريع", Key.E, ModifierKeys.Control | ModifierKeys.Alt, () => ExecuteCommand("QuickAddAppointment"));

            // اختصارات التبديل
            AddShortcut("ToggleDarkMode", "تبديل الوضع الليلي", Key.D, ModifierKeys.Control | ModifierKeys.Alt, () => ExecuteCommand("ToggleDarkMode"));
            AddShortcut("ToggleSidebar", "إظهار/إخفاء الشريط الجانبي", Key.B, ModifierKeys.Control, () => ExecuteCommand("ToggleSidebar"));
        }

        private void AddShortcut(string id, string description, Key key, ModifierKeys modifiers, Action action)
        {
            var shortcut = new ShortcutAction
            {
                Id = id,
                Description = description,
                Key = key,
                Modifiers = modifiers,
                Action = action,
                IsEnabled = true
            };

            _shortcuts[id] = shortcut;
            _keyGestures[id] = new KeyGesture(key, modifiers);
        }

        private void RegisterKeyBindings()
        {
            if (_mainWindow == null) return;

            foreach (var shortcut in _shortcuts.Values.Where(s => s.IsEnabled))
            {
                var command = new RelayCommand(() => ExecuteShortcut(shortcut));
                var keyBinding = new KeyBinding(command, _keyGestures[shortcut.Id]);
                _mainWindow.InputBindings.Add(keyBinding);
            }
        }

        private void ExecuteShortcut(ShortcutAction shortcut)
        {
            try
            {
                shortcut.Action?.Invoke();
                ShortcutExecuted?.Invoke(this, new ShortcutExecutedEventArgs(shortcut));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تنفيذ الاختصار {shortcut.Description}: {ex.Message}", 
                               "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExecuteCommand(string command)
        {
            // تنفيذ الأوامر المختلفة
            switch (command)
            {
                case "NewFile":
                    // فتح نافذة إنشاء ملف جديد
                    break;
                case "Search":
                    // فتح نافذة البحث
                    break;
                case "ToggleDarkMode":
                    ThemeManager.Instance.ToggleDarkMode();
                    break;
                case "FullScreen":
                    ToggleFullScreen();
                    break;
                // إضافة المزيد من الأوامر حسب الحاجة
            }
        }

        private void NavigateToPage(string pageName)
        {
            // التنقل إلى الصفحة المحددة
            // يمكن تنفيذ هذا من خلال event أو service للتنقل
        }

        private void ToggleFullScreen()
        {
            if (_mainWindow != null)
            {
                if (_mainWindow.WindowState == WindowState.Maximized && _mainWindow.WindowStyle == WindowStyle.None)
                {
                    _mainWindow.WindowState = WindowState.Normal;
                    _mainWindow.WindowStyle = WindowStyle.SingleBorderWindow;
                }
                else
                {
                    _mainWindow.WindowStyle = WindowStyle.None;
                    _mainWindow.WindowState = WindowState.Maximized;
                }
            }
        }

        public void UpdateShortcut(string id, Key newKey, ModifierKeys newModifiers)
        {
            if (_shortcuts.ContainsKey(id))
            {
                var shortcut = _shortcuts[id];
                shortcut.Key = newKey;
                shortcut.Modifiers = newModifiers;
                _keyGestures[id] = new KeyGesture(newKey, newModifiers);
                
                // إعادة تسجيل الاختصارات
                RefreshKeyBindings();
                SaveCustomShortcuts();
            }
        }

        public void EnableShortcut(string id, bool enabled)
        {
            if (_shortcuts.ContainsKey(id))
            {
                _shortcuts[id].IsEnabled = enabled;
                RefreshKeyBindings();
                SaveCustomShortcuts();
            }
        }

        private void RefreshKeyBindings()
        {
            if (_mainWindow == null) return;

            // إزالة جميع الاختصارات الحالية
            _mainWindow.InputBindings.Clear();
            
            // إعادة تسجيل الاختصارات المفعلة
            RegisterKeyBindings();
        }

        public IEnumerable<ShortcutAction> GetAllShortcuts()
        {
            return _shortcuts.Values.OrderBy(s => s.Description);
        }

        public ShortcutAction GetShortcut(string id)
        {
            return _shortcuts.TryGetValue(id, out var shortcut) ? shortcut : null;
        }

        public string GetShortcutText(string id)
        {
            if (_shortcuts.TryGetValue(id, out var shortcut))
            {
                return FormatShortcutText(shortcut.Key, shortcut.Modifiers);
            }
            return string.Empty;
        }

        private string FormatShortcutText(Key key, ModifierKeys modifiers)
        {
            var parts = new List<string>();

            if (modifiers.HasFlag(ModifierKeys.Control))
                parts.Add("Ctrl");
            if (modifiers.HasFlag(ModifierKeys.Alt))
                parts.Add("Alt");
            if (modifiers.HasFlag(ModifierKeys.Shift))
                parts.Add("Shift");
            if (modifiers.HasFlag(ModifierKeys.Windows))
                parts.Add("Win");

            parts.Add(key.ToString());

            return string.Join(" + ", parts);
        }

        private async void SaveCustomShortcuts()
        {
            try
            {
                var customShortcuts = _shortcuts.Values
                    .Where(s => s.IsCustom || !s.IsEnabled || s.Key != s.OriginalKey || s.Modifiers != s.OriginalModifiers)
                    .Select(s => new CustomShortcut
                    {
                        Id = s.Id,
                        Key = s.Key,
                        Modifiers = s.Modifiers,
                        IsEnabled = s.IsEnabled
                    })
                    .ToList();

                var json = JsonSerializer.Serialize(customShortcuts, new JsonSerializerOptions { WriteIndented = true });
                var path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                                       "AvocatPro", "shortcuts.json");
                
                Directory.CreateDirectory(Path.GetDirectoryName(path));
                await File.WriteAllTextAsync(path, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ الاختصارات: {ex.Message}");
            }
        }

        private async void LoadCustomShortcuts()
        {
            try
            {
                var path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                                       "AvocatPro", "shortcuts.json");
                
                if (File.Exists(path))
                {
                    var json = await File.ReadAllTextAsync(path);
                    var customShortcuts = JsonSerializer.Deserialize<List<CustomShortcut>>(json);
                    
                    if (customShortcuts != null)
                    {
                        foreach (var custom in customShortcuts)
                        {
                            if (_shortcuts.ContainsKey(custom.Id))
                            {
                                var shortcut = _shortcuts[custom.Id];
                                shortcut.Key = custom.Key;
                                shortcut.Modifiers = custom.Modifiers;
                                shortcut.IsEnabled = custom.IsEnabled;
                                _keyGestures[custom.Id] = new KeyGesture(custom.Key, custom.Modifiers);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الاختصارات: {ex.Message}");
            }
        }
    }

    public class ShortcutAction
    {
        public string Id { get; set; }
        public string Description { get; set; }
        public Key Key { get; set; }
        public ModifierKeys Modifiers { get; set; }
        public Key OriginalKey { get; set; }
        public ModifierKeys OriginalModifiers { get; set; }
        public Action Action { get; set; }
        public bool IsEnabled { get; set; }
        public bool IsCustom { get; set; }

        public ShortcutAction()
        {
            OriginalKey = Key;
            OriginalModifiers = Modifiers;
        }
    }

    public class CustomShortcut
    {
        public string Id { get; set; }
        public Key Key { get; set; }
        public ModifierKeys Modifiers { get; set; }
        public bool IsEnabled { get; set; }
    }

    public class ShortcutExecutedEventArgs : EventArgs
    {
        public ShortcutAction Shortcut { get; }

        public ShortcutExecutedEventArgs(ShortcutAction shortcut)
        {
            Shortcut = shortcut;
        }
    }


}
