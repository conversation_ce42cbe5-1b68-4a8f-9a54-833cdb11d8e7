﻿#pragma checksum "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "A2F5B9B682F85D41A38C0FB4D6A5F53B0781FF09"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Windows {
    
    
    /// <summary>
    /// FileDetailsWindow
    /// </summary>
    public partial class FileDetailsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 32 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileNumberTitle;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileSubject;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileNumberText;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CourtReferenceText;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OfficeReferenceText;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border FileTypeBorder;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileTypeText;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CourtText;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border CaseTypeBorder;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CaseTypeText;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border StatusBorder;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border PriorityBorder;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PriorityText;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TrackingStatusIcon;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TrackingStatusText;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ElectronicCodePanel;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ElectronicCodeText;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel FileYearPanel;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileYearText;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AppealCourtPanel;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AppealCourtText;
        
        #line default
        #line hidden
        
        
        #line 208 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ClientText;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OpponentText;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AssignedLawyerText;
        
        #line default
        #line hidden
        
        
        #line 233 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SubjectText;
        
        #line default
        #line hidden
        
        
        #line 246 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProcedureTypeText;
        
        #line default
        #line hidden
        
        
        #line 255 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DecisionText;
        
        #line default
        #line hidden
        
        
        #line 268 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CreatedDateText;
        
        #line default
        #line hidden
        
        
        #line 276 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastUpdateText;
        
        #line default
        #line hidden
        
        
        #line 285 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NextSessionIcon;
        
        #line default
        #line hidden
        
        
        #line 286 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NextSessionText;
        
        #line default
        #line hidden
        
        
        #line 295 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastSessionText;
        
        #line default
        #line hidden
        
        
        #line 307 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EstimatedValueText;
        
        #line default
        #line hidden
        
        
        #line 315 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SessionsCountText;
        
        #line default
        #line hidden
        
        
        #line 324 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NotesText;
        
        #line default
        #line hidden
        
        
        #line 339 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditButton;
        
        #line default
        #line hidden
        
        
        #line 364 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TrackButton;
        
        #line default
        #line hidden
        
        
        #line 389 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/windows/filedetailswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.FileNumberTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.FileSubject = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 39 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.Close_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.FileNumberText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.CourtReferenceText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.OfficeReferenceText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.FileTypeBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 8:
            this.FileTypeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.CourtText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.CaseTypeBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 11:
            this.CaseTypeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.StatusBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 13:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.PriorityBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 15:
            this.PriorityText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.TrackingStatusIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.TrackingStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.ElectronicCodePanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 19:
            this.ElectronicCodeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.FileYearPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 21:
            this.FileYearText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.AppealCourtPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 23:
            this.AppealCourtText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.ClientText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.OpponentText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.AssignedLawyerText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 27:
            this.SubjectText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.ProcedureTypeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 29:
            this.DecisionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            this.CreatedDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 31:
            this.LastUpdateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 32:
            this.NextSessionIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 33:
            this.NextSessionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 34:
            this.LastSessionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 35:
            this.EstimatedValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 36:
            this.SessionsCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 37:
            this.NotesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 38:
            this.EditButton = ((System.Windows.Controls.Button)(target));
            
            #line 342 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
            this.EditButton.Click += new System.Windows.RoutedEventHandler(this.Edit_Click);
            
            #line default
            #line hidden
            return;
            case 39:
            this.TrackButton = ((System.Windows.Controls.Button)(target));
            
            #line 367 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
            this.TrackButton.Click += new System.Windows.RoutedEventHandler(this.Track_Click);
            
            #line default
            #line hidden
            return;
            case 40:
            this.PrintButton = ((System.Windows.Controls.Button)(target));
            
            #line 392 "..\..\..\..\..\Views\Windows\FileDetailsWindow.xaml"
            this.PrintButton.Click += new System.Windows.RoutedEventHandler(this.Print_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

