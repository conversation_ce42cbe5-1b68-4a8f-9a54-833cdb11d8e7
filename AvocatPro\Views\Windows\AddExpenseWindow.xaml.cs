using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using AvocatPro.Models;

namespace AvocatPro.Views.Windows;

public partial class AddExpenseWindow : Window
{
    public Expense? NewExpense { get; private set; }
    public bool SaveAndAddAnother { get; private set; }
    public bool SaveAndPay { get; private set; }
    
    private readonly ObservableCollection<string> _attachments;

    public AddExpenseWindow()
    {
        InitializeComponent();
        _attachments = new ObservableCollection<string>();
        AttachmentsListBox.ItemsSource = _attachments;
        InitializeForm();
    }

    private void InitializeForm()
    {
        GenerateReference();
        SetDefaultValues();
    }

    private void GenerateReference()
    {
        var reference = $"EXP-{DateTime.Now:yyyyMMdd}-{DateTime.Now:HHmmss}";
        ReferenceTextBox.Text = reference;
    }

    private void SetDefaultValues()
    {
        ExpenseDatePicker.SelectedDate = DateTime.Now;
        CategoryComboBox.SelectedIndex = 0;
        TypeComboBox.SelectedIndex = 6; // أخرى
        PaymentMethodComboBox.SelectedIndex = 0; // نقدي
        PaymentStatusComboBox.SelectedIndex = 0; // في الانتظار
        CaseComboBox.SelectedIndex = 0; // بدون قضية
        ClientComboBox.SelectedIndex = 0; // بدون موكل
    }

    private void AddAttachmentButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Title = "اختيار ملف للإرفاق",
                Filter = "جميع الملفات (*.*)|*.*|ملفات PDF (*.pdf)|*.pdf|صور (*.jpg;*.jpeg;*.png)|*.jpg;*.jpeg;*.png|مستندات Word (*.doc;*.docx)|*.doc;*.docx|جداول Excel (*.xls;*.xlsx)|*.xls;*.xlsx",
                Multiselect = true
            };

            if (openFileDialog.ShowDialog() == true)
            {
                foreach (var fileName in openFileDialog.FileNames)
                {
                    var fileInfo = new System.IO.FileInfo(fileName);
                    if (!_attachments.Contains(fileInfo.Name))
                    {
                        _attachments.Add(fileInfo.Name);
                    }
                }
                
                if (openFileDialog.FileNames.Length > 0)
                {
                    MessageBox.Show($"تم إرفاق {openFileDialog.FileNames.Length} ملف بنجاح!", "إرفاق الملفات", 
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إرفاق الملف: {ex.Message}", "خطأ", 
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ScanDocumentButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // محاكاة عملية المسح الضوئي
            var result = MessageBox.Show("هل تريد بدء عملية المسح الضوئي؟\n\nسيتم فتح تطبيق المسح الضوئي المثبت على النظام.", 
                                       "مسح ضوئي", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                var scannedFileName = $"مسح_ضوئي_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";
                _attachments.Add(scannedFileName);
                
                MessageBox.Show($"تم إنشاء الملف الممسوح ضوئياً: {scannedFileName}", "نجح المسح الضوئي", 
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في المسح الضوئي: {ex.Message}", "خطأ", 
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void RemoveAttachmentButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is string fileName)
        {
            var result = MessageBox.Show($"هل تريد حذف المرفق: {fileName}؟", "تأكيد الحذف", 
                                       MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                _attachments.Remove(fileName);
            }
        }
    }

    private void SaveButton_Click(object sender, RoutedEventArgs e)
    {
        if (ValidateForm())
        {
            CreateExpense();
            SaveAndAddAnother = false;
            SaveAndPay = false;
            DialogResult = true;
            Close();
        }
    }

    private void SaveAndNewButton_Click(object sender, RoutedEventArgs e)
    {
        if (ValidateForm())
        {
            CreateExpense();
            SaveAndAddAnother = true;
            SaveAndPay = false;
            MessageBox.Show("تم حفظ المصروف بنجاح!", "نجح الحفظ", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
            
            // مسح النموذج لإضافة مصروف جديد
            ClearForm();
            GenerateReference();
            SetDefaultValues();
        }
    }

    private void SaveAndPayButton_Click(object sender, RoutedEventArgs e)
    {
        if (ValidateForm())
        {
            // تعيين حالة الدفع كمدفوع
            PaymentStatusComboBox.SelectedIndex = 1; // مدفوع
            PaymentDatePicker.SelectedDate = DateTime.Now;
            
            CreateExpense();
            SaveAndAddAnother = false;
            SaveAndPay = true;
            DialogResult = true;
            Close();
        }
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("هل تريد إلغاء العملية؟\nسيتم فقدان جميع البيانات المدخلة.", 
                                   "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Question);
        
        if (result == MessageBoxResult.Yes)
        {
            DialogResult = false;
            Close();
        }
    }

    private bool ValidateForm()
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(DescriptionTextBox.Text))
            errors.Add("يجب إدخال وصف المصروف");

        if (string.IsNullOrWhiteSpace(AmountTextBox.Text))
            errors.Add("يجب إدخال مبلغ المصروف");
        else if (!decimal.TryParse(AmountTextBox.Text, out var amount) || amount <= 0)
            errors.Add("مبلغ المصروف يجب أن يكون رقماً صحيحاً أكبر من صفر");

        if (!ExpenseDatePicker.SelectedDate.HasValue)
            errors.Add("يجب تحديد تاريخ المصروف");

        if (CategoryComboBox.SelectedItem == null)
            errors.Add("يجب اختيار فئة المصروف");

        if (TypeComboBox.SelectedItem == null)
            errors.Add("يجب اختيار نوع المصروف");

        if (PaymentMethodComboBox.SelectedItem == null)
            errors.Add("يجب اختيار طريقة الدفع");

        if (PaymentStatusComboBox.SelectedItem == null)
            errors.Add("يجب اختيار حالة الدفع");

        // التحقق من تاريخ الدفع إذا كانت الحالة مدفوع
        var selectedStatus = (PaymentStatusComboBox.SelectedItem as ComboBoxItem)?.Tag?.ToString();
        if (selectedStatus == "Paid" && !PaymentDatePicker.SelectedDate.HasValue)
            errors.Add("يجب تحديد تاريخ الدفع عند اختيار حالة 'مدفوع'");

        if (errors.Count > 0)
        {
            MessageBox.Show($"يرجى تصحيح الأخطاء التالية:\n\n{string.Join("\n", errors)}", 
                           "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
            return false;
        }

        return true;
    }

    private void CreateExpense()
    {
        var attachmentsJson = _attachments.Count > 0 ? 
            System.Text.Json.JsonSerializer.Serialize(_attachments.ToList()) : null;

        NewExpense = new Expense
        {
            Reference = ReferenceTextBox.Text.Trim(),
            Description = DescriptionTextBox.Text.Trim(),
            Amount = decimal.Parse(AmountTextBox.Text),
            ExpenseDate = ExpenseDatePicker.SelectedDate!.Value,
            Category = Enum.Parse<ExpenseCategory>((string)((ComboBoxItem)CategoryComboBox.SelectedItem).Tag),
            Type = Enum.Parse<ExpenseType>((string)((ComboBoxItem)TypeComboBox.SelectedItem).Tag),
            CaseId = GetSelectedId(CaseComboBox),
            ClientId = GetSelectedId(ClientComboBox),
            Vendor = VendorTextBox.Text.Trim(),
            InvoiceNumber = InvoiceNumberTextBox.Text.Trim(),
            PaymentMethod = Enum.Parse<PaymentMethod>((string)((ComboBoxItem)PaymentMethodComboBox.SelectedItem).Tag),
            PaymentStatus = Enum.Parse<PaymentStatus>((string)((ComboBoxItem)PaymentStatusComboBox.SelectedItem).Tag),
            PaymentDate = PaymentDatePicker.SelectedDate,
            IsReimbursable = IsReimbursableCheckBox.IsChecked == true,
            Attachments = attachmentsJson,
            CreatedAt = DateTime.Now,
            UpdatedAt = DateTime.Now
        };
    }

    private int? GetSelectedId(ComboBox comboBox)
    {
        if (comboBox.SelectedItem is ComboBoxItem item && item.Tag is string tag)
        {
            if (int.TryParse(tag, out var id) && id > 0)
                return id;
        }
        return null;
    }

    private void ClearForm()
    {
        DescriptionTextBox.Clear();
        AmountTextBox.Clear();
        VendorTextBox.Clear();
        InvoiceNumberTextBox.Clear();
        NotesTextBox.Clear();
        
        CategoryComboBox.SelectedIndex = 0;
        TypeComboBox.SelectedIndex = 6;
        PaymentMethodComboBox.SelectedIndex = 0;
        PaymentStatusComboBox.SelectedIndex = 0;
        CaseComboBox.SelectedIndex = 0;
        ClientComboBox.SelectedIndex = 0;
        
        PaymentDatePicker.SelectedDate = null;
        IsReimbursableCheckBox.IsChecked = false;
        
        _attachments.Clear();
    }
}
