using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Linq;
using System.Text.Json;
using System.IO;
using System.Threading.Tasks;

namespace AvocatPro.Services
{
    public class UICustomizationManager
    {
        private static UICustomizationManager _instance;
        public static UICustomizationManager Instance => _instance ??= new UICustomizationManager();

        private readonly Dictionary<string, UILayout> _layouts;
        private readonly Dictionary<string, CustomWidget> _widgets;
        private UILayout _currentLayout;

        public event EventHandler<LayoutChangedEventArgs> LayoutChanged;
        public event EventHandler<WidgetChangedEventArgs> WidgetChanged;

        private UICustomizationManager()
        {
            _layouts = new Dictionary<string, UILayout>();
            _widgets = new Dictionary<string, CustomWidget>();
            InitializeDefaultLayouts();
        }

        private void InitializeDefaultLayouts()
        {
            // تخطيط افتراضي
            var defaultLayout = new UILayout
            {
                Id = "Default",
                Name = "التخطيط الافتراضي",
                Description = "التخطيط الأساسي للتطبيق",
                IsDefault = true,
                Widgets = new List<WidgetPosition>
                {
                    new WidgetPosition { WidgetId = "QuickStats", X = 0, Y = 0, Width = 2, Height = 1 },
                    new WidgetPosition { WidgetId = "RecentFiles", X = 2, Y = 0, Width = 2, Height = 1 },
                    new WidgetPosition { WidgetId = "UpcomingAppointments", X = 0, Y = 1, Width = 2, Height = 2 },
                    new WidgetPosition { WidgetId = "TaskList", X = 2, Y = 1, Width = 2, Height = 2 },
                    new WidgetPosition { WidgetId = "Calendar", X = 0, Y = 3, Width = 4, Height = 2 }
                }
            };

            // تخطيط مدمج
            var compactLayout = new UILayout
            {
                Id = "Compact",
                Name = "التخطيط المدمج",
                Description = "تخطيط مدمج للشاشات الصغيرة",
                IsDefault = false,
                Widgets = new List<WidgetPosition>
                {
                    new WidgetPosition { WidgetId = "QuickStats", X = 0, Y = 0, Width = 4, Height = 1 },
                    new WidgetPosition { WidgetId = "RecentFiles", X = 0, Y = 1, Width = 2, Height = 1 },
                    new WidgetPosition { WidgetId = "UpcomingAppointments", X = 2, Y = 1, Width = 2, Height = 1 },
                    new WidgetPosition { WidgetId = "TaskList", X = 0, Y = 2, Width = 4, Height = 1 }
                }
            };

            // تخطيط موسع
            var expandedLayout = new UILayout
            {
                Id = "Expanded",
                Name = "التخطيط الموسع",
                Description = "تخطيط موسع للشاشات الكبيرة",
                IsDefault = false,
                Widgets = new List<WidgetPosition>
                {
                    new WidgetPosition { WidgetId = "QuickStats", X = 0, Y = 0, Width = 3, Height = 1 },
                    new WidgetPosition { WidgetId = "RecentFiles", X = 3, Y = 0, Width = 3, Height = 1 },
                    new WidgetPosition { WidgetId = "UpcomingAppointments", X = 0, Y = 1, Width = 2, Height = 2 },
                    new WidgetPosition { WidgetId = "TaskList", X = 2, Y = 1, Width = 2, Height = 2 },
                    new WidgetPosition { WidgetId = "Calendar", X = 4, Y = 1, Width = 2, Height = 2 },
                    new WidgetPosition { WidgetId = "Charts", X = 0, Y = 3, Width = 3, Height = 2 },
                    new WidgetPosition { WidgetId = "Notifications", X = 3, Y = 3, Width = 3, Height = 2 }
                }
            };

            _layouts["Default"] = defaultLayout;
            _layouts["Compact"] = compactLayout;
            _layouts["Expanded"] = expandedLayout;
            _currentLayout = defaultLayout;

            InitializeDefaultWidgets();
        }

        private void InitializeDefaultWidgets()
        {
            var widgets = new[]
            {
                new CustomWidget
                {
                    Id = "QuickStats",
                    Name = "الإحصائيات السريعة",
                    Description = "عرض سريع للإحصائيات الأساسية",
                    Category = "إحصائيات",
                    IsEnabled = true,
                    MinWidth = 2,
                    MinHeight = 1,
                    MaxWidth = 4,
                    MaxHeight = 2,
                    Icon = "📊",
                    BackgroundColor = "#F8FAFC",
                    BorderColor = "#E5E7EB"
                },
                new CustomWidget
                {
                    Id = "RecentFiles",
                    Name = "الملفات الحديثة",
                    Description = "قائمة بآخر الملفات المفتوحة",
                    Category = "ملفات",
                    IsEnabled = true,
                    MinWidth = 2,
                    MinHeight = 1,
                    MaxWidth = 4,
                    MaxHeight = 3,
                    Icon = "📁",
                    BackgroundColor = "#F8FAFC",
                    BorderColor = "#E5E7EB"
                },
                new CustomWidget
                {
                    Id = "UpcomingAppointments",
                    Name = "المواعيد القادمة",
                    Description = "عرض المواعيد القادمة",
                    Category = "مواعيد",
                    IsEnabled = true,
                    MinWidth = 2,
                    MinHeight = 1,
                    MaxWidth = 4,
                    MaxHeight = 3,
                    Icon = "📅",
                    BackgroundColor = "#F8FAFC",
                    BorderColor = "#E5E7EB"
                },
                new CustomWidget
                {
                    Id = "TaskList",
                    Name = "قائمة المهام",
                    Description = "المهام المعلقة والمكتملة",
                    Category = "مهام",
                    IsEnabled = true,
                    MinWidth = 2,
                    MinHeight = 1,
                    MaxWidth = 4,
                    MaxHeight = 4,
                    Icon = "✅",
                    BackgroundColor = "#F8FAFC",
                    BorderColor = "#E5E7EB"
                },
                new CustomWidget
                {
                    Id = "Calendar",
                    Name = "التقويم",
                    Description = "عرض التقويم الشهري",
                    Category = "تقويم",
                    IsEnabled = true,
                    MinWidth = 3,
                    MinHeight = 2,
                    MaxWidth = 6,
                    MaxHeight = 4,
                    Icon = "🗓️",
                    BackgroundColor = "#F8FAFC",
                    BorderColor = "#E5E7EB"
                },
                new CustomWidget
                {
                    Id = "Charts",
                    Name = "الرسوم البيانية",
                    Description = "رسوم بيانية للإحصائيات",
                    Category = "إحصائيات",
                    IsEnabled = false,
                    MinWidth = 2,
                    MinHeight = 2,
                    MaxWidth = 4,
                    MaxHeight = 3,
                    Icon = "📈",
                    BackgroundColor = "#F8FAFC",
                    BorderColor = "#E5E7EB"
                },
                new CustomWidget
                {
                    Id = "Notifications",
                    Name = "الإشعارات",
                    Description = "آخر الإشعارات والتنبيهات",
                    Category = "إشعارات",
                    IsEnabled = false,
                    MinWidth = 2,
                    MinHeight = 1,
                    MaxWidth = 4,
                    MaxHeight = 3,
                    Icon = "🔔",
                    BackgroundColor = "#F8FAFC",
                    BorderColor = "#E5E7EB"
                }
            };

            foreach (var widget in widgets)
            {
                _widgets[widget.Id] = widget;
            }
        }

        public async Task ApplyLayoutAsync(string layoutId)
        {
            if (!_layouts.ContainsKey(layoutId))
                return;

            var oldLayout = _currentLayout;
            _currentLayout = _layouts[layoutId];

            LayoutChanged?.Invoke(this, new LayoutChangedEventArgs(oldLayout, _currentLayout));
            await SaveCurrentLayoutAsync();
        }

        public UILayout CreateCustomLayout(string name, string description)
        {
            var customLayout = new UILayout
            {
                Id = Guid.NewGuid().ToString(),
                Name = name,
                Description = description,
                IsDefault = false,
                IsCustom = true,
                Widgets = new List<WidgetPosition>()
            };

            _layouts[customLayout.Id] = customLayout;
            return customLayout;
        }

        public void UpdateWidgetPosition(string widgetId, int x, int y, int width, int height)
        {
            var widgetPosition = _currentLayout.Widgets.FirstOrDefault(w => w.WidgetId == widgetId);
            if (widgetPosition != null)
            {
                widgetPosition.X = x;
                widgetPosition.Y = y;
                widgetPosition.Width = width;
                widgetPosition.Height = height;

                WidgetChanged?.Invoke(this, new WidgetChangedEventArgs(widgetId, "PositionChanged"));
                _ = SaveCurrentLayoutAsync();
            }
        }

        public void AddWidgetToLayout(string widgetId, int x, int y, int width, int height)
        {
            if (!_widgets.ContainsKey(widgetId))
                return;

            var widget = _widgets[widgetId];
            var position = new WidgetPosition
            {
                WidgetId = widgetId,
                X = x,
                Y = y,
                Width = Math.Max(width, widget.MinWidth),
                Height = Math.Max(height, widget.MinHeight)
            };

            _currentLayout.Widgets.Add(position);
            widget.IsEnabled = true;

            WidgetChanged?.Invoke(this, new WidgetChangedEventArgs(widgetId, "Added"));
            _ = SaveCurrentLayoutAsync();
        }

        public void RemoveWidgetFromLayout(string widgetId)
        {
            var widgetPosition = _currentLayout.Widgets.FirstOrDefault(w => w.WidgetId == widgetId);
            if (widgetPosition != null)
            {
                _currentLayout.Widgets.Remove(widgetPosition);
                if (_widgets.ContainsKey(widgetId))
                {
                    _widgets[widgetId].IsEnabled = false;
                }

                WidgetChanged?.Invoke(this, new WidgetChangedEventArgs(widgetId, "Removed"));
                _ = SaveCurrentLayoutAsync();
            }
        }

        public void CustomizeWidget(string widgetId, string backgroundColor, string borderColor, string textColor)
        {
            if (_widgets.ContainsKey(widgetId))
            {
                var widget = _widgets[widgetId];
                widget.BackgroundColor = backgroundColor;
                widget.BorderColor = borderColor;
                widget.TextColor = textColor;

                WidgetChanged?.Invoke(this, new WidgetChangedEventArgs(widgetId, "StyleChanged"));
                _ = SaveWidgetCustomizationsAsync();
            }
        }

        public UILayout GetCurrentLayout() => _currentLayout;
        public IEnumerable<UILayout> GetAvailableLayouts() => _layouts.Values;
        public IEnumerable<CustomWidget> GetAvailableWidgets() => _widgets.Values;
        public CustomWidget GetWidget(string widgetId) => _widgets.TryGetValue(widgetId, out var widget) ? widget : null;

        public bool CanPlaceWidget(string widgetId, int x, int y, int width, int height)
        {
            if (!_widgets.ContainsKey(widgetId))
                return false;

            var widget = _widgets[widgetId];
            
            // فحص الحد الأدنى والأقصى للحجم
            if (width < widget.MinWidth || height < widget.MinHeight ||
                width > widget.MaxWidth || height > widget.MaxHeight)
                return false;

            // فحص التداخل مع الويدجت الأخرى
            foreach (var existingWidget in _currentLayout.Widgets)
            {
                if (existingWidget.WidgetId == widgetId)
                    continue;

                if (IsOverlapping(x, y, width, height, 
                                existingWidget.X, existingWidget.Y, 
                                existingWidget.Width, existingWidget.Height))
                    return false;
            }

            return true;
        }

        private bool IsOverlapping(int x1, int y1, int w1, int h1, int x2, int y2, int w2, int h2)
        {
            return !(x1 >= x2 + w2 || x2 >= x1 + w1 || y1 >= y2 + h2 || y2 >= y1 + h1);
        }

        private async Task SaveCurrentLayoutAsync()
        {
            try
            {
                var json = JsonSerializer.Serialize(_currentLayout, new JsonSerializerOptions { WriteIndented = true });
                var path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                                       "AvocatPro", "current-layout.json");
                
                Directory.CreateDirectory(Path.GetDirectoryName(path));
                await File.WriteAllTextAsync(path, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ التخطيط: {ex.Message}");
            }
        }

        private async Task SaveWidgetCustomizationsAsync()
        {
            try
            {
                var customizations = _widgets.Values
                    .Where(w => w.IsCustomized)
                    .ToDictionary(w => w.Id, w => new
                    {
                        w.BackgroundColor,
                        w.BorderColor,
                        w.TextColor,
                        w.IsEnabled
                    });

                var json = JsonSerializer.Serialize(customizations, new JsonSerializerOptions { WriteIndented = true });
                var path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                                       "AvocatPro", "widget-customizations.json");
                
                Directory.CreateDirectory(Path.GetDirectoryName(path));
                await File.WriteAllTextAsync(path, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ تخصيصات الويدجت: {ex.Message}");
            }
        }

        public async Task LoadUserCustomizationsAsync()
        {
            await LoadCurrentLayoutAsync();
            await LoadWidgetCustomizationsAsync();
        }

        private async Task LoadCurrentLayoutAsync()
        {
            try
            {
                var path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                                       "AvocatPro", "current-layout.json");
                
                if (File.Exists(path))
                {
                    var json = await File.ReadAllTextAsync(path);
                    var layout = JsonSerializer.Deserialize<UILayout>(json);
                    
                    if (layout != null)
                    {
                        _currentLayout = layout;
                        if (!_layouts.ContainsKey(layout.Id))
                        {
                            _layouts[layout.Id] = layout;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل التخطيط: {ex.Message}");
            }
        }

        private async Task LoadWidgetCustomizationsAsync()
        {
            try
            {
                var path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                                       "AvocatPro", "widget-customizations.json");
                
                if (File.Exists(path))
                {
                    var json = await File.ReadAllTextAsync(path);
                    var customizations = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(json);
                    
                    if (customizations != null)
                    {
                        foreach (var kvp in customizations)
                        {
                            if (_widgets.ContainsKey(kvp.Key))
                            {
                                var widget = _widgets[kvp.Key];
                                var custom = kvp.Value;
                                
                                if (custom.TryGetProperty("BackgroundColor", out var bgColor))
                                    widget.BackgroundColor = bgColor.GetString();
                                if (custom.TryGetProperty("BorderColor", out var borderColor))
                                    widget.BorderColor = borderColor.GetString();
                                if (custom.TryGetProperty("TextColor", out var textColor))
                                    widget.TextColor = textColor.GetString();
                                if (custom.TryGetProperty("IsEnabled", out var isEnabled))
                                    widget.IsEnabled = isEnabled.GetBoolean();
                                
                                widget.IsCustomized = true;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل تخصيصات الويدجت: {ex.Message}");
            }
        }
    }

    #region فئات البيانات

    public class UILayout
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public bool IsDefault { get; set; }
        public bool IsCustom { get; set; }
        public List<WidgetPosition> Widgets { get; set; } = new List<WidgetPosition>();
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime LastModified { get; set; } = DateTime.Now;
    }

    public class WidgetPosition
    {
        public string WidgetId { get; set; }
        public int X { get; set; }
        public int Y { get; set; }
        public int Width { get; set; }
        public int Height { get; set; }
        public bool IsVisible { get; set; } = true;
        public int ZIndex { get; set; } = 0;
    }

    public class CustomWidget
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Category { get; set; }
        public string Icon { get; set; }
        public bool IsEnabled { get; set; }
        public bool IsCustomized { get; set; }
        public int MinWidth { get; set; } = 1;
        public int MinHeight { get; set; } = 1;
        public int MaxWidth { get; set; } = 6;
        public int MaxHeight { get; set; } = 4;
        public string BackgroundColor { get; set; } = "#FFFFFF";
        public string BorderColor { get; set; } = "#E5E7EB";
        public string TextColor { get; set; } = "#1F2937";
        public double Opacity { get; set; } = 1.0;
        public string FontFamily { get; set; } = "Segoe UI";
        public int FontSize { get; set; } = 14;
        public bool HasShadow { get; set; } = true;
        public int CornerRadius { get; set; } = 8;
    }

    public class LayoutChangedEventArgs : EventArgs
    {
        public UILayout OldLayout { get; }
        public UILayout NewLayout { get; }

        public LayoutChangedEventArgs(UILayout oldLayout, UILayout newLayout)
        {
            OldLayout = oldLayout;
            NewLayout = newLayout;
        }
    }

    public class WidgetChangedEventArgs : EventArgs
    {
        public string WidgetId { get; }
        public string ChangeType { get; }

        public WidgetChangedEventArgs(string widgetId, string changeType)
        {
            WidgetId = widgetId;
            ChangeType = changeType;
        }
    }

    #endregion
}
