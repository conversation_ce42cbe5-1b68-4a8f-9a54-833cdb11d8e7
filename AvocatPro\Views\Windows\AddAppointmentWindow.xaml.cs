using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using AvocatPro.Models;

namespace AvocatPro.Views.Windows;

public partial class AddAppointmentWindow : Window
{
    public Appointment? NewAppointment { get; private set; }
    public bool SaveAndAddAnother { get; private set; }
    public bool SaveAndNotify { get; private set; }

    public AddAppointmentWindow()
    {
        InitializeComponent();
        InitializeForm();
    }

    private void InitializeForm()
    {
        LoadTimeComboBoxes();
        SetDefaultValues();
    }

    private void LoadTimeComboBoxes()
    {
        // تحميل الساعات (8 صباحاً إلى 10 مساءً)
        StartHourComboBox.Items.Clear();
        EndHourComboBox.Items.Clear();
        for (int hour = 8; hour <= 22; hour++)
        {
            var hourText = hour.ToString("00");
            StartHourComboBox.Items.Add(new ComboBoxItem { Content = hourText, Tag = hour });
            EndHourComboBox.Items.Add(new ComboBoxItem { Content = hourText, Tag = hour });
        }

        // تحميل الدقائق (كل 15 دقيقة)
        StartMinuteComboBox.Items.Clear();
        EndMinuteComboBox.Items.Clear();
        for (int minute = 0; minute < 60; minute += 15)
        {
            var minuteText = minute.ToString("00");
            StartMinuteComboBox.Items.Add(new ComboBoxItem { Content = minuteText, Tag = minute });
            EndMinuteComboBox.Items.Add(new ComboBoxItem { Content = minuteText, Tag = minute });
        }

        // تعيين القيم الافتراضية
        StartHourComboBox.SelectedIndex = 1; // 9 صباحاً
        StartMinuteComboBox.SelectedIndex = 0; // 00 دقيقة
        EndHourComboBox.SelectedIndex = 2; // 10 صباحاً
        EndMinuteComboBox.SelectedIndex = 0; // 00 دقيقة
    }

    private void SetDefaultValues()
    {
        AppointmentDatePicker.SelectedDate = DateTime.Now.AddDays(1); // غداً
        ClientComboBox.SelectedIndex = 0; // بدون موكل
        CaseComboBox.SelectedIndex = 0; // بدون قضية
    }

    private void IsRecurringCheckBox_Checked(object sender, RoutedEventArgs e)
    {
        RecurrencePatternComboBox.Visibility = Visibility.Visible;
        RecurrencePatternComboBox.SelectedIndex = 1; // أسبوعياً
    }

    private void IsRecurringCheckBox_Unchecked(object sender, RoutedEventArgs e)
    {
        RecurrencePatternComboBox.Visibility = Visibility.Collapsed;
        RecurrencePatternComboBox.SelectedIndex = -1;
    }

    private void SaveButton_Click(object sender, RoutedEventArgs e)
    {
        if (ValidateForm())
        {
            CreateAppointment();
            SaveAndAddAnother = false;
            SaveAndNotify = false;
            DialogResult = true;
            Close();
        }
    }

    private void SaveAndNewButton_Click(object sender, RoutedEventArgs e)
    {
        if (ValidateForm())
        {
            CreateAppointment();
            SaveAndAddAnother = true;
            SaveAndNotify = false;
            MessageBox.Show("تم حفظ الموعد بنجاح!", "نجح الحفظ", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
            
            // مسح النموذج لإضافة موعد جديد
            ClearForm();
            SetDefaultValues();
        }
    }

    private void SaveAndNotifyButton_Click(object sender, RoutedEventArgs e)
    {
        if (ValidateForm())
        {
            CreateAppointment();
            SaveAndAddAnother = false;
            SaveAndNotify = true;
            DialogResult = true;
            Close();
        }
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("هل تريد إلغاء العملية؟\nسيتم فقدان جميع البيانات المدخلة.", 
                                   "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Question);
        
        if (result == MessageBoxResult.Yes)
        {
            DialogResult = false;
            Close();
        }
    }

    private bool ValidateForm()
    {
        var errors = new System.Collections.Generic.List<string>();

        if (string.IsNullOrWhiteSpace(TitleTextBox.Text))
            errors.Add("يجب إدخال عنوان الموعد");

        if (!AppointmentDatePicker.SelectedDate.HasValue)
            errors.Add("يجب تحديد تاريخ الموعد");

        if (StartHourComboBox.SelectedItem == null || StartMinuteComboBox.SelectedItem == null)
            errors.Add("يجب تحديد وقت بداية الموعد");

        if (EndHourComboBox.SelectedItem == null || EndMinuteComboBox.SelectedItem == null)
            errors.Add("يجب تحديد وقت نهاية الموعد");

        // التحقق من أن وقت النهاية بعد وقت البداية
        if (StartHourComboBox.SelectedItem != null && StartMinuteComboBox.SelectedItem != null &&
            EndHourComboBox.SelectedItem != null && EndMinuteComboBox.SelectedItem != null)
        {
            var startHour = (int)((ComboBoxItem)StartHourComboBox.SelectedItem).Tag;
            var startMinute = (int)((ComboBoxItem)StartMinuteComboBox.SelectedItem).Tag;
            var endHour = (int)((ComboBoxItem)EndHourComboBox.SelectedItem).Tag;
            var endMinute = (int)((ComboBoxItem)EndMinuteComboBox.SelectedItem).Tag;

            var startTime = new TimeSpan(startHour, startMinute, 0);
            var endTime = new TimeSpan(endHour, endMinute, 0);

            if (endTime <= startTime)
                errors.Add("وقت النهاية يجب أن يكون بعد وقت البداية");
        }

        // التحقق من صحة التكلفة
        if (!string.IsNullOrWhiteSpace(CostTextBox.Text))
        {
            if (!decimal.TryParse(CostTextBox.Text, out var cost) || cost < 0)
                errors.Add("التكلفة يجب أن تكون رقماً صحيحاً أكبر من أو يساوي صفر");
        }

        // التحقق من صحة البريد الإلكتروني
        if (!string.IsNullOrWhiteSpace(ContactEmailTextBox.Text))
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(ContactEmailTextBox.Text);
                if (addr.Address != ContactEmailTextBox.Text)
                    errors.Add("البريد الإلكتروني غير صحيح");
            }
            catch
            {
                errors.Add("البريد الإلكتروني غير صحيح");
            }
        }

        if (errors.Count > 0)
        {
            MessageBox.Show($"يرجى تصحيح الأخطاء التالية:\n\n{string.Join("\n", errors)}", 
                           "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
            return false;
        }

        return true;
    }

    private void CreateAppointment()
    {
        var appointmentDate = AppointmentDatePicker.SelectedDate!.Value;
        var startHour = (int)((ComboBoxItem)StartHourComboBox.SelectedItem).Tag;
        var startMinute = (int)((ComboBoxItem)StartMinuteComboBox.SelectedItem).Tag;
        var endHour = (int)((ComboBoxItem)EndHourComboBox.SelectedItem).Tag;
        var endMinute = (int)((ComboBoxItem)EndMinuteComboBox.SelectedItem).Tag;

        var startTime = new TimeSpan(startHour, startMinute, 0);
        var endTime = new TimeSpan(endHour, endMinute, 0);

        // تحديد الأولوية
        var priority = AppointmentPriority.Medium;
        if (LowPriorityRadio.IsChecked == true) priority = AppointmentPriority.Low;
        else if (MediumPriorityRadio.IsChecked == true) priority = AppointmentPriority.Medium;
        else if (HighPriorityRadio.IsChecked == true) priority = AppointmentPriority.High;
        else if (UrgentPriorityRadio.IsChecked == true) priority = AppointmentPriority.Urgent;

        NewAppointment = new Appointment
        {
            Title = TitleTextBox.Text.Trim(),
            Description = DescriptionTextBox.Text.Trim(),
            AppointmentDate = appointmentDate,
            StartTime = startTime,
            EndTime = endTime,
            Type = Enum.Parse<AppointmentType>((string)((ComboBoxItem)TypeComboBox.SelectedItem).Tag),
            Status = Enum.Parse<AppointmentStatus>((string)((ComboBoxItem)StatusComboBox.SelectedItem).Tag),
            Priority = priority,
            ClientId = GetSelectedId(ClientComboBox),
            CaseId = GetSelectedId(CaseComboBox),
            Location = LocationTextBox.Text.Trim(),
            RequiredAttendees = RequiredAttendeesTextBox.Text.Trim(),
            Cost = decimal.TryParse(CostTextBox.Text, out var cost) ? cost : null,
            IsPaid = IsPaidCheckBox.IsChecked == true,
            NotificationMinutes = int.Parse((string)((ComboBoxItem)NotificationComboBox.SelectedItem).Tag),
            AppointmentNotes = NotesTextBox.Text.Trim(),
            ContactPhone = ContactPhoneTextBox.Text.Trim(),
            ContactEmail = ContactEmailTextBox.Text.Trim(),
            IsRecurring = IsRecurringCheckBox.IsChecked == true,
            RecurrencePattern = IsRecurringCheckBox.IsChecked == true && RecurrencePatternComboBox.SelectedItem != null ?
                               (string)((ComboBoxItem)RecurrencePatternComboBox.SelectedItem).Tag : null,
            CreatedByUserId = 1, // في التطبيق الحقيقي من المستخدم الحالي
            CreatedAt = DateTime.Now,
            UpdatedAt = DateTime.Now
        };
    }

    private int? GetSelectedId(ComboBox comboBox)
    {
        if (comboBox.SelectedItem is ComboBoxItem item && item.Tag is string tag)
        {
            if (int.TryParse(tag, out var id) && id > 0)
                return id;
        }
        return null;
    }

    private void ClearForm()
    {
        TitleTextBox.Clear();
        DescriptionTextBox.Clear();
        LocationTextBox.Clear();
        RequiredAttendeesTextBox.Clear();
        CostTextBox.Clear();
        NotesTextBox.Clear();
        ContactPhoneTextBox.Clear();
        ContactEmailTextBox.Clear();
        
        TypeComboBox.SelectedIndex = 0;
        StatusComboBox.SelectedIndex = 0;
        ClientComboBox.SelectedIndex = 0;
        CaseComboBox.SelectedIndex = 0;
        NotificationComboBox.SelectedIndex = 3; // 30 دقيقة
        
        MediumPriorityRadio.IsChecked = true;
        IsPaidCheckBox.IsChecked = false;
        IsRecurringCheckBox.IsChecked = false;
        RecurrencePatternComboBox.Visibility = Visibility.Collapsed;
        
        AppointmentDatePicker.SelectedDate = DateTime.Now.AddDays(1);
    }
}
