using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using AvocatPro.Models;

namespace AvocatPro.Views.Windows
{
    /// <summary>
    /// نافذة تقويم الجلسات
    /// </summary>
    public partial class SessionsCalendarWindow : Window
    {
        #region Fields

        private List<AdvancedSessionModel> _sessions;
        private DateTime _currentMonth;
        private DateTime? _selectedDate;
        private readonly string[] _monthNames = {
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        };

        #endregion

        #region Constructor

        public SessionsCalendarWindow(List<AdvancedSessionModel> sessions)
        {
            InitializeComponent();
            
            _sessions = sessions ?? new List<AdvancedSessionModel>();
            _currentMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            
            UpdateCalendar();
        }

        #endregion

        #region Calendar Methods

        private void UpdateCalendar()
        {
            // تحديث عنوان الشهر
            CurrentMonthLabel.Text = $"{_monthNames[_currentMonth.Month - 1]} {_currentMonth.Year}";
            
            // مسح التقويم الحالي
            CalendarGrid.Children.Clear();
            
            // الحصول على أول يوم في الشهر وآخر يوم
            var firstDayOfMonth = _currentMonth;
            var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);
            
            // الحصول على يوم الأسبوع لأول يوم في الشهر (الأحد = 0)
            var startDayOfWeek = (int)firstDayOfMonth.DayOfWeek;
            
            // إضافة الأيام من الشهر السابق
            var previousMonth = firstDayOfMonth.AddMonths(-1);
            var daysInPreviousMonth = DateTime.DaysInMonth(previousMonth.Year, previousMonth.Month);
            
            for (int i = startDayOfWeek - 1; i >= 0; i--)
            {
                var day = daysInPreviousMonth - i;
                var date = new DateTime(previousMonth.Year, previousMonth.Month, day);
                CreateDayButton(date, false);
            }
            
            // إضافة أيام الشهر الحالي
            for (int day = 1; day <= DateTime.DaysInMonth(_currentMonth.Year, _currentMonth.Month); day++)
            {
                var date = new DateTime(_currentMonth.Year, _currentMonth.Month, day);
                CreateDayButton(date, true);
            }
            
            // إضافة الأيام من الشهر التالي لملء الشبكة
            var totalCells = CalendarGrid.Children.Count;
            var remainingCells = 42 - totalCells; // 6 أسابيع × 7 أيام
            var nextMonth = firstDayOfMonth.AddMonths(1);
            
            for (int day = 1; day <= remainingCells; day++)
            {
                var date = new DateTime(nextMonth.Year, nextMonth.Month, day);
                CreateDayButton(date, false);
            }
        }

        private void CreateDayButton(DateTime date, bool isCurrentMonth)
        {
            var button = new Button
            {
                Style = (Style)FindResource("CalendarDayStyle"),
                Tag = date
            };

            // إنشاء محتوى الزر
            var stackPanel = new StackPanel();
            
            // رقم اليوم
            var dayNumber = new TextBlock
            {
                Text = date.Day.ToString(),
                FontSize = 14,
                FontWeight = FontWeights.SemiBold,
                HorizontalAlignment = HorizontalAlignment.Right,
                Margin = new Thickness(0, 0, 4, 2)
            };

            // تلوين النص حسب نوع اليوم
            if (!isCurrentMonth)
            {
                dayNumber.Foreground = Brushes.LightGray;
            }
            else if (date.Date == DateTime.Now.Date)
            {
                dayNumber.Foreground = Brushes.White;
                button.Background = new SolidColorBrush(Color.FromRgb(99, 102, 241)); // #6366F1
            }
            else
            {
                dayNumber.Foreground = new SolidColorBrush(Color.FromRgb(55, 65, 81)); // #374151
            }

            stackPanel.Children.Add(dayNumber);

            // إضافة مؤشرات الجلسات
            var sessionsForDay = _sessions.Where(s => s.SessionDate.Date == date.Date).ToList();
            if (sessionsForDay.Any())
            {
                var sessionsPanel = new WrapPanel
                {
                    Orientation = Orientation.Horizontal,
                    HorizontalAlignment = HorizontalAlignment.Left,
                    Margin = new Thickness(2, 0, 2, 2)
                };

                foreach (var session in sessionsForDay.Take(3)) // عرض أول 3 جلسات فقط
                {
                    var sessionIndicator = new Border
                    {
                        Width = 6,
                        Height = 6,
                        CornerRadius = new CornerRadius(3),
                        Margin = new Thickness(1),
                        Background = new SolidColorBrush(GetStatusColor(session.Status))
                    };
                    sessionsPanel.Children.Add(sessionIndicator);
                }

                // إضافة مؤشر للجلسات الإضافية
                if (sessionsForDay.Count > 3)
                {
                    var moreIndicator = new TextBlock
                    {
                        Text = $"+{sessionsForDay.Count - 3}",
                        FontSize = 8,
                        Foreground = Brushes.Gray,
                        Margin = new Thickness(2, 0, 0, 0)
                    };
                    sessionsPanel.Children.Add(moreIndicator);
                }

                stackPanel.Children.Add(sessionsPanel);
            }

            button.Content = stackPanel;
            button.Click += DayButton_Click;

            // تحديد موقع الزر في الشبكة
            var totalDays = CalendarGrid.Children.Count;
            var row = totalDays / 7;
            var column = totalDays % 7;

            Grid.SetRow(button, row);
            Grid.SetColumn(button, column);

            CalendarGrid.Children.Add(button);
        }

        private Color GetStatusColor(string status)
        {
            return status switch
            {
                "مجدولة" => Color.FromRgb(99, 102, 241),   // #6366F1
                "جارية" => Color.FromRgb(245, 158, 11),    // #F59E0B
                "مكتملة" => Color.FromRgb(16, 185, 129),   // #10B981
                "مؤجلة" => Color.FromRgb(139, 92, 246),    // #8B5CF6
                "ملغية" => Color.FromRgb(239, 68, 68),     // #EF4444
                _ => Color.FromRgb(107, 114, 128)          // #6B7280
            };
        }

        private void UpdateSelectedDayDetails(DateTime date)
        {
            _selectedDate = date;
            
            // تحديث تسمية التاريخ المحدد
            var culture = new CultureInfo("ar-SA");
            SelectedDateLabel.Text = $"جلسات يوم {date.ToString("dddd، dd MMMM yyyy", culture)}";
            
            // الحصول على جلسات اليوم
            var sessionsForDay = _sessions.Where(s => s.SessionDate.Date == date.Date)
                                         .OrderBy(s => s.SessionTime)
                                         .ToList();
            
            // تحديث عدد الجلسات
            SessionsCountLabel.Text = sessionsForDay.Count == 0 
                ? "لا توجد جلسات في هذا اليوم"
                : $"عدد الجلسات: {sessionsForDay.Count}";
            
            // مسح الجلسات السابقة
            DaySessionsPanel.Children.Clear();
            
            // إضافة بطاقات الجلسات
            foreach (var session in sessionsForDay)
            {
                CreateSessionCard(session);
            }
        }

        private void CreateSessionCard(AdvancedSessionModel session)
        {
            var card = new Border
            {
                Background = Brushes.White,
                BorderBrush = new SolidColorBrush(GetStatusColor(session.Status)),
                BorderThickness = new Thickness(0, 3, 0, 0),
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(12),
                Margin = new Thickness(0, 0, 10, 0),
                Width = 200,
                Cursor = System.Windows.Input.Cursors.Hand
            };

            var stackPanel = new StackPanel();

            // وقت الجلسة
            var timeText = new TextBlock
            {
                Text = session.SessionTimeText,
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(GetStatusColor(session.Status)),
                Margin = new Thickness(0, 0, 0, 5)
            };
            stackPanel.Children.Add(timeText);

            // رقم الجلسة
            var sessionNumberText = new TextBlock
            {
                Text = $"جلسة رقم: {session.SessionNumber}",
                FontSize = 12,
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush(Color.FromRgb(55, 65, 81)),
                Margin = new Thickness(0, 0, 0, 3)
            };
            stackPanel.Children.Add(sessionNumberText);

            // الموكل
            var clientText = new TextBlock
            {
                Text = session.Client,
                FontSize = 11,
                Foreground = new SolidColorBrush(Color.FromRgb(107, 114, 128)),
                TextTrimming = TextTrimming.CharacterEllipsis,
                Margin = new Thickness(0, 0, 0, 3)
            };
            stackPanel.Children.Add(clientText);

            // نوع الجلسة
            var sessionTypeText = new TextBlock
            {
                Text = session.SessionType,
                FontSize = 10,
                Foreground = new SolidColorBrush(Color.FromRgb(107, 114, 128)),
                TextTrimming = TextTrimming.CharacterEllipsis,
                Margin = new Thickness(0, 0, 0, 5)
            };
            stackPanel.Children.Add(sessionTypeText);

            // حالة الجلسة
            var statusBorder = new Border
            {
                Background = new SolidColorBrush(GetStatusColor(session.Status)),
                CornerRadius = new CornerRadius(10),
                Padding = new Thickness(8, 2, 8, 2)
            };

            var statusText = new TextBlock
            {
                Text = session.Status,
                FontSize = 9,
                Foreground = Brushes.White,
                FontWeight = FontWeights.SemiBold
            };

            statusBorder.Child = statusText;
            stackPanel.Children.Add(statusBorder);

            card.Child = stackPanel;

            // إضافة حدث النقر
            card.MouseLeftButtonUp += (sender, e) =>
            {
                var detailsWindow = new SessionDetailsWindow(session);
                detailsWindow.ShowDialog();
            };

            DaySessionsPanel.Children.Add(card);
        }

        #endregion

        #region Event Handlers

        private void DayButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is DateTime date)
            {
                UpdateSelectedDayDetails(date);
            }
        }

        private void PreviousMonthButton_Click(object sender, RoutedEventArgs e)
        {
            _currentMonth = _currentMonth.AddMonths(-1);
            UpdateCalendar();
        }

        private void NextMonthButton_Click(object sender, RoutedEventArgs e)
        {
            _currentMonth = _currentMonth.AddMonths(1);
            UpdateCalendar();
        }

        private void TodayButton_Click(object sender, RoutedEventArgs e)
        {
            _currentMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            UpdateCalendar();
            UpdateSelectedDayDetails(DateTime.Now.Date);
        }

        private void AddSessionButton_Click(object sender, RoutedEventArgs e)
        {
            var addWindow = new AdvancedAddSessionWindow();
            if (_selectedDate.HasValue)
            {
                // تعيين التاريخ المحدد كتاريخ افتراضي للجلسة الجديدة
                // يمكن تمرير التاريخ للنافذة هنا
            }
            
            if (addWindow.ShowDialog() == true)
            {
                _sessions.Add(addWindow.NewSession);
                UpdateCalendar();
                
                if (_selectedDate.HasValue)
                {
                    UpdateSelectedDayDetails(_selectedDate.Value);
                }
            }
        }

        #endregion
    }
}
