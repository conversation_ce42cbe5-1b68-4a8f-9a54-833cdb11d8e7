﻿#pragma checksum "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "DB2C51F7DBDEA2844F6BF41337B2039E2DC36F2A"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Pages {
    
    
    /// <summary>
    /// AdvancedUISettingsPage
    /// </summary>
    public partial class AdvancedUISettingsPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 45 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetBtn;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveBtn;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ThemeModeComboBox;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PredefinedThemeComboBox;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border PrimaryColorPreview;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PrimaryColorTextBox;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border SecondaryColorPreview;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SecondaryColorTextBox;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border AccentColorPreview;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AccentColorTextBox;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border SuccessColorPreview;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border WarningColorPreview;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ErrorColorPreview;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border InfoColorPreview;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableAnimationsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableTransitionsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 221 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableHoverEffectsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 228 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TransitionTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 239 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestTransitionBtn;
        
        #line default
        #line hidden
        
        
        #line 260 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewAllShortcutsBtn;
        
        #line default
        #line hidden
        
        
        #line 266 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableShortcutsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 271 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ShortcutsPanel;
        
        #line default
        #line hidden
        
        
        #line 280 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border LoadingOverlay;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/pages/advanceduisettingspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ResetBtn = ((System.Windows.Controls.Button)(target));
            
            #line 46 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.ResetBtn.Click += new System.Windows.RoutedEventHandler(this.Reset_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SaveBtn = ((System.Windows.Controls.Button)(target));
            
            #line 53 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.SaveBtn.Click += new System.Windows.RoutedEventHandler(this.Save_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ThemeModeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 88 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.ThemeModeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ThemeMode_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.PredefinedThemeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 100 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.PredefinedThemeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PredefinedTheme_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.PrimaryColorPreview = ((System.Windows.Controls.Border)(target));
            
            #line 131 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.PrimaryColorPreview.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.ColorPreview_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.PrimaryColorTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 134 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.PrimaryColorTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.SecondaryColorPreview = ((System.Windows.Controls.Border)(target));
            
            #line 142 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.SecondaryColorPreview.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.ColorPreview_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.SecondaryColorTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 145 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.SecondaryColorTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.AccentColorPreview = ((System.Windows.Controls.Border)(target));
            
            #line 153 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.AccentColorPreview.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.ColorPreview_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.AccentColorTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 156 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.AccentColorTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.SuccessColorPreview = ((System.Windows.Controls.Border)(target));
            
            #line 167 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.SuccessColorPreview.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.ColorPreview_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.WarningColorPreview = ((System.Windows.Controls.Border)(target));
            
            #line 175 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.WarningColorPreview.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.ColorPreview_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.ErrorColorPreview = ((System.Windows.Controls.Border)(target));
            
            #line 183 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.ErrorColorPreview.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.ColorPreview_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.InfoColorPreview = ((System.Windows.Controls.Border)(target));
            
            #line 191 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.InfoColorPreview.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.ColorPreview_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.EnableAnimationsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 218 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.EnableAnimationsCheckBox.Checked += new System.Windows.RoutedEventHandler(this.AnimationSettings_Changed);
            
            #line default
            #line hidden
            
            #line 218 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.EnableAnimationsCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.AnimationSettings_Changed);
            
            #line default
            #line hidden
            return;
            case 16:
            this.EnableTransitionsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 220 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.EnableTransitionsCheckBox.Checked += new System.Windows.RoutedEventHandler(this.AnimationSettings_Changed);
            
            #line default
            #line hidden
            
            #line 220 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.EnableTransitionsCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.AnimationSettings_Changed);
            
            #line default
            #line hidden
            return;
            case 17:
            this.EnableHoverEffectsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 222 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.EnableHoverEffectsCheckBox.Checked += new System.Windows.RoutedEventHandler(this.AnimationSettings_Changed);
            
            #line default
            #line hidden
            
            #line 222 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.EnableHoverEffectsCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.AnimationSettings_Changed);
            
            #line default
            #line hidden
            return;
            case 18:
            this.TransitionTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 229 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.TransitionTypeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.TransitionType_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 19:
            this.TestTransitionBtn = ((System.Windows.Controls.Button)(target));
            
            #line 240 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.TestTransitionBtn.Click += new System.Windows.RoutedEventHandler(this.TestTransition_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.ViewAllShortcutsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 261 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.ViewAllShortcutsBtn.Click += new System.Windows.RoutedEventHandler(this.ViewAllShortcuts_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.EnableShortcutsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 267 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.EnableShortcutsCheckBox.Checked += new System.Windows.RoutedEventHandler(this.ShortcutSettings_Changed);
            
            #line default
            #line hidden
            
            #line 267 "..\..\..\..\..\Views\Pages\AdvancedUISettingsPage.xaml"
            this.EnableShortcutsCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.ShortcutSettings_Changed);
            
            #line default
            #line hidden
            return;
            case 22:
            this.ShortcutsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 23:
            this.LoadingOverlay = ((System.Windows.Controls.Border)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

