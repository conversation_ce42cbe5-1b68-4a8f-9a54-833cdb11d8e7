using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using AvocatPro.Models;

namespace AvocatPro.Views.Pages
{
    /// <summary>
    /// صفحة إدارة الملفات البسيطة
    /// </summary>
    public partial class SimpleFilesPage : Page, INotifyPropertyChanged
    {
        #region Private Fields

        private ObservableCollection<SimpleFileModel> _allFiles;
        private ObservableCollection<SimpleFileModel> _filteredFiles;
        private bool _isSearchFocused = false;

        #endregion

        #region Properties

        public ObservableCollection<SimpleFileModel> AllFiles
        {
            get => _allFiles;
            set { _allFiles = value; OnPropertyChanged(nameof(AllFiles)); }
        }

        public ObservableCollection<SimpleFileModel> FilteredFiles
        {
            get => _filteredFiles;
            set { _filteredFiles = value; OnPropertyChanged(nameof(FilteredFiles)); }
        }

        #endregion

        #region Constructor

        public SimpleFilesPage()
        {
            try
            {
                InitializeComponent();
                DataContext = this;

                // تهيئة المجموعات
                AllFiles = new ObservableCollection<SimpleFileModel>();
                FilteredFiles = new ObservableCollection<SimpleFileModel>();

                // تحميل البيانات التجريبية
                LoadSampleData();

                // ربط البيانات بالجدول
                FilesDataGrid.ItemsSource = FilteredFiles;

                // تحديث الإحصائيات
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة صفحة الملفات: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region Data Loading

        /// <summary>
        /// تحميل البيانات التجريبية
        /// </summary>
        private void LoadSampleData()
        {
            var sampleFiles = new[]
            {
                new SimpleFileModel
                {
                    FileNumber = "2024/001",
                    Client = "أحمد محمد علي",
                    CaseType = "مدني",
                    Court = "المحكمة الابتدائية بالرباط",
                    Subject = "نزاع عقاري",
                    Status = "نشط",
                    CreatedDate = DateTime.Now.AddDays(-30)
                },
                new SimpleFileModel
                {
                    FileNumber = "2024/002",
                    Client = "فاطمة الزهراء",
                    CaseType = "تجاري",
                    Court = "المحكمة التجارية بالدار البيضاء",
                    Subject = "نزاع تجاري",
                    Status = "في الجلسات",
                    CreatedDate = DateTime.Now.AddDays(-25)
                },
                new SimpleFileModel
                {
                    FileNumber = "2024/003",
                    Client = "محمد الحسن",
                    CaseType = "عائلي",
                    Court = "محكمة الأسرة بفاس",
                    Subject = "قضية طلاق",
                    Status = "مؤرشف",
                    CreatedDate = DateTime.Now.AddDays(-60)
                },
                new SimpleFileModel
                {
                    FileNumber = "2024/004",
                    Client = "عائشة بنت محمد",
                    CaseType = "جنائي",
                    Court = "المحكمة الجنائية بمراكش",
                    Subject = "قضية احتيال",
                    Status = "نشط",
                    CreatedDate = DateTime.Now.AddDays(-15)
                },
                new SimpleFileModel
                {
                    FileNumber = "2024/005",
                    Client = "يوسف الإدريسي",
                    CaseType = "إداري",
                    Court = "المحكمة الإدارية بالرباط",
                    Subject = "طعن في قرار إداري",
                    Status = "في الجلسات",
                    CreatedDate = DateTime.Now.AddDays(-10)
                }
            };

            foreach (var file in sampleFiles)
            {
                AllFiles.Add(file);
                FilteredFiles.Add(file);
            }
        }

        #endregion

        #region Statistics

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        private void UpdateStatistics()
        {
            if (AllFiles == null) return;

            TotalFilesCount.Text = AllFiles.Count.ToString();
            ActiveFilesCount.Text = AllFiles.Count(f => f.Status == "نشط").ToString();
            InSessionsCount.Text = AllFiles.Count(f => f.Status == "في الجلسات").ToString();
            ArchivedFilesCount.Text = AllFiles.Count(f => f.Status == "مؤرشف").ToString();
        }

        #endregion

        #region Search and Filter

        /// <summary>
        /// تطبيق المرشحات
        /// </summary>
        private void ApplyFilters()
        {
            if (AllFiles == null || FilteredFiles == null) return;

            var filtered = AllFiles.AsEnumerable();

            // تصفية البحث النصي
            if (_isSearchFocused && !string.IsNullOrWhiteSpace(SearchTextBox.Text) && 
                SearchTextBox.Text != "البحث في الملفات...")
            {
                var searchText = SearchTextBox.Text.ToLower();
                filtered = filtered.Where(f => 
                    f.FileNumber.ToLower().Contains(searchText) ||
                    f.Client.ToLower().Contains(searchText) ||
                    f.CaseType.ToLower().Contains(searchText) ||
                    f.Court.ToLower().Contains(searchText) ||
                    f.Subject.ToLower().Contains(searchText));
            }

            // تصفية الحالة
            if (StatusFilterComboBox.SelectedItem is ComboBoxItem statusItem && 
                statusItem.Content.ToString() != "جميع الحالات")
            {
                filtered = filtered.Where(f => f.Status == statusItem.Content.ToString());
            }

            // تصفية نوع القضية
            if (CaseTypeFilterComboBox.SelectedItem is ComboBoxItem caseTypeItem && 
                caseTypeItem.Content.ToString() != "جميع أنواع القضايا")
            {
                filtered = filtered.Where(f => f.CaseType == caseTypeItem.Content.ToString());
            }

            FilteredFiles.Clear();
            foreach (var file in filtered)
            {
                FilteredFiles.Add(file);
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// البحث في النص
        /// </summary>
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (!_isSearchFocused) return;
            ApplyFilters();
        }

        /// <summary>
        /// التركيز على مربع البحث
        /// </summary>
        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            _isSearchFocused = true;
            if (SearchTextBox.Text == "البحث في الملفات...")
            {
                SearchTextBox.Text = "";
                SearchTextBox.Foreground = System.Windows.Media.Brushes.Black;
            }
        }

        /// <summary>
        /// فقدان التركيز من مربع البحث
        /// </summary>
        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                _isSearchFocused = false;
                SearchTextBox.Text = "البحث في الملفات...";
                SearchTextBox.Foreground = System.Windows.Media.Brushes.Gray;
            }
        }

        /// <summary>
        /// تصفية حسب الحالة
        /// </summary>
        private void StatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        /// <summary>
        /// تصفية حسب نوع القضية
        /// </summary>
        private void CaseTypeFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        /// <summary>
        /// إضافة ملف جديد
        /// </summary>
        private void AddFile_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم إضافة نافذة إضافة ملف جديد قريباً", "قيد التطوير", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// تصدير البيانات
        /// </summary>
        private void Export_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم إضافة وظيفة التصدير قريباً", "قيد التطوير", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// طباعة القائمة
        /// </summary>
        private void Print_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم إضافة وظيفة الطباعة قريباً", "قيد التطوير", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    /// <summary>
    /// نموذج ملف بسيط
    /// </summary>
    public class SimpleFileModel : INotifyPropertyChanged
    {
        private string _fileNumber;
        private string _client;
        private string _caseType;
        private string _court;
        private string _subject;
        private string _status;
        private DateTime _createdDate;

        public string FileNumber
        {
            get => _fileNumber;
            set { _fileNumber = value; OnPropertyChanged(nameof(FileNumber)); }
        }

        public string Client
        {
            get => _client;
            set { _client = value; OnPropertyChanged(nameof(Client)); }
        }

        public string CaseType
        {
            get => _caseType;
            set { _caseType = value; OnPropertyChanged(nameof(CaseType)); }
        }

        public string Court
        {
            get => _court;
            set { _court = value; OnPropertyChanged(nameof(Court)); }
        }

        public string Subject
        {
            get => _subject;
            set { _subject = value; OnPropertyChanged(nameof(Subject)); }
        }

        public string Status
        {
            get => _status;
            set { _status = value; OnPropertyChanged(nameof(Status)); }
        }

        public DateTime CreatedDate
        {
            get => _createdDate;
            set { _createdDate = value; OnPropertyChanged(nameof(CreatedDate)); }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
