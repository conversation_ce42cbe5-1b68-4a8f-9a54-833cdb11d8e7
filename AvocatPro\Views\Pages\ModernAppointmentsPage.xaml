<Page x:Class="AvocatPro.Views.Pages.ModernAppointmentsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="إدارة المواعيد"
      Background="#F8FAFC"
      FlowDirection="RightToLeft">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- عنوان الصفحة -->
        <Border Grid.Row="0" Background="White" Padding="25" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="📅" FontSize="28" Margin="0,0,15,0"/>
                    <StackPanel>
                        <TextBlock Text="إدارة المواعيد" FontSize="24" FontWeight="Bold" 
                                   Foreground="#1F2937" Margin="0,0,0,5"/>
                        <TextBlock Text="تنظيم وإدارة المواعيد مع الموكلين والاجتماعات" FontSize="14" 
                                   Foreground="#6B7280"/>
                    </StackPanel>
                </StackPanel>

                <Button Grid.Column="1" Background="#6366F1" Foreground="White" 
                        BorderThickness="0" Padding="20,12" FontSize="14" FontWeight="SemiBold"
                        Cursor="Hand" Click="AddAppointment_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="➕" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="إضافة موعد جديد"/>
                    </StackPanel>
                </Button>
            </Grid>
        </Border>

        <!-- محتوى الصفحة -->
        <Border Grid.Row="1" Background="White" Padding="40">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <TextBlock Text="📅" FontSize="80" HorizontalAlignment="Center" 
                           Foreground="#E5E7EB" Margin="0,0,0,20"/>
                <TextBlock Text="إدارة المواعيد" FontSize="28" FontWeight="Bold" 
                           Foreground="#1F2937" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                <TextBlock Text="هذا القسم قيد التطوير" FontSize="16" 
                           Foreground="#6B7280" HorizontalAlignment="Center" Margin="0,0,0,20"/>
                <TextBlock Text="سيتم إضافة المميزات التالية قريباً:" FontSize="14" 
                           Foreground="#9CA3AF" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                
                <StackPanel Margin="0,10,0,0">
                    <TextBlock Text="• تقويم تفاعلي لعرض المواعيد" FontSize="14" 
                               Foreground="#6B7280" Margin="0,5,0,0"/>
                    <TextBlock Text="• إضافة وتعديل المواعيد" FontSize="14" 
                               Foreground="#6B7280" Margin="0,5,0,0"/>
                    <TextBlock Text="• تذكيرات تلقائية" FontSize="14" 
                               Foreground="#6B7280" Margin="0,5,0,0"/>
                    <TextBlock Text="• ربط المواعيد بالموكلين والملفات" FontSize="14" 
                               Foreground="#6B7280" Margin="0,5,0,0"/>
                    <TextBlock Text="• إشعارات البريد الإلكتروني والرسائل" FontSize="14" 
                               Foreground="#6B7280" Margin="0,5,0,0"/>
                </StackPanel>

                <Button Background="#6366F1" Foreground="White" BorderThickness="0" 
                        Padding="20,12" FontSize="14" FontWeight="SemiBold" Margin="0,30,0,0"
                        Cursor="Hand" Click="ComingSoon_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🚀" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="قريباً جداً"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Page>
