﻿using System;
using System.Windows;
using System.Windows.Threading;
using AvocatPro.Views.Pages;
using AvocatPro.Models;
using AvocatPro.Services;

namespace AvocatPro;

/// <summary>
/// النافذة الرئيسية المتطورة لتطبيق AvocatPro
/// </summary>
public partial class MainWindow : Window
{
    private DispatcherTimer _timeTimer;
    private readonly User _currentUser;
    private readonly ThemeService _themeService;
    private readonly AnimationService _animationService;

    public MainWindow(User currentUser)
    {
        InitializeComponent();
        _currentUser = currentUser;
        _themeService = new ThemeService();
        _animationService = new AnimationService();

        InitializeApplication();
    }

    private void InitializeApplication()
    {
        // تهيئة مؤقت الوقت
        _timeTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(1)
        };
        _timeTimer.Tick += TimeTimer_Tick;
        _timeTimer.Start();

        // تحديث الوقت فوراً
        UpdateTime();

        // تحديث معلومات المستخدم
        UpdateUserInfo();

        // تطبيق الثيم الحالي
        ApplyCurrentTheme();

        // تحميل الصفحة الرئيسية
        _ = LoadDashboard();
    }

    private void TimeTimer_Tick(object? sender, EventArgs e)
    {
        UpdateTime();
    }

    private void UpdateTime()
    {
        TimeText.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
    }

    private void UpdateUserInfo()
    {
        // تحديث معلومات المستخدم في الواجهة
        if (_currentUser != null)
        {
            // يمكن إضافة عرض اسم المستخدم هنا إذا كان هناك عنصر مخصص لذلك
            PageTitleText.Text = $"مرحباً {_currentUser.FullName}";
            PageDescriptionText.Text = "لوحة التحكم الشخصية - إدارة مكتبك القانوني بكفاءة";
        }
    }

    private void ApplyCurrentTheme()
    {
        try
        {
            // تطبيق الثيم الحالي
            _ = _themeService.GetCurrentTheme();
            // يمكن إضافة المزيد من تطبيق الثيمات هنا
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق الثيم: {ex.Message}");
        }
    }

    private async Task LoadDashboard()
    {
        try
        {
            // تطبيق أنيميشن تحميل
            await _animationService.AnimateElementEntry(MainFrame);

            // تحميل لوحة التحكم التفاعلية
            var dashboardPage = new InteractiveDashboardPage(_currentUser);
            MainFrame.Navigate(dashboardPage);

            // تحديث عنوان الصفحة
            PageTitleText.Text = "لوحة التحكم الرئيسية";
            PageDescriptionText.Text = "نظرة شاملة على أداء مكتبك القانوني";
            StatusText.Text = "تم تحميل لوحة التحكم الرئيسية بنجاح";
        }
        catch (Exception ex)
        {
            StatusText.Text = $"خطأ في تحميل لوحة التحكم: {ex.Message}";
        }
    }

    // دالة مساعدة للتنقل مع الأنيميشن
    private async Task NavigateToPage<T>(Func<T> pageFactory, string title, string description) where T : class
    {
        try
        {
            // تطبيق أنيميشن الخروج
            await _animationService.AnimateElementExit(MainFrame);

            // إنشاء الصفحة الجديدة
            var page = pageFactory();
            MainFrame.Navigate(page);

            // تحديث العناوين
            PageTitleText.Text = title;
            PageDescriptionText.Text = description;
            StatusText.Text = $"تم تحميل {title} بنجاح";

            // تطبيق أنيميشن الدخول
            await _animationService.AnimateElementEntry(MainFrame);
        }
        catch (Exception ex)
        {
            StatusText.Text = $"خطأ في التحميل: {ex.Message}";
            MessageBox.Show($"خطأ في تحميل الصفحة: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    // معالجات أحداث القائمة الرئيسية المتطورة
    private async void Dashboard_Click(object sender, RoutedEventArgs e)
    {
        await NavigateToPage(() => new InteractiveDashboardPage(_currentUser),
                           "لوحة التحكم الرئيسية",
                           "نظرة شاملة على أداء مكتبك القانوني");
    }

    private async void InteractiveDashboard_Click(object sender, RoutedEventArgs e)
    {
        await NavigateToPage(() => new InteractiveDashboardPage(_currentUser),
                           "لوحة التحكم التفاعلية",
                           "إحصائيات ومخططات تفاعلية متقدمة");
    }

    private async void Security_Click(object sender, RoutedEventArgs e)
    {
        await NavigateToPage(() => new SecurityManagementPage(),
                           "إدارة الأمان والحماية",
                           "حماية وتشفير البيانات الحساسة");
    }

    private async void Documents_Click(object sender, RoutedEventArgs e)
    {
        await NavigateToPage(() => new AdvancedDocumentPage(),
                           "إدارة الوثائق المتطورة",
                           "مسح ضوئي، توقيع رقمي، وأرشفة ذكية");
    }

    private async void SmartAssistant_Click(object sender, RoutedEventArgs e)
    {
        await NavigateToPage(() => new SmartAssistantPage(),
                           "المساعد الذكي القانوني",
                           "مساعدة قانونية ذكية بالذكاء الاصطناعي");
    }

    private async void UISettings_Click(object sender, RoutedEventArgs e)
    {
        await NavigateToPage(() => new AdvancedUISettingsPage(),
                           "إعدادات الواجهة المتطورة",
                           "تخصيص الثيمات والألوان والتأثيرات");
    }

    protected override void OnClosed(EventArgs e)
    {
        // تنظيف الموارد
        _timeTimer?.Stop();
        base.OnClosed(e);
    }
}