using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AvocatPro.Models
{
    // نموذج الأدوار والصلاحيات
    public class Role
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;
        
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }
        
        // العلاقات
        public virtual ICollection<UserRoleAssignment> UserRoles { get; set; } = new List<UserRoleAssignment>();
        public virtual ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();
    }

    // نموذج الصلاحيات
    public class Permission
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [StringLength(100)]
        public string Module { get; set; } = string.Empty; // الوحدة (العملاء، القضايا، المالية، إلخ)
        
        [Required]
        [StringLength(50)]
        public string Action { get; set; } = string.Empty; // العملية (قراءة، كتابة، حذف، إلخ)
        
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;
        
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        // العلاقات
        public virtual ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();
        public virtual ICollection<UserPermission> UserPermissions { get; set; } = new List<UserPermission>();
    }

    // ربط الأدوار بالصلاحيات
    public class RolePermission
    {
        public int RoleId { get; set; }
        public int PermissionId { get; set; }
        public DateTime GrantedAt { get; set; } = DateTime.Now;
        public int GrantedBy { get; set; }
        
        // العلاقات
        public virtual Role Role { get; set; } = null!;
        public virtual Permission Permission { get; set; } = null!;
        public virtual User GrantedByUser { get; set; } = null!;
    }

    // ربط المستخدمين بالأدوار
    public class UserRoleAssignment
    {
        public int UserId { get; set; }
        public int RoleId { get; set; }
        public DateTime AssignedAt { get; set; } = DateTime.Now;
        public int AssignedBy { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public bool IsActive { get; set; } = true;

        // العلاقات
        public virtual User User { get; set; } = null!;
        public virtual Role Role { get; set; } = null!;
        public virtual User AssignedByUser { get; set; } = null!;
    }

    // صلاحيات مخصصة للمستخدمين
    public class UserPermission
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public int PermissionId { get; set; }
        public bool IsGranted { get; set; } = true; // منح أو منع
        public DateTime GrantedAt { get; set; } = DateTime.Now;
        public int GrantedBy { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public string? Notes { get; set; }
        
        // العلاقات
        public virtual User User { get; set; } = null!;
        public virtual Permission Permission { get; set; } = null!;
        public virtual User GrantedByUser { get; set; } = null!;
    }

    // سجل تسجيل الدخول
    public class UserSession
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public DateTime LoginTime { get; set; } = DateTime.Now;
        public DateTime? LogoutTime { get; set; }
        public string IpAddress { get; set; } = string.Empty;
        public string DeviceInfo { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public UserSessionStatus Status { get; set; } = UserSessionStatus.Active;
        
        // العلاقات
        public virtual User User { get; set; } = null!;
        
        // خصائص محسوبة
        public TimeSpan Duration => LogoutTime?.Subtract(LoginTime) ?? DateTime.Now.Subtract(LoginTime);
        public string DurationDisplay => $"{Duration.Hours:D2}:{Duration.Minutes:D2}:{Duration.Seconds:D2}";
        public bool IsOnline => IsActive && Status == UserSessionStatus.Active;
    }

    // سجل أنشطة المستخدم
    public class UserActivity
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string Action { get; set; } = string.Empty; // نوع العملية
        public string Module { get; set; } = string.Empty; // الوحدة
        public string Details { get; set; } = string.Empty; // تفاصيل العملية
        public string? OldValues { get; set; } // القيم القديمة (JSON)
        public string? NewValues { get; set; } // القيم الجديدة (JSON)
        public int? AffectedRecordId { get; set; } // معرف السجل المتأثر
        public string IpAddress { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public ActivityType Type { get; set; } = ActivityType.View;
        public ActivityLevel Level { get; set; } = ActivityLevel.Info;
        
        // العلاقات
        public virtual User User { get; set; } = null!;
    }

    // رسائل المحادثة
    public class UserMessage
    {
        public int Id { get; set; }
        public int SenderId { get; set; }
        public int ReceiverId { get; set; }
        public string Content { get; set; } = string.Empty;
        public MessageType Type { get; set; } = MessageType.Text;
        public string? AttachmentPath { get; set; }
        public string? AttachmentName { get; set; }
        public long? AttachmentSize { get; set; }
        public DateTime SentAt { get; set; } = DateTime.Now;
        public DateTime? ReadAt { get; set; }
        public bool IsRead { get; set; } = false;
        public bool IsDeleted { get; set; } = false;
        public MessagePriority Priority { get; set; } = MessagePriority.Normal;
        
        // العلاقات
        public virtual User Sender { get; set; } = null!;
        public virtual User Receiver { get; set; } = null!;
        
        // خصائص محسوبة
        public string AttachmentSizeDisplay => AttachmentSize.HasValue ? 
            AttachmentSize.Value < 1024 ? $"{AttachmentSize.Value} B" :
            AttachmentSize.Value < 1024 * 1024 ? $"{AttachmentSize.Value / 1024:F1} KB" :
            $"{AttachmentSize.Value / (1024 * 1024):F1} MB" : "";
    }

    // إعدادات المستخدم
    public class UserSettings
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string Theme { get; set; } = "Light"; // Light, Dark
        public string Language { get; set; } = "ar"; // ar, en
        public bool EmailNotifications { get; set; } = true;
        public bool SystemNotifications { get; set; } = true;
        public bool SoundNotifications { get; set; } = true;
        public int SessionTimeout { get; set; } = 30; // بالدقائق
        public bool TwoFactorEnabled { get; set; } = false;
        public string? ProfilePicture { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }
        
        // العلاقات
        public virtual User User { get; set; } = null!;
    }

    // تقارير المستخدمين
    public class UserReport
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public bool IsOnline { get; set; }
        public DateTime LastLogin { get; set; }
        public TimeSpan TotalSessionTime { get; set; }
        public int TotalSessions { get; set; }
        public int TotalActivities { get; set; }
        public List<string> Roles { get; set; } = new List<string>();
        public List<string> Permissions { get; set; } = new List<string>();
        public DateTime CreatedAt { get; set; }
        
        // خصائص محسوبة
        public string TotalSessionTimeDisplay => $"{TotalSessionTime.Days} أيام، {TotalSessionTime.Hours} ساعات، {TotalSessionTime.Minutes} دقائق";
        public string LastLoginDisplay => LastLogin.ToString("yyyy/MM/dd HH:mm");
        public string StatusDisplay => IsOnline ? "متصل" : IsActive ? "غير متصل" : "معطل";
    }

    // التعدادات
    public enum UserSessionStatus
    {
        Active = 1,
        Inactive = 2,
        Expired = 3,
        Terminated = 4
    }

    public enum ActivityType
    {
        View = 1,
        Create = 2,
        Update = 3,
        Delete = 4,
        Login = 5,
        Logout = 6,
        Export = 7,
        Import = 8,
        Print = 9,
        Download = 10
    }

    public enum ActivityLevel
    {
        Info = 1,
        Warning = 2,
        Error = 3,
        Critical = 4
    }

    public enum MessageType
    {
        Text = 1,
        File = 2,
        Image = 3,
        Document = 4,
        System = 5
    }

    public enum MessagePriority
    {
        Low = 1,
        Normal = 2,
        High = 3,
        Urgent = 4
    }

    // نموذج بطاقة المستخدم
    public class UserCard
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? ProfilePicture { get; set; }
        public bool IsOnline { get; set; }
        public DateTime? LastSeen { get; set; }
        public DateTime? CurrentSessionStart { get; set; }
        public TimeSpan? CurrentSessionDuration { get; set; }
        public string Location { get; set; } = string.Empty;
        public string DeviceInfo { get; set; } = string.Empty;
        public List<string> Roles { get; set; } = new List<string>();
        public int UnreadMessages { get; set; }
        public bool CanMessage { get; set; } = true;
        public bool CanCall { get; set; } = false;
        
        // خصائص محسوبة
        public string StatusDisplay => IsOnline ? "متصل الآن" : 
            LastSeen.HasValue ? $"آخر ظهور: {LastSeen.Value:HH:mm}" : "غير متصل";
        public string SessionDurationDisplay => CurrentSessionDuration.HasValue ? 
            $"{CurrentSessionDuration.Value.Hours:D2}:{CurrentSessionDuration.Value.Minutes:D2}" : "00:00";
    }
}
