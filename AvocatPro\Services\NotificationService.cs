using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Net.Mail;
using System.Net;
using AvocatPro.Models;
// using AvocatPro.Views.Windows;

namespace AvocatPro.Services
{
    /// <summary>
    /// خدمة الإشعارات والتذكيرات
    /// </summary>
    public class NotificationService
    {
        private readonly SettingsService _settingsService;
        private readonly List<NotificationModel> _pendingNotifications;

        public NotificationService()
        {
            _settingsService = SettingsService.Instance;
            _pendingNotifications = new List<NotificationModel>();
        }

        #region Desktop Notifications

        /// <summary>
        /// إرسال إشعار سطح المكتب
        /// </summary>
        public async Task SendDesktopNotificationAsync(AdvancedAppointmentModel appointment)
        {
            try
            {
                if (!_settingsService.Settings.EnableDesktopNotifications)
                    return;

                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    // var notificationWindow = new AppointmentNotificationWindow(appointment);
                    // notificationWindow.Show();

                    // إشعار مؤقت بدلاً من النافذة
                    MessageBox.Show($"تذكير بموعد: {appointment.Title}\nالتاريخ: {appointment.AppointmentDate:dd/MM/yyyy}\nالوقت: {appointment.AppointmentTimeText}",
                        "تذكير بموعد", MessageBoxButton.OK, MessageBoxImage.Information);
                });

                // تحديث حالة التذكير
                appointment.ReminderSent = true;
                appointment.ReminderSentDate = DateTime.Now;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إرسال إشعار سطح المكتب: {ex.Message}");
            }
        }

        /// <summary>
        /// إرسال إشعار نصي بسيط
        /// </summary>
        public void SendSimpleNotification(string title, string message, NotificationType type = NotificationType.Info)
        {
            try
            {
                if (!_settingsService.Settings.EnableDesktopNotifications)
                    return;

                var notification = new NotificationModel
                {
                    Title = title,
                    Message = message,
                    Type = type,
                    Timestamp = DateTime.Now
                };

                _pendingNotifications.Add(notification);

                // عرض الإشعار
                Application.Current.Dispatcher.Invoke(() =>
                {
                    var icon = type switch
                    {
                        NotificationType.Success => MessageBoxImage.Information,
                        NotificationType.Warning => MessageBoxImage.Warning,
                        NotificationType.Error => MessageBoxImage.Error,
                        _ => MessageBoxImage.Information
                    };

                    MessageBox.Show(message, title, MessageBoxButton.OK, icon);
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إرسال الإشعار: {ex.Message}");
            }
        }

        #endregion

        #region Email Notifications

        /// <summary>
        /// إرسال تذكير عبر البريد الإلكتروني
        /// </summary>
        public async Task SendEmailReminderAsync(AdvancedAppointmentModel appointment)
        {
            try
            {
                if (!_settingsService.Settings.EnableEmailNotifications || 
                    !appointment.EmailReminderEnabled ||
                    string.IsNullOrWhiteSpace(appointment.ClientEmail))
                    return;

                var settings = _settingsService.Settings;
                
                if (!_settingsService.ValidateEmailSettings())
                    throw new Exception("إعدادات البريد الإلكتروني غير مكتملة");

                var subject = $"تذكير بموعد: {appointment.Title}";
                var body = CreateEmailBody(appointment);

                using var client = new SmtpClient(settings.SmtpServer, settings.SmtpPort)
                {
                    Credentials = new NetworkCredential(settings.SmtpUsername, settings.SmtpPassword),
                    EnableSsl = settings.EnableSsl
                };

                var message = new MailMessage
                {
                    From = new MailAddress(settings.FromEmail, settings.FromName),
                    Subject = subject,
                    Body = body,
                    IsBodyHtml = true
                };

                message.To.Add(appointment.ClientEmail);

                await client.SendMailAsync(message);

                // تحديث حالة التذكير
                appointment.ReminderSent = true;
                appointment.ReminderSentDate = DateTime.Now;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إرسال البريد الإلكتروني: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء محتوى البريد الإلكتروني
        /// </summary>
        private string CreateEmailBody(AdvancedAppointmentModel appointment)
        {
            var officeName = _settingsService.Settings.OfficeName;
            var officePhone = _settingsService.Settings.OfficePhone;
            var officeEmail = _settingsService.Settings.OfficeEmail;

            return $@"
<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Arial, sans-serif; margin: 0; padding: 20px; background-color: #f8fafc; }}
        .container {{ max-width: 600px; margin: 0 auto; background-color: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }}
        .header {{ background: linear-gradient(135deg, #6366f1, #8b5cf6); color: white; padding: 30px; text-align: center; }}
        .content {{ padding: 30px; }}
        .appointment-details {{ background-color: #f8fafc; border-radius: 8px; padding: 20px; margin: 20px 0; }}
        .detail-row {{ display: flex; justify-content: space-between; margin: 10px 0; padding: 8px 0; border-bottom: 1px solid #e5e7eb; }}
        .label {{ font-weight: bold; color: #374151; }}
        .value {{ color: #6b7280; }}
        .footer {{ background-color: #f8fafc; padding: 20px; text-align: center; color: #6b7280; font-size: 12px; }}
        .button {{ display: inline-block; background-color: #6366f1; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>🔔 تذكير بموعد</h1>
            <p>{officeName}</p>
        </div>
        
        <div class='content'>
            <h2>عزيزي/عزيزتي {appointment.ClientName}</h2>
            <p>نذكركم بموعدكم القادم معنا:</p>
            
            <div class='appointment-details'>
                <div class='detail-row'>
                    <span class='label'>عنوان الموعد:</span>
                    <span class='value'>{appointment.Title}</span>
                </div>
                <div class='detail-row'>
                    <span class='label'>نوع الموعد:</span>
                    <span class='value'>{appointment.AppointmentType}</span>
                </div>
                <div class='detail-row'>
                    <span class='label'>التاريخ:</span>
                    <span class='value'>{appointment.AppointmentDate:dddd، dd MMMM yyyy}</span>
                </div>
                <div class='detail-row'>
                    <span class='label'>الوقت:</span>
                    <span class='value'>{appointment.AppointmentTimeText}</span>
                </div>
                <div class='detail-row'>
                    <span class='label'>المدة المتوقعة:</span>
                    <span class='value'>{appointment.DurationText}</span>
                </div>
                <div class='detail-row'>
                    <span class='label'>المكان:</span>
                    <span class='value'>{(string.IsNullOrWhiteSpace(appointment.Location) ? "المكتب الرئيسي" : appointment.Location)}</span>
                </div>
                <div class='detail-row'>
                    <span class='label'>المحامي المكلف:</span>
                    <span class='value'>{appointment.AssignedLawyer}</span>
                </div>
            </div>
            
            {(string.IsNullOrWhiteSpace(appointment.Notes) ? "" : $"<p><strong>ملاحظات:</strong> {appointment.Notes}</p>")}
            
            <p>يرجى الحضور في الوقت المحدد. في حالة عدم التمكن من الحضور، يرجى التواصل معنا مسبقاً.</p>
        </div>
        
        <div class='footer'>
            <p><strong>{officeName}</strong></p>
            <p>📞 {officePhone} | 📧 {officeEmail}</p>
            <p>تم إرسال هذا التذكير تلقائياً في {DateTime.Now:dd/MM/yyyy HH:mm}</p>
        </div>
    </div>
</body>
</html>";
        }

        #endregion

        #region SMS Notifications

        /// <summary>
        /// إرسال تذكير عبر الرسائل النصية
        /// </summary>
        public async Task SendSmsReminderAsync(AdvancedAppointmentModel appointment)
        {
            try
            {
                if (!_settingsService.Settings.EnableSmsNotifications || 
                    !appointment.SmsReminderEnabled ||
                    string.IsNullOrWhiteSpace(appointment.ClientPhone))
                    return;

                var message = CreateSmsMessage(appointment);
                
                // هنا يمكن إضافة كود إرسال الرسائل النصية
                // مثل استخدام خدمة Twilio أو أي خدمة أخرى
                
                await Task.Delay(100); // محاكاة الإرسال
                
                // تحديث حالة التذكير
                appointment.ReminderSent = true;
                appointment.ReminderSentDate = DateTime.Now;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إرسال الرسالة النصية: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء محتوى الرسالة النصية
        /// </summary>
        private string CreateSmsMessage(AdvancedAppointmentModel appointment)
        {
            var officeName = _settingsService.Settings.OfficeName;
            var officePhone = _settingsService.Settings.OfficePhone;

            return $"تذكير: موعدكم '{appointment.Title}' يوم {appointment.AppointmentDate:dd/MM} في {appointment.AppointmentTimeText}. {officeName} - {officePhone}";
        }

        #endregion

        #region Batch Notifications

        /// <summary>
        /// إرسال تذكيرات جماعية
        /// </summary>
        public async Task SendBatchRemindersAsync(List<AdvancedAppointmentModel> appointments)
        {
            var results = new List<NotificationResult>();

            foreach (var appointment in appointments)
            {
                try
                {
                    // إرسال إشعار سطح المكتب
                    if (_settingsService.Settings.EnableDesktopNotifications)
                    {
                        await SendDesktopNotificationAsync(appointment);
                    }

                    // إرسال بريد إلكتروني
                    if (appointment.EmailReminderEnabled && !string.IsNullOrWhiteSpace(appointment.ClientEmail))
                    {
                        await SendEmailReminderAsync(appointment);
                    }

                    // إرسال رسالة نصية
                    if (appointment.SmsReminderEnabled && !string.IsNullOrWhiteSpace(appointment.ClientPhone))
                    {
                        await SendSmsReminderAsync(appointment);
                    }

                    results.Add(new NotificationResult
                    {
                        AppointmentId = appointment.Id,
                        Success = true,
                        Message = "تم إرسال التذكير بنجاح"
                    });
                }
                catch (Exception ex)
                {
                    results.Add(new NotificationResult
                    {
                        AppointmentId = appointment.Id,
                        Success = false,
                        Message = ex.Message
                    });
                }
            }

            // عرض ملخص النتائج
            var successCount = results.Count(r => r.Success);
            var failureCount = results.Count(r => !r.Success);

            SendSimpleNotification(
                "نتائج إرسال التذكيرات",
                $"تم إرسال {successCount} تذكير بنجاح، فشل في إرسال {failureCount} تذكير",
                failureCount > 0 ? NotificationType.Warning : NotificationType.Success
            );
        }

        /// <summary>
        /// فحص المواعيد التي تحتاج تذكير وإرسالها
        /// </summary>
        public async Task CheckAndSendRemindersAsync(List<AdvancedAppointmentModel> appointments)
        {
            var appointmentsNeedingReminder = appointments.Where(a => a.NeedsReminder).ToList();
            
            if (appointmentsNeedingReminder.Any())
            {
                await SendBatchRemindersAsync(appointmentsNeedingReminder);
            }
        }

        #endregion

        #region Notification History

        /// <summary>
        /// الحصول على سجل الإشعارات
        /// </summary>
        public List<NotificationModel> GetNotificationHistory()
        {
            return _pendingNotifications.OrderByDescending(n => n.Timestamp).ToList();
        }

        /// <summary>
        /// مسح سجل الإشعارات
        /// </summary>
        public void ClearNotificationHistory()
        {
            _pendingNotifications.Clear();
        }

        #endregion
    }

    /// <summary>
    /// نموذج الإشعار
    /// </summary>
    public class NotificationModel
    {
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public NotificationType Type { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// نتيجة الإشعار
    /// </summary>
    public class NotificationResult
    {
        public int AppointmentId { get; set; }
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// أنواع الإشعارات
    /// </summary>
    public enum NotificationType
    {
        Info,
        Success,
        Warning,
        Error
    }
}
