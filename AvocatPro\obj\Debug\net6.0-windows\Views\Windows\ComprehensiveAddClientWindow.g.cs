﻿#pragma checksum "..\..\..\..\..\Views\Windows\ComprehensiveAddClientWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "7F3E012019083B099A4D8EB3FDC71057B98AD1AB"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Windows {
    
    
    /// <summary>
    /// ComprehensiveAddClientWindow
    /// </summary>
    public partial class ComprehensiveAddClientWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 90 "..\..\..\..\..\Views\Windows\ComprehensiveAddClientWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ClientNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\..\Views\Windows\ComprehensiveAddClientWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ClientTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\..\Views\Windows\ComprehensiveAddClientWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PhoneTextBox;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\..\Views\Windows\ComprehensiveAddClientWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox EmailTextBox;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\..\..\Views\Windows\ComprehensiveAddClientWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OfficeRefTextBox;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\..\..\Views\Windows\ComprehensiveAddClientWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FileRefTextBox;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\..\..\Views\Windows\ComprehensiveAddClientWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\..\..\Views\Windows\ComprehensiveAddClientWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CityComboBox;
        
        #line default
        #line hidden
        
        
        #line 233 "..\..\..\..\..\Views\Windows\ComprehensiveAddClientWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DistrictTextBox;
        
        #line default
        #line hidden
        
        
        #line 242 "..\..\..\..\..\Views\Windows\ComprehensiveAddClientWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox StreetTextBox;
        
        #line default
        #line hidden
        
        
        #line 251 "..\..\..\..\..\Views\Windows\ComprehensiveAddClientWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BuildingNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 260 "..\..\..\..\..\Views\Windows\ComprehensiveAddClientWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FullAddressTextBox;
        
        #line default
        #line hidden
        
        
        #line 278 "..\..\..\..\..\Views\Windows\ComprehensiveAddClientWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/windows/comprehensiveaddclientwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\ComprehensiveAddClientWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 38 "..\..\..\..\..\Views\Windows\ComprehensiveAddClientWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Close_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ClientNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.ClientTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.PhoneTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.EmailTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.OfficeRefTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.FileRefTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.StatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.CityComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.DistrictTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.StreetTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.BuildingNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.FullAddressTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            
            #line 310 "..\..\..\..\..\Views\Windows\ComprehensiveAddClientWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Cancel_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 335 "..\..\..\..\..\Views\Windows\ComprehensiveAddClientWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveAndContinue_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 360 "..\..\..\..\..\Views\Windows\ComprehensiveAddClientWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveAndFinish_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

