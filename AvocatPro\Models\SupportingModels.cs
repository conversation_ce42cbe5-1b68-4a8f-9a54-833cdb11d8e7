using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AvocatPro.Models;

/// <summary>
/// جهات اتصال الموكل
/// </summary>
public class ClientContact : BaseEntity
{
    /// <summary>
    /// معرف الموكل
    /// </summary>
    [Required]
    public int ClientId { get; set; }
    
    /// <summary>
    /// الاسم
    /// </summary>
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// المنصب/العلاقة
    /// </summary>
    [StringLength(100)]
    public string? Position { get; set; }
    
    /// <summary>
    /// الهاتف
    /// </summary>
    [StringLength(20)]
    public string? Phone { get; set; }
    
    /// <summary>
    /// البريد الإلكتروني
    /// </summary>
    [EmailAddress]
    [StringLength(100)]
    public string? Email { get; set; }
    
    /// <summary>
    /// جهة اتصال رئيسية
    /// </summary>
    public bool IsPrimary { get; set; } = false;
    
    // Navigation Properties
    [ForeignKey("ClientId")]
    public virtual Client Client { get; set; } = null!;
}

/// <summary>
/// مستندات الموكل
/// </summary>
public class ClientDocument : BaseEntity
{
    /// <summary>
    /// معرف الموكل
    /// </summary>
    [Required]
    public int ClientId { get; set; }
    
    /// <summary>
    /// اسم المستند
    /// </summary>
    [Required]
    [StringLength(200)]
    public string DocumentName { get; set; } = string.Empty;
    
    /// <summary>
    /// نوع المستند
    /// </summary>
    public DocumentType Type { get; set; }
    
    /// <summary>
    /// مسار الملف
    /// </summary>
    [Required]
    [StringLength(500)]
    public string FilePath { get; set; } = string.Empty;
    
    /// <summary>
    /// حجم الملف (بالبايت)
    /// </summary>
    public long FileSize { get; set; }
    
    /// <summary>
    /// نوع الملف
    /// </summary>
    [StringLength(50)]
    public string? FileType { get; set; }
    
    /// <summary>
    /// تاريخ انتهاء الصلاحية
    /// </summary>
    public DateTime? ExpiryDate { get; set; }
    
    // Navigation Properties
    [ForeignKey("ClientId")]
    public virtual Client Client { get; set; } = null!;
}

/// <summary>
/// مستندات القضية
/// </summary>
public class CaseDocument : BaseEntity
{
    /// <summary>
    /// معرف القضية
    /// </summary>
    [Required]
    public int CaseId { get; set; }
    
    /// <summary>
    /// اسم المستند
    /// </summary>
    [Required]
    [StringLength(200)]
    public string DocumentName { get; set; } = string.Empty;
    
    /// <summary>
    /// نوع المستند
    /// </summary>
    public DocumentType Type { get; set; }
    
    /// <summary>
    /// مسار الملف
    /// </summary>
    [Required]
    [StringLength(500)]
    public string FilePath { get; set; } = string.Empty;
    
    /// <summary>
    /// حجم الملف (بالبايت)
    /// </summary>
    public long FileSize { get; set; }
    
    /// <summary>
    /// نوع الملف
    /// </summary>
    [StringLength(50)]
    public string? FileType { get; set; }
    
    /// <summary>
    /// فئة المستند
    /// </summary>
    public CaseDocumentCategory Category { get; set; }
    
    /// <summary>
    /// مستند سري
    /// </summary>
    public bool IsConfidential { get; set; } = false;
    
    // Navigation Properties
    [ForeignKey("CaseId")]
    public virtual Case Case { get; set; } = null!;
}

/// <summary>
/// مستندات الجلسة
/// </summary>
public class SessionDocument : BaseEntity
{
    /// <summary>
    /// معرف الجلسة
    /// </summary>
    [Required]
    public int SessionId { get; set; }
    
    /// <summary>
    /// اسم المستند
    /// </summary>
    [Required]
    [StringLength(200)]
    public string DocumentName { get; set; } = string.Empty;
    
    /// <summary>
    /// نوع المستند
    /// </summary>
    public DocumentType Type { get; set; }
    
    /// <summary>
    /// مسار الملف
    /// </summary>
    [Required]
    [StringLength(500)]
    public string FilePath { get; set; } = string.Empty;
    
    /// <summary>
    /// حجم الملف (بالبايت)
    /// </summary>
    public long FileSize { get; set; }
    
    /// <summary>
    /// نوع الملف
    /// </summary>
    [StringLength(50)]
    public string? FileType { get; set; }
    
    // Navigation Properties
    [ForeignKey("SessionId")]
    public virtual Session Session { get; set; } = null!;
}

/// <summary>
/// ملاحظات القضية
/// </summary>
public class CaseNote : BaseEntity
{
    /// <summary>
    /// معرف القضية
    /// </summary>
    [Required]
    public int CaseId { get; set; }
    
    /// <summary>
    /// عنوان الملاحظة
    /// </summary>
    [Required]
    [StringLength(200)]
    public string Title { get; set; } = string.Empty;
    
    /// <summary>
    /// محتوى الملاحظة
    /// </summary>
    [Required]
    [StringLength(2000)]
    public string Content { get; set; } = string.Empty;
    
    /// <summary>
    /// نوع الملاحظة
    /// </summary>
    public NoteType Type { get; set; } = NoteType.General;
    
    /// <summary>
    /// أولوية الملاحظة
    /// </summary>
    public NotePriority Priority { get; set; } = NotePriority.Medium;
    
    /// <summary>
    /// ملاحظة خاصة
    /// </summary>
    public bool IsPrivate { get; set; } = false;
    
    /// <summary>
    /// تاريخ التذكير
    /// </summary>
    public DateTime? ReminderDate { get; set; }
    
    // Navigation Properties
    [ForeignKey("CaseId")]
    public virtual Case Case { get; set; } = null!;
}

/// <summary>
/// تذكيرات المواعيد
/// </summary>
public class AppointmentReminder : BaseEntity
{
    /// <summary>
    /// معرف الموعد
    /// </summary>
    [Required]
    public int AppointmentId { get; set; }
    
    /// <summary>
    /// وقت التذكير (بالدقائق قبل الموعد)
    /// </summary>
    [Required]
    public int MinutesBefore { get; set; }
    
    /// <summary>
    /// نوع التذكير
    /// </summary>
    public ReminderType Type { get; set; }
    
    /// <summary>
    /// تم الإرسال
    /// </summary>
    public bool IsSent { get; set; } = false;
    
    /// <summary>
    /// تاريخ الإرسال
    /// </summary>
    public DateTime? SentDate { get; set; }
    
    // Navigation Properties
    [ForeignKey("AppointmentId")]
    public virtual Appointment Appointment { get; set; } = null!;
}

/// <summary>
/// إعدادات المكتب
/// </summary>
public class OfficeSettings : BaseEntity
{
    /// <summary>
    /// اسم المكتب
    /// </summary>
    [Required]
    [StringLength(200)]
    public string OfficeName { get; set; } = string.Empty;
    
    /// <summary>
    /// عنوان المكتب
    /// </summary>
    [StringLength(500)]
    public string? OfficeAddress { get; set; }
    
    /// <summary>
    /// هاتف المكتب
    /// </summary>
    [StringLength(20)]
    public string? OfficePhone { get; set; }
    
    /// <summary>
    /// فاكس المكتب
    /// </summary>
    [StringLength(20)]
    public string? OfficeFax { get; set; }
    
    /// <summary>
    /// بريد المكتب الإلكتروني
    /// </summary>
    [EmailAddress]
    [StringLength(100)]
    public string? OfficeEmail { get; set; }
    
    /// <summary>
    /// موقع المكتب الإلكتروني
    /// </summary>
    [StringLength(200)]
    public string? OfficeWebsite { get; set; }
    
    /// <summary>
    /// شعار المكتب
    /// </summary>
    public string? OfficeLogo { get; set; }
    
    /// <summary>
    /// رقم الترخيص
    /// </summary>
    [StringLength(50)]
    public string? LicenseNumber { get; set; }
    
    /// <summary>
    /// إعدادات النظام (JSON)
    /// </summary>
    public string? SystemSettings { get; set; }
    
    /// <summary>
    /// إعدادات النسخ الاحتياطي (JSON)
    /// </summary>
    public string? BackupSettings { get; set; }
}

/// <summary>
/// أنواع المستندات
/// </summary>
public enum DocumentType
{
    /// <summary>
    /// بطاقة هوية
    /// </summary>
    Identity = 1,
    
    /// <summary>
    /// جواز سفر
    /// </summary>
    Passport = 2,
    
    /// <summary>
    /// عقد
    /// </summary>
    Contract = 3,
    
    /// <summary>
    /// فاتورة
    /// </summary>
    Invoice = 4,
    
    /// <summary>
    /// مرافعة
    /// </summary>
    Pleading = 5,
    
    /// <summary>
    /// حكم
    /// </summary>
    Judgment = 6,
    
    /// <summary>
    /// شهادة
    /// </summary>
    Certificate = 7,
    
    /// <summary>
    /// أخرى
    /// </summary>
    Other = 8
}

/// <summary>
/// فئات مستندات القضية
/// </summary>
public enum CaseDocumentCategory
{
    /// <summary>
    /// مستندات أساسية
    /// </summary>
    Essential = 1,
    
    /// <summary>
    /// أدلة
    /// </summary>
    Evidence = 2,
    
    /// <summary>
    /// مراسلات
    /// </summary>
    Correspondence = 3,
    
    /// <summary>
    /// تقارير
    /// </summary>
    Reports = 4,
    
    /// <summary>
    /// أحكام
    /// </summary>
    Judgments = 5
}

/// <summary>
/// أنواع الملاحظات
/// </summary>
public enum NoteType
{
    /// <summary>
    /// عامة
    /// </summary>
    General = 1,
    
    /// <summary>
    /// مهمة
    /// </summary>
    Task = 2,
    
    /// <summary>
    /// تذكير
    /// </summary>
    Reminder = 3,
    
    /// <summary>
    /// تحديث
    /// </summary>
    Update = 4
}

/// <summary>
/// أولوية الملاحظات
/// </summary>
public enum NotePriority
{
    /// <summary>
    /// منخفضة
    /// </summary>
    Low = 1,
    
    /// <summary>
    /// متوسطة
    /// </summary>
    Medium = 2,
    
    /// <summary>
    /// عالية
    /// </summary>
    High = 3,
    
    /// <summary>
    /// عاجلة
    /// </summary>
    Urgent = 4
}

/// <summary>
/// أنواع التذكير
/// </summary>
public enum ReminderType
{
    /// <summary>
    /// إشعار في التطبيق
    /// </summary>
    InApp = 1,
    
    /// <summary>
    /// بريد إلكتروني
    /// </summary>
    Email = 2,
    
    /// <summary>
    /// رسالة نصية
    /// </summary>
    SMS = 3,
    
    /// <summary>
    /// إشعار صوتي
    /// </summary>
    Sound = 4
}
