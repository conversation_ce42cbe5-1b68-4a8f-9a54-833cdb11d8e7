using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace AvocatPro.Models
{
    /// <summary>
    /// نموذج الموعد المتقدم
    /// </summary>
    public class AdvancedAppointmentModel : INotifyPropertyChanged
    {
        #region Private Fields

        private int _id;
        private string _appointmentNumber = string.Empty;
        private string _title = string.Empty;
        private string _description = string.Empty;
        private DateTime _appointmentDate = DateTime.Now;
        private TimeSpan _appointmentTime = new TimeSpan(9, 0, 0);
        private TimeSpan _duration = new TimeSpan(1, 0, 0);
        private string _appointmentType = string.Empty;
        private string _location = string.Empty;
        private string _clientName = string.Empty;
        private string _clientPhone = string.Empty;
        private string _clientEmail = string.Empty;
        private string _assignedLawyer = string.Empty;
        private string _status = "مجدول";
        private string _priority = "عادي";
        private string _notes = string.Empty;
        private bool _reminderEnabled = true;
        private int _reminderMinutes = 15;
        private bool _emailReminderEnabled = false;
        private bool _smsReminderEnabled = false;
        private bool _reminderSent = false;
        private DateTime? _reminderSentDate;
        private string _outcome = string.Empty;
        private decimal _fees = 0;
        private string _documents = string.Empty;
        private bool _isRecurring = false;
        private string _recurrencePattern = string.Empty;
        private DateTime? _recurrenceEndDate;
        private DateTime _createdDate = DateTime.Now;
        private DateTime? _lastUpdated;
        private string _createdBy = string.Empty;
        private string _category = string.Empty;
        private string _relatedFileNumber = string.Empty;

        #endregion

        #region Properties

        public int Id
        {
            get => _id;
            set { _id = value; OnPropertyChanged(); }
        }

        public string AppointmentNumber
        {
            get => _appointmentNumber;
            set { _appointmentNumber = value; OnPropertyChanged(); }
        }

        public string Title
        {
            get => _title;
            set { _title = value; OnPropertyChanged(); }
        }

        public string Description
        {
            get => _description;
            set { _description = value; OnPropertyChanged(); }
        }

        public DateTime AppointmentDate
        {
            get => _appointmentDate;
            set { _appointmentDate = value; OnPropertyChanged(); OnPropertyChanged(nameof(AppointmentDateTime)); OnPropertyChanged(nameof(DaysUntilAppointment)); }
        }

        public TimeSpan AppointmentTime
        {
            get => _appointmentTime;
            set { _appointmentTime = value; OnPropertyChanged(); OnPropertyChanged(nameof(AppointmentDateTime)); OnPropertyChanged(nameof(AppointmentTimeText)); }
        }

        public TimeSpan Duration
        {
            get => _duration;
            set { _duration = value; OnPropertyChanged(); OnPropertyChanged(nameof(DurationText)); OnPropertyChanged(nameof(EndTime)); }
        }

        public string AppointmentType
        {
            get => _appointmentType;
            set { _appointmentType = value; OnPropertyChanged(); }
        }

        public string Location
        {
            get => _location;
            set { _location = value; OnPropertyChanged(); }
        }

        public string ClientName
        {
            get => _clientName;
            set { _clientName = value; OnPropertyChanged(); }
        }

        public string ClientPhone
        {
            get => _clientPhone;
            set { _clientPhone = value; OnPropertyChanged(); }
        }

        public string ClientEmail
        {
            get => _clientEmail;
            set { _clientEmail = value; OnPropertyChanged(); }
        }

        public string AssignedLawyer
        {
            get => _assignedLawyer;
            set { _assignedLawyer = value; OnPropertyChanged(); }
        }

        public string Status
        {
            get => _status;
            set { _status = value; OnPropertyChanged(); OnPropertyChanged(nameof(StatusColor)); }
        }

        public string Priority
        {
            get => _priority;
            set { _priority = value; OnPropertyChanged(); OnPropertyChanged(nameof(PriorityColor)); }
        }

        public string Notes
        {
            get => _notes;
            set { _notes = value; OnPropertyChanged(); }
        }

        public bool ReminderEnabled
        {
            get => _reminderEnabled;
            set { _reminderEnabled = value; OnPropertyChanged(); }
        }

        public int ReminderMinutes
        {
            get => _reminderMinutes;
            set { _reminderMinutes = value; OnPropertyChanged(); OnPropertyChanged(nameof(ReminderText)); }
        }

        public bool EmailReminderEnabled
        {
            get => _emailReminderEnabled;
            set { _emailReminderEnabled = value; OnPropertyChanged(); }
        }

        public bool SmsReminderEnabled
        {
            get => _smsReminderEnabled;
            set { _smsReminderEnabled = value; OnPropertyChanged(); }
        }

        public bool ReminderSent
        {
            get => _reminderSent;
            set { _reminderSent = value; OnPropertyChanged(); OnPropertyChanged(nameof(ReminderStatusText)); }
        }

        public DateTime? ReminderSentDate
        {
            get => _reminderSentDate;
            set { _reminderSentDate = value; OnPropertyChanged(); }
        }

        public string Outcome
        {
            get => _outcome;
            set { _outcome = value; OnPropertyChanged(); }
        }

        public decimal Fees
        {
            get => _fees;
            set { _fees = value; OnPropertyChanged(); OnPropertyChanged(nameof(FeesText)); }
        }

        public string Documents
        {
            get => _documents;
            set { _documents = value; OnPropertyChanged(); }
        }

        public bool IsRecurring
        {
            get => _isRecurring;
            set { _isRecurring = value; OnPropertyChanged(); }
        }

        public string RecurrencePattern
        {
            get => _recurrencePattern;
            set { _recurrencePattern = value; OnPropertyChanged(); }
        }

        public DateTime? RecurrenceEndDate
        {
            get => _recurrenceEndDate;
            set { _recurrenceEndDate = value; OnPropertyChanged(); }
        }

        public DateTime CreatedDate
        {
            get => _createdDate;
            set { _createdDate = value; OnPropertyChanged(); }
        }

        public DateTime? LastUpdated
        {
            get => _lastUpdated;
            set { _lastUpdated = value; OnPropertyChanged(); }
        }

        public string CreatedBy
        {
            get => _createdBy;
            set { _createdBy = value; OnPropertyChanged(); }
        }

        public string Category
        {
            get => _category;
            set { _category = value; OnPropertyChanged(); }
        }

        public string RelatedFileNumber
        {
            get => _relatedFileNumber;
            set { _relatedFileNumber = value; OnPropertyChanged(); }
        }

        #endregion

        #region Computed Properties

        public DateTime AppointmentDateTime => AppointmentDate.Date + AppointmentTime;

        public DateTime EndTime => AppointmentDateTime + Duration;

        public string AppointmentTimeText => AppointmentTime.ToString(@"hh\:mm");

        public string DurationText => Duration.ToString(@"hh\:mm");

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    "مجدول" => "#6366F1",
                    "جاري" => "#F59E0B",
                    "مكتمل" => "#10B981",
                    "مؤجل" => "#8B5CF6",
                    "ملغي" => "#EF4444",
                    "متأخر" => "#DC2626",
                    _ => "#6B7280"
                };
            }
        }

        public string PriorityColor
        {
            get
            {
                return Priority switch
                {
                    "عاجل" => "#EF4444",
                    "مهم" => "#F59E0B",
                    "عادي" => "#10B981",
                    _ => "#6B7280"
                };
            }
        }

        public int DaysUntilAppointment
        {
            get
            {
                var days = (AppointmentDate.Date - DateTime.Now.Date).Days;
                return days;
            }
        }

        public string DaysUntilAppointmentText
        {
            get
            {
                var days = DaysUntilAppointment;
                if (days < 0)
                    return "منتهي";
                else if (days == 0)
                    return "اليوم";
                else if (days == 1)
                    return "غداً";
                else
                    return $"خلال {days} يوم";
            }
        }

        public string FeesText => Fees > 0 ? $"{Fees:N0} درهم" : "مجاني";

        public string ReminderText
        {
            get
            {
                if (!ReminderEnabled) return "معطل";
                
                if (ReminderMinutes < 60)
                    return $"{ReminderMinutes} دقيقة";
                else if (ReminderMinutes == 60)
                    return "ساعة واحدة";
                else if (ReminderMinutes < 1440)
                    return $"{ReminderMinutes / 60} ساعة";
                else
                    return $"{ReminderMinutes / 1440} يوم";
            }
        }

        public string ReminderStatusText
        {
            get
            {
                if (!ReminderEnabled) return "معطل";
                if (ReminderSent) return "تم الإرسال";
                return "في الانتظار";
            }
        }

        public string StatusIcon
        {
            get
            {
                return Status switch
                {
                    "مجدول" => "📅",
                    "جاري" => "⏳",
                    "مكتمل" => "✅",
                    "مؤجل" => "⏸️",
                    "ملغي" => "❌",
                    "متأخر" => "⚠️",
                    _ => "❓"
                };
            }
        }

        public string PriorityIcon
        {
            get
            {
                return Priority switch
                {
                    "عاجل" => "🔴",
                    "مهم" => "🟡",
                    "عادي" => "🟢",
                    _ => "⚪"
                };
            }
        }

        public string CategoryIcon
        {
            get
            {
                return Category switch
                {
                    "استشارة" => "💬",
                    "اجتماع" => "🤝",
                    "محكمة" => "⚖️",
                    "عقد" => "📄",
                    "متابعة" => "📋",
                    "شخصي" => "👤",
                    _ => "📅"
                };
            }
        }

        public bool IsToday => AppointmentDate.Date == DateTime.Now.Date;

        public bool IsTomorrow => AppointmentDate.Date == DateTime.Now.Date.AddDays(1);

        public bool IsOverdue => AppointmentDateTime < DateTime.Now && Status == "مجدول";

        public bool NeedsReminder
        {
            get
            {
                if (!ReminderEnabled || ReminderSent) return false;
                var reminderTime = AppointmentDateTime.AddMinutes(-ReminderMinutes);
                return DateTime.Now >= reminderTime;
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    /// <summary>
    /// إحصائيات المواعيد
    /// </summary>
    public class AppointmentStatistics
    {
        public int TotalAppointments { get; set; }
        public int TodayAppointments { get; set; }
        public int TomorrowAppointments { get; set; }
        public int ThisWeekAppointments { get; set; }
        public int ScheduledAppointments { get; set; }
        public int CompletedAppointments { get; set; }
        public int CancelledAppointments { get; set; }
        public int OverdueAppointments { get; set; }
        public int PendingReminders { get; set; }
        public decimal TotalFees { get; set; }
        public double AverageAppointmentsPerDay { get; set; }
        public string MostActiveType { get; set; } = string.Empty;
        public string MostActiveLawyer { get; set; } = string.Empty;
    }
}
