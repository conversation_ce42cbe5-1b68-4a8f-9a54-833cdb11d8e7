using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using Microsoft.Win32;
using AvocatPro.Models;

namespace AvocatPro.Views.Windows
{
    public partial class UserChatWindow : Window
    {
        private readonly User _currentUser;
        private readonly int _targetUserId;
        private UserCard? _targetUser;
        private List<UserMessage> _messages;
        private string? _selectedFilePath;

        public UserChatWindow(User currentUser, int targetUserId)
        {
            InitializeComponent();
            _currentUser = currentUser;
            _targetUserId = targetUserId;
            _messages = new List<UserMessage>();
            
            LoadTargetUser();
            LoadMessages();
            LoadSharedFiles();
            
            MessageTextBox.Focus();
        }

        private void LoadTargetUser()
        {
            // محاكاة تحميل بيانات المستخدم المستهدف
            _targetUser = new UserCard
            {
                UserId = _targetUserId,
                UserName = "lawyer1",
                FullName = "سارة أحمد القانونية",
                Email = "<EMAIL>",
                IsOnline = true,
                CurrentSessionStart = DateTime.Now.AddMinutes(-45),
                CurrentSessionDuration = TimeSpan.FromMinutes(45),
                Location = "جدة، السعودية",
                DeviceInfo = "Windows 10 - Edge",
                Roles = new List<string> { "محامي", "مستشار قانوني" }
            };

            // تحديث واجهة المستخدم
            UserNameText.Text = _targetUser.FullName;
            UserAvatarText.Text = _targetUser.FullName.Split(' ').FirstOrDefault()?.Substring(0, 1) ?? "؟";
            StatusText.Text = _targetUser.StatusDisplay;
            StatusIndicator.Fill = _targetUser.IsOnline ? Brushes.Green : Brushes.Gray;
            
            Title = $"محادثة مع {_targetUser.FullName}";
        }

        private void LoadMessages()
        {
            // محاكاة تحميل الرسائل
            _messages = new List<UserMessage>
            {
                new UserMessage
                {
                    Id = 1,
                    SenderId = _targetUserId,
                    ReceiverId = _currentUser.Id,
                    Content = "مرحباً، كيف حالك؟",
                    Type = MessageType.Text,
                    SentAt = DateTime.Now.AddHours(-2),
                    IsRead = true
                },
                new UserMessage
                {
                    Id = 2,
                    SenderId = _currentUser.Id,
                    ReceiverId = _targetUserId,
                    Content = "أهلاً وسهلاً، بخير والحمد لله. كيف حالك أنت؟",
                    Type = MessageType.Text,
                    SentAt = DateTime.Now.AddHours(-2).AddMinutes(5),
                    IsRead = true
                },
                new UserMessage
                {
                    Id = 3,
                    SenderId = _targetUserId,
                    ReceiverId = _currentUser.Id,
                    Content = "بخير، شكراً لك. هل يمكنك مراجعة الملف المرفق؟",
                    Type = MessageType.Text,
                    SentAt = DateTime.Now.AddHours(-1),
                    IsRead = true
                },
                new UserMessage
                {
                    Id = 4,
                    SenderId = _targetUserId,
                    ReceiverId = _currentUser.Id,
                    Content = "عقد العمل الجديد.pdf",
                    Type = MessageType.File,
                    AttachmentPath = @"C:\Files\عقد العمل الجديد.pdf",
                    AttachmentName = "عقد العمل الجديد.pdf",
                    AttachmentSize = 2560000, // 2.5 MB
                    SentAt = DateTime.Now.AddHours(-1).AddMinutes(1),
                    IsRead = false
                }
            };

            DisplayMessages();
        }

        private void DisplayMessages()
        {
            MessagesPanel.Children.Clear();

            foreach (var message in _messages.OrderBy(m => m.SentAt))
            {
                var messageElement = CreateMessageElement(message);
                MessagesPanel.Children.Add(messageElement);
            }

            // التمرير إلى آخر رسالة
            MessagesScrollViewer.ScrollToEnd();
        }

        private UIElement CreateMessageElement(UserMessage message)
        {
            var container = new StackPanel { Margin = new Thickness(0, 5, 0, 5) };
            
            // تحديد ما إذا كانت الرسالة مرسلة أم مستقبلة
            bool isSent = message.SenderId == _currentUser.Id;
            
            // إنشاء فقاعة الرسالة
            var bubble = new Border
            {
                Style = (Style)FindResource(isSent ? "SentMessage" : "ReceivedMessage"),
                HorizontalAlignment = isSent ? HorizontalAlignment.Left : HorizontalAlignment.Right
            };

            var content = new StackPanel();

            if (message.Type == MessageType.Text)
            {
                var textBlock = new TextBlock
                {
                    Text = message.Content,
                    Style = (Style)FindResource(isSent ? "SentMessageText" : "ReceivedMessageText")
                };
                content.Children.Add(textBlock);
            }
            else if (message.Type == MessageType.File)
            {
                var filePanel = new StackPanel { Orientation = Orientation.Horizontal };
                
                var fileIcon = new TextBlock
                {
                    Text = GetFileIcon(message.AttachmentName),
                    FontSize = 20,
                    Margin = new Thickness(0, 0, 8, 0),
                    VerticalAlignment = VerticalAlignment.Center
                };
                
                var fileInfo = new StackPanel();
                
                var fileName = new TextBlock
                {
                    Text = message.AttachmentName,
                    Style = (Style)FindResource(isSent ? "SentMessageText" : "ReceivedMessageText"),
                    FontWeight = FontWeights.Bold
                };
                
                var fileSize = new TextBlock
                {
                    Text = message.AttachmentSizeDisplay,
                    Style = (Style)FindResource(isSent ? "SentMessageText" : "ReceivedMessageText"),
                    FontSize = 12,
                    Opacity = 0.8
                };
                
                fileInfo.Children.Add(fileName);
                fileInfo.Children.Add(fileSize);
                
                filePanel.Children.Add(fileIcon);
                filePanel.Children.Add(fileInfo);
                
                // إضافة زر التحميل
                var downloadBtn = new Button
                {
                    Content = "📥",
                    Width = 30,
                    Height = 30,
                    Background = Brushes.Transparent,
                    BorderThickness = new Thickness(0),
                    Margin = new Thickness(10, 0, 0, 0),
                    ToolTip = "تحميل الملف",
                    Tag = message
                };
                downloadBtn.Click += DownloadFile_Click;
                filePanel.Children.Add(downloadBtn);
                
                content.Children.Add(filePanel);
            }

            // إضافة وقت الإرسال
            var timeText = new TextBlock
            {
                Text = message.SentAt.ToString("HH:mm"),
                FontSize = 10,
                Opacity = 0.7,
                HorizontalAlignment = HorizontalAlignment.Right,
                Margin = new Thickness(0, 5, 0, 0),
                Foreground = isSent ? Brushes.White : new SolidColorBrush(Color.FromRgb(108, 117, 125))
            };
            content.Children.Add(timeText);

            bubble.Child = content;
            container.Children.Add(bubble);

            return container;
        }

        private string GetFileIcon(string fileName)
        {
            var extension = Path.GetExtension(fileName)?.ToLower();
            return extension switch
            {
                ".pdf" => "📄",
                ".doc" or ".docx" => "📝",
                ".xls" or ".xlsx" => "📊",
                ".ppt" or ".pptx" => "📋",
                ".jpg" or ".jpeg" or ".png" or ".gif" => "🖼️",
                ".zip" or ".rar" => "📦",
                _ => "📎"
            };
        }

        private void LoadSharedFiles()
        {
            // محاكاة تحميل الملفات المشتركة
            var sharedFiles = _messages.Where(m => m.Type == MessageType.File).ToList();
            
            foreach (var file in sharedFiles)
            {
                var fileElement = CreateSharedFileElement(file);
                SharedFilesPanel.Children.Add(fileElement);
            }
        }

        private UIElement CreateSharedFileElement(UserMessage fileMessage)
        {
            var container = new Border
            {
                Background = Brushes.White,
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(10),
                Margin = new Thickness(0, 5, 0, 5),
                BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
                BorderThickness = new Thickness(1, 1, 1, 1)
            };

            var content = new StackPanel();
            
            var header = new StackPanel { Orientation = Orientation.Horizontal };
            
            var icon = new TextBlock
            {
                Text = GetFileIcon(fileMessage.AttachmentName),
                FontSize = 16,
                Margin = new Thickness(0, 0, 8, 0)
            };
            
            var fileName = new TextBlock
            {
                Text = fileMessage.AttachmentName,
                FontSize = 12,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(44, 62, 80)),
                TextTrimming = TextTrimming.CharacterEllipsis
            };
            
            header.Children.Add(icon);
            header.Children.Add(fileName);
            
            var info = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 5, 0, 0) };
            
            var size = new TextBlock
            {
                Text = fileMessage.AttachmentSizeDisplay,
                FontSize = 10,
                Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125))
            };
            
            var date = new TextBlock
            {
                Text = fileMessage.SentAt.ToString("dd/MM"),
                FontSize = 10,
                Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                Margin = new Thickness(10, 0, 0, 0)
            };
            
            info.Children.Add(size);
            info.Children.Add(date);
            
            content.Children.Add(header);
            content.Children.Add(info);
            
            container.Child = content;
            container.MouseLeftButtonUp += (s, e) => DownloadFile_Click(s, new RoutedEventArgs { Source = fileMessage });
            container.Cursor = Cursors.Hand;
            
            return container;
        }

        private void SendBtn_Click(object sender, RoutedEventArgs e)
        {
            SendMessage();
        }

        private void MessageTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && Keyboard.Modifiers != ModifierKeys.Shift)
            {
                e.Handled = true;
                SendMessage();
            }
        }

        private void SendMessage()
        {
            var messageText = MessageTextBox.Text.Trim();
            
            if (string.IsNullOrEmpty(messageText) && string.IsNullOrEmpty(_selectedFilePath))
                return;

            try
            {
                if (!string.IsNullOrEmpty(_selectedFilePath))
                {
                    // إرسال ملف
                    SendFile(_selectedFilePath, messageText);
                }
                else
                {
                    // إرسال رسالة نصية
                    SendTextMessage(messageText);
                }

                MessageTextBox.Clear();
                ClearFilePreview();
                MessageTextBox.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إرسال الرسالة:\n{ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SendTextMessage(string content)
        {
            var message = new UserMessage
            {
                Id = _messages.Count + 1,
                SenderId = _currentUser.Id,
                ReceiverId = _targetUserId,
                Content = content,
                Type = MessageType.Text,
                SentAt = DateTime.Now,
                IsRead = false
            };

            _messages.Add(message);
            
            // محاكاة حفظ الرسالة في قاعدة البيانات
            SaveMessageToDatabase(message);
            
            // إضافة الرسالة إلى الواجهة
            var messageElement = CreateMessageElement(message);
            MessagesPanel.Children.Add(messageElement);
            
            MessagesScrollViewer.ScrollToEnd();
        }

        private void SendFile(string filePath, string caption = "")
        {
            var fileInfo = new FileInfo(filePath);
            
            var message = new UserMessage
            {
                Id = _messages.Count + 1,
                SenderId = _currentUser.Id,
                ReceiverId = _targetUserId,
                Content = caption,
                Type = MessageType.File,
                AttachmentPath = filePath,
                AttachmentName = fileInfo.Name,
                AttachmentSize = fileInfo.Length,
                SentAt = DateTime.Now,
                IsRead = false
            };

            _messages.Add(message);
            
            // محاكاة حفظ الرسالة والملف
            SaveMessageToDatabase(message);
            
            // إضافة الرسالة إلى الواجهة
            var messageElement = CreateMessageElement(message);
            MessagesPanel.Children.Add(messageElement);
            
            // إضافة الملف إلى الملفات المشتركة
            var sharedFileElement = CreateSharedFileElement(message);
            SharedFilesPanel.Children.Add(sharedFileElement);
            
            MessagesScrollViewer.ScrollToEnd();
        }

        private void SaveMessageToDatabase(UserMessage message)
        {
            // محاكاة حفظ الرسالة في قاعدة البيانات
            System.Diagnostics.Debug.WriteLine($"تم حفظ الرسالة: {message.Content} من {message.SenderId} إلى {message.ReceiverId}");
        }

        private void AttachFileBtn_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "اختر ملف للإرفاق",
                Filter = "جميع الملفات (*.*)|*.*|" +
                        "ملفات PDF (*.pdf)|*.pdf|" +
                        "ملفات Word (*.doc;*.docx)|*.doc;*.docx|" +
                        "ملفات Excel (*.xls;*.xlsx)|*.xls;*.xlsx|" +
                        "الصور (*.jpg;*.jpeg;*.png;*.gif)|*.jpg;*.jpeg;*.png;*.gif",
                Multiselect = false
            };

            if (openFileDialog.ShowDialog() == true)
            {
                _selectedFilePath = openFileDialog.FileName;
                ShowFilePreview(_selectedFilePath);
            }
        }

        private void ShowFilePreview(string filePath)
        {
            var fileInfo = new FileInfo(filePath);
            
            FilePreviewName.Text = fileInfo.Name;
            FilePreviewSize.Text = FormatFileSize(fileInfo.Length);
            FilePreviewIcon.Icon = GetFileIconType(fileInfo.Extension);
            
            FilePreviewPanel.Visibility = Visibility.Visible;
        }

        private void ClearFilePreview()
        {
            _selectedFilePath = null;
            FilePreviewPanel.Visibility = Visibility.Collapsed;
        }

        private string FormatFileSize(long bytes)
        {
            if (bytes < 1024) return $"{bytes} B";
            if (bytes < 1024 * 1024) return $"{bytes / 1024.0:F1} KB";
            return $"{bytes / (1024.0 * 1024.0):F1} MB";
        }

        private FontAwesome.WPF.FontAwesomeIcon GetFileIconType(string extension)
        {
            return extension.ToLower() switch
            {
                ".pdf" => FontAwesome.WPF.FontAwesomeIcon.FilePdfOutline,
                ".doc" or ".docx" => FontAwesome.WPF.FontAwesomeIcon.FileWordOutline,
                ".xls" or ".xlsx" => FontAwesome.WPF.FontAwesomeIcon.FileExcelOutline,
                ".ppt" or ".pptx" => FontAwesome.WPF.FontAwesomeIcon.FilePowerpointOutline,
                ".jpg" or ".jpeg" or ".png" or ".gif" => FontAwesome.WPF.FontAwesomeIcon.FileImageOutline,
                ".zip" or ".rar" => FontAwesome.WPF.FontAwesomeIcon.FileArchiveOutline,
                _ => FontAwesome.WPF.FontAwesomeIcon.FileOutline
            };
        }

        private void RemoveFileBtn_Click(object sender, RoutedEventArgs e)
        {
            ClearFilePreview();
        }

        private void DownloadFile_Click(object sender, RoutedEventArgs e)
        {
            UserMessage fileMessage = null;
            
            if (sender is Button btn && btn.Tag is UserMessage msg)
            {
                fileMessage = msg;
            }
            else if (e.Source is UserMessage msg2)
            {
                fileMessage = msg2;
            }
            
            if (fileMessage == null) return;

            var saveFileDialog = new SaveFileDialog
            {
                FileName = fileMessage.AttachmentName,
                Filter = "جميع الملفات (*.*)|*.*"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    // محاكاة تحميل الملف
                    MessageBox.Show($"تم تحميل الملف: {fileMessage.AttachmentName}", "تم التحميل", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ أثناء تحميل الملف:\n{ex.Message}", "خطأ", 
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void EmojiBtn_Click(object sender, RoutedEventArgs e)
        {
            // إضافة رموز تعبيرية شائعة
            var emojis = new[] { "😊", "😂", "❤️", "👍", "👎", "😢", "😮", "😡", "🎉", "👏" };
            var random = new Random();
            var emoji = emojis[random.Next(emojis.Length)];
            
            MessageTextBox.Text += emoji;
            MessageTextBox.Focus();
            MessageTextBox.CaretIndex = MessageTextBox.Text.Length;
        }

        private void CallBtn_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show($"بدء مكالمة صوتية مع {_targetUser.FullName}", "مكالمة صوتية", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void VideoCallBtn_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show($"بدء مكالمة فيديو مع {_targetUser.FullName}", "مكالمة فيديو", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void UserInfoBtn_Click(object sender, RoutedEventArgs e)
        {
            // إظهار/إخفاء الشريط الجانبي للملفات
            FilesSidebar.Visibility = FilesSidebar.Visibility == Visibility.Visible ? 
                                     Visibility.Collapsed : Visibility.Visible;
        }

        private void CloseBtn_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
