﻿#pragma checksum "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "246915433B014E818BDC3C1C3DEF99C62F3EB737"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Pages {
    
    
    /// <summary>
    /// AdvancedFilesPage
    /// </summary>
    public partial class AdvancedFilesPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 58 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddFileButton;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SyncAllButton;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalFilesCount;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActiveFilesCount;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InSessionsCount;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ArchivedFilesCount;
        
        #line default
        #line hidden
        
        
        #line 212 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SyncedFilesCount;
        
        #line default
        #line hidden
        
        
        #line 253 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 262 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 274 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CaseTypeFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 279 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CourtFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 285 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 290 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintButton;
        
        #line default
        #line hidden
        
        
        #line 307 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PriorityFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 317 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SyncFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 326 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DateFromPicker;
        
        #line default
        #line hidden
        
        
        #line 330 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DateToPicker;
        
        #line default
        #line hidden
        
        
        #line 343 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid FilesDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/pages/advancedfilespage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AddFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 61 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
            this.AddFileButton.Click += new System.Windows.RoutedEventHandler(this.AddFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SyncAllButton = ((System.Windows.Controls.Button)(target));
            
            #line 86 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
            this.SyncAllButton.Click += new System.Windows.RoutedEventHandler(this.SyncAllButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.TotalFilesCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.ActiveFilesCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.InSessionsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.ArchivedFilesCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.SyncedFilesCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 256 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
            this.SearchTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.SearchTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 257 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
            this.SearchTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.SearchTextBox_LostFocus);
            
            #line default
            #line hidden
            
            #line 257 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.StatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 264 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
            this.StatusFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.StatusFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.CaseTypeFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 276 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
            this.CaseTypeFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CaseTypeFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.CourtFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 281 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
            this.CourtFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CourtFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 12:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 287 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.Export_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.PrintButton = ((System.Windows.Controls.Button)(target));
            
            #line 292 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
            this.PrintButton.Click += new System.Windows.RoutedEventHandler(this.Print_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.PriorityFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 309 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
            this.PriorityFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PriorityFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 15:
            this.SyncFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 319 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
            this.SyncFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SyncFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 16:
            this.DateFromPicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 328 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
            this.DateFromPicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 17:
            this.DateToPicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 332 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
            this.DateToPicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 18:
            this.FilesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 348 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
            this.FilesDataGrid.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.FilesDataGrid_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 19:
            
            #line 426 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditFile_Click);
            
            #line default
            #line hidden
            break;
            case 20:
            
            #line 429 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteFile_Click);
            
            #line default
            #line hidden
            break;
            case 21:
            
            #line 432 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PrintFile_Click);
            
            #line default
            #line hidden
            break;
            case 22:
            
            #line 435 "..\..\..\..\..\Views\Pages\AdvancedFilesPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SyncFile_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

