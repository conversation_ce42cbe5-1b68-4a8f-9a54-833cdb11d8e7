<Window x:Class="AvocatPro.Views.Windows.ReportExportWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تصدير التقارير" 
        Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">

    <Window.Resources>
        <!-- أنماط النافذة -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1976D2"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#333"/>
            <Setter Property="Margin" Value="0,15,0,10"/>
        </Style>

        <Style x:Key="ExportOptionStyle" TargetType="CheckBox">
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Margin" Value="0,5,0,5"/>
            <Setter Property="Padding" Value="8,5"/>
        </Style>

        <Style x:Key="FormatButtonStyle" TargetType="RadioButton">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="RadioButton">
                        <Border Name="border" Background="White" BorderBrush="#DDD" BorderThickness="1" 
                               CornerRadius="8" Padding="15,10" Margin="5" Cursor="Hand">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Name="icon" Text="{Binding Tag, RelativeSource={RelativeSource TemplatedParent}}" 
                                          FontSize="20" Margin="0,0,10,0"/>
                                <StackPanel>
                                    <TextBlock Name="title" Text="{Binding Content, RelativeSource={RelativeSource TemplatedParent}}" 
                                              FontWeight="Bold" FontSize="13"/>
                                    <TextBlock Name="description" Text="{Binding ToolTip, RelativeSource={RelativeSource TemplatedParent}}" 
                                              FontSize="11" Foreground="#666"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsChecked" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#E3F2FD"/>
                                <Setter TargetName="border" Property="BorderBrush" Value="#1976D2"/>
                                <Setter TargetName="border" Property="BorderThickness" Value="2"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#F5F5F5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#1976D2"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               CornerRadius="6"
                               Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1565C0"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#F5F5F5"/>
            <Setter Property="Foreground" Value="#333"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               BorderBrush="{TemplateBinding BorderBrush}"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               CornerRadius="6"
                               Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E0E0E0"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="PreviewCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="4" Opacity="0.2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- رأس النافذة -->
        <Border Grid.Row="0" Background="#1976D2" Padding="25">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📤" FontSize="28" Foreground="White" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="تصدير التقارير والإحصائيات" FontSize="20" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="اختر تنسيق التصدير والمحتوى المطلوب" FontSize="12" Foreground="#E3F2FD"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- محتوى النافذة -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="30">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- الجانب الأيسر - خيارات التصدير -->
                <StackPanel Grid.Column="0" Margin="0,0,15,0">
                    <!-- تنسيق التصدير -->
                    <TextBlock Text="📋 تنسيق التصدير" Style="{StaticResource HeaderTextStyle}"/>
                    
                    <StackPanel Name="FormatOptionsPanel">
                        <RadioButton Name="PdfFormatRadio" Content="PDF" Tag="📄" 
                                    ToolTip="ملف PDF قابل للطباعة والمشاركة"
                                    Style="{StaticResource FormatButtonStyle}" IsChecked="True"/>
                        
                        <RadioButton Name="ExcelFormatRadio" Content="Excel" Tag="📊" 
                                    ToolTip="جدول بيانات Excel قابل للتحرير"
                                    Style="{StaticResource FormatButtonStyle}"/>
                        
                        <RadioButton Name="WordFormatRadio" Content="Word" Tag="📝" 
                                    ToolTip="مستند Word قابل للتحرير"
                                    Style="{StaticResource FormatButtonStyle}"/>
                        
                        <RadioButton Name="PowerPointFormatRadio" Content="PowerPoint" Tag="📈" 
                                    ToolTip="عرض تقديمي PowerPoint"
                                    Style="{StaticResource FormatButtonStyle}"/>
                        
                        <RadioButton Name="HtmlFormatRadio" Content="HTML" Tag="🌐" 
                                    ToolTip="صفحة ويب HTML"
                                    Style="{StaticResource FormatButtonStyle}"/>
                    </StackPanel>

                    <!-- محتوى التقرير -->
                    <TextBlock Text="📊 محتوى التقرير" Style="{StaticResource HeaderTextStyle}"/>
                    
                    <StackPanel Name="ContentOptionsPanel">
                        <CheckBox Name="IncludeKPIsCheckBox" Content="المؤشرات الرئيسية (KPIs)" 
                                 Style="{StaticResource ExportOptionStyle}" IsChecked="True"/>
                        
                        <CheckBox Name="IncludeFinancialCheckBox" Content="التقارير المالية" 
                                 Style="{StaticResource ExportOptionStyle}" IsChecked="True"/>
                        
                        <CheckBox Name="IncludeCasesCheckBox" Content="إحصائيات القضايا" 
                                 Style="{StaticResource ExportOptionStyle}" IsChecked="True"/>
                        
                        <CheckBox Name="IncludeClientsCheckBox" Content="تحليل الموكلين" 
                                 Style="{StaticResource ExportOptionStyle}" IsChecked="True"/>
                        
                        <CheckBox Name="IncludeAppointmentsCheckBox" Content="إحصائيات المواعيد" 
                                 Style="{StaticResource ExportOptionStyle}" IsChecked="True"/>
                        
                        <CheckBox Name="IncludePerformanceCheckBox" Content="مؤشرات الأداء" 
                                 Style="{StaticResource ExportOptionStyle}" IsChecked="True"/>
                        
                        <CheckBox Name="IncludeChartsCheckBox" Content="الرسوم البيانية" 
                                 Style="{StaticResource ExportOptionStyle}" IsChecked="True"/>
                        
                        <CheckBox Name="IncludeTrendsCheckBox" Content="تحليل الاتجاهات" 
                                 Style="{StaticResource ExportOptionStyle}" IsChecked="False"/>
                        
                        <CheckBox Name="IncludeComparisonsCheckBox" Content="المقارنات الزمنية" 
                                 Style="{StaticResource ExportOptionStyle}" IsChecked="False"/>
                        
                        <CheckBox Name="IncludeRecommendationsCheckBox" Content="التوصيات والتحليلات" 
                                 Style="{StaticResource ExportOptionStyle}" IsChecked="False"/>
                    </StackPanel>

                    <!-- خيارات إضافية -->
                    <TextBlock Text="⚙️ خيارات إضافية" Style="{StaticResource HeaderTextStyle}"/>
                    
                    <StackPanel Name="AdditionalOptionsPanel">
                        <CheckBox Name="IncludeCoverPageCheckBox" Content="إضافة صفحة غلاف" 
                                 Style="{StaticResource ExportOptionStyle}" IsChecked="True"/>
                        
                        <CheckBox Name="IncludeTableOfContentsCheckBox" Content="إضافة فهرس المحتويات" 
                                 Style="{StaticResource ExportOptionStyle}" IsChecked="True"/>
                        
                        <CheckBox Name="IncludeWatermarkCheckBox" Content="إضافة علامة مائية" 
                                 Style="{StaticResource ExportOptionStyle}" IsChecked="False"/>
                        
                        <CheckBox Name="IncludeFooterCheckBox" Content="إضافة تذييل الصفحة" 
                                 Style="{StaticResource ExportOptionStyle}" IsChecked="True"/>
                        
                        <CheckBox Name="HighQualityChartsCheckBox" Content="رسوم بيانية عالية الجودة" 
                                 Style="{StaticResource ExportOptionStyle}" IsChecked="True"/>
                        
                        <CheckBox Name="CompressFileCheckBox" Content="ضغط الملف" 
                                 Style="{StaticResource ExportOptionStyle}" IsChecked="False"/>
                    </StackPanel>
                </StackPanel>

                <!-- الجانب الأيمن - معاينة التقرير -->
                <StackPanel Grid.Column="1" Margin="15,0,0,0">
                    <TextBlock Text="👁️ معاينة التقرير" Style="{StaticResource HeaderTextStyle}"/>
                    
                    <Border Style="{StaticResource PreviewCardStyle}">
                        <ScrollViewer Name="PreviewScrollViewer" Height="400" VerticalScrollBarVisibility="Auto">
                            <StackPanel Name="PreviewPanel">
                                <!-- سيتم ملؤها ديناميكياً -->
                                <TextBlock Text="📊 تقرير شامل - AvocatPro" FontSize="18" FontWeight="Bold" 
                                          HorizontalAlignment="Center" Margin="0,0,0,20"/>
                                
                                <TextBlock Name="PreviewPeriodText" Text="الفترة: يونيو 2024" FontSize="12" 
                                          HorizontalAlignment="Center" Foreground="#666" Margin="0,0,0,20"/>
                                
                                <StackPanel Name="PreviewContentPanel">
                                    <!-- المحتوى المعاين -->
                                </StackPanel>
                            </StackPanel>
                        </ScrollViewer>
                    </Border>

                    <!-- معلومات الملف -->
                    <Border Style="{StaticResource PreviewCardStyle}">
                        <StackPanel>
                            <TextBlock Text="📁 معلومات الملف" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم الملف:" FontWeight="Bold" Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Name="FileNameText" Text="تقرير_شامل_2024.pdf" Margin="0,0,0,5"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="الحجم المتوقع:" FontWeight="Bold" Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Name="FileSizeText" Text="~2.5 MB" Margin="0,0,0,5"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="عدد الصفحات:" FontWeight="Bold" Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Name="PageCountText" Text="~15 صفحة" Margin="0,0,0,5"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="وقت الإنشاء:" FontWeight="Bold" Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="3" Grid.Column="1" Name="GenerationTimeText" Text="~30 ثانية" Margin="0,0,0,5"/>
                            </Grid>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </ScrollViewer>

        <!-- أزرار الإجراءات -->
        <Border Grid.Row="2" Background="White" Padding="25" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Name="ExportButton" Style="{StaticResource ActionButtonStyle}" Click="ExportButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📤" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="تصدير التقرير"/>
                    </StackPanel>
                </Button>
                
                <Button Name="PreviewButton" Style="{StaticResource ActionButtonStyle}" 
                       Background="#4CAF50" Click="PreviewButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="👁️" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="معاينة كاملة"/>
                    </StackPanel>
                </Button>
                
                <Button Name="SaveTemplateButton" Style="{StaticResource ActionButtonStyle}" 
                       Background="#FF9800" Click="SaveTemplateButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="💾" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ كقالب"/>
                    </StackPanel>
                </Button>
                
                <Button Name="CancelButton" Style="{StaticResource SecondaryButtonStyle}" Click="CancelButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="❌" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="إلغاء"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
