<Page x:Class="AvocatPro.Views.Pages.AdvancedFilesPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="إدارة الملفات المتقدمة" Background="#F8FAFC"
      FlowDirection="RightToLeft">

    <Page.Resources>
        <Style TargetType="Button" x:Key="ActionButtonStyle">
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4" Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Page.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان والأزرار الرئيسية -->
        <Border Grid.Row="0" Background="White" CornerRadius="12" Padding="24" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Text="إدارة الملفات المتقدمة" FontSize="24" FontWeight="Bold" Foreground="#1F2937"/>
                    <TextBlock Text="نظام إدارة الملفات القانونية مع التكامل الإلكتروني" FontSize="14" Foreground="#6B7280"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="AddFileButton" Content="➕ إضافة ملف جديد"
                            Background="#6366F1" Foreground="White" BorderThickness="0"
                            Padding="16,8" FontSize="14" FontWeight="SemiBold"
                            Cursor="Hand" Click="AddFileButton_Click" Margin="0,0,10,0">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" 
                                                    CornerRadius="8" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#5B5BD6"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>

                    <Button x:Name="SyncAllButton" Content="🔄 مزامنة جميع الملفات"
                            Background="#10B981" Foreground="White" BorderThickness="0"
                            Padding="16,8" FontSize="14" FontWeight="SemiBold"
                            Cursor="Hand" Click="SyncAllButton_Click">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" 
                                                    CornerRadius="8" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#059669"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- الإحصائيات -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- إجمالي الملفات -->
            <Border Grid.Column="0" Background="White" CornerRadius="12" Padding="20" Margin="0,0,10,0">
                <Border.Effect>
                    <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <StackPanel Grid.Column="0">
                        <TextBlock x:Name="TotalFilesCount" Text="0" FontSize="28" FontWeight="Bold" Foreground="#1F2937"/>
                        <TextBlock Text="إجمالي الملفات" FontSize="12" Foreground="#6B7280"/>
                    </StackPanel>
                    <Border Grid.Column="1" Background="#6366F1" CornerRadius="8" Padding="8">
                        <TextBlock Text="📁" FontSize="16" Foreground="White"/>
                    </Border>
                </Grid>
            </Border>

            <!-- ملفات نشطة -->
            <Border Grid.Column="1" Background="White" CornerRadius="12" Padding="20" Margin="5,0">
                <Border.Effect>
                    <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <StackPanel Grid.Column="0">
                        <TextBlock x:Name="ActiveFilesCount" Text="0" FontSize="28" FontWeight="Bold" Foreground="#10B981"/>
                        <TextBlock Text="ملفات نشطة" FontSize="12" Foreground="#6B7280"/>
                    </StackPanel>
                    <Border Grid.Column="1" Background="#10B981" CornerRadius="8" Padding="8">
                        <TextBlock Text="✅" FontSize="16" Foreground="White"/>
                    </Border>
                </Grid>
            </Border>

            <!-- في الجلسات -->
            <Border Grid.Column="2" Background="White" CornerRadius="12" Padding="20" Margin="5,0">
                <Border.Effect>
                    <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <StackPanel Grid.Column="0">
                        <TextBlock x:Name="InSessionsCount" Text="0" FontSize="28" FontWeight="Bold" Foreground="#F59E0B"/>
                        <TextBlock Text="في الجلسات" FontSize="12" Foreground="#6B7280"/>
                    </StackPanel>
                    <Border Grid.Column="1" Background="#F59E0B" CornerRadius="8" Padding="8">
                        <TextBlock Text="⚖️" FontSize="16" Foreground="White"/>
                    </Border>
                </Grid>
            </Border>

            <!-- مؤرشفة -->
            <Border Grid.Column="3" Background="White" CornerRadius="12" Padding="20" Margin="5,0">
                <Border.Effect>
                    <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <StackPanel Grid.Column="0">
                        <TextBlock x:Name="ArchivedFilesCount" Text="0" FontSize="28" FontWeight="Bold" Foreground="#6B7280"/>
                        <TextBlock Text="مؤرشفة" FontSize="12" Foreground="#6B7280"/>
                    </StackPanel>
                    <Border Grid.Column="1" Background="#6B7280" CornerRadius="8" Padding="8">
                        <TextBlock Text="📦" FontSize="16" Foreground="White"/>
                    </Border>
                </Grid>
            </Border>

            <!-- متزامنة إلكترونياً -->
            <Border Grid.Column="4" Background="White" CornerRadius="12" Padding="20" Margin="10,0,0,0">
                <Border.Effect>
                    <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <StackPanel Grid.Column="0">
                        <TextBlock x:Name="SyncedFilesCount" Text="0" FontSize="28" FontWeight="Bold" Foreground="#8B5CF6"/>
                        <TextBlock Text="متزامنة إلكترونياً" FontSize="12" Foreground="#6B7280"/>
                    </StackPanel>
                    <Border Grid.Column="1" Background="#8B5CF6" CornerRadius="8" Padding="8">
                        <TextBlock Text="🌐" FontSize="16" Foreground="White"/>
                    </Border>
                </Grid>
            </Border>
        </Grid>

        <!-- شريط البحث والتصفية -->
        <Border Grid.Row="2" Background="White" CornerRadius="16" Padding="20" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="15" ShadowDepth="0" Opacity="0.3"/>
            </Border.Effect>
            
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- الصف الأول -->
                <Grid Grid.Row="0" Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- البحث -->
                    <Border Grid.Column="0" Background="#F9FAFB" CornerRadius="8" Margin="0,0,12,0">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="🔍" FontSize="16" Foreground="#6B7280"
                                       Margin="12,0,8,0" VerticalAlignment="Center"/>
                            <TextBox x:Name="SearchTextBox" Grid.Column="1" Background="Transparent" BorderThickness="0"
                                     Padding="0,12,12,12" FontSize="14"
                                     Text="البحث في الملفات..."
                                     Foreground="#9CA3AF" GotFocus="SearchTextBox_GotFocus"
                                     LostFocus="SearchTextBox_LostFocus" TextChanged="SearchTextBox_TextChanged"/>
                        </Grid>
                    </Border>

                    <!-- تصفية حسب الحالة -->
                    <ComboBox x:Name="StatusFilterComboBox" Grid.Column="1" Background="#F9FAFB"
                              BorderBrush="#D1D5DB" BorderThickness="1" Padding="12" FontSize="14"
                              Margin="0,0,12,0" SelectionChanged="StatusFilter_SelectionChanged">
                        <ComboBoxItem Content="جميع الحالات" IsSelected="True"/>
                        <ComboBoxItem Content="نشط"/>
                        <ComboBoxItem Content="في الجلسات"/>
                        <ComboBoxItem Content="مؤرشف"/>
                        <ComboBoxItem Content="مغلق"/>
                        <ComboBoxItem Content="معلق"/>
                    </ComboBox>

                    <!-- تصفية حسب نوع القضية -->
                    <ComboBox x:Name="CaseTypeFilterComboBox" Grid.Column="2" Background="#F9FAFB"
                              BorderBrush="#D1D5DB" BorderThickness="1" Padding="12" FontSize="14"
                              Margin="0,0,12,0" SelectionChanged="CaseTypeFilter_SelectionChanged"/>

                    <!-- تصفية حسب المحكمة -->
                    <ComboBox x:Name="CourtFilterComboBox" Grid.Column="3" Background="#F9FAFB"
                              BorderBrush="#D1D5DB" BorderThickness="1" Padding="12" FontSize="14"
                              Margin="0,0,12,0" SelectionChanged="CourtFilter_SelectionChanged"/>

                    <!-- أزرار الإجراءات -->
                    <StackPanel Grid.Column="4" Orientation="Horizontal">
                        <Button x:Name="ExportButton" Content="📤" Background="#10B981" Foreground="White"
                                BorderThickness="0" Width="40" Height="40" FontSize="16"
                                Cursor="Hand" Margin="0,0,8,0" Click="Export_Click"
                                ToolTip="تصدير البيانات"/>

                        <Button x:Name="PrintButton" Content="🖨️" Background="#6366F1" Foreground="White"
                                BorderThickness="0" Width="40" Height="40" FontSize="16"
                                Cursor="Hand" Click="Print_Click"
                                ToolTip="طباعة القائمة"/>
                    </StackPanel>
                </Grid>

                <!-- الصف الثاني -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- تصفية حسب الأولوية -->
                    <ComboBox x:Name="PriorityFilterComboBox" Grid.Column="0" Background="#F9FAFB"
                              BorderBrush="#D1D5DB" BorderThickness="1" Padding="12" FontSize="14"
                              Margin="0,0,12,0" SelectionChanged="PriorityFilter_SelectionChanged">
                        <ComboBoxItem Content="جميع الأولويات" IsSelected="True"/>
                        <ComboBoxItem Content="عاجل"/>
                        <ComboBoxItem Content="مهم"/>
                        <ComboBoxItem Content="عادي"/>
                    </ComboBox>

                    <!-- تصفية حسب التتبع الإلكتروني -->
                    <ComboBox x:Name="SyncFilterComboBox" Grid.Column="1" Background="#F9FAFB"
                              BorderBrush="#D1D5DB" BorderThickness="1" Padding="12" FontSize="14"
                              Margin="0,0,12,0" SelectionChanged="SyncFilter_SelectionChanged">
                        <ComboBoxItem Content="جميع الملفات" IsSelected="True"/>
                        <ComboBoxItem Content="متزامنة إلكترونياً"/>
                        <ComboBoxItem Content="غير متزامنة"/>
                    </ComboBox>

                    <!-- تصفية حسب التاريخ -->
                    <DatePicker x:Name="DateFromPicker" Grid.Column="2" Background="#F9FAFB"
                                BorderBrush="#D1D5DB" BorderThickness="1" Padding="12" FontSize="14"
                                Margin="0,0,12,0" SelectedDateChanged="DateFilter_Changed"/>

                    <DatePicker x:Name="DateToPicker" Grid.Column="3" Background="#F9FAFB"
                                BorderBrush="#D1D5DB" BorderThickness="1" Padding="12" FontSize="14"
                                SelectedDateChanged="DateFilter_Changed"/>
                </Grid>
            </Grid>
        </Border>

        <!-- جدول الملفات -->
        <Border Grid.Row="3" Background="White" CornerRadius="16" Padding="0">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="15" ShadowDepth="0" Opacity="0.3"/>
            </Border.Effect>
            
            <DataGrid x:Name="FilesDataGrid" Background="Transparent" BorderThickness="0"
                      GridLinesVisibility="None" HeadersVisibility="Column"
                      AutoGenerateColumns="False" CanUserAddRows="False"
                      CanUserDeleteRows="False" IsReadOnly="True"
                      SelectionMode="Single" SelectionUnit="FullRow"
                      MouseDoubleClick="FilesDataGrid_MouseDoubleClick">

                <DataGrid.ColumnHeaderStyle>
                    <Style TargetType="DataGridColumnHeader">
                        <Setter Property="Background" Value="#F8FAFC"/>
                        <Setter Property="Foreground" Value="#374151"/>
                        <Setter Property="FontWeight" Value="SemiBold"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="Padding" Value="16,12"/>
                        <Setter Property="BorderThickness" Value="0,0,0,1"/>
                        <Setter Property="BorderBrush" Value="#E5E7EB"/>
                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                    </Style>
                </DataGrid.ColumnHeaderStyle>

                <DataGrid.Columns>
                    <!-- رقم الملف -->
                    <DataGridTextColumn Header="رقم الملف" Binding="{Binding FileNumber}" Width="120"/>
                    
                    <!-- الموكل -->
                    <DataGridTextColumn Header="الموكل" Binding="{Binding Client}" Width="150"/>
                    
                    <!-- نوع القضية -->
                    <DataGridTextColumn Header="نوع القضية" Binding="{Binding CaseType}" Width="100"/>
                    
                    <!-- المحكمة -->
                    <DataGridTextColumn Header="المحكمة" Binding="{Binding Court}" Width="180"/>
                    
                    <!-- الموضوع -->
                    <DataGridTextColumn Header="الموضوع" Binding="{Binding Subject}" Width="200"/>
                    
                    <!-- الحالة -->
                    <DataGridTemplateColumn Header="الحالة" Width="100">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border Background="{Binding StatusColor}" CornerRadius="12" Padding="8,4">
                                    <TextBlock Text="{Binding Status}" Foreground="White" FontWeight="SemiBold" 
                                               HorizontalAlignment="Center" FontSize="12"/>
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    
                    <!-- الأولوية -->
                    <DataGridTemplateColumn Header="الأولوية" Width="80">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border Background="{Binding PriorityColor}" CornerRadius="8" Padding="6,3">
                                    <TextBlock Text="{Binding Priority}" Foreground="White" FontWeight="SemiBold" 
                                               HorizontalAlignment="Center" FontSize="11"/>
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    
                    <!-- التتبع الإلكتروني -->
                    <DataGridTemplateColumn Header="التتبع" Width="80">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding SyncStatusIcon}" HorizontalAlignment="Center" 
                                           FontSize="16" ToolTip="{Binding SyncStatus}"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    
                    <!-- الجلسة المقبلة -->
                    <DataGridTextColumn Header="الجلسة المقبلة" Binding="{Binding NextSessionText}" Width="120"/>
                    
                    <!-- تاريخ الإنشاء -->
                    <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedDate, StringFormat=dd/MM/yyyy}" Width="120"/>
                    
                    <!-- الإجراءات -->
                    <DataGridTemplateColumn Header="الإجراءات" Width="200">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button Content="✏️" Background="#6366F1" Foreground="White" 
                                            Style="{StaticResource ActionButtonStyle}"
                                            Click="EditFile_Click" ToolTip="تعديل"/>
                                    <Button Content="🗑️" Background="#EF4444" Foreground="White" 
                                            Style="{StaticResource ActionButtonStyle}"
                                            Click="DeleteFile_Click" ToolTip="حذف"/>
                                    <Button Content="🖨️" Background="#10B981" Foreground="White" 
                                            Style="{StaticResource ActionButtonStyle}"
                                            Click="PrintFile_Click" ToolTip="طباعة"/>
                                    <Button Content="🔄" Background="#F59E0B" Foreground="White" 
                                            Style="{StaticResource ActionButtonStyle}"
                                            Click="SyncFile_Click" ToolTip="مزامنة"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>
    </Grid>
</Page>
