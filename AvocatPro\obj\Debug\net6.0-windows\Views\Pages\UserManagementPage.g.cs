﻿#pragma checksum "..\..\..\..\..\Views\Pages\UserManagementPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "B8BD5A5B72081D6BB99C26648B79F10E72DAEE5F"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using FontAwesome.WPF;
using FontAwesome.WPF.Converters;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Pages {
    
    
    /// <summary>
    /// UserManagementPage
    /// </summary>
    public partial class UserManagementPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 81 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UsersCountText;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddUserBtn;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ManageRolesBtn;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button UserReportsBtn;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshBtn;
        
        #line default
        #line hidden
        
        
        #line 139 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusFilterCombo;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox RoleFilterCombo;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DateFilterCombo;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton CardViewBtn;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton ListViewBtn;
        
        #line default
        #line hidden
        
        
        #line 190 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer CardViewScroller;
        
        #line default
        #line hidden
        
        
        #line 191 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.WrapPanel UsersCardsPanel;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid UsersDataGrid;
        
        #line default
        #line hidden
        
        
        #line 240 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border NoDataPanel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/pages/usermanagementpage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.UsersCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.AddUserBtn = ((System.Windows.Controls.Button)(target));
            
            #line 85 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
            this.AddUserBtn.Click += new System.Windows.RoutedEventHandler(this.AddUserBtn_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ManageRolesBtn = ((System.Windows.Controls.Button)(target));
            
            #line 92 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
            this.ManageRolesBtn.Click += new System.Windows.RoutedEventHandler(this.ManageRolesBtn_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.UserReportsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 99 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
            this.UserReportsBtn.Click += new System.Windows.RoutedEventHandler(this.UserReportsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.RefreshBtn = ((System.Windows.Controls.Button)(target));
            
            #line 106 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
            this.RefreshBtn.Click += new System.Windows.RoutedEventHandler(this.RefreshBtn_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 140 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.StatusFilterCombo = ((System.Windows.Controls.ComboBox)(target));
            
            #line 147 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
            this.StatusFilterCombo.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.StatusFilterCombo_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.RoleFilterCombo = ((System.Windows.Controls.ComboBox)(target));
            
            #line 157 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
            this.RoleFilterCombo.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.RoleFilterCombo_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.DateFilterCombo = ((System.Windows.Controls.ComboBox)(target));
            
            #line 163 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
            this.DateFilterCombo.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DateFilterCombo_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.CardViewBtn = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 175 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
            this.CardViewBtn.Checked += new System.Windows.RoutedEventHandler(this.ViewModeChanged);
            
            #line default
            #line hidden
            
            #line 175 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
            this.CardViewBtn.Unchecked += new System.Windows.RoutedEventHandler(this.ViewModeChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ListViewBtn = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 180 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
            this.ListViewBtn.Checked += new System.Windows.RoutedEventHandler(this.ViewModeChanged);
            
            #line default
            #line hidden
            
            #line 180 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
            this.ListViewBtn.Unchecked += new System.Windows.RoutedEventHandler(this.ViewModeChanged);
            
            #line default
            #line hidden
            return;
            case 12:
            this.CardViewScroller = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 13:
            this.UsersCardsPanel = ((System.Windows.Controls.WrapPanel)(target));
            return;
            case 14:
            this.UsersDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 18:
            this.NoDataPanel = ((System.Windows.Controls.Border)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 15:
            
            #line 225 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditUser_Click);
            
            #line default
            #line hidden
            break;
            case 16:
            
            #line 228 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ChatUser_Click);
            
            #line default
            #line hidden
            break;
            case 17:
            
            #line 231 "..\..\..\..\..\Views\Pages\UserManagementPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.UserReport_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

