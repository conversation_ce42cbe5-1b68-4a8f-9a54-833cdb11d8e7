<Page x:Class="AvocatPro.Views.Pages.AdvancedDocumentPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="إدارة الوثائق المتطورة" FlowDirection="RightToLeft"
      Background="#F8FAFC">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان والأدوات -->
        <Border Grid.Row="0" Background="White" CornerRadius="15" Padding="20" Margin="20,20,20,10">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border CornerRadius="50" Width="60" Height="60" Margin="0,0,20,0">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#8B5CF6" Offset="0"/>
                                <GradientStop Color="#7C3AED" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <Border.Effect>
                            <DropShadowEffect Color="#8B5CF6" Opacity="0.4" BlurRadius="15" ShadowDepth="0"/>
                        </Border.Effect>
                        <TextBlock Text="📄" FontSize="30" Foreground="White" 
                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <StackPanel>
                        <TextBlock Text="إدارة الوثائق المتطورة" FontSize="24" FontWeight="Bold" Foreground="#1F2937"/>
                        <TextBlock Text="OCR، التوقيع الإلكتروني، المشاركة الآمنة والقوالب الجاهزة" FontSize="14" Foreground="#6B7280"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="UploadDocumentBtn" Background="#10B981" Foreground="White" Padding="12,8" 
                            Margin="0,0,10,0" Click="UploadDocument_Click" BorderThickness="0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📤" FontSize="14" Margin="0,0,8,0"/>
                            <TextBlock Text="رفع وثيقة"/>
                        </StackPanel>
                    </Button>
                    <Button x:Name="ScanDocumentBtn" Background="#3B82F6" Foreground="White" Padding="12,8" 
                            Margin="0,0,10,0" Click="ScanDocument_Click" BorderThickness="0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📷" FontSize="14" Margin="0,0,8,0"/>
                            <TextBlock Text="مسح ضوئي"/>
                        </StackPanel>
                    </Button>
                    <Button x:Name="CreateFromTemplateBtn" Background="#F59E0B" Foreground="White" Padding="12,8" 
                            Click="CreateFromTemplate_Click" BorderThickness="0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📋" FontSize="14" Margin="0,0,8,0"/>
                            <TextBlock Text="من قالب"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1" Margin="20,0,20,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="20"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- الشريط الجانبي -->
            <Border Grid.Column="0" Background="White" CornerRadius="15" Padding="20">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                </Border.Effect>
                <StackPanel>
                    <!-- البحث -->
                    <StackPanel Margin="0,0,0,20">
                        <TextBlock Text="🔍 البحث في الوثائق" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,10"/>
                        <TextBox x:Name="SearchTextBox" Padding="10,8" FontSize="14" 
                                 TextChanged="SearchTextBox_TextChanged" 
                                 Text="ابحث في الوثائق..."/>
                    </StackPanel>

                    <!-- التصفية -->
                    <StackPanel Margin="0,0,0,20">
                        <TextBlock Text="📂 تصفية حسب الفئة" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,10"/>
                        <ComboBox x:Name="CategoryFilterComboBox" Padding="10,8" FontSize="14" 
                                  SelectionChanged="CategoryFilter_SelectionChanged">
                            <ComboBoxItem Content="جميع الفئات" IsSelected="True"/>
                            <ComboBoxItem Content="عقود"/>
                            <ComboBoxItem Content="توكيلات"/>
                            <ComboBoxItem Content="إنذارات قانونية"/>
                            <ComboBoxItem Content="وثائق المحكمة"/>
                            <ComboBoxItem Content="أدلة"/>
                            <ComboBoxItem Content="وثائق العملاء"/>
                        </ComboBox>
                    </StackPanel>

                    <!-- الحالة -->
                    <StackPanel Margin="0,0,0,20">
                        <TextBlock Text="✅ حالة الوثيقة" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,10"/>
                        <StackPanel>
                            <CheckBox x:Name="SignedDocumentsCheckBox" Content="وثائق موقعة" Margin="0,0,0,5" 
                                      Checked="DocumentFilter_Changed" Unchecked="DocumentFilter_Changed"/>
                            <CheckBox x:Name="EncryptedDocumentsCheckBox" Content="وثائق مشفرة" Margin="0,0,0,5"
                                      Checked="DocumentFilter_Changed" Unchecked="DocumentFilter_Changed"/>
                            <CheckBox x:Name="SharedDocumentsCheckBox" Content="وثائق مشاركة" Margin="0,0,0,5"
                                      Checked="DocumentFilter_Changed" Unchecked="DocumentFilter_Changed"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- إحصائيات سريعة -->
                    <StackPanel>
                        <TextBlock Text="📊 إحصائيات سريعة" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,10"/>
                        <StackPanel x:Name="QuickStatsPanel">
                            <!-- سيتم إضافة الإحصائيات ديناميكياً -->
                        </StackPanel>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- منطقة الوثائق الرئيسية -->
            <Border Grid.Column="2" Background="White" CornerRadius="15" Padding="20">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                </Border.Effect>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- شريط الأدوات العلوي -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
                        <TextBlock Text="📄 الوثائق" FontSize="18" FontWeight="SemiBold" VerticalAlignment="Center" Margin="0,0,20,0"/>
                        
                        <ComboBox x:Name="SortComboBox" Width="150" Padding="8" Margin="0,0,10,0" 
                                  SelectionChanged="Sort_SelectionChanged">
                            <ComboBoxItem Content="الأحدث أولاً" IsSelected="True"/>
                            <ComboBoxItem Content="الأقدم أولاً"/>
                            <ComboBoxItem Content="حسب الاسم"/>
                            <ComboBoxItem Content="حسب الحجم"/>
                        </ComboBox>

                        <ComboBox x:Name="ViewModeComboBox" Width="120" Padding="8" Margin="0,0,10,0"
                                  SelectionChanged="ViewMode_SelectionChanged">
                            <ComboBoxItem Content="عرض شبكي" IsSelected="True"/>
                            <ComboBoxItem Content="عرض قائمة"/>
                            <ComboBoxItem Content="عرض تفصيلي"/>
                        </ComboBox>

                        <Button x:Name="RefreshDocumentsBtn" Background="#6B7280" Foreground="White" Padding="8" 
                                Click="RefreshDocuments_Click" BorderThickness="0">
                            <TextBlock Text="🔄"/>
                        </Button>
                    </StackPanel>

                    <!-- قائمة الوثائق -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <StackPanel x:Name="DocumentsPanel">
                            <!-- سيتم إضافة الوثائق ديناميكياً -->
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>

        <!-- مؤشر التحميل -->
        <Border x:Name="LoadingOverlay" Grid.RowSpan="2" Background="Black" Opacity="0.5" Visibility="Collapsed">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="300" Height="6" 
                             Foreground="#8B5CF6" Background="Transparent"/>
                <TextBlock Text="جاري معالجة الوثائق..." Foreground="White" 
                           HorizontalAlignment="Center" Margin="0,20,0,0" FontSize="18"/>
            </StackPanel>
        </Border>
    </Grid>
</Page>
