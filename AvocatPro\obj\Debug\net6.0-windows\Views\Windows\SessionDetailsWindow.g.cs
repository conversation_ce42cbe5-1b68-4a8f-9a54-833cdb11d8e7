﻿#pragma checksum "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "D44BC70E8374A748569B3C3C2DBCF18EFC23E6DA"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Windows {
    
    
    /// <summary>
    /// SessionDetailsWindow
    /// </summary>
    public partial class SessionDetailsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 68 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SessionTitleText;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SessionSubtitleText;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border StatusBorder;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusIcon;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SessionNumberValue;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileNumberValue;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ClientValue;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CaseTypeValue;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CourtValue;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SessionDateValue;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SessionTimeValue;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SessionTypeValue;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AssignedLawyerValue;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock JudgeValue;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CourtRoomValue;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border PriorityBorder;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PriorityIcon;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PriorityValue;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ExpensesValue;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProcedureTypeValue;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProcedureDateValue;
        
        #line default
        #line hidden
        
        
        #line 205 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DecisionValue;
        
        #line default
        #line hidden
        
        
        #line 208 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OutcomeValue;
        
        #line default
        #line hidden
        
        
        #line 223 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NextSessionValue;
        
        #line default
        #line hidden
        
        
        #line 227 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReminderIcon;
        
        #line default
        #line hidden
        
        
        #line 228 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReminderStatusValue;
        
        #line default
        #line hidden
        
        
        #line 244 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DocumentsValue;
        
        #line default
        #line hidden
        
        
        #line 248 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NotesValue;
        
        #line default
        #line hidden
        
        
        #line 263 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditButton;
        
        #line default
        #line hidden
        
        
        #line 265 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintButton;
        
        #line default
        #line hidden
        
        
        #line 267 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/windows/sessiondetailswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SessionTitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.SessionSubtitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.StatusBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 4:
            this.StatusIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.SessionNumberValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.FileNumberValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.ClientValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.CaseTypeValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.CourtValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.SessionDateValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.SessionTimeValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.SessionTypeValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.AssignedLawyerValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.JudgeValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.CourtRoomValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.PriorityBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 18:
            this.PriorityIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.PriorityValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.ExpensesValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.ProcedureTypeValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.ProcedureDateValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.DecisionValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.OutcomeValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.NextSessionValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.ReminderIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 27:
            this.ReminderStatusValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.DocumentsValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 29:
            this.NotesValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            this.EditButton = ((System.Windows.Controls.Button)(target));
            
            #line 264 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
            this.EditButton.Click += new System.Windows.RoutedEventHandler(this.EditButton_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            this.PrintButton = ((System.Windows.Controls.Button)(target));
            
            #line 266 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
            this.PrintButton.Click += new System.Windows.RoutedEventHandler(this.PrintButton_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 268 "..\..\..\..\..\Views\Windows\SessionDetailsWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

