﻿#pragma checksum "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "98DC1B53358FCED1052C6BF050CD02E346133F0F"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Pages {
    
    
    /// <summary>
    /// SecurityManagementPage
    /// </summary>
    public partial class SecurityManagementPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 45 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshBtn;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SettingsBtn;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TwoFactorStatusText;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EnableTwoFactorBtn;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EncryptionStatusText;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EncryptionSettingsBtn;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BackupStatusText;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreateBackupBtn;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AuditLogCountText;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewAuditLogBtn;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel SecurityAlertsPanel;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ActivityStatsPanel;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoLockCheckBox;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox LoginNotificationCheckBox;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox DataEncryptionCheckBox;
        
        #line default
        #line hidden
        
        
        #line 205 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AuditLoggingCheckBox;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveSecuritySettingsBtn;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border LoadingOverlay;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/pages/securitymanagementpage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.RefreshBtn = ((System.Windows.Controls.Button)(target));
            
            #line 46 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
            this.RefreshBtn.Click += new System.Windows.RoutedEventHandler(this.Refresh_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SettingsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 53 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
            this.SettingsBtn.Click += new System.Windows.RoutedEventHandler(this.Settings_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.TwoFactorStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.EnableTwoFactorBtn = ((System.Windows.Controls.Button)(target));
            
            #line 90 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
            this.EnableTwoFactorBtn.Click += new System.Windows.RoutedEventHandler(this.EnableTwoFactor_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.EncryptionStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.EncryptionSettingsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 108 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
            this.EncryptionSettingsBtn.Click += new System.Windows.RoutedEventHandler(this.EncryptionSettings_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.BackupStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.CreateBackupBtn = ((System.Windows.Controls.Button)(target));
            
            #line 126 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
            this.CreateBackupBtn.Click += new System.Windows.RoutedEventHandler(this.CreateBackup_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.AuditLogCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.ViewAuditLogBtn = ((System.Windows.Controls.Button)(target));
            
            #line 144 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
            this.ViewAuditLogBtn.Click += new System.Windows.RoutedEventHandler(this.ViewAuditLog_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.SecurityAlertsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 12:
            this.ActivityStatsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 13:
            this.AutoLockCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 14:
            this.LoginNotificationCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 15:
            this.DataEncryptionCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 16:
            this.AuditLoggingCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 17:
            this.SaveSecuritySettingsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 207 "..\..\..\..\..\Views\Pages\SecurityManagementPage.xaml"
            this.SaveSecuritySettingsBtn.Click += new System.Windows.RoutedEventHandler(this.SaveSecuritySettings_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.LoadingOverlay = ((System.Windows.Controls.Border)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

