﻿#pragma checksum "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "93B68615192400A05E3D1EAE410C13C817B2B0AF"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Windows {
    
    
    /// <summary>
    /// AdvancedAddFileWindow
    /// </summary>
    public partial class AdvancedAddFileWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 72 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WindowTitle;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SyncButton;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FileNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OfficeReferenceTextBox;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CourtReferenceTextBox;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FileTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CourtComboBox;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CaseTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OpponentTextBox;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SubjectTextBox;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ClientComboBox;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LawyerComboBox;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FileValueTextBox;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PriorityComboBox;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CategoryTextBox;
        
        #line default
        #line hidden
        
        
        #line 191 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ProcedureTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DecisionTextBox;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker NextSessionDatePicker;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ElectronicTrackingCheckBox;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ElectronicTrackingPanel;
        
        #line default
        #line hidden
        
        
        #line 234 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ElectronicFileNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 237 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ElectronicFileCodeTextBox;
        
        #line default
        #line hidden
        
        
        #line 240 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ElectronicYearTextBox;
        
        #line default
        #line hidden
        
        
        #line 242 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox SearchInPrimaryCourtsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 249 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AppealCourtComboBox;
        
        #line default
        #line hidden
        
        
        #line 251 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SearchElectronicButton;
        
        #line default
        #line hidden
        
        
        #line 276 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SyncStatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 293 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 317 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/windows/advancedaddfilewindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.WindowTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.SyncButton = ((System.Windows.Controls.Button)(target));
            
            #line 79 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
            this.SyncButton.Click += new System.Windows.RoutedEventHandler(this.SyncButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.FileNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.OfficeReferenceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.CourtReferenceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.FileTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.CourtComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.CaseTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.OpponentTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.SubjectTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.ClientComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 12:
            this.LawyerComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 13:
            this.FileValueTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.PriorityComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 15:
            this.StatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 16:
            this.CategoryTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.ProcedureTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 18:
            this.DecisionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.NextSessionDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 20:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.ElectronicTrackingCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 221 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
            this.ElectronicTrackingCheckBox.Checked += new System.Windows.RoutedEventHandler(this.ElectronicTrackingCheckBox_Checked);
            
            #line default
            #line hidden
            
            #line 222 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
            this.ElectronicTrackingCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.ElectronicTrackingCheckBox_Unchecked);
            
            #line default
            #line hidden
            return;
            case 22:
            this.ElectronicTrackingPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 23:
            this.ElectronicFileNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 24:
            this.ElectronicFileCodeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 25:
            this.ElectronicYearTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 26:
            this.SearchInPrimaryCourtsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 27:
            this.AppealCourtComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 28:
            this.SearchElectronicButton = ((System.Windows.Controls.Button)(target));
            
            #line 254 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
            this.SearchElectronicButton.Click += new System.Windows.RoutedEventHandler(this.SearchElectronicButton_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.SyncStatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 295 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 319 "..\..\..\..\..\Views\Windows\AdvancedAddFileWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

