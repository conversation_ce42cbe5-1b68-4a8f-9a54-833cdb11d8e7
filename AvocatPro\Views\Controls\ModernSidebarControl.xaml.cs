using System;
using System.Windows;
using System.Windows.Controls;

namespace AvocatPro.Views.Controls
{
    /// <summary>
    /// القائمة الجانبية الحديثة للتنقل
    /// </summary>
    public partial class ModernSidebarControl : UserControl
    {
        // أحداث التنقل
        public event EventHandler<string> NavigationRequested;

        public ModernSidebarControl()
        {
            InitializeComponent();
            LoadUserInfo();
        }

        /// <summary>
        /// تحميل معلومات المستخدم الحالي
        /// </summary>
        private void LoadUserInfo()
        {
            try
            {
                // هنا يمكن تحميل معلومات المستخدم من قاعدة البيانات
                // مثال: اسم المستخدم، الصورة الشخصية، الصلاحيات
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل معلومات المستخدم: {ex.Message}", 
                               "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديد العنصر النشط في القائمة
        /// </summary>
        public void SetActiveItem(string itemName)
        {
            try
            {
                // إزالة التحديد من جميع العناصر
                ClearActiveItems();

                // تحديد العنصر المطلوب كنشط
                // يمكن البحث عن الزر بالاسم وتعيين Tag="Active"
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديد العنصر النشط: {ex.Message}", 
                               "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إزالة التحديد من جميع عناصر القائمة
        /// </summary>
        private void ClearActiveItems()
        {
            // البحث عن جميع الأزرار وإزالة Tag="Active"
        }

        /// <summary>
        /// معالج النقر على لوحة التحكم
        /// </summary>
        private void Dashboard_Click(object sender, RoutedEventArgs e)
        {
            NavigationRequested?.Invoke(this, "Dashboard");
            SetActiveButton(sender as Button);
        }

        /// <summary>
        /// معالج النقر على إدارة الموكلين
        /// </summary>
        private void Clients_Click(object sender, RoutedEventArgs e)
        {
            NavigationRequested?.Invoke(this, "ComprehensiveClients");
            SetActiveButton(sender as Button);
        }

        /// <summary>
        /// معالج النقر على إدارة الملفات
        /// </summary>
        private void Files_Click(object sender, RoutedEventArgs e)
        {
            NavigationRequested?.Invoke(this, "Files");
            SetActiveButton(sender as Button);
        }

        /// <summary>
        /// معالج النقر على الملفات الشاملة
        /// </summary>
        private void ComprehensiveFiles_Click(object sender, RoutedEventArgs e)
        {
            NavigationRequested?.Invoke(this, "ComprehensiveFiles");
            SetActiveButton(sender as Button);
        }

        /// <summary>
        /// معالج النقر على إدارة الجلسات
        /// </summary>
        private void Sessions_Click(object sender, RoutedEventArgs e)
        {
            NavigationRequested?.Invoke(this, "Sessions");
            SetActiveButton(sender as Button);
        }

        /// <summary>
        /// معالج النقر على إدارة المواعيد
        /// </summary>
        private void Appointments_Click(object sender, RoutedEventArgs e)
        {
            NavigationRequested?.Invoke(this, "Appointments");
            SetActiveButton(sender as Button);
        }

        /// <summary>
        /// معالج النقر على طباعة الشيكات والأظرفة
        /// </summary>
        private void CheckPrinting_Click(object sender, RoutedEventArgs e)
        {
            NavigationRequested?.Invoke(this, "CheckPrinting");
            SetActiveButton(sender as Button);
        }

        /// <summary>
        /// معالج النقر على إدارة الفواتير
        /// </summary>
        private void Invoices_Click(object sender, RoutedEventArgs e)
        {
            NavigationRequested?.Invoke(this, "Invoices");
            SetActiveButton(sender as Button);
        }

        /// <summary>
        /// معالج النقر على التقارير المالية
        /// </summary>
        private void Reports_Click(object sender, RoutedEventArgs e)
        {
            NavigationRequested?.Invoke(this, "Reports");
            SetActiveButton(sender as Button);
        }

        /// <summary>
        /// معالج النقر على إدارة المستخدمين المتقدمة
        /// </summary>
        private void AdvancedUsers_Click(object sender, RoutedEventArgs e)
        {
            NavigationRequested?.Invoke(this, "AdvancedUsers");
            SetActiveButton(sender as Button);
        }

        /// <summary>
        /// معالج النقر على النسخ الاحتياطي
        /// </summary>
        private void Backup_Click(object sender, RoutedEventArgs e)
        {
            NavigationRequested?.Invoke(this, "Backup");
            SetActiveButton(sender as Button);
        }

        /// <summary>
        /// معالج النقر على الإعدادات العامة
        /// </summary>
        private void Settings_Click(object sender, RoutedEventArgs e)
        {
            NavigationRequested?.Invoke(this, "Settings");
            SetActiveButton(sender as Button);
        }

        /// <summary>
        /// معالج النقر على المساعدة
        /// </summary>
        private void Help_Click(object sender, RoutedEventArgs e)
        {
            NavigationRequested?.Invoke(this, "Help");
            SetActiveButton(sender as Button);
        }

        /// <summary>
        /// معالج النقر على حول البرنامج
        /// </summary>
        private void About_Click(object sender, RoutedEventArgs e)
        {
            NavigationRequested?.Invoke(this, "About");
            SetActiveButton(sender as Button);
        }

        /// <summary>
        /// معالج النقر على تسجيل الخروج
        /// </summary>
        private void Logout_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show("هل أنت متأكد من تسجيل الخروج؟", 
                                           "تأكيد تسجيل الخروج", 
                                           MessageBoxButton.YesNo, 
                                           MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // تنفيذ عملية تسجيل الخروج
                    NavigationRequested?.Invoke(this, "Logout");
                    
                    // يمكن هنا إغلاق النافذة الحالية وفتح نافذة تسجيل الدخول
                    Application.Current.MainWindow?.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسجيل الخروج: {ex.Message}", 
                               "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تعيين الزر كنشط
        /// </summary>
        private void SetActiveButton(Button clickedButton)
        {
            try
            {
                if (clickedButton == null) return;

                // إزالة التحديد من جميع الأزرار
                ClearAllActiveButtons();

                // تعيين الزر المنقور كنشط
                clickedButton.Tag = "Active";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديد الزر النشط: {ex.Message}", 
                               "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إزالة التحديد من جميع الأزرار
        /// </summary>
        private void ClearAllActiveButtons()
        {
            try
            {
                // البحث عن جميع الأزرار في القائمة وإزالة Tag="Active"
                foreach (var child in LogicalTreeHelper.GetChildren(this))
                {
                    if (child is Button button)
                    {
                        button.Tag = null;
                    }
                }
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ دون إظهار رسالة للمستخدم
                System.Diagnostics.Debug.WriteLine($"خطأ في إزالة التحديد: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث عداد الإشعارات
        /// </summary>
        public void UpdateNotificationCount(int count)
        {
            try
            {
                // يمكن إضافة عداد للإشعارات في القائمة الجانبية
                // مثل عدد الجلسات القادمة، المواعيد، الرسائل الجديدة
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث عداد الإشعارات: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث معلومات المستخدم
        /// </summary>
        public void UpdateUserInfo(string userName, string userRole)
        {
            try
            {
                // تحديث اسم المستخدم ودوره في القائمة الجانبية
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث معلومات المستخدم: {ex.Message}");
            }
        }
    }
}
