using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using AvocatPro.Models;
using AvocatPro.Views.Windows;

namespace AvocatPro.Views.Pages;

public partial class CasesPageNew : Page
{
    private readonly User _currentUser;
    private readonly ObservableCollection<CaseDisplayModel> _allCases;
    private readonly ObservableCollection<CaseDisplayModel> _filteredCases;

    public CasesPageNew(User currentUser)
    {
        InitializeComponent();
        _currentUser = currentUser;
        _allCases = new ObservableCollection<CaseDisplayModel>();
        _filteredCases = new ObservableCollection<CaseDisplayModel>();
        
        // تأكد من أن العناصر محملة قبل الوصول إليها
        this.Loaded += CasesPageNew_Loaded;
    }

    private void CasesPageNew_Loaded(object sender, RoutedEventArgs e)
    {
        try
        {
            if (CasesDataGrid != null)
            {
                CasesDataGrid.ItemsSource = _filteredCases;
            }
            
            LoadSampleData();
            SetupSearchBox();
            UpdateStatistics();
            UpdateStatusText();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل صفحة الملفات: {ex.Message}", "خطأ", 
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void LoadSampleData()
    {
        var sampleCases = new List<CaseDisplayModel>
        {
            new CaseDisplayModel
            {
                Id = 1,
                OfficeReference = "CASE-20241201-001",
                Title = "دعوى مطالبة مالية",
                Type = CaseType.Civil,
                Status = CaseStatus.Active,
                Court = "المحكمة العامة بالرياض",
                ClientName = "أحمد محمد علي السعيد",
                LawyerName = "د. محمد أحمد الشريف",
                Opponent = "شركة الأمل التجارية",
                Subject = "مطالبة بمبلغ 500,000 ريال قيمة صفقة تجارية",
                Priority = CasePriority.High,
                StartDate = DateTime.Now.AddDays(-45),
                NextSessionDate = DateTime.Now.AddDays(15),
                FinancialValue = 500000,
                LawyerFees = 50000,
                Stage = "مرحلة المرافعة"
            },
            new CaseDisplayModel
            {
                Id = 2,
                OfficeReference = "CASE-20241215-002",
                Title = "قضية عمالية - فصل تعسفي",
                Type = CaseType.Labor,
                Status = CaseStatus.Active,
                Court = "محكمة العمل بالرياض",
                ClientName = "فاطمة أحمد سالم",
                LawyerName = "أ. سارة عبدالله النور",
                Opponent = "شركة البناء الحديث",
                Subject = "دعوى فصل تعسفي ومطالبة بالتعويض",
                Priority = CasePriority.Medium,
                StartDate = DateTime.Now.AddDays(-30),
                NextSessionDate = DateTime.Now.AddDays(7),
                FinancialValue = 150000,
                LawyerFees = 25000,
                Stage = "مرحلة تقديم البينات"
            },
            new CaseDisplayModel
            {
                Id = 3,
                OfficeReference = "CASE-20241101-003",
                Title = "قضية تجارية - نزاع شراكة",
                Type = CaseType.Commercial,
                Status = CaseStatus.Closed,
                Court = "المحكمة التجارية بجدة",
                ClientName = "شركة النور للتجارة والاستثمار",
                LawyerName = "أ. خالد سالم العتيبي",
                Opponent = "شركة الشرق للاستثمار",
                Subject = "نزاع حول اتفاقية شراكة تجارية",
                Priority = CasePriority.High,
                StartDate = DateTime.Now.AddDays(-90),
                NextSessionDate = null,
                FinancialValue = 2000000,
                LawyerFees = 150000,
                Stage = "منتهية - حكم نهائي"
            },
            new CaseDisplayModel
            {
                Id = 4,
                OfficeReference = "CASE-20241120-004",
                Title = "قضية أسرة - نفقة",
                Type = CaseType.Family,
                Status = CaseStatus.Active,
                Court = "محكمة الأحوال الشخصية",
                ClientName = "مريم عبدالله الزهراني",
                LawyerName = "د. نورا محمد الزهراني",
                Opponent = "محمد سعد الغامدي",
                Subject = "دعوى نفقة أطفال وزوجة",
                Priority = CasePriority.Medium,
                StartDate = DateTime.Now.AddDays(-20),
                NextSessionDate = DateTime.Now.AddDays(10),
                FinancialValue = 60000,
                LawyerFees = 15000,
                Stage = "مرحلة التحقيق"
            },
            new CaseDisplayModel
            {
                Id = 5,
                OfficeReference = "CASE-20241210-005",
                Title = "قضية عقارية - منازعة ملكية",
                Type = CaseType.RealEstate,
                Status = CaseStatus.Archived,
                Court = "المحكمة العامة بالدمام",
                ClientName = "وزارة التربية والتعليم",
                LawyerName = "د. محمد أحمد الشريف",
                Opponent = "ورثة عبدالرحمن الخالد",
                Subject = "منازعة ملكية أرض مدرسة",
                Priority = CasePriority.Low,
                StartDate = DateTime.Now.AddDays(-180),
                NextSessionDate = null,
                FinancialValue = 800000,
                LawyerFees = 80000,
                Stage = "مؤرشفة"
            },
            new CaseDisplayModel
            {
                Id = 6,
                OfficeReference = "CASE-20241205-006",
                Title = "قضية جنائية - اختلاس",
                Type = CaseType.Criminal,
                Status = CaseStatus.Active,
                Court = "المحكمة الجزائية بالرياض",
                ClientName = "جمعية البر الخيرية",
                LawyerName = "أ. خالد سالم العتيبي",
                Opponent = "أحمد محمد الفهد",
                Subject = "دعوى اختلاس أموال الجمعية",
                Priority = CasePriority.Urgent,
                StartDate = DateTime.Now.AddDays(-10),
                NextSessionDate = DateTime.Now.AddDays(5),
                FinancialValue = 300000,
                LawyerFees = 40000,
                Stage = "مرحلة التحقيق الأولي"
            }
        };

        _allCases.Clear();
        _filteredCases.Clear();
        
        foreach (var caseItem in sampleCases)
        {
            _allCases.Add(caseItem);
            _filteredCases.Add(caseItem);
        }
    }

    private void SetupSearchBox()
    {
        try
        {
            if (SearchTextBox != null)
            {
                SearchTextBox.GotFocus += (s, e) =>
                {
                    if (SearchTextBox.Text == "البحث في الملفات...")
                    {
                        SearchTextBox.Text = "";
                        SearchTextBox.Foreground = new SolidColorBrush(Colors.Black);
                    }
                };

                SearchTextBox.LostFocus += (s, e) =>
                {
                    if (string.IsNullOrWhiteSpace(SearchTextBox.Text))
                    {
                        SearchTextBox.Text = "البحث في الملفات...";
                        SearchTextBox.Foreground = new SolidColorBrush(Color.FromRgb(153, 153, 153));
                    }
                };
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في إعداد مربع البحث: {ex.Message}");
        }
    }

    private void UpdateStatistics()
    {
        try
        {
            if (TotalCasesTextBlock != null)
            {
                TotalCasesTextBlock.Text = _allCases.Count.ToString();
                ActiveCasesTextBlock.Text = _allCases.Count(c => c.Status == CaseStatus.Active).ToString();
                ClosedCasesTextBlock.Text = _allCases.Count(c => c.Status == CaseStatus.Closed).ToString();
                ArchivedCasesTextBlock.Text = _allCases.Count(c => c.Status == CaseStatus.Archived).ToString();
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الإحصائيات: {ex.Message}");
        }
    }

    private void UpdateStatusText()
    {
        try
        {
            if (StatusTextBlock != null)
            {
                StatusTextBlock.Text = $"عرض {_filteredCases.Count} من أصل {_allCases.Count} ملف";
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث نص الحالة: {ex.Message}");
        }
    }

    private void ApplyFilters()
    {
        try
        {
            var searchTerm = SearchTextBox?.Text?.Trim().ToLower();
            if (searchTerm == "البحث في الملفات...") searchTerm = "";

            var selectedType = (CaseTypeComboBox?.SelectedItem as ComboBoxItem)?.Tag?.ToString();
            var selectedStatus = (CaseStatusComboBox?.SelectedItem as ComboBoxItem)?.Tag?.ToString();
            var selectedPriority = (CasePriorityComboBox?.SelectedItem as ComboBoxItem)?.Tag?.ToString();

            var filtered = _allCases.Where(caseItem =>
            {
                // تصفية البحث النصي
                var matchesSearch = string.IsNullOrEmpty(searchTerm) ||
                                   caseItem.Title.ToLower().Contains(searchTerm) ||
                                   caseItem.ClientName.ToLower().Contains(searchTerm) ||
                                   caseItem.OfficeReference.ToLower().Contains(searchTerm) ||
                                   caseItem.Court.ToLower().Contains(searchTerm);

                // تصفية النوع
                var matchesType = selectedType == "All" || selectedType == null ||
                                 caseItem.Type.ToString() == selectedType;

                // تصفية الحالة
                var matchesStatus = selectedStatus == "All" || selectedStatus == null ||
                                   caseItem.Status.ToString() == selectedStatus;

                // تصفية الأولوية
                var matchesPriority = selectedPriority == "All" || selectedPriority == null ||
                                     caseItem.Priority.ToString() == selectedPriority;

                return matchesSearch && matchesType && matchesStatus && matchesPriority;
            });

            _filteredCases.Clear();
            foreach (var caseItem in filtered)
            {
                _filteredCases.Add(caseItem);
            }

            UpdateStatusText();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق المرشحات: {ex.Message}");
        }
    }

    // معالجات الأحداث
    private void AddCaseButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var addWindow = new AddCaseWindowNew();
            if (addWindow.ShowDialog() == true)
            {
                if (addWindow.NewCase != null)
                {
                    // إضافة الملف الجديد إلى القائمة
                    var newCaseDisplay = new CaseDisplayModel
                    {
                        Id = _allCases.Count + 1,
                        OfficeReference = addWindow.NewCase.OfficeReference,
                        Title = addWindow.NewCase.Title,
                        Type = addWindow.NewCase.Type,
                        Status = addWindow.NewCase.Status,
                        Court = addWindow.NewCase.Court,
                        ClientName = "موكل جديد", // في التطبيق الحقيقي من قاعدة البيانات
                        LawyerName = "محامي مكلف", // في التطبيق الحقيقي من قاعدة البيانات
                        Opponent = addWindow.NewCase.Opponent,
                        Subject = addWindow.NewCase.Subject,
                        Priority = addWindow.NewCase.Priority,
                        StartDate = addWindow.NewCase.StartDate,
                        FinancialValue = addWindow.NewCase.FinancialValue,
                        LawyerFees = addWindow.NewCase.LawyerFees,
                        Stage = addWindow.NewCase.Stage,
                        CreatedAt = DateTime.Now
                    };

                    _allCases.Add(newCaseDisplay);
                    ApplyFilters();
                    UpdateStatistics();

                    MessageBox.Show("تم إضافة الملف بنجاح!", "نجح الحفظ", 
                                   MessageBoxButton.OK, MessageBoxImage.Information);

                    // إذا كان المستخدم اختار "حفظ وإضافة آخر"
                    if (addWindow.SaveAndAddAnother)
                    {
                        AddCaseButton_Click(sender, e);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إضافة الملف: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void RefreshButton_Click(object sender, RoutedEventArgs e)
    {
        LoadSampleData();
        UpdateStatistics();
        UpdateStatusText();
        MessageBox.Show("تم تحديث البيانات بنجاح!", "تحديث",
                       MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
    {
        ApplyFilters();
    }

    private void FilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        ApplyFilters();
    }

    private void ClearFiltersButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            SearchTextBox.Text = "البحث في الملفات...";
            SearchTextBox.Foreground = new SolidColorBrush(Color.FromRgb(153, 153, 153));

            CaseTypeComboBox.SelectedIndex = 0;
            CaseStatusComboBox.SelectedIndex = 0;
            CasePriorityComboBox.SelectedIndex = 0;

            ApplyFilters();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في مسح المرشحات: {ex.Message}");
        }
    }

    private void ExportButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var dialog = new Microsoft.Win32.SaveFileDialog
            {
                Filter = "Excel Files (*.xlsx)|*.xlsx|PDF Files (*.pdf)|*.pdf|CSV Files (*.csv)|*.csv",
                DefaultExt = "xlsx",
                FileName = $"ملفات_المكتب_{DateTime.Now:yyyyMMdd}"
            };

            if (dialog.ShowDialog() == true)
            {
                // محاكاة التصدير
                MessageBox.Show($"تم تصدير {_filteredCases.Count} ملف بنجاح إلى:\n{dialog.FileName}",
                               "نجح التصدير", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void PrintButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            MessageBox.Show($"تم إرسال تقرير الملفات للطباعة\n\nعدد الملفات: {_filteredCases.Count}",
                           "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ArchiveButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var selectedCases = CasesDataGrid.SelectedItems.Cast<CaseDisplayModel>().ToList();
            if (selectedCases.Count == 0)
            {
                MessageBox.Show("يرجى اختيار الملفات المراد أرشفتها", "تنبيه",
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show($"هل تريد أرشفة {selectedCases.Count} ملف؟",
                                       "تأكيد الأرشفة", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                foreach (var caseItem in selectedCases)
                {
                    caseItem.Status = CaseStatus.Archived;
                }

                ApplyFilters();
                UpdateStatistics();

                MessageBox.Show($"تم أرشفة {selectedCases.Count} ملف بنجاح!", "نجحت الأرشفة",
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في الأرشفة: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ViewCaseButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int caseId)
        {
            var caseItem = _allCases.FirstOrDefault(c => c.Id == caseId);
            if (caseItem != null)
            {
                MessageBox.Show($"عرض تفاصيل الملف:\n\n" +
                               $"العنوان: {caseItem.Title}\n" +
                               $"المرجع: {caseItem.OfficeReference}\n" +
                               $"النوع: {caseItem.TypeDisplay}\n" +
                               $"الحالة: {caseItem.StatusDisplay}\n" +
                               $"المحكمة: {caseItem.Court}\n" +
                               $"الموكل: {caseItem.ClientName}\n" +
                               $"المحامي: {caseItem.LawyerName}\n" +
                               $"الخصم: {caseItem.OpponentDisplay}\n" +
                               $"القيمة المالية: {caseItem.FinancialValueDisplay}\n" +
                               $"الأتعاب: {caseItem.LawyerFeesDisplay}\n" +
                               $"المرحلة: {caseItem.StageDisplay}",
                               "تفاصيل الملف", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }

    private void EditCaseButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int caseId)
        {
            MessageBox.Show($"سيتم فتح نافذة تعديل الملف رقم: {caseId}", "تعديل الملف",
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void SessionsButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int caseId)
        {
            MessageBox.Show($"عرض جلسات الملف رقم: {caseId}\n\nسيتم تطوير هذه الميزة في التحديث القادم",
                           "جلسات الملف", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void DocumentsButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int caseId)
        {
            MessageBox.Show($"عرض مرفقات الملف رقم: {caseId}\n\nسيتم تطوير هذه الميزة في التحديث القادم",
                           "مرفقات الملف", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void DeleteCaseButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int caseId)
        {
            var caseItem = _allCases.FirstOrDefault(c => c.Id == caseId);
            if (caseItem != null)
            {
                var result = MessageBox.Show($"هل تريد حذف الملف:\n{caseItem.Title}؟\n\nلا يمكن التراجع عن هذا الإجراء!",
                                           "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    _allCases.Remove(caseItem);
                    ApplyFilters();
                    UpdateStatistics();

                    MessageBox.Show("تم حذف الملف بنجاح!", "نجح الحذف",
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }
    }
}
