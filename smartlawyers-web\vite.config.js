import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/js/app.js',
                'resources/css/dashboard.css',
                'resources/js/dashboard.js',
                'resources/css/auth.css',
                'resources/js/auth.js'
            ],
            refresh: true,
        }),
        vue({
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false,
                },
            },
        }),
    ],
    resolve: {
        alias: {
            '@': resolve(__dirname, 'resources/js'),
            '~bootstrap': resolve(__dirname, 'node_modules/bootstrap'),
            '~@fortawesome': resolve(__dirname, 'node_modules/@fortawesome'),
        },
    },
    css: {
        preprocessorOptions: {
            scss: {
                additionalData: `
                    @import "resources/css/variables.scss";
                    @import "~bootstrap/scss/functions";
                    @import "~bootstrap/scss/variables";
                    @import "~bootstrap/scss/mixins";
                `
            }
        }
    },
    build: {
        rollupOptions: {
            output: {
                manualChunks: {
                    vendor: ['vue', 'vue-router', 'pinia'],
                    bootstrap: ['bootstrap', '@popperjs/core'],
                    charts: ['chart.js', 'vue-chartjs'],
                    calendar: ['@fullcalendar/core', '@fullcalendar/vue3'],
                    utils: ['lodash', 'moment', 'axios']
                }
            }
        },
        chunkSizeWarningLimit: 1000
    },
    server: {
        host: '0.0.0.0',
        port: 5173,
        hmr: {
            host: 'localhost',
        },
        watch: {
            usePolling: true,
        }
    },
    optimizeDeps: {
        include: [
            'vue',
            'vue-router',
            'pinia',
            'bootstrap',
            '@popperjs/core',
            'chart.js',
            'vue-chartjs',
            '@fullcalendar/core',
            '@fullcalendar/vue3',
            'moment',
            'lodash',
            'axios'
        ]
    }
});
