using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using AvocatPro.ViewModels;
using AvocatPro.Services;
using AvocatPro.Models;

namespace AvocatPro.Views;

/// <summary>
/// نافذة تسجيل الدخول
/// </summary>
public partial class LoginWindow : Window
{
    private readonly LoginViewModel _viewModel;

    public LoginWindow(IAuthenticationService authService, IUserService userService)
    {
        InitializeComponent();
        
        _viewModel = new LoginViewModel(authService, userService);
        DataContext = _viewModel;
        
        // ربط الأحداث
        _viewModel.LoginSuccessful += OnLoginSuccessful;
        _viewModel.PasswordResetRequested += OnPasswordResetRequested;
        
        // السماح بسحب النافذة
        MouseLeftButtonDown += (s, e) => DragMove();
        
        // التركيز على حقل اسم المستخدم
        Loaded += (s, e) => UsernameTextBox.Focus();
        
        // ربط Enter بتسجيل الدخول
        KeyDown += LoginWindow_KeyDown;
    }

    private void LoginWindow_KeyDown(object sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter && _viewModel.CanLogin)
        {
            LoginButton_Click(sender, e);
        }
    }

    private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
    {
        if (sender is PasswordBox passwordBox)
        {
            _viewModel.Password = passwordBox.Password;
        }
    }

    private void LoginButton_Click(object sender, RoutedEventArgs e)
    {
        // تحديث كلمة المرور من PasswordBox
        _viewModel.Password = PasswordBox.Password;
        
        // تنفيذ أمر تسجيل الدخول
        if (_viewModel.LoginCommand.CanExecute(null))
        {
            _viewModel.LoginCommand.Execute(null);
        }
    }

    private void ForgotPasswordButton_Click(object sender, RoutedEventArgs e)
    {
        if (_viewModel.ForgotPasswordCommand.CanExecute(null))
        {
            _viewModel.ForgotPasswordCommand.Execute(null);
        }
    }

    private void MinimizeButton_Click(object sender, RoutedEventArgs e)
    {
        WindowState = WindowState.Minimized;
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        Application.Current.Shutdown();
    }

    private void OnLoginSuccessful(User user)
    {
        try
        {
            // إنشاء النافذة الرئيسية
            var mainWindow = new MainWindow(user);
            
            // إظهار النافذة الرئيسية
            mainWindow.Show();
            
            // إغلاق نافذة تسجيل الدخول
            Close();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح النافذة الرئيسية: {ex.Message}", 
                          "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void OnPasswordResetRequested(string email, string resetToken)
    {
        try
        {
            // إظهار نافذة إعادة تعيين كلمة المرور
            var resetWindow = new PasswordResetWindow(email, resetToken);
            resetWindow.Owner = this;
            resetWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح نافذة إعادة تعيين كلمة المرور: {ex.Message}", 
                          "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    protected override void OnClosed(EventArgs e)
    {
        // إلغاء ربط الأحداث
        _viewModel.LoginSuccessful -= OnLoginSuccessful;
        _viewModel.PasswordResetRequested -= OnPasswordResetRequested;
        
        base.OnClosed(e);
    }
}

/// <summary>
/// نافذة إعادة تعيين كلمة المرور (مؤقتة)
/// </summary>
public partial class PasswordResetWindow : Window
{
    public PasswordResetWindow(string email, string resetToken)
    {
        InitializeComponent();
        
        // تعيين البيانات
        EmailTextBlock.Text = email;
        ResetTokenTextBlock.Text = resetToken;
    }

    private void InitializeComponent()
    {
        Title = "إعادة تعيين كلمة المرور";
        Width = 500;
        Height = 300;
        WindowStartupLocation = WindowStartupLocation.CenterOwner;
        FlowDirection = FlowDirection.RightToLeft;

        var grid = new Grid();
        grid.Margin = new Thickness(20);

        var stackPanel = new StackPanel();
        
        var titleTextBlock = new TextBlock
        {
            Text = "إعادة تعيين كلمة المرور",
            FontSize = 18,
            FontWeight = FontWeights.Bold,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 20)
        };
        stackPanel.Children.Add(titleTextBlock);

        var messageTextBlock = new TextBlock
        {
            Text = "تم إرسال رمز إعادة تعيين كلمة المرور إلى البريد الإلكتروني التالي:",
            TextWrapping = TextWrapping.Wrap,
            Margin = new Thickness(0, 0, 0, 10)
        };
        stackPanel.Children.Add(messageTextBlock);

        EmailTextBlock = new TextBlock
        {
            FontWeight = FontWeights.Bold,
            Margin = new Thickness(0, 0, 0, 20)
        };
        stackPanel.Children.Add(EmailTextBlock);

        var tokenLabel = new TextBlock
        {
            Text = "رمز إعادة التعيين:",
            Margin = new Thickness(0, 0, 0, 5)
        };
        stackPanel.Children.Add(tokenLabel);

        ResetTokenTextBlock = new TextBlock
        {
            FontFamily = new System.Windows.Media.FontFamily("Consolas"),
            Background = System.Windows.Media.Brushes.LightGray,
            Padding = new Thickness(10),
            Margin = new Thickness(0, 0, 0, 20)
        };
        stackPanel.Children.Add(ResetTokenTextBlock);

        var closeButton = new Button
        {
            Content = "إغلاق",
            Width = 100,
            Height = 35,
            HorizontalAlignment = HorizontalAlignment.Center
        };
        closeButton.Click += (s, e) => Close();
        stackPanel.Children.Add(closeButton);

        grid.Children.Add(stackPanel);
        Content = grid;
    }

    public TextBlock EmailTextBlock { get; private set; } = null!;
    public TextBlock ResetTokenTextBlock { get; private set; } = null!;
}
