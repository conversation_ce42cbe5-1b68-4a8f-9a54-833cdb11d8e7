<Window x:Class="AvocatPro.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="AvocatPro - تسجيل الدخول"
        Height="500" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">

    <Window.Resources>
        <!-- لا توجد أنماط مخصصة -->
    </Window.Resources>

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="#2196F3" Padding="20,15">
            <TextBlock Text="AvocatPro - نظام إدارة مكتب المحاماة"
                      Foreground="White" FontSize="18" FontWeight="Bold"
                      HorizontalAlignment="Center"/>
        </Border>

        <!-- محتوى النموذج -->
        <StackPanel Grid.Row="1" VerticalAlignment="Center" Margin="100,50">
            <!-- العنوان -->
            <TextBlock Text="AvocatPro" FontSize="32" FontWeight="Bold"
                      Foreground="#2196F3" HorizontalAlignment="Center" Margin="0,0,0,10"/>
            <TextBlock Text="تسجيل الدخول" FontSize="18" FontWeight="Bold"
                      Foreground="#333" HorizontalAlignment="Center" Margin="0,0,0,40"/>

            <!-- اسم المستخدم -->
            <StackPanel Margin="0,0,0,20">
                <TextBlock Text="اسم المستخدم" FontSize="14" FontWeight="Medium"
                          Foreground="#333" Margin="0,0,0,8"/>
                <TextBox Name="UsernameTextBox" Height="35" FontSize="14" Padding="10,8"
                        Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"/>
            </StackPanel>

            <!-- كلمة المرور -->
            <StackPanel Margin="0,0,0,20">
                <TextBlock Text="كلمة المرور" FontSize="14" FontWeight="Medium"
                          Foreground="#333" Margin="0,0,0,8"/>
                <PasswordBox Name="PasswordBox" Height="35" FontSize="14" Padding="10,8"
                            PasswordChanged="PasswordBox_PasswordChanged"/>
            </StackPanel>

            <!-- خيارات إضافية -->
            <Grid Margin="0,0,0,30">
                <CheckBox Name="RememberMeCheckBox" Content="تذكرني"
                         HorizontalAlignment="Right" VerticalAlignment="Center"
                         IsChecked="{Binding RememberMe}"/>
                <Button Name="ForgotPasswordButton" Content="نسيت كلمة المرور؟"
                       HorizontalAlignment="Left" VerticalAlignment="Center"
                       Background="Transparent" BorderThickness="0"
                       Foreground="#2196F3" Cursor="Hand"
                       Click="ForgotPasswordButton_Click"/>
            </Grid>

            <!-- زر تسجيل الدخول -->
            <Button Name="LoginButton" Content="تسجيل الدخول"
                   Background="#2196F3" Foreground="White" BorderThickness="0"
                   Padding="15,10" FontSize="16" FontWeight="Bold"
                   Height="40" Width="200"
                   Click="LoginButton_Click"
                   IsEnabled="{Binding CanLogin}"/>

            <!-- رسائل الخطأ -->
            <TextBlock Name="ErrorMessageTextBlock"
                      Text="{Binding ErrorMessage}"
                      Foreground="Red" FontSize="12"
                      HorizontalAlignment="Center" Margin="0,15,0,0"
                      Visibility="Collapsed"/>

            <!-- مؤشر التحميل -->
            <ProgressBar Name="LoadingProgressBar"
                        Height="4" Margin="0,20,0,0"
                        IsIndeterminate="False"
                        Visibility="Collapsed"/>
        </StackPanel>

    </Grid>
</Window>
