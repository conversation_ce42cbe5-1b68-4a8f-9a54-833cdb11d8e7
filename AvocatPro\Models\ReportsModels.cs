using System;
using System.Collections.Generic;
using System.Linq;

namespace AvocatPro.Models;

/// <summary>
/// نموذج التقرير الشامل
/// </summary>
public class ComprehensiveReportModel
{
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public FinancialSummary Financial { get; set; } = new();
    public CasesSummary Cases { get; set; } = new();
    public ClientsSummary Clients { get; set; } = new();
    public AppointmentsSummary Appointments { get; set; } = new();
    public PerformanceMetrics Performance { get; set; } = new();
    public List<TrendData> Trends { get; set; } = new();
    public List<ComparisonData> Comparisons { get; set; } = new();
    
    public string PeriodDisplay => $"{PeriodStart:dd/MM/yyyy} - {PeriodEnd:dd/MM/yyyy}";
    public string GeneratedAt => DateTime.Now.ToString("dd/MM/yyyy HH:mm");
}

/// <summary>
/// ملخص مالي للتقارير
/// </summary>
public class FinancialSummary
{
    public decimal TotalRevenues { get; set; }
    public decimal TotalExpenses { get; set; }
    public decimal NetProfit { get; set; }
    public decimal ProfitMargin { get; set; }
    public decimal PendingRevenues { get; set; }
    public decimal OverdueRevenues { get; set; }
    public decimal ReimbursableExpenses { get; set; }
    public int TotalTransactions { get; set; }
    public decimal AverageTransactionValue { get; set; }
    public decimal LargestTransaction { get; set; }
    public decimal SmallestTransaction { get; set; }
    
    // خصائص العرض
    public string TotalRevenuesDisplay => TotalRevenues.ToString("N0") + " درهم";
    public string TotalExpensesDisplay => TotalExpenses.ToString("N0") + " درهم";
    public string NetProfitDisplay => NetProfit.ToString("N0") + " درهم";
    public string ProfitMarginDisplay => ProfitMargin.ToString("F1") + "%";
    public string PendingRevenuesDisplay => PendingRevenues.ToString("N0") + " درهم";
    public string OverdueRevenuesDisplay => OverdueRevenues.ToString("N0") + " درهم";
    public string ReimbursableExpensesDisplay => ReimbursableExpenses.ToString("N0") + " درهم";
    public string AverageTransactionDisplay => AverageTransactionValue.ToString("N0") + " درهم";
    public string LargestTransactionDisplay => LargestTransaction.ToString("N0") + " درهم";
    public string SmallestTransactionDisplay => SmallestTransaction.ToString("N0") + " درهم";
    
    public bool IsProfitable => NetProfit > 0;
    public string ProfitabilityStatus => IsProfitable ? "مربح" : "خسارة";
    public string ProfitabilityIcon => IsProfitable ? "📈" : "📉";
    public string ProfitabilityColor => IsProfitable ? "#4CAF50" : "#F44336";
}

/// <summary>
/// ملخص القضايا
/// </summary>
public class CasesSummary
{
    public int TotalCases { get; set; }
    public int ActiveCases { get; set; }
    public int ClosedCases { get; set; }
    public int WonCases { get; set; }
    public int LostCases { get; set; }
    public int PendingCases { get; set; }
    public decimal SuccessRate { get; set; }
    public decimal AverageCaseDuration { get; set; }
    public decimal TotalCaseValue { get; set; }
    public int NewCasesThisPeriod { get; set; }
    public int ClosedCasesThisPeriod { get; set; }
    
    // خصائص العرض
    public string SuccessRateDisplay => SuccessRate.ToString("F1") + "%";
    public string AverageCaseDurationDisplay => AverageCaseDuration.ToString("F0") + " يوم";
    public string TotalCaseValueDisplay => TotalCaseValue.ToString("N0") + " درهم";
    public string SuccessRateColor => SuccessRate >= 80 ? "#4CAF50" : SuccessRate >= 60 ? "#FF9800" : "#F44336";
}

/// <summary>
/// ملخص الموكلين
/// </summary>
public class ClientsSummary
{
    public int TotalClients { get; set; }
    public int ActiveClients { get; set; }
    public int NewClients { get; set; }
    public int IndividualClients { get; set; }
    public int CorporateClients { get; set; }
    public decimal AverageClientValue { get; set; }
    public decimal TopClientValue { get; set; }
    public int ClientsWithMultipleCases { get; set; }
    public decimal ClientRetentionRate { get; set; }
    public decimal ClientSatisfactionRate { get; set; }
    
    // خصائص العرض
    public string AverageClientValueDisplay => AverageClientValue.ToString("N0") + " درهم";
    public string TopClientValueDisplay => TopClientValue.ToString("N0") + " درهم";
    public string ClientRetentionRateDisplay => ClientRetentionRate.ToString("F1") + "%";
    public string ClientSatisfactionRateDisplay => ClientSatisfactionRate.ToString("F1") + "%";
    public string RetentionRateColor => ClientRetentionRate >= 90 ? "#4CAF50" : ClientRetentionRate >= 70 ? "#FF9800" : "#F44336";
}

/// <summary>
/// ملخص المواعيد
/// </summary>
public class AppointmentsSummary
{
    public int TotalAppointments { get; set; }
    public int CompletedAppointments { get; set; }
    public int CancelledAppointments { get; set; }
    public int UpcomingAppointments { get; set; }
    public int OverdueAppointments { get; set; }
    public decimal AttendanceRate { get; set; }
    public decimal AverageAppointmentDuration { get; set; }
    public int ConsultationAppointments { get; set; }
    public int CourtAppointments { get; set; }
    public int ClientMeetings { get; set; }
    
    // خصائص العرض
    public string AttendanceRateDisplay => AttendanceRate.ToString("F1") + "%";
    public string AverageAppointmentDurationDisplay => AverageAppointmentDuration.ToString("F0") + " دقيقة";
    public string AttendanceRateColor => AttendanceRate >= 90 ? "#4CAF50" : AttendanceRate >= 75 ? "#FF9800" : "#F44336";
}

/// <summary>
/// مؤشرات الأداء
/// </summary>
public class PerformanceMetrics
{
    public decimal ProductivityIndex { get; set; }
    public decimal EfficiencyRating { get; set; }
    public decimal QualityScore { get; set; }
    public decimal ClientSatisfactionIndex { get; set; }
    public decimal FinancialHealthScore { get; set; }
    public decimal OverallPerformanceScore { get; set; }
    public string PerformanceGrade { get; set; } = "";
    public List<string> Strengths { get; set; } = new();
    public List<string> ImprovementAreas { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
    
    // خصائص العرض
    public string ProductivityIndexDisplay => ProductivityIndex.ToString("F1") + "%";
    public string EfficiencyRatingDisplay => EfficiencyRating.ToString("F1") + "%";
    public string QualityScoreDisplay => QualityScore.ToString("F1") + "%";
    public string ClientSatisfactionIndexDisplay => ClientSatisfactionIndex.ToString("F1") + "%";
    public string FinancialHealthScoreDisplay => FinancialHealthScore.ToString("F1") + "%";
    public string OverallPerformanceScoreDisplay => OverallPerformanceScore.ToString("F1") + "%";
    
    public string PerformanceColor => OverallPerformanceScore >= 90 ? "#4CAF50" : 
                                     OverallPerformanceScore >= 75 ? "#2196F3" :
                                     OverallPerformanceScore >= 60 ? "#FF9800" : "#F44336";
    
    public string PerformanceIcon => OverallPerformanceScore >= 90 ? "🏆" : 
                                    OverallPerformanceScore >= 75 ? "⭐" :
                                    OverallPerformanceScore >= 60 ? "👍" : "⚠️";
}

/// <summary>
/// بيانات الاتجاهات
/// </summary>
public class TrendData
{
    public string Period { get; set; } = "";
    public string Category { get; set; } = "";
    public decimal Value { get; set; }
    public decimal PreviousValue { get; set; }
    public decimal ChangePercentage { get; set; }
    public string TrendDirection { get; set; } = "";
    
    public string ValueDisplay => Value.ToString("N0");
    public string ChangeDisplay => ChangePercentage.ToString("F1") + "%";
    public string TrendIcon => TrendDirection switch
    {
        "Up" => "📈",
        "Down" => "📉",
        "Stable" => "➡️",
        _ => "❓"
    };
    public string TrendColor => TrendDirection switch
    {
        "Up" => "#4CAF50",
        "Down" => "#F44336",
        "Stable" => "#FF9800",
        _ => "#9E9E9E"
    };
}

/// <summary>
/// بيانات المقارنة
/// </summary>
public class ComparisonData
{
    public string Category { get; set; } = "";
    public string CurrentPeriod { get; set; } = "";
    public string PreviousPeriod { get; set; } = "";
    public decimal CurrentValue { get; set; }
    public decimal PreviousValue { get; set; }
    public decimal Difference { get; set; }
    public decimal PercentageChange { get; set; }
    public string ComparisonType { get; set; } = "";
    
    public string CurrentValueDisplay => CurrentValue.ToString("N0");
    public string PreviousValueDisplay => PreviousValue.ToString("N0");
    public string DifferenceDisplay => Difference.ToString("N0");
    public string PercentageChangeDisplay => PercentageChange.ToString("F1") + "%";
    
    public bool IsImprovement => PercentageChange > 0;
    public string ImprovementIcon => IsImprovement ? "📈" : PercentageChange < 0 ? "📉" : "➡️";
    public string ImprovementColor => IsImprovement ? "#4CAF50" : PercentageChange < 0 ? "#F44336" : "#FF9800";
}

/// <summary>
/// نموذج تقرير مخصص
/// </summary>
public class CustomReportModel
{
    public string ReportId { get; set; } = "";
    public string ReportName { get; set; } = "";
    public string ReportType { get; set; } = "";
    public DateTime CreatedAt { get; set; }
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
    public List<string> SelectedMetrics { get; set; } = new();
    public List<string> SelectedCharts { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
    public Dictionary<string, object> Results { get; set; } = new();
    public string Status { get; set; } = "";
    
    public string CreatedAtDisplay => CreatedAt.ToString("dd/MM/yyyy HH:mm");
    public string PeriodDisplay => $"{PeriodStart:dd/MM/yyyy} - {PeriodEnd:dd/MM/yyyy}";
}

/// <summary>
/// نموذج لوحة المعلومات
/// </summary>
public class DashboardModel
{
    public DateTime LastUpdated { get; set; }
    public List<KPIWidget> KPIWidgets { get; set; } = new();
    public List<ChartWidget> ChartWidgets { get; set; } = new();
    public List<AlertWidget> AlertWidgets { get; set; } = new();
    public List<QuickStatWidget> QuickStats { get; set; } = new();
    
    public string LastUpdatedDisplay => LastUpdated.ToString("dd/MM/yyyy HH:mm");
}

/// <summary>
/// عنصر مؤشر أداء رئيسي
/// </summary>
public class KPIWidget
{
    public string Title { get; set; } = "";
    public string Value { get; set; } = "";
    public string Unit { get; set; } = "";
    public string Icon { get; set; } = "";
    public string Color { get; set; } = "";
    public decimal ChangePercentage { get; set; }
    public string TrendDirection { get; set; } = "";
    public string Description { get; set; } = "";
    
    public string ChangeDisplay => ChangePercentage.ToString("F1") + "%";
    public string TrendIcon => TrendDirection switch
    {
        "Up" => "📈",
        "Down" => "📉",
        "Stable" => "➡️",
        _ => "❓"
    };
}

/// <summary>
/// عنصر رسم بياني
/// </summary>
public class ChartWidget
{
    public string Title { get; set; } = "";
    public string ChartType { get; set; } = "";
    public List<ChartDataPoint> DataPoints { get; set; } = new();
    public string XAxisLabel { get; set; } = "";
    public string YAxisLabel { get; set; } = "";
    public List<string> Colors { get; set; } = new();
}

/// <summary>
/// نقطة بيانات الرسم البياني
/// </summary>
public class ChartDataPoint
{
    public string Label { get; set; } = "";
    public decimal Value { get; set; }
    public string Color { get; set; } = "";
    public string Description { get; set; } = "";
    
    public string ValueDisplay => Value.ToString("N0");
}

/// <summary>
/// عنصر تنبيه
/// </summary>
public class AlertWidget
{
    public string Title { get; set; } = "";
    public string Message { get; set; } = "";
    public string AlertType { get; set; } = "";
    public string Icon { get; set; } = "";
    public string Color { get; set; } = "";
    public DateTime CreatedAt { get; set; }
    public bool IsRead { get; set; }
    
    public string CreatedAtDisplay => CreatedAt.ToString("dd/MM/yyyy HH:mm");
    public string AlertTypeDisplay => AlertType switch
    {
        "Warning" => "تحذير",
        "Error" => "خطأ",
        "Info" => "معلومات",
        "Success" => "نجح",
        _ => "عام"
    };
}

/// <summary>
/// عنصر إحصائية سريعة
/// </summary>
public class QuickStatWidget
{
    public string Title { get; set; } = "";
    public string Value { get; set; } = "";
    public string Icon { get; set; } = "";
    public string Color { get; set; } = "";
    public string Link { get; set; } = "";
    public string Description { get; set; } = "";
}
