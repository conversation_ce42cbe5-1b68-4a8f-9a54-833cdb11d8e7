using System;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using AvocatPro.Models;
using AvocatPro.Services;

namespace AvocatPro.Views.Pages;

/// <summary>
/// صفحة طباعة الشيكات والأظرفة
/// </summary>
public partial class CheckAndEnvelopePrintingPage : Page
{
    private readonly PrintingService _printingService;
    private CheckModel _currentCheck;
    private EnvelopeModel _currentEnvelope;

    public CheckAndEnvelopePrintingPage()
    {
        InitializeComponent();
        
        _printingService = new PrintingService();
        _currentCheck = new CheckModel();
        _currentEnvelope = new EnvelopeModel();
        
        InitializeControls();
        LoadDefaultData();
    }

    /// <summary>
    /// تهيئة العناصر
    /// </summary>
    private void InitializeControls()
    {
        try
        {
            // تهيئة قوالب الشيكات
            var checkTemplates = _printingService.GetAvailableCheckTemplates();
            CheckTemplateComboBox.ItemsSource = checkTemplates;
            CheckTemplateComboBox.DisplayMemberPath = "Name";
            CheckTemplateComboBox.SelectedIndex = 0;

            // تهيئة أحجام الأظرفة
            EnvelopeSizeComboBox.ItemsSource = Enum.GetValues(typeof(EnvelopeSize))
                .Cast<EnvelopeSize>()
                .Select(size => new { Value = size, Display = GetEnvelopeSizeDisplay(size) });
            EnvelopeSizeComboBox.DisplayMemberPath = "Display";
            EnvelopeSizeComboBox.SelectedValuePath = "Value";
            EnvelopeSizeComboBox.SelectedIndex = 0;

            // تهيئة البنوك
            BankComboBox.ItemsSource = new[]
            {
                "البنك الشعبي",
                "التجاري وفا بنك",
                "بنك المغرب",
                "القرض الفلاحي",
                "البنك المغربي للتجارة الخارجية",
                "صندوق الإيداع والتدبير",
                "بنك الأمان",
                "أخرى"
            };
            BankComboBox.SelectedIndex = 0;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تهيئة العناصر: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// تحميل البيانات الافتراضية
    /// </summary>
    private void LoadDefaultData()
    {
        try
        {
            // بيانات الشيك الافتراضية
            CheckDatePicker.SelectedDate = DateTime.Now;
            CheckNumberTextBox.Text = GenerateCheckNumber();

            // بيانات المرسل الافتراضية (من إعدادات المكتب)
            SenderNameTextBox.Text = "مكتب المحاماة - المحامي الرئيسي";
            SenderAddressTextBox.Text = "شارع محمد الخامس، الرباط";
            SenderCityTextBox.Text = "الرباط 10000";
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل البيانات الافتراضية: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// توليد رقم شيك تلقائي
    /// </summary>
    private string GenerateCheckNumber()
    {
        return DateTime.Now.ToString("yyyyMMddHHmmss");
    }

    /// <summary>
    /// الحصول على عرض حجم الظرف
    /// </summary>
    private string GetEnvelopeSizeDisplay(EnvelopeSize size)
    {
        return size switch
        {
            EnvelopeSize.Standard => "قياسي (220×110 مم)",
            EnvelopeSize.Large => "كبير (324×229 مم)",
            EnvelopeSize.A4 => "A4 (297×210 مم)",
            EnvelopeSize.Custom => "مخصص (250×125 مم)",
            _ => "غير محدد"
        };
    }

    /// <summary>
    /// معالج تغيير المبلغ
    /// </summary>
    private void AmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
    {
        try
        {
            if (decimal.TryParse(AmountTextBox.Text, out decimal amount))
            {
                _currentCheck.Amount = amount;
                AmountWordsTextBox.Text = _currentCheck.AmountInWords;
            }
            else
            {
                AmountWordsTextBox.Text = "";
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحويل المبلغ: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// معالج تفعيل الشطب
    /// </summary>
    private void CrossedCheckBox_Checked(object sender, RoutedEventArgs e)
    {
        CrossedTextTextBox.IsEnabled = true;
        _currentCheck.IsCrossed = true;
    }

    /// <summary>
    /// معالج إلغاء تفعيل الشطب
    /// </summary>
    private void CrossedCheckBox_Unchecked(object sender, RoutedEventArgs e)
    {
        CrossedTextTextBox.IsEnabled = false;
        _currentCheck.IsCrossed = false;
    }

    /// <summary>
    /// معاينة الشيك
    /// </summary>
    private async void PreviewCheckButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (!ValidateCheckData())
                return;

            UpdateCheckModel();
            var selectedTemplate = (CheckTemplate)CheckTemplateComboBox.SelectedItem;
            
            await _printingService.PrintCheckAsync(_currentCheck, selectedTemplate, preview: true);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في معاينة الشيك: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// طباعة الشيك
    /// </summary>
    private async void PrintCheckButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (!ValidateCheckData())
                return;

            var result = MessageBox.Show("هل أنت متأكد من طباعة الشيك؟", "تأكيد الطباعة", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                UpdateCheckModel();
                var selectedTemplate = (CheckTemplate)CheckTemplateComboBox.SelectedItem;
                
                var success = await _printingService.PrintCheckAsync(_currentCheck, selectedTemplate, preview: false);
                
                if (success)
                {
                    MessageBox.Show("تم طباعة الشيك بنجاح!", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة الشيك: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// حفظ قالب الشيك
    /// </summary>
    private void SaveCheckTemplateButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            MessageBox.Show("سيتم إضافة ميزة حفظ القوالب في التحديث القادم", "قريباً", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// مسح نموذج الشيك
    /// </summary>
    private void ClearCheckFormButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var result = MessageBox.Show("هل تريد مسح جميع بيانات الشيك؟", "تأكيد المسح", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                CheckNumberTextBox.Text = GenerateCheckNumber();
                PayeeNameTextBox.Clear();
                AmountTextBox.Clear();
                AmountWordsTextBox.Clear();
                AccountNumberTextBox.Clear();
                SignatureTextBox.Clear();
                MemoTextBox.Clear();
                CrossedCheckBox.IsChecked = false;
                CrossedTextTextBox.Text = "للمستفيد فقط";
                CheckDatePicker.SelectedDate = DateTime.Now;
                
                _currentCheck = new CheckModel();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في مسح النموذج: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// معاينة الظرف
    /// </summary>
    private async void PreviewEnvelopeButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (!ValidateEnvelopeData())
                return;

            UpdateEnvelopeModel();
            
            await _printingService.PrintEnvelopeAsync(_currentEnvelope, preview: true);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في معاينة الظرف: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// طباعة الظرف
    /// </summary>
    private async void PrintEnvelopeButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (!ValidateEnvelopeData())
                return;

            var result = MessageBox.Show("هل أنت متأكد من طباعة الظرف؟", "تأكيد الطباعة", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                UpdateEnvelopeModel();
                
                var success = await _printingService.PrintEnvelopeAsync(_currentEnvelope, preview: false);
                
                if (success)
                {
                    MessageBox.Show("تم طباعة الظرف بنجاح!", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة الظرف: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// حفظ قالب الظرف
    /// </summary>
    private void SaveEnvelopeTemplateButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            MessageBox.Show("سيتم إضافة ميزة حفظ القوالب في التحديث القادم", "قريباً", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// مسح نموذج الظرف
    /// </summary>
    private void ClearEnvelopeFormButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var result = MessageBox.Show("هل تريد مسح جميع بيانات الظرف؟", "تأكيد المسح", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                RecipientNameTextBox.Clear();
                RecipientAddressTextBox.Clear();
                RecipientCityTextBox.Clear();
                RegisteredCheckBox.IsChecked = false;
                UrgentCheckBox.IsChecked = false;
                SpecialInstructionsTextBox.Clear();
                
                _currentEnvelope = new EnvelopeModel();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في مسح النموذج: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// التحقق من صحة بيانات الشيك
    /// </summary>
    private bool ValidateCheckData()
    {
        if (string.IsNullOrWhiteSpace(PayeeNameTextBox.Text))
        {
            MessageBox.Show("يرجى إدخال اسم المستفيد", "بيانات ناقصة", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
            PayeeNameTextBox.Focus();
            return false;
        }

        if (string.IsNullOrWhiteSpace(AmountTextBox.Text) || !decimal.TryParse(AmountTextBox.Text, out decimal amount) || amount <= 0)
        {
            MessageBox.Show("يرجى إدخال مبلغ صحيح", "بيانات ناقصة", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
            AmountTextBox.Focus();
            return false;
        }

        if (!CheckDatePicker.SelectedDate.HasValue)
        {
            MessageBox.Show("يرجى اختيار تاريخ الشيك", "بيانات ناقصة", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
            CheckDatePicker.Focus();
            return false;
        }

        return true;
    }

    /// <summary>
    /// التحقق من صحة بيانات الظرف
    /// </summary>
    private bool ValidateEnvelopeData()
    {
        if (string.IsNullOrWhiteSpace(RecipientNameTextBox.Text))
        {
            MessageBox.Show("يرجى إدخال اسم المستقبل", "بيانات ناقصة", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
            RecipientNameTextBox.Focus();
            return false;
        }

        if (string.IsNullOrWhiteSpace(RecipientAddressTextBox.Text))
        {
            MessageBox.Show("يرجى إدخال عنوان المستقبل", "بيانات ناقصة", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
            RecipientAddressTextBox.Focus();
            return false;
        }

        return true;
    }

    /// <summary>
    /// تحديث نموذج الشيك
    /// </summary>
    private void UpdateCheckModel()
    {
        _currentCheck.CheckNumber = CheckNumberTextBox.Text;
        _currentCheck.PayeeName = PayeeNameTextBox.Text;
        _currentCheck.Amount = decimal.TryParse(AmountTextBox.Text, out decimal amount) ? amount : 0;
        _currentCheck.CheckDate = CheckDatePicker.SelectedDate ?? DateTime.Now;
        _currentCheck.BankName = BankComboBox.SelectedItem?.ToString() ?? "";
        _currentCheck.AccountNumber = AccountNumberTextBox.Text;
        _currentCheck.Signature = SignatureTextBox.Text;
        _currentCheck.Memo = MemoTextBox.Text;
        _currentCheck.IsCrossed = CrossedCheckBox.IsChecked ?? false;
        _currentCheck.CrossedText = CrossedTextTextBox.Text;
    }

    /// <summary>
    /// تحديث نموذج الظرف
    /// </summary>
    private void UpdateEnvelopeModel()
    {
        _currentEnvelope.SenderName = SenderNameTextBox.Text;
        _currentEnvelope.SenderAddress = SenderAddressTextBox.Text;
        _currentEnvelope.SenderCity = SenderCityTextBox.Text;
        _currentEnvelope.RecipientName = RecipientNameTextBox.Text;
        _currentEnvelope.RecipientAddress = RecipientAddressTextBox.Text;
        _currentEnvelope.RecipientCity = RecipientCityTextBox.Text;
        _currentEnvelope.EnvelopeSize = (EnvelopeSize)(EnvelopeSizeComboBox.SelectedValue ?? EnvelopeSize.Standard);
        _currentEnvelope.IsRegistered = RegisteredCheckBox.IsChecked ?? false;
        _currentEnvelope.IsUrgent = UrgentCheckBox.IsChecked ?? false;
        _currentEnvelope.SpecialInstructions = SpecialInstructionsTextBox.Text;
    }
}
