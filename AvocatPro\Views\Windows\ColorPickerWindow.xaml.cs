using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;

namespace AvocatPro.Views.Windows
{
    public partial class ColorPickerWindow : Window
    {
        public string SelectedColor { get; private set; } = "#3B82F6";

        public ColorPickerWindow()
        {
            InitializeComponent();
            InitializeColors();
            UpdatePreview();
        }

        private void InitializeColors()
        {
            // الألوان الشائعة
            var commonColors = new[]
            {
                "#FF0000", "#00FF00", "#0000FF", "#FFFF00", "#FF00FF", "#00FFFF",
                "#800000", "#008000", "#000080", "#808000", "#800080", "#008080",
                "#C0C0C0", "#808080", "#000000", "#FFFFFF", "#FFA500", "#A52A2A",
                "#DDA0DD", "#98FB98", "#F0E68C", "#87CEEB", "#FFB6C1", "#20B2AA",
                "#FF6347", "#40E0D0", "#EE82EE", "#90EE90", "#FFD700", "#FF69B4",
                "#DC143C", "#00CED1", "#FF1493", "#00FA9A", "#FFE4B5", "#ADFF2F",
                "#FF4500", "#DA70D6", "#32CD32", "#FF8C00", "#9370DB", "#228B22",
                "#FF7F50", "#6A5ACD", "#7FFF00", "#D2691E", "#9932CC", "#006400"
            };

            foreach (var color in commonColors)
            {
                var colorButton = CreateColorButton(color);
                ColorsGrid.Children.Add(colorButton);
            }

            // ألوان النظام المتقدمة
            var systemColors = new[]
            {
                "#3B82F6", "#1E40AF", "#60A5FA", "#93C5FD", "#DBEAFE", "#EFF6FF",
                "#10B981", "#047857", "#34D399", "#6EE7B7", "#A7F3D0", "#D1FAE5",
                "#F59E0B", "#D97706", "#FBBF24", "#FCD34D", "#FDE68A", "#FEF3C7",
                "#EF4444", "#DC2626", "#F87171", "#FCA5A5", "#FECACA", "#FEE2E2",
                "#8B5CF6", "#7C3AED", "#A78BFA", "#C4B5FD", "#DDD6FE", "#EDE9FE",
                "#EC4899", "#DB2777", "#F472B6", "#F9A8D4", "#FBCFE8", "#FCE7F3",
                "#6B7280", "#4B5563", "#9CA3AF", "#D1D5DB", "#E5E7EB", "#F3F4F6"
            };

            foreach (var color in systemColors)
            {
                var colorButton = CreateColorButton(color);
                SystemColorsGrid.Children.Add(colorButton);
            }
        }

        private Border CreateColorButton(string colorHex)
        {
            var border = new Border
            {
                Width = 30,
                Height = 30,
                Margin = new Thickness(2),
                CornerRadius = new CornerRadius(6),
                BorderThickness = new Thickness(2),
                BorderBrush = Brushes.Transparent,
                Cursor = Cursors.Hand,
                ToolTip = colorHex
            };

            try
            {
                var color = (Color)ColorConverter.ConvertFromString(colorHex);
                border.Background = new SolidColorBrush(color);
            }
            catch
            {
                border.Background = Brushes.Gray;
            }

            border.MouseLeftButtonDown += (s, e) =>
            {
                SelectedColor = colorHex;
                ColorCodeTextBox.Text = colorHex;
                UpdatePreview();
                HighlightSelectedColor(border);
            };

            border.MouseEnter += (s, e) =>
            {
                border.BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#3B82F6"));
            };

            border.MouseLeave += (s, e) =>
            {
                if (colorHex != SelectedColor)
                {
                    border.BorderBrush = Brushes.Transparent;
                }
            };

            return border;
        }

        private void HighlightSelectedColor(Border selectedBorder)
        {
            // إزالة التحديد من جميع الألوان
            foreach (Border border in ColorsGrid.Children)
            {
                border.BorderBrush = Brushes.Transparent;
            }

            foreach (Border border in SystemColorsGrid.Children)
            {
                border.BorderBrush = Brushes.Transparent;
            }

            // تحديد اللون المختار
            selectedBorder.BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#3B82F6"));
        }

        private void UpdatePreview()
        {
            try
            {
                var color = (Color)ColorConverter.ConvertFromString(SelectedColor);
                SelectedColorPreview.Background = new SolidColorBrush(color);
            }
            catch
            {
                SelectedColorPreview.Background = Brushes.Gray;
            }
        }

        private void ColorCode_TextChanged(object sender, TextChangedEventArgs e)
        {
            var colorText = ColorCodeTextBox.Text;
            if (IsValidHexColor(colorText))
            {
                SelectedColor = colorText;
                UpdatePreview();
            }
        }

        private bool IsValidHexColor(string color)
        {
            if (string.IsNullOrEmpty(color) || !color.StartsWith("#"))
                return false;

            if (color.Length != 7 && color.Length != 4)
                return false;

            try
            {
                ColorConverter.ConvertFromString(color);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private void Ok_Click(object sender, RoutedEventArgs e)
        {
            if (IsValidHexColor(SelectedColor))
            {
                DialogResult = true;
                Close();
            }
            else
            {
                MessageBox.Show("يرجى إدخال لون صحيح بصيغة hex (مثل: #FF0000)", "لون غير صحيح", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
