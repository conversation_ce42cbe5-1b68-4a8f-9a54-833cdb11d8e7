<Window x:Class="AvocatPro.Views.Windows.ColorPickerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="اختيار اللون" Height="400" Width="350"
        WindowStartupLocation="CenterOwner" ResizeMode="NoResize"
        FlowDirection="RightToLeft" Background="{DynamicResource BackgroundColor}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="اختر اللون المطلوب" FontSize="18" FontWeight="SemiBold" 
                   Foreground="{DynamicResource TextColor}" Margin="0,0,0,20"/>

        <!-- الألوان المحددة مسبقاً -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <TextBlock Text="الألوان الشائعة:" FontSize="14" FontWeight="SemiBold" 
                           Foreground="{DynamicResource TextColor}" Margin="0,0,0,10"/>
                
                <UniformGrid x:Name="ColorsGrid" Columns="6" Margin="0,0,0,20">
                    <!-- سيتم إضافة الألوان ديناميكياً -->
                </UniformGrid>

                <TextBlock Text="ألوان النظام:" FontSize="14" FontWeight="SemiBold" 
                           Foreground="{DynamicResource TextColor}" Margin="0,0,0,10"/>
                
                <UniformGrid x:Name="SystemColorsGrid" Columns="6" Margin="0,0,0,20">
                    <!-- سيتم إضافة ألوان النظام ديناميكياً -->
                </UniformGrid>
            </StackPanel>
        </ScrollViewer>

        <!-- معاينة اللون المحدد -->
        <Border Grid.Row="2" Height="60" CornerRadius="8" Margin="0,10,0,20"
                BorderBrush="{DynamicResource TextSecondaryColor}" BorderThickness="2">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <Border x:Name="SelectedColorPreview" Grid.Column="0" Background="#3B82F6" 
                        CornerRadius="6" Margin="5"/>
                
                <StackPanel Grid.Column="1" VerticalAlignment="Center" Margin="10,0">
                    <TextBlock Text="اللون المحدد:" FontSize="12" 
                               Foreground="{DynamicResource TextSecondaryColor}"/>
                    <TextBox x:Name="ColorCodeTextBox" Text="#3B82F6" FontSize="14" 
                             Padding="8,4" Margin="0,5,0,0" TextChanged="ColorCode_TextChanged"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Left">
            <Button x:Name="OkButton" Content="موافق" Padding="20,8" Margin="0,0,10,0"
                    Background="{DynamicResource SuccessColor}" Foreground="White" 
                    BorderThickness="0" Click="Ok_Click"/>
            <Button x:Name="CancelButton" Content="إلغاء" Padding="20,8"
                    Background="{DynamicResource ErrorColor}" Foreground="White" 
                    BorderThickness="0" Click="Cancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
