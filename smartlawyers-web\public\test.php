<?php
echo "PHP يعمل بنجاح! 🎉<br>";
echo "الإصدار: " . PHP_VERSION . "<br>";
echo "الوقت: " . date('Y-m-d H:i:s') . "<br>";
echo "المجلد: " . __DIR__ . "<br>";

echo "<h3>الإضافات المطلوبة:</h3>";
$extensions = ['pdo', 'mbstring', 'openssl', 'tokenizer', 'xml', 'ctype', 'json', 'curl'];
foreach ($extensions as $ext) {
    $status = extension_loaded($ext) ? '✅' : '❌';
    echo "$status $ext<br>";
}

echo "<h3>الملفات:</h3>";
echo "index.php موجود: " . (file_exists('index.php') ? '✅' : '❌') . "<br>";
echo "install.php موجود: " . (file_exists('../install.php') ? '✅' : '❌') . "<br>";
echo ".env موجود: " . (file_exists('../.env') ? '✅' : '❌') . "<br>";

echo "<h3>الروابط:</h3>";
echo '<a href="/">الصفحة الرئيسية</a><br>';
echo '<a href="/install.php">صفحة التثبيت</a><br>';
echo '<a href="?phpinfo=1">معلومات PHP</a><br>';

if (isset($_GET['phpinfo'])) {
    echo "<hr>";
    phpinfo();
}
?>
