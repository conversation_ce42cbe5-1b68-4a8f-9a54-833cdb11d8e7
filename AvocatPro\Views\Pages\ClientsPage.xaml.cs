using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using AvocatPro.Models;

namespace AvocatPro.Views.Pages;

public partial class ClientsPage : Page
{
    private readonly User _currentUser;
    private readonly ObservableCollection<ClientDisplayModel> _allClients;
    private readonly ObservableCollection<ClientDisplayModel> _filteredClients;

    public ClientsPage(User currentUser)
    {
        InitializeComponent();
        _currentUser = currentUser;
        _allClients = new ObservableCollection<ClientDisplayModel>();
        _filteredClients = new ObservableCollection<ClientDisplayModel>();

        // تأكد من أن العناصر محملة قبل الوصول إليها
        this.Loaded += ClientsPage_Loaded;
    }

    private void ClientsPage_Loaded(object sender, RoutedEventArgs e)
    {
        try
        {
            if (ClientsDataGrid != null)
            {
                ClientsDataGrid.ItemsSource = _filteredClients;
            }

            LoadSampleData();
            SetupSearchBox();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل صفحة الموكلين: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void LoadSampleData()
    {
        var sampleClients = new List<ClientDisplayModel>
        {
            new ClientDisplayModel
            {
                Id = 1,
                FullName = "أحمد محمد علي السعيد",
                Phone = "0123456789",
                Email = "<EMAIL>",
                Type = ClientType.Individual,
                Status = ClientStatus.Active,
                CreatedAt = DateTime.Now.AddDays(-30),
                OfficeReference = "CLT-20241201-001",
                FileReference = "FILE-2024-001",
                IdentityNumber = "1234567890",
                Address = "الرياض، حي النخيل، شارع الملك فهد",
                City = "الرياض",
                Nationality = "سعودي",
                Profession = "مهندس"
            },
            new ClientDisplayModel
            {
                Id = 2,
                FullName = "شركة النور للتجارة والاستثمار",
                Phone = "0987654321",
                Email = "<EMAIL>",
                Type = ClientType.Company,
                Status = ClientStatus.Active,
                CreatedAt = DateTime.Now.AddDays(-15),
                OfficeReference = "CLT-20241215-002",
                FileReference = "FILE-2024-002",
                IdentityNumber = "7001234567",
                Address = "جدة، حي الحمراء، طريق الملك عبدالعزيز",
                City = "جدة",
                Nationality = "سعودي",
                Profession = "تجارة واستثمار"
            },
            new ClientDisplayModel
            {
                Id = 3,
                FullName = "فاطمة أحمد سالم",
                Phone = "0555123456",
                Email = "<EMAIL>",
                Type = ClientType.Individual,
                Status = ClientStatus.Inactive,
                CreatedAt = DateTime.Now.AddDays(-60),
                OfficeReference = "CLT-20241101-003",
                FileReference = "FILE-2024-003",
                IdentityNumber = "2345678901",
                Address = "الدمام، حي الشاطئ، شارع الخليج",
                City = "الدمام",
                Nationality = "سعودي",
                Profession = "طبيبة"
            },
            new ClientDisplayModel
            {
                Id = 4,
                FullName = "وزارة التربية والتعليم",
                Phone = "0444567890",
                Email = "<EMAIL>",
                Type = ClientType.Government,
                Status = ClientStatus.Active,
                CreatedAt = DateTime.Now.AddDays(-45),
                OfficeReference = "CLT-20241115-004",
                FileReference = "FILE-2024-004",
                IdentityNumber = "GOV-123456",
                Address = "الرياض، حي الوزارات، طريق الملك فهد",
                City = "الرياض",
                Nationality = "سعودي",
                Profession = "جهة حكومية"
            },
            new ClientDisplayModel
            {
                Id = 5,
                FullName = "جمعية البر الخيرية",
                Phone = "0333789012",
                Email = "<EMAIL>",
                Type = ClientType.Association,
                Status = ClientStatus.Active,
                CreatedAt = DateTime.Now.AddDays(-20),
                OfficeReference = "CLT-20241210-005",
                FileReference = "FILE-2024-005",
                IdentityNumber = "ASS-789012",
                Address = "مكة المكرمة، حي العزيزية، شارع الحرم",
                City = "مكة المكرمة",
                Nationality = "سعودي",
                Profession = "عمل خيري"
            }
        };

        _allClients.Clear();
        _filteredClients.Clear();

        foreach (var client in sampleClients)
        {
            _allClients.Add(client);
            _filteredClients.Add(client);
        }
    }

    private void SetupSearchBox()
    {
        try
        {
            if (SearchTextBox != null)
            {
                SearchTextBox.GotFocus += (s, e) =>
                {
                    if (SearchTextBox.Text == "البحث في الموكلين...")
                    {
                        SearchTextBox.Text = "";
                        SearchTextBox.Foreground = new SolidColorBrush(Colors.Black);
                    }
                };

                SearchTextBox.LostFocus += (s, e) =>
                {
                    if (string.IsNullOrWhiteSpace(SearchTextBox.Text))
                    {
                        SearchTextBox.Text = "البحث في الموكلين...";
                        SearchTextBox.Foreground = new SolidColorBrush(Color.FromRgb(153, 153, 153));
                    }
                };
            }
        }
        catch (Exception ex)
        {
            // تجاهل الأخطاء في إعداد مربع البحث
            System.Diagnostics.Debug.WriteLine($"خطأ في إعداد مربع البحث: {ex.Message}");
        }
    }

    private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
    {
        if (SearchTextBox.Text == "البحث في الموكلين...")
        {
            SearchTextBox.Text = "";
            SearchTextBox.Foreground = new SolidColorBrush(Colors.Black);
        }
    }

    private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
    {
        if (string.IsNullOrWhiteSpace(SearchTextBox.Text))
        {
            SearchTextBox.Text = "البحث في الموكلين...";
            SearchTextBox.Foreground = new SolidColorBrush(Color.FromRgb(153, 153, 153));
        }
    }

    private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
    {
        ApplyFilters();
    }

    private void ClientTypeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        ApplyFilters();
    }

    private void ClientStatusComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        ApplyFilters();
    }

    private void ApplyFilters()
    {
        try
        {
            var searchTerm = SearchTextBox?.Text?.Trim().ToLower();
            if (searchTerm == "البحث في الموكلين...") searchTerm = "";

            var selectedType = (ClientTypeComboBox?.SelectedItem as ComboBoxItem)?.Tag?.ToString();
            var selectedStatus = (ClientStatusComboBox?.SelectedItem as ComboBoxItem)?.Tag?.ToString();

            var filtered = _allClients.Where(client =>
            {
                // تصفية البحث النصي
                var matchesSearch = string.IsNullOrEmpty(searchTerm) ||
                                   client.FullName.ToLower().Contains(searchTerm) ||
                                   client.Email.ToLower().Contains(searchTerm) ||
                                   client.Phone.Contains(searchTerm);

                // تصفية النوع
                var matchesType = selectedType == "All" || selectedType == null ||
                                 client.Type.ToString() == selectedType;

                // تصفية الحالة
                var matchesStatus = selectedStatus == "All" || selectedStatus == null ||
                                   client.Status.ToString() == selectedStatus;

                return matchesSearch && matchesType && matchesStatus;
            });

            _filteredClients.Clear();
            foreach (var client in filtered)
            {
                _filteredClients.Add(client);
            }
        }
        catch (Exception ex)
        {
            // تجاهل الأخطاء في التصفية
            System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق المرشحات: {ex.Message}");
        }
    }

    private void ClearFiltersButton_Click(object sender, RoutedEventArgs e)
    {
        SearchTextBox.Text = "البحث في الموكلين...";
        SearchTextBox.Foreground = new SolidColorBrush(Color.FromRgb(153, 153, 153));
        ClientTypeComboBox.SelectedIndex = 0;
        ClientStatusComboBox.SelectedIndex = 0;
        ApplyFilters();
    }

    private void AddClientButton_Click(object sender, RoutedEventArgs e)
    {
        var addWindow = new AvocatPro.Views.Windows.AddClientWindow();
        if (addWindow.ShowDialog() == true)
        {
            if (addWindow.NewClient != null)
            {
                // إضافة الموكل الجديد إلى القائمة
                var newClientDisplay = new ClientDisplayModel
                {
                    Id = _allClients.Count + 1,
                    FullName = addWindow.NewClient.FullName,
                    Phone = addWindow.NewClient.Phone,
                    Email = addWindow.NewClient.Email ?? "",
                    Type = addWindow.NewClient.Type,
                    Status = addWindow.NewClient.Status,
                    CreatedAt = addWindow.NewClient.CreatedAt,
                    OfficeReference = addWindow.NewClient.OfficeReference,
                    FileReference = addWindow.NewClient.FileReference,
                    IdentityNumber = addWindow.NewClient.IdentityNumber,
                    Address = addWindow.NewClient.Address,
                    City = addWindow.NewClient.City,
                    Nationality = addWindow.NewClient.Nationality,
                    Profession = addWindow.NewClient.Profession
                };

                _allClients.Add(newClientDisplay);
                ApplyFilters();

                if (addWindow.SaveAndAddAnother)
                {
                    // فتح نافذة جديدة لإضافة موكل آخر
                    AddClientButton_Click(sender, e);
                }
            }
        }
    }

    private void RefreshButton_Click(object sender, RoutedEventArgs e)
    {
        LoadSampleData();
        MessageBox.Show("تم تحديث البيانات بنجاح", "تحديث", 
                       MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void ExportButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var dialog = new Microsoft.Win32.SaveFileDialog
            {
                Filter = "Excel Files (*.xlsx)|*.xlsx|PDF Files (*.pdf)|*.pdf|CSV Files (*.csv)|*.csv",
                DefaultExt = "xlsx",
                FileName = $"قائمة_الموكلين_{DateTime.Now:yyyyMMdd}"
            };

            if (dialog.ShowDialog() == true)
            {
                var extension = System.IO.Path.GetExtension(dialog.FileName).ToLower();

                switch (extension)
                {
                    case ".xlsx":
                        ExportToExcel(dialog.FileName);
                        break;
                    case ".pdf":
                        ExportToPdf(dialog.FileName);
                        break;
                    case ".csv":
                        ExportToCsv(dialog.FileName);
                        break;
                }

                MessageBox.Show($"تم تصدير البيانات بنجاح إلى:\n{dialog.FileName}", "نجح التصدير",
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء التصدير:\n{ex.Message}", "خطأ في التصدير",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ExportToExcel(string fileName)
    {
        // محاكاة تصدير Excel - في التطبيق الحقيقي نستخدم مكتبة مثل EPPlus
        var csv = GenerateCsvContent();
        System.IO.File.WriteAllText(fileName.Replace(".xlsx", ".csv"), csv, System.Text.Encoding.UTF8);
    }

    private void ExportToPdf(string fileName)
    {
        // محاكاة تصدير PDF - في التطبيق الحقيقي نستخدم مكتبة مثل iTextSharp
        var content = GenerateReportContent();
        System.IO.File.WriteAllText(fileName.Replace(".pdf", ".txt"), content, System.Text.Encoding.UTF8);
    }

    private void ExportToCsv(string fileName)
    {
        var csv = GenerateCsvContent();
        System.IO.File.WriteAllText(fileName, csv, System.Text.Encoding.UTF8);
    }

    private string GenerateCsvContent()
    {
        var sb = new System.Text.StringBuilder();
        sb.AppendLine("الرقم,مرجع المكتب,الاسم الكامل,النوع,رقم الهاتف,البريد الإلكتروني,المدينة,الحالة,تاريخ الإضافة");

        foreach (var client in _filteredClients)
        {
            sb.AppendLine($"{client.Id},{client.OfficeReferenceDisplay},{client.FullName},{client.TypeDisplay},{client.Phone},{client.Email},{client.CityDisplay},{client.StatusDisplay},{client.CreatedAtDisplay}");
        }

        return sb.ToString();
    }

    private string GenerateReportContent()
    {
        var sb = new System.Text.StringBuilder();
        sb.AppendLine("تقرير الموكلين");
        sb.AppendLine("================");
        sb.AppendLine($"تاريخ التقرير: {DateTime.Now:dd/MM/yyyy HH:mm}");
        sb.AppendLine($"عدد الموكلين: {_filteredClients.Count}");
        sb.AppendLine();

        foreach (var client in _filteredClients)
        {
            sb.AppendLine($"الرقم: {client.Id}");
            sb.AppendLine($"مرجع المكتب: {client.OfficeReferenceDisplay}");
            sb.AppendLine($"الاسم: {client.FullName}");
            sb.AppendLine($"النوع: {client.TypeDisplay}");
            sb.AppendLine($"الهاتف: {client.Phone}");
            sb.AppendLine($"البريد: {client.Email}");
            sb.AppendLine($"المدينة: {client.CityDisplay}");
            sb.AppendLine($"الحالة: {client.StatusDisplay}");
            sb.AppendLine($"تاريخ الإضافة: {client.CreatedAtDisplay}");
            sb.AppendLine("---");
        }

        return sb.ToString();
    }

    private void ViewClientButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int clientId)
        {
            MessageBox.Show($"عرض تفاصيل الموكل رقم: {clientId}", "عرض الموكل", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void EditClientButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int clientId)
        {
            MessageBox.Show($"تعديل بيانات الموكل رقم: {clientId}", "تعديل الموكل", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void DeleteClientButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int clientId)
        {
            var client = _allClients.FirstOrDefault(c => c.Id == clientId);
            if (client == null) return;

            var result = MessageBox.Show($"هل أنت متأكد من حذف الموكل: {client.FullName}؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                                       "تأكيد الحذف",
                                       MessageBoxButton.YesNo,
                                       MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                _allClients.Remove(client);
                ApplyFilters();
                MessageBox.Show("تم حذف الموكل بنجاح", "حذف",
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }

    private void ViewClientCasesButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int clientId)
        {
            var client = _allClients.FirstOrDefault(c => c.Id == clientId);
            if (client != null)
            {
                MessageBox.Show($"عرض ملفات وقضايا الموكل: {client.FullName}\n\nهذه الميزة قيد التطوير",
                               "ملفات الموكل", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }

    private void ViewClientAppointmentsButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int clientId)
        {
            var client = _allClients.FirstOrDefault(c => c.Id == clientId);
            if (client != null)
            {
                MessageBox.Show($"عرض مواعيد وجلسات الموكل: {client.FullName}\n\nهذه الميزة قيد التطوير",
                               "مواعيد الموكل", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }
}

// نموذج عرض البيانات
public class ClientDisplayModel
{
    public int Id { get; set; }
    public string FullName { get; set; } = "";
    public string Phone { get; set; } = "";
    public string Email { get; set; } = "";
    public ClientType Type { get; set; }
    public ClientStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public string? OfficeReference { get; set; }
    public string? FileReference { get; set; }
    public string? IdentityNumber { get; set; }
    public string? Address { get; set; }
    public string? City { get; set; }
    public string? Nationality { get; set; }
    public string? Profession { get; set; }

    public string TypeDisplay => Type switch
    {
        ClientType.Individual => "فرد",
        ClientType.Company => "شركة",
        ClientType.Government => "حكومي",
        ClientType.Association => "جمعية",
        _ => "غير محدد"
    };

    public string StatusDisplay => Status switch
    {
        ClientStatus.Active => "نشط",
        ClientStatus.Inactive => "غير نشط",
        ClientStatus.Blocked => "محظور",
        _ => "غير محدد"
    };

    public string CreatedAtDisplay => CreatedAt.ToString("dd/MM/yyyy");

    public string OfficeReferenceDisplay => OfficeReference ?? "غير محدد";
    public string FileReferenceDisplay => FileReference ?? "غير محدد";
    public string IdentityNumberDisplay => IdentityNumber ?? "غير محدد";
    public string AddressDisplay => Address ?? "غير محدد";
    public string CityDisplay => City ?? "غير محدد";
    public string NationalityDisplay => Nationality ?? "غير محدد";
    public string ProfessionDisplay => Profession ?? "غير محدد";
}

// إضافة وظائف الطباعة والاستيراد
public partial class ClientsPage
{
    private void PrintButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var printDialog = new System.Windows.Controls.PrintDialog();
            if (printDialog.ShowDialog() == true)
            {
                // إنشاء مستند للطباعة
                var document = new System.Windows.Documents.FlowDocument();
                var paragraph = new System.Windows.Documents.Paragraph();

                paragraph.Inlines.Add(new System.Windows.Documents.Run("تقرير الموكلين\n")
                {
                    FontSize = 18,
                    FontWeight = FontWeights.Bold
                });

                paragraph.Inlines.Add(new System.Windows.Documents.Run($"تاريخ التقرير: {DateTime.Now:dd/MM/yyyy HH:mm}\n"));
                paragraph.Inlines.Add(new System.Windows.Documents.Run($"عدد الموكلين: {_filteredClients.Count}\n\n"));

                foreach (var client in _filteredClients)
                {
                    paragraph.Inlines.Add(new System.Windows.Documents.Run($"• {client.FullName} - {client.Phone} - {client.TypeDisplay}\n"));
                }

                document.Blocks.Add(paragraph);

                var paginator = ((System.Windows.Documents.IDocumentPaginatorSource)document).DocumentPaginator;
                printDialog.PrintDocument(paginator, "تقرير الموكلين");

                MessageBox.Show("تم إرسال التقرير للطباعة بنجاح", "طباعة",
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء الطباعة:\n{ex.Message}", "خطأ في الطباعة",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ImportButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var dialog = new Microsoft.Win32.OpenFileDialog
            {
                Filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv",
                Title = "اختر ملف لاستيراد الموكلين"
            };

            if (dialog.ShowDialog() == true)
            {
                var result = MessageBox.Show($"هل تريد استيراد الموكلين من الملف:\n{dialog.FileName}؟\n\nسيتم إضافة الموكلين الجدد إلى القائمة الحالية.",
                                           "تأكيد الاستيراد",
                                           MessageBoxButton.YesNo,
                                           MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // محاكاة استيراد البيانات
                    ImportFromFile(dialog.FileName);
                    MessageBox.Show("تم استيراد البيانات بنجاح!", "نجح الاستيراد",
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء الاستيراد:\n{ex.Message}", "خطأ في الاستيراد",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ImportFromFile(string fileName)
    {
        // محاكاة استيراد البيانات - في التطبيق الحقيقي نقرأ من Excel أو CSV
        var importedClients = new List<ClientDisplayModel>
        {
            new ClientDisplayModel
            {
                Id = _allClients.Count + 1,
                FullName = "موكل مستورد 1",
                Phone = "0501234567",
                Email = "<EMAIL>",
                Type = ClientType.Individual,
                Status = ClientStatus.Active,
                CreatedAt = DateTime.Now,
                OfficeReference = $"IMP-{DateTime.Now:yyyyMMdd}-001",
                City = "الرياض"
            },
            new ClientDisplayModel
            {
                Id = _allClients.Count + 2,
                FullName = "شركة مستوردة 2",
                Phone = "0507654321",
                Email = "<EMAIL>",
                Type = ClientType.Company,
                Status = ClientStatus.Active,
                CreatedAt = DateTime.Now,
                OfficeReference = $"IMP-{DateTime.Now:yyyyMMdd}-002",
                City = "جدة"
            }
        };

        foreach (var client in importedClients)
        {
            _allClients.Add(client);
        }

        ApplyFilters();
    }
}
