<Page x:Class="AvocatPro.Views.Pages.CasesPageNew"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="إدارة الملفات"
      FlowDirection="RightToLeft">

    <Page.Resources>
        <!-- أنماط الصفحة -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1976D2"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>

        <Style x:Key="StatCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#1976D2"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5,0"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1565C0"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="FontSize" Value="14"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F0F0F0"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="SearchTextBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Foreground" Value="#999"/>
            <Style.Triggers>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="BorderBrush" Value="#1976D2"/>
                    <Setter Property="BorderThickness" Value="2"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="FilterComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Margin" Value="5,0"/>
        </Style>
    </Page.Resources>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- رأس الصفحة -->
        <Border Grid.Row="0" Background="White" Padding="30,20" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="📁" FontSize="32" Margin="0,0,15,0"/>
                    <StackPanel>
                        <TextBlock Text="إدارة الملفات والقضايا" Style="{StaticResource HeaderTextStyle}"/>
                        <TextBlock Text="إدارة شاملة لجميع ملفات المكتب والقضايا" FontSize="14" Foreground="#666"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Name="AddCaseButton" Style="{StaticResource PrimaryButtonStyle}" Click="AddCaseButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="➕" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="إضافة ملف جديد"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Name="RefreshButton" Style="{StaticResource PrimaryButtonStyle}" 
                           Background="#4CAF50" Click="RefreshButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔄" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- بطاقات الإحصائيات -->
        <Grid Grid.Row="1" Margin="20,0,20,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Border Grid.Column="0" Style="{StaticResource StatCardStyle}">
                <StackPanel>
                    <TextBlock Text="📊 إجمالي الملفات" FontSize="14" Foreground="#666" Margin="0,0,0,10"/>
                    <TextBlock Name="TotalCasesTextBlock" Text="0" FontSize="28" FontWeight="Bold" Foreground="#1976D2"/>
                </StackPanel>
            </Border>

            <Border Grid.Column="1" Style="{StaticResource StatCardStyle}">
                <StackPanel>
                    <TextBlock Text="🟢 الملفات النشطة" FontSize="14" Foreground="#666" Margin="0,0,0,10"/>
                    <TextBlock Name="ActiveCasesTextBlock" Text="0" FontSize="28" FontWeight="Bold" Foreground="#4CAF50"/>
                </StackPanel>
            </Border>

            <Border Grid.Column="2" Style="{StaticResource StatCardStyle}">
                <StackPanel>
                    <TextBlock Text="🔴 الملفات المغلقة" FontSize="14" Foreground="#666" Margin="0,0,0,10"/>
                    <TextBlock Name="ClosedCasesTextBlock" Text="0" FontSize="28" FontWeight="Bold" Foreground="#F44336"/>
                </StackPanel>
            </Border>

            <Border Grid.Column="3" Style="{StaticResource StatCardStyle}">
                <StackPanel>
                    <TextBlock Text="📦 الملفات المؤرشفة" FontSize="14" Foreground="#666" Margin="0,0,0,10"/>
                    <TextBlock Name="ArchivedCasesTextBlock" Text="0" FontSize="28" FontWeight="Bold" Foreground="#FF9800"/>
                </StackPanel>
            </Border>
        </Grid>

        <!-- شريط البحث والتصفية -->
        <Border Grid.Row="2" Background="White" Padding="20" Margin="20,0,20,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBox Name="SearchTextBox" Grid.Column="0" 
                        Style="{StaticResource SearchTextBoxStyle}"
                        Text="البحث في الملفات..." 
                        TextChanged="SearchTextBox_TextChanged"/>

                <ComboBox Name="CaseTypeComboBox" Grid.Column="1" 
                         Style="{StaticResource FilterComboBoxStyle}"
                         SelectionChanged="FilterComboBox_SelectionChanged">
                    <ComboBoxItem Content="جميع الأنواع" Tag="All" IsSelected="True"/>
                    <ComboBoxItem Content="مدنية" Tag="Civil"/>
                    <ComboBoxItem Content="جنائية" Tag="Criminal"/>
                    <ComboBoxItem Content="تجارية" Tag="Commercial"/>
                    <ComboBoxItem Content="إدارية" Tag="Administrative"/>
                    <ComboBoxItem Content="عمالية" Tag="Labor"/>
                    <ComboBoxItem Content="أسرة" Tag="Family"/>
                    <ComboBoxItem Content="عقارية" Tag="RealEstate"/>
                    <ComboBoxItem Content="ضريبية" Tag="Tax"/>
                </ComboBox>

                <ComboBox Name="CaseStatusComboBox" Grid.Column="2" 
                         Style="{StaticResource FilterComboBoxStyle}"
                         SelectionChanged="FilterComboBox_SelectionChanged">
                    <ComboBoxItem Content="جميع الحالات" Tag="All" IsSelected="True"/>
                    <ComboBoxItem Content="نشطة" Tag="Active"/>
                    <ComboBoxItem Content="مؤجلة" Tag="Postponed"/>
                    <ComboBoxItem Content="مغلقة" Tag="Closed"/>
                    <ComboBoxItem Content="مؤرشفة" Tag="Archived"/>
                    <ComboBoxItem Content="ملغاة" Tag="Cancelled"/>
                </ComboBox>

                <ComboBox Name="CasePriorityComboBox" Grid.Column="3" 
                         Style="{StaticResource FilterComboBoxStyle}"
                         SelectionChanged="FilterComboBox_SelectionChanged">
                    <ComboBoxItem Content="جميع الأولويات" Tag="All" IsSelected="True"/>
                    <ComboBoxItem Content="منخفضة" Tag="Low"/>
                    <ComboBoxItem Content="متوسطة" Tag="Medium"/>
                    <ComboBoxItem Content="عالية" Tag="High"/>
                    <ComboBoxItem Content="عاجلة" Tag="Urgent"/>
                </ComboBox>

                <Button Name="ClearFiltersButton" Grid.Column="4" 
                       Style="{StaticResource PrimaryButtonStyle}" 
                       Background="#FF9800" Click="ClearFiltersButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🗑️" FontSize="14" Margin="0,0,5,0"/>
                        <TextBlock Text="مسح التصفية"/>
                    </StackPanel>
                </Button>
            </Grid>
        </Border>

        <!-- جدول البيانات -->
        <Border Grid.Row="3" Background="White" Margin="20,0,20,20" CornerRadius="8">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- شريط الأدوات -->
                <Border Grid.Row="0" Background="#F8F9FA" Padding="15" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                        <Button Name="ExportButton" Style="{StaticResource PrimaryButtonStyle}" 
                               Background="#FF9800" Click="ExportButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📤" FontSize="14" Margin="0,0,5,0"/>
                                <TextBlock Text="تصدير"/>
                            </StackPanel>
                        </Button>
                        
                        <Button Name="PrintButton" Style="{StaticResource PrimaryButtonStyle}" 
                               Background="#9C27B0" Click="PrintButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🖨️" FontSize="14" Margin="0,0,5,0"/>
                                <TextBlock Text="طباعة"/>
                            </StackPanel>
                        </Button>
                        
                        <Button Name="ArchiveButton" Style="{StaticResource PrimaryButtonStyle}" 
                               Background="#607D8B" Click="ArchiveButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📦" FontSize="14" Margin="0,0,5,0"/>
                                <TextBlock Text="أرشفة"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Border>

                <!-- الجدول -->
                <DataGrid Name="CasesDataGrid" Grid.Row="1" 
                         AutoGenerateColumns="False" 
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         IsReadOnly="True"
                         GridLinesVisibility="Horizontal"
                         HeadersVisibility="Column"
                         AlternatingRowBackground="#F9F9F9"
                         RowHeight="45"
                         FontSize="12">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="60"/>
                        <DataGridTextColumn Header="مرجع المكتب" Binding="{Binding OfficeReference}" Width="120"/>
                        <DataGridTextColumn Header="عنوان القضية" Binding="{Binding Title}" Width="200"/>
                        <DataGridTextColumn Header="النوع" Binding="{Binding TypeDisplay}" Width="80"/>
                        <DataGridTextColumn Header="الحالة" Binding="{Binding StatusDisplay}" Width="80"/>
                        <DataGridTextColumn Header="المحكمة" Binding="{Binding Court}" Width="150"/>
                        <DataGridTextColumn Header="الموكل" Binding="{Binding ClientName}" Width="150"/>
                        <DataGridTextColumn Header="المحامي" Binding="{Binding LawyerName}" Width="120"/>
                        <DataGridTextColumn Header="الجلسة القادمة" Binding="{Binding NextSessionDateDisplay}" Width="100"/>
                        
                        <DataGridTemplateColumn Header="الإجراءات" Width="200">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button Style="{StaticResource ActionButtonStyle}" 
                                               ToolTip="عرض التفاصيل" 
                                               Click="ViewCaseButton_Click" 
                                               Tag="{Binding Id}">
                                            <TextBlock Text="👁️" FontSize="14"/>
                                        </Button>
                                        <Button Style="{StaticResource ActionButtonStyle}" 
                                               ToolTip="تعديل" 
                                               Click="EditCaseButton_Click" 
                                               Tag="{Binding Id}">
                                            <TextBlock Text="✏️" FontSize="14"/>
                                        </Button>
                                        <Button Style="{StaticResource ActionButtonStyle}" 
                                               ToolTip="الجلسات" 
                                               Click="SessionsButton_Click" 
                                               Tag="{Binding Id}">
                                            <TextBlock Text="⚖️" FontSize="14"/>
                                        </Button>
                                        <Button Style="{StaticResource ActionButtonStyle}" 
                                               ToolTip="المرفقات" 
                                               Click="DocumentsButton_Click" 
                                               Tag="{Binding Id}">
                                            <TextBlock Text="📎" FontSize="14"/>
                                        </Button>
                                        <Button Style="{StaticResource ActionButtonStyle}" 
                                               ToolTip="حذف" 
                                               Click="DeleteCaseButton_Click" 
                                               Tag="{Binding Id}">
                                            <TextBlock Text="🗑️" FontSize="14" Foreground="Red"/>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- تذييل الجدول -->
                <Border Grid.Row="2" Background="#F8F9FA" Padding="15" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
                    <TextBlock Name="StatusTextBlock" Text="جاري التحميل..." FontSize="12" Foreground="#666"/>
                </Border>
            </Grid>
        </Border>
    </Grid>
</Page>
