<Page x:Class="AvocatPro.Views.Pages.AdvancedComprehensiveSettingsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="الإعدادات الشاملة المتطورة" Background="#F8FAFC"
      FlowDirection="RightToLeft">

    <Page.Resources>
        <!-- أنماط مخصصة -->
        <Style TargetType="TextBox" x:Key="ModernTextBoxStyle">
            <Setter Property="Padding" Value="12,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#E5E7EB"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Margin" Value="0,5,0,15"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Style.Triggers>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="BorderBrush" Value="#3B82F6"/>
                    <Setter Property="Background" Value="#F8FAFF"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style TargetType="ComboBox" x:Key="ModernComboBoxStyle">
            <Setter Property="Padding" Value="12,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#E5E7EB"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Margin" Value="0,5,0,15"/>
        </Style>

        <Style TargetType="Button" x:Key="PrimaryButtonStyle">
            <Setter Property="Background" Value="#3B82F6"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="8" Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#2563EB"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style TargetType="Button" x:Key="SecondaryButtonStyle" BasedOn="{StaticResource PrimaryButtonStyle}">
            <Setter Property="Background" Value="#6B7280"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#4B5563"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style TargetType="Button" x:Key="DangerButtonStyle" BasedOn="{StaticResource PrimaryButtonStyle}">
            <Setter Property="Background" Value="#EF4444"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#DC2626"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style TargetType="Button" x:Key="SuccessButtonStyle" BasedOn="{StaticResource PrimaryButtonStyle}">
            <Setter Property="Background" Value="#10B981"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#059669"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style TargetType="CheckBox" x:Key="ModernCheckBoxStyle">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,5,0,10"/>
            <Setter Property="Foreground" Value="#374151"/>
        </Style>

        <Style TargetType="Label" x:Key="SectionHeaderStyle">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1F2937"/>
            <Setter Property="Margin" Value="0,20,0,10"/>
        </Style>

        <Style TargetType="Label" x:Key="FieldLabelStyle">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#374151"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>
    </Page.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان الرئيسي والأزرار -->
        <Border Grid.Row="0" Background="White" Padding="30,20" Margin="20,20,20,10">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="15" ShadowDepth="3"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Text="⚙️ الإعدادات الشاملة المتطورة" FontSize="32" FontWeight="Bold" 
                              Foreground="#1F2937" HorizontalAlignment="Center"/>
                    <TextBlock Text="تحكم كامل ومتطور في جميع جوانب النظام" FontSize="16" 
                              Foreground="#6B7280" HorizontalAlignment="Center" Margin="0,8,0,0"/>
                </StackPanel>
                
                <!-- أزرار التحكم السريع -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="DarkModeToggleButton" Content="🌙" Width="60" Height="60" 
                            Style="{StaticResource SecondaryButtonStyle}"
                            Click="DarkModeToggle_Click" ToolTip="تبديل الوضع الليلي/النهاري"/>
                    <Button x:Name="SaveAllButton" Content="💾" Width="60" Height="60" 
                            Style="{StaticResource SuccessButtonStyle}"
                            Click="SaveAllSettings_Click" ToolTip="حفظ جميع الإعدادات"/>
                    <Button x:Name="ResetAllButton" Content="🔄" Width="60" Height="60" 
                            Style="{StaticResource DangerButtonStyle}"
                            Click="ResetAllSettings_Click" ToolTip="إعادة تعيين جميع الإعدادات"/>
                    <Button x:Name="TestConnectionButton" Content="🔗" Width="60" Height="60" 
                            Style="{StaticResource PrimaryButtonStyle}"
                            Click="TestConnection_Click" ToolTip="اختبار الاتصالات"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="20,0,20,20">
            <TabControl x:Name="SettingsTabControl" Background="Transparent" BorderThickness="0">
                <TabControl.Resources>
                    <Style TargetType="TabItem">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="TabItem">
                                    <Border Name="Border" Background="#E5E7EB" BorderThickness="0" 
                                            CornerRadius="8,8,0,0" Margin="2,0">
                                        <ContentPresenter x:Name="ContentSite" VerticalAlignment="Center" 
                                                        HorizontalAlignment="Center" ContentSource="Header" 
                                                        Margin="15,10" TextBlock.FontSize="14" 
                                                        TextBlock.FontWeight="SemiBold"/>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter TargetName="Border" Property="Background" Value="#3B82F6"/>
                                            <Setter TargetName="ContentSite" Property="TextBlock.Foreground" Value="White"/>
                                        </Trigger>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter TargetName="Border" Property="Background" Value="#D1D5DB"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </TabControl.Resources>

                <!-- تبويب إعدادات المكتب -->
                <TabItem Header="🏢 إعدادات المكتب">
                    <Border Background="White" CornerRadius="0,8,8,8" Padding="30">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                        </Border.Effect>
                        <StackPanel>
                            <Label Content="📋 معلومات المكتب الأساسية" Style="{StaticResource SectionHeaderStyle}"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0" Margin="0,0,15,0">
                                    <Label Content="اسم مكتب المحاماة" Style="{StaticResource FieldLabelStyle}"/>
                                    <TextBox x:Name="OfficeNameTextBox" Style="{StaticResource ModernTextBoxStyle}"
                                             Text="مكتب المحامي الذكي للاستشارات القانونية"/>
                                    
                                    <Label Content="رقم الترخيص" Style="{StaticResource FieldLabelStyle}"/>
                                    <TextBox x:Name="LicenseNumberTextBox" Style="{StaticResource ModernTextBoxStyle}"/>
                                    
                                    <Label Content="رقم الهاتف الرئيسي" Style="{StaticResource FieldLabelStyle}"/>
                                    <TextBox x:Name="MainPhoneTextBox" Style="{StaticResource ModernTextBoxStyle}"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="1" Margin="15,0,0,0">
                                    <Label Content="العنوان الكامل" Style="{StaticResource FieldLabelStyle}"/>
                                    <TextBox x:Name="OfficeAddressTextBox" Style="{StaticResource ModernTextBoxStyle}" 
                                             Height="80" TextWrapping="Wrap" AcceptsReturn="True"/>
                                    
                                    <Label Content="البريد الإلكتروني الرسمي" Style="{StaticResource FieldLabelStyle}"/>
                                    <TextBox x:Name="OfficialEmailTextBox" Style="{StaticResource ModernTextBoxStyle}"/>
                                    
                                    <Label Content="الموقع الإلكتروني" Style="{StaticResource FieldLabelStyle}"/>
                                    <TextBox x:Name="WebsiteTextBox" Style="{StaticResource ModernTextBoxStyle}"/>
                                </StackPanel>
                            </Grid>
                            
                            <Label Content="🎨 تخصيص الهوية البصرية" Style="{StaticResource SectionHeaderStyle}"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                    <Label Content="شعار المكتب" Style="{StaticResource FieldLabelStyle}"/>
                                    <Button Content="📁 اختيار الشعار" Style="{StaticResource PrimaryButtonStyle}"
                                            Click="SelectLogo_Click"/>
                                    <Image x:Name="LogoPreview" Height="100" Margin="0,10" 
                                           Source="/Resources/default-logo.png" Stretch="Uniform"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="1" Margin="10,0">
                                    <Label Content="اللون الأساسي" Style="{StaticResource FieldLabelStyle}"/>
                                    <Button x:Name="PrimaryColorButton" Content="🎨 اختيار اللون" 
                                            Style="{StaticResource PrimaryButtonStyle}" Click="SelectPrimaryColor_Click"/>
                                    <Rectangle x:Name="PrimaryColorPreview" Height="40" Fill="#3B82F6" 
                                               Margin="0,10" Stroke="#E5E7EB" StrokeThickness="2"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="2" Margin="10,0,0,0">
                                    <Label Content="اللون الثانوي" Style="{StaticResource FieldLabelStyle}"/>
                                    <Button x:Name="SecondaryColorButton" Content="🎨 اختيار اللون" 
                                            Style="{StaticResource SecondaryButtonStyle}" Click="SelectSecondaryColor_Click"/>
                                    <Rectangle x:Name="SecondaryColorPreview" Height="40" Fill="#6B7280" 
                                               Margin="0,10" Stroke="#E5E7EB" StrokeThickness="2"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>
                </TabItem>

                <!-- تبويب إعدادات البريد الإلكتروني -->
                <TabItem Header="📧 البريد الإلكتروني">
                    <Border Background="White" CornerRadius="0,8,8,8" Padding="30">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                        </Border.Effect>
                        <StackPanel>
                            <Label Content="📮 إعدادات خادم البريد الصادر (SMTP)" Style="{StaticResource SectionHeaderStyle}"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0" Margin="0,0,15,0">
                                    <Label Content="خادم SMTP" Style="{StaticResource FieldLabelStyle}"/>
                                    <TextBox x:Name="SmtpServerTextBox" Style="{StaticResource ModernTextBoxStyle}"
                                             Text="smtp.gmail.com"/>
                                    
                                    <Label Content="المنفذ (Port)" Style="{StaticResource FieldLabelStyle}"/>
                                    <TextBox x:Name="SmtpPortTextBox" Style="{StaticResource ModernTextBoxStyle}"
                                             Text="587"/>
                                    
                                    <CheckBox x:Name="EnableSslCheckBox" Content="تفعيل SSL/TLS" 
                                              Style="{StaticResource ModernCheckBoxStyle}" IsChecked="True"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="1" Margin="15,0,0,0">
                                    <Label Content="اسم المستخدم" Style="{StaticResource FieldLabelStyle}"/>
                                    <TextBox x:Name="EmailUsernameTextBox" Style="{StaticResource ModernTextBoxStyle}"/>
                                    
                                    <Label Content="كلمة المرور" Style="{StaticResource FieldLabelStyle}"/>
                                    <PasswordBox x:Name="EmailPasswordBox" Style="{StaticResource ModernTextBoxStyle}"/>
                                    
                                    <Button Content="🧪 اختبار الاتصال" Style="{StaticResource PrimaryButtonStyle}"
                                            Click="TestEmailConnection_Click"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>
                </TabItem>

                <!-- تبويب الإشعارات والتنبيهات -->
                <TabItem Header="🔔 الإشعارات">
                    <Border Background="White" CornerRadius="0,8,8,8" Padding="30">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                        </Border.Effect>
                        <StackPanel>
                            <Label Content="🔊 التنبيهات الصوتية" Style="{StaticResource SectionHeaderStyle}"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,15,0">
                                    <CheckBox x:Name="EnableSoundNotificationsCheckBox" Content="تفعيل التنبيهات الصوتية"
                                              Style="{StaticResource ModernCheckBoxStyle}" IsChecked="True"/>
                                    <CheckBox x:Name="AppointmentSoundCheckBox" Content="صوت تنبيه المواعيد"
                                              Style="{StaticResource ModernCheckBoxStyle}" IsChecked="True"/>
                                    <CheckBox x:Name="EmailSoundCheckBox" Content="صوت تنبيه البريد الإلكتروني"
                                              Style="{StaticResource ModernCheckBoxStyle}" IsChecked="True"/>
                                    <CheckBox x:Name="SystemSoundCheckBox" Content="أصوات النظام"
                                              Style="{StaticResource ModernCheckBoxStyle}" IsChecked="True"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Margin="15,0,0,0">
                                    <Label Content="مستوى الصوت" Style="{StaticResource FieldLabelStyle}"/>
                                    <Slider x:Name="VolumeSlider" Minimum="0" Maximum="100" Value="75"
                                            Margin="0,5,0,15"/>
                                    <TextBlock x:Name="VolumeLabel" Text="75%" HorizontalAlignment="Center"
                                               FontSize="12" Foreground="#6B7280"/>

                                    <Button Content="🎵 اختبار الصوت" Style="{StaticResource PrimaryButtonStyle}"
                                            Click="TestSound_Click" Margin="0,10,0,0"/>
                                </StackPanel>
                            </Grid>

                            <Label Content="💡 التنبيهات البصرية" Style="{StaticResource SectionHeaderStyle}"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,15,0">
                                    <CheckBox x:Name="EnableVisualNotificationsCheckBox" Content="تفعيل التنبيهات البصرية"
                                              Style="{StaticResource ModernCheckBoxStyle}" IsChecked="True"/>
                                    <CheckBox x:Name="PopupNotificationsCheckBox" Content="النوافذ المنبثقة"
                                              Style="{StaticResource ModernCheckBoxStyle}" IsChecked="True"/>
                                    <CheckBox x:Name="TaskbarNotificationsCheckBox" Content="تنبيهات شريط المهام"
                                              Style="{StaticResource ModernCheckBoxStyle}" IsChecked="True"/>
                                    <CheckBox x:Name="FlashingNotificationsCheckBox" Content="الوميض التحذيري"
                                              Style="{StaticResource ModernCheckBoxStyle}"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Margin="15,0,0,0">
                                    <Label Content="مدة عرض التنبيه (ثانية)" Style="{StaticResource FieldLabelStyle}"/>
                                    <TextBox x:Name="NotificationDurationTextBox" Style="{StaticResource ModernTextBoxStyle}"
                                             Text="5"/>

                                    <Label Content="موضع التنبيهات" Style="{StaticResource FieldLabelStyle}"/>
                                    <ComboBox x:Name="NotificationPositionComboBox" Style="{StaticResource ModernComboBoxStyle}">
                                        <ComboBoxItem Content="أعلى اليمين" IsSelected="True"/>
                                        <ComboBoxItem Content="أعلى اليسار"/>
                                        <ComboBoxItem Content="أسفل اليمين"/>
                                        <ComboBoxItem Content="أسفل اليسار"/>
                                        <ComboBoxItem Content="وسط الشاشة"/>
                                    </ComboBox>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>
                </TabItem>

                <!-- تبويب المظهر والواجهة -->
                <TabItem Header="🎨 المظهر">
                    <Border Background="White" CornerRadius="0,8,8,8" Padding="30">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                        </Border.Effect>
                        <StackPanel>
                            <Label Content="🌓 وضع العرض" Style="{StaticResource SectionHeaderStyle}"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,15,0">
                                    <RadioButton x:Name="LightModeRadio" Content="🌞 الوضع النهاري"
                                                 GroupName="ThemeMode" IsChecked="True"
                                                 Style="{StaticResource ModernCheckBoxStyle}"/>
                                    <RadioButton x:Name="DarkModeRadio" Content="🌙 الوضع الليلي"
                                                 GroupName="ThemeMode"
                                                 Style="{StaticResource ModernCheckBoxStyle}"/>
                                    <RadioButton x:Name="AutoModeRadio" Content="🔄 تلقائي (حسب الوقت)"
                                                 GroupName="ThemeMode"
                                                 Style="{StaticResource ModernCheckBoxStyle}"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Margin="15,0,0,0">
                                    <Label Content="حجم الخط الافتراضي" Style="{StaticResource FieldLabelStyle}"/>
                                    <ComboBox x:Name="FontSizeComboBox" Style="{StaticResource ModernComboBoxStyle}">
                                        <ComboBoxItem Content="صغير (12px)"/>
                                        <ComboBoxItem Content="متوسط (14px)" IsSelected="True"/>
                                        <ComboBoxItem Content="كبير (16px)"/>
                                        <ComboBoxItem Content="كبير جداً (18px)"/>
                                    </ComboBox>

                                    <Label Content="نوع الخط" Style="{StaticResource FieldLabelStyle}"/>
                                    <ComboBox x:Name="FontFamilyComboBox" Style="{StaticResource ModernComboBoxStyle}">
                                        <ComboBoxItem Content="Segoe UI" IsSelected="True"/>
                                        <ComboBoxItem Content="Arial"/>
                                        <ComboBoxItem Content="Tahoma"/>
                                        <ComboBoxItem Content="Calibri"/>
                                        <ComboBoxItem Content="Times New Roman"/>
                                    </ComboBox>
                                </StackPanel>
                            </Grid>

                            <Label Content="🎯 تخصيص الأيقونات والقوائم" Style="{StaticResource SectionHeaderStyle}"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,15,0">
                                    <Label Content="حجم الأيقونات" Style="{StaticResource FieldLabelStyle}"/>
                                    <ComboBox x:Name="IconSizeComboBox" Style="{StaticResource ModernComboBoxStyle}">
                                        <ComboBoxItem Content="صغير (16px)"/>
                                        <ComboBoxItem Content="متوسط (24px)" IsSelected="True"/>
                                        <ComboBoxItem Content="كبير (32px)"/>
                                        <ComboBoxItem Content="كبير جداً (48px)"/>
                                    </ComboBox>

                                    <CheckBox x:Name="ShowIconLabelsCheckBox" Content="إظهار تسميات الأيقونات"
                                              Style="{StaticResource ModernCheckBoxStyle}" IsChecked="True"/>
                                    <CheckBox x:Name="AnimatedIconsCheckBox" Content="أيقونات متحركة"
                                              Style="{StaticResource ModernCheckBoxStyle}" IsChecked="True"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Margin="15,0,0,0">
                                    <Button Content="🎨 تخصيص أسماء القوائم" Style="{StaticResource PrimaryButtonStyle}"
                                            Click="CustomizeMenuNames_Click"/>
                                    <Button Content="📁 تغيير مجموعة الأيقونات" Style="{StaticResource SecondaryButtonStyle}"
                                            Click="ChangeIconSet_Click"/>
                                    <Button Content="🔄 استعادة الافتراضي" Style="{StaticResource DangerButtonStyle}"
                                            Click="ResetAppearance_Click"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>
                </TabItem>

                <!-- تبويب إعدادات قاعدة البيانات والخادم -->
                <TabItem Header="🗄️ قاعدة البيانات">
                    <Border Background="White" CornerRadius="0,8,8,8" Padding="30">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                        </Border.Effect>
                        <StackPanel>
                            <Label Content="💾 نوع قاعدة البيانات" Style="{StaticResource SectionHeaderStyle}"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,15,0">
                                    <Label Content="نوع الخادم" Style="{StaticResource FieldLabelStyle}"/>
                                    <ComboBox x:Name="DatabaseTypeComboBox" Style="{StaticResource ModernComboBoxStyle}"
                                              SelectionChanged="DatabaseType_SelectionChanged">
                                        <ComboBoxItem Content="SQLite (محلي)" IsSelected="True"/>
                                        <ComboBoxItem Content="SQL Server"/>
                                        <ComboBoxItem Content="MySQL"/>
                                        <ComboBoxItem Content="PostgreSQL"/>
                                        <ComboBoxItem Content="Oracle"/>
                                        <ComboBoxItem Content="MongoDB"/>
                                    </ComboBox>

                                    <Label Content="اسم قاعدة البيانات" Style="{StaticResource FieldLabelStyle}"/>
                                    <TextBox x:Name="DatabaseNameTextBox" Style="{StaticResource ModernTextBoxStyle}"
                                             Text="AvocatProDB"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Margin="15,0,0,0">
                                    <Label Content="عنوان الخادم" Style="{StaticResource FieldLabelStyle}"/>
                                    <TextBox x:Name="ServerAddressTextBox" Style="{StaticResource ModernTextBoxStyle}"
                                             Text="localhost" IsEnabled="False"/>

                                    <Label Content="المنفذ" Style="{StaticResource FieldLabelStyle}"/>
                                    <TextBox x:Name="DatabasePortTextBox" Style="{StaticResource ModernTextBoxStyle}"
                                             Text="1433" IsEnabled="False"/>
                                </StackPanel>
                            </Grid>

                            <Label Content="🔐 بيانات الاعتماد" Style="{StaticResource SectionHeaderStyle}"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,15,0">
                                    <Label Content="اسم المستخدم" Style="{StaticResource FieldLabelStyle}"/>
                                    <TextBox x:Name="DatabaseUsernameTextBox" Style="{StaticResource ModernTextBoxStyle}"
                                             IsEnabled="False"/>

                                    <CheckBox x:Name="IntegratedSecurityCheckBox" Content="استخدام أمان Windows المتكامل"
                                              Style="{StaticResource ModernCheckBoxStyle}" IsChecked="True" IsEnabled="False"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Margin="15,0,0,0">
                                    <Label Content="كلمة المرور" Style="{StaticResource FieldLabelStyle}"/>
                                    <PasswordBox x:Name="DatabasePasswordBox" Style="{StaticResource ModernTextBoxStyle}"
                                                 IsEnabled="False"/>

                                    <Button Content="🔗 اختبار الاتصال" Style="{StaticResource PrimaryButtonStyle}"
                                            Click="TestDatabaseConnection_Click"/>
                                </StackPanel>
                            </Grid>

                            <Label Content="⚡ إعدادات الأداء" Style="{StaticResource SectionHeaderStyle}"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,15,0">
                                    <Label Content="مهلة الاتصال (ثانية)" Style="{StaticResource FieldLabelStyle}"/>
                                    <TextBox x:Name="ConnectionTimeoutTextBox" Style="{StaticResource ModernTextBoxStyle}"
                                             Text="30"/>

                                    <Label Content="حد الاستعلامات المتزامنة" Style="{StaticResource FieldLabelStyle}"/>
                                    <TextBox x:Name="MaxConnectionsTextBox" Style="{StaticResource ModernTextBoxStyle}"
                                             Text="100"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Margin="15,0,0,0">
                                    <CheckBox x:Name="EnableConnectionPoolingCheckBox" Content="تفعيل تجميع الاتصالات"
                                              Style="{StaticResource ModernCheckBoxStyle}" IsChecked="True"/>
                                    <CheckBox x:Name="EnableQueryCachingCheckBox" Content="تفعيل ذاكرة التخزين المؤقت"
                                              Style="{StaticResource ModernCheckBoxStyle}" IsChecked="True"/>
                                    <CheckBox x:Name="EnableAutoBackupCheckBox" Content="النسخ الاحتياطي التلقائي"
                                              Style="{StaticResource ModernCheckBoxStyle}" IsChecked="True"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>
                </TabItem>

                <!-- تبويب اللغة والمنطقة -->
                <TabItem Header="🌍 اللغة">
                    <Border Background="White" CornerRadius="0,8,8,8" Padding="30">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                        </Border.Effect>
                        <StackPanel>
                            <Label Content="🗣️ إعدادات اللغة" Style="{StaticResource SectionHeaderStyle}"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,15,0">
                                    <Label Content="لغة الواجهة" Style="{StaticResource FieldLabelStyle}"/>
                                    <ComboBox x:Name="InterfaceLanguageComboBox" Style="{StaticResource ModernComboBoxStyle}">
                                        <ComboBoxItem Content="العربية" IsSelected="True"/>
                                        <ComboBoxItem Content="English"/>
                                        <ComboBoxItem Content="Français"/>
                                        <ComboBoxItem Content="Español"/>
                                    </ComboBox>

                                    <Label Content="اتجاه النص" Style="{StaticResource FieldLabelStyle}"/>
                                    <ComboBox x:Name="TextDirectionComboBox" Style="{StaticResource ModernComboBoxStyle}">
                                        <ComboBoxItem Content="من اليمين إلى اليسار (RTL)" IsSelected="True"/>
                                        <ComboBoxItem Content="من اليسار إلى اليمين (LTR)"/>
                                    </ComboBox>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Margin="15,0,0,0">
                                    <Label Content="المنطقة الزمنية" Style="{StaticResource FieldLabelStyle}"/>
                                    <ComboBox x:Name="TimezoneComboBox" Style="{StaticResource ModernComboBoxStyle}">
                                        <ComboBoxItem Content="(GMT+01:00) المغرب" IsSelected="True"/>
                                        <ComboBoxItem Content="(GMT+02:00) مصر"/>
                                        <ComboBoxItem Content="(GMT+03:00) السعودية"/>
                                        <ComboBoxItem Content="(GMT+04:00) الإمارات"/>
                                    </ComboBox>

                                    <Label Content="تنسيق التاريخ" Style="{StaticResource FieldLabelStyle}"/>
                                    <ComboBox x:Name="DateFormatComboBox" Style="{StaticResource ModernComboBoxStyle}">
                                        <ComboBoxItem Content="dd/MM/yyyy" IsSelected="True"/>
                                        <ComboBoxItem Content="MM/dd/yyyy"/>
                                        <ComboBoxItem Content="yyyy-MM-dd"/>
                                        <ComboBoxItem Content="dd-MM-yyyy"/>
                                    </ComboBox>
                                </StackPanel>
                            </Grid>

                            <Label Content="💰 إعدادات العملة" Style="{StaticResource SectionHeaderStyle}"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,15,0">
                                    <Label Content="العملة الافتراضية" Style="{StaticResource FieldLabelStyle}"/>
                                    <ComboBox x:Name="CurrencyComboBox" Style="{StaticResource ModernComboBoxStyle}">
                                        <ComboBoxItem Content="درهم مغربي (MAD)" IsSelected="True"/>
                                        <ComboBoxItem Content="دولار أمريكي (USD)"/>
                                        <ComboBoxItem Content="يورو (EUR)"/>
                                        <ComboBoxItem Content="ريال سعودي (SAR)"/>
                                        <ComboBoxItem Content="جنيه مصري (EGP)"/>
                                    </ComboBox>

                                    <Label Content="رمز العملة" Style="{StaticResource FieldLabelStyle}"/>
                                    <TextBox x:Name="CurrencySymbolTextBox" Style="{StaticResource ModernTextBoxStyle}"
                                             Text="د.م."/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Margin="15,0,0,0">
                                    <Label Content="موضع رمز العملة" Style="{StaticResource FieldLabelStyle}"/>
                                    <ComboBox x:Name="CurrencyPositionComboBox" Style="{StaticResource ModernComboBoxStyle}">
                                        <ComboBoxItem Content="قبل المبلغ" IsSelected="True"/>
                                        <ComboBoxItem Content="بعد المبلغ"/>
                                    </ComboBox>

                                    <Label Content="عدد الأرقام العشرية" Style="{StaticResource FieldLabelStyle}"/>
                                    <ComboBox x:Name="DecimalPlacesComboBox" Style="{StaticResource ModernComboBoxStyle}">
                                        <ComboBoxItem Content="0"/>
                                        <ComboBoxItem Content="2" IsSelected="True"/>
                                        <ComboBoxItem Content="3"/>
                                        <ComboBoxItem Content="4"/>
                                    </ComboBox>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>
                </TabItem>

                <!-- تبويب فحص الأخطاء والصيانة -->
                <TabItem Header="🔧 الصيانة">
                    <Border Background="White" CornerRadius="0,8,8,8" Padding="30">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                        </Border.Effect>
                        <StackPanel>
                            <Label Content="🔍 فحص الأخطاء البرمجية" Style="{StaticResource SectionHeaderStyle}"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,15,0">
                                    <Button Content="🔍 فحص شامل للنظام" Style="{StaticResource PrimaryButtonStyle}"
                                            Click="RunSystemDiagnostics_Click"/>
                                    <Button Content="🗄️ فحص قاعدة البيانات" Style="{StaticResource SecondaryButtonStyle}"
                                            Click="CheckDatabaseIntegrity_Click"/>
                                    <Button Content="📁 فحص الملفات المفقودة" Style="{StaticResource SecondaryButtonStyle}"
                                            Click="CheckMissingFiles_Click"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Margin="15,0,0,0">
                                    <Button Content="🧹 تنظيف الملفات المؤقتة" Style="{StaticResource SuccessButtonStyle}"
                                            Click="CleanTempFiles_Click"/>
                                    <Button Content="📊 تحليل الأداء" Style="{StaticResource PrimaryButtonStyle}"
                                            Click="AnalyzePerformance_Click"/>
                                    <Button Content="📋 تقرير حالة النظام" Style="{StaticResource SecondaryButtonStyle}"
                                            Click="GenerateSystemReport_Click"/>
                                </StackPanel>
                            </Grid>

                            <Label Content="📝 سجلات النظام" Style="{StaticResource SectionHeaderStyle}"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,15,0">
                                    <CheckBox x:Name="EnableLoggingCheckBox" Content="تفعيل تسجيل الأحداث"
                                              Style="{StaticResource ModernCheckBoxStyle}" IsChecked="True"/>
                                    <CheckBox x:Name="LogErrorsCheckBox" Content="تسجيل الأخطاء"
                                              Style="{StaticResource ModernCheckBoxStyle}" IsChecked="True"/>
                                    <CheckBox x:Name="LogWarningsCheckBox" Content="تسجيل التحذيرات"
                                              Style="{StaticResource ModernCheckBoxStyle}" IsChecked="True"/>
                                    <CheckBox x:Name="LogInfoCheckBox" Content="تسجيل المعلومات"
                                              Style="{StaticResource ModernCheckBoxStyle}"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Margin="15,0,0,0">
                                    <Label Content="مستوى التفصيل" Style="{StaticResource FieldLabelStyle}"/>
                                    <ComboBox x:Name="LogLevelComboBox" Style="{StaticResource ModernComboBoxStyle}">
                                        <ComboBoxItem Content="أساسي"/>
                                        <ComboBoxItem Content="متوسط" IsSelected="True"/>
                                        <ComboBoxItem Content="مفصل"/>
                                        <ComboBoxItem Content="تشخيصي"/>
                                    </ComboBox>

                                    <Button Content="📄 عرض السجلات" Style="{StaticResource PrimaryButtonStyle}"
                                            Click="ViewLogs_Click"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>
                </TabItem>
            </TabControl>
        </ScrollViewer>
    </Grid>
</Page>
