<Page x:Class="AvocatPro.Views.Pages.UserManagementPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:fa="http://schemas.fontawesome.io/icons/"
      Title="إدارة المستخدمين"
      Background="#F8F9FA"
      FlowDirection="RightToLeft">

    <Page.Resources>
        <!-- أنماط مخصصة -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#007BFF"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="6"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#0056B3"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="UserCard" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="StatusIndicator" TargetType="Ellipse">
            <Setter Property="Width" Value="12"/>
            <Setter Property="Height" Value="12"/>
            <Setter Property="Margin" Value="5,0"/>
        </Style>
    </Page.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان والأدوات -->
        <Border Grid.Row="0" Background="White" Padding="20" Margin="0,0,0,10">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="1" Opacity="0.2" BlurRadius="5"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="👥" Foreground="#007BFF" FontSize="24" Margin="0,0,10,0"/>
                    <TextBlock Text="إدارة المستخدمين" FontSize="24" FontWeight="Bold" Foreground="#2C3E50" VerticalAlignment="Center"/>
                    <TextBlock x:Name="UsersCountText" Text="(0 مستخدم)" FontSize="16" Foreground="#6C757D" VerticalAlignment="Center" Margin="10,0,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="AddUserBtn" Style="{StaticResource ModernButton}" Click="AddUserBtn_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="➕" FontSize="14" Margin="0,0,5,0"/>
                            <TextBlock Text="إضافة مستخدم"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="ManageRolesBtn" Background="#28A745" Style="{StaticResource ModernButton}" Click="ManageRolesBtn_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="⚙️" FontSize="14" Margin="0,0,5,0"/>
                            <TextBlock Text="إدارة الأدوار"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="UserReportsBtn" Background="#17A2B8" Style="{StaticResource ModernButton}" Click="UserReportsBtn_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📊" FontSize="14" Margin="0,0,5,0"/>
                            <TextBlock Text="التقارير"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="RefreshBtn" Background="#6C757D" Style="{StaticResource ModernButton}" Click="RefreshBtn_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔄" FontSize="14" Margin="0,0,5,0"/>
                            <TextBlock Text="تحديث"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- شريط البحث والفلاتر -->
        <Border Grid.Row="1" Background="White" Padding="20" Margin="0,0,0,10">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="1" Opacity="0.2" BlurRadius="5"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- البحث -->
                <Border Grid.Column="0" Background="#F8F9FA" CornerRadius="6" Padding="10" Margin="0,0,10,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="🔍" Foreground="#6C757D" FontSize="16" Margin="0,0,10,0"/>
                        <TextBox x:Name="SearchTextBox" Grid.Column="1" Background="Transparent" BorderThickness="0" 
                                FontSize="14" VerticalAlignment="Center" TextChanged="SearchTextBox_TextChanged"
                                Tag="البحث في المستخدمين..."/>
                    </Grid>
                </Border>

                <!-- فلتر الحالة -->
                <ComboBox x:Name="StatusFilterCombo" Grid.Column="1" Margin="5,0" FontSize="14"
                         SelectionChanged="StatusFilterCombo_SelectionChanged">
                    <ComboBoxItem Content="جميع الحالات"/>
                    <ComboBoxItem Content="متصل"/>
                    <ComboBoxItem Content="غير متصل"/>
                    <ComboBoxItem Content="نشط"/>
                    <ComboBoxItem Content="معطل"/>
                </ComboBox>

                <!-- فلتر الدور -->
                <ComboBox x:Name="RoleFilterCombo" Grid.Column="2" Margin="5,0" FontSize="14"
                         SelectionChanged="RoleFilterCombo_SelectionChanged">
                    <ComboBoxItem Content="جميع الأدوار"/>
                </ComboBox>

                <!-- فلتر التاريخ -->
                <ComboBox x:Name="DateFilterCombo" Grid.Column="3" Margin="5,0" FontSize="14"
                         SelectionChanged="DateFilterCombo_SelectionChanged">
                    <ComboBoxItem Content="جميع التواريخ"/>
                    <ComboBoxItem Content="اليوم"/>
                    <ComboBoxItem Content="هذا الأسبوع"/>
                    <ComboBoxItem Content="هذا الشهر"/>
                    <ComboBoxItem Content="آخر 30 يوم"/>
                </ComboBox>

                <!-- أزرار العرض -->
                <StackPanel Grid.Column="4" Orientation="Horizontal" Margin="10,0,0,0">
                    <ToggleButton x:Name="CardViewBtn" Width="40" Height="35"
                                 Background="#007BFF" BorderThickness="0" Margin="2"
                                 Checked="ViewModeChanged" Unchecked="ViewModeChanged">
                        <TextBlock Text="⊞" Foreground="White" FontSize="16"/>
                    </ToggleButton>
                    <ToggleButton x:Name="ListViewBtn" Width="40" Height="35" 
                                 Background="#6C757D" BorderThickness="0" Margin="2"
                                 Checked="ViewModeChanged" Unchecked="ViewModeChanged">
                        <TextBlock Text="☰" Foreground="White" FontSize="16"/>
                    </ToggleButton>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="2">
            <!-- عرض البطاقات -->
            <ScrollViewer x:Name="CardViewScroller" VerticalScrollBarVisibility="Auto" Visibility="Visible">
                <WrapPanel x:Name="UsersCardsPanel" Orientation="Horizontal" Margin="10"/>
            </ScrollViewer>

            <!-- عرض القائمة -->
            <DataGrid x:Name="UsersDataGrid" Visibility="Collapsed" AutoGenerateColumns="False" 
                     CanUserAddRows="False" CanUserDeleteRows="False" IsReadOnly="True"
                     Background="White" GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                     RowBackground="White" AlternatingRowBackground="#F8F9FA"
                     FontSize="14" Margin="10">
                
                <DataGrid.Columns>
                    <DataGridTemplateColumn Header="الحالة" Width="80">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Ellipse Width="12" Height="12" Fill="{Binding StatusColor}"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    
                    <DataGridTextColumn Header="اسم المستخدم" Binding="{Binding UserName}" Width="150"/>
                    <DataGridTextColumn Header="الاسم الكامل" Binding="{Binding FullName}" Width="200"/>
                    <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding Email}" Width="200"/>
                    <DataGridTextColumn Header="الدور" Binding="{Binding RolesDisplay}" Width="150"/>
                    <DataGridTextColumn Header="آخر دخول" Binding="{Binding LastLoginDisplay}" Width="150"/>
                    <DataGridTextColumn Header="مدة الجلسة" Binding="{Binding SessionDurationDisplay}" Width="120"/>
                    
                    <DataGridTemplateColumn Header="الإجراءات" Width="200">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button Content="تعديل" Background="#007BFF" Foreground="White" 
                                           BorderThickness="0" Padding="8,4" Margin="2" FontSize="12"
                                           Click="EditUser_Click" Tag="{Binding UserId}"/>
                                    <Button Content="محادثة" Background="#28A745" Foreground="White" 
                                           BorderThickness="0" Padding="8,4" Margin="2" FontSize="12"
                                           Click="ChatUser_Click" Tag="{Binding UserId}"/>
                                    <Button Content="تقرير" Background="#17A2B8" Foreground="White" 
                                           BorderThickness="0" Padding="8,4" Margin="2" FontSize="12"
                                           Click="UserReport_Click" Tag="{Binding UserId}"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>

            <!-- رسالة عدم وجود بيانات -->
            <Border x:Name="NoDataPanel" Background="White" CornerRadius="8" Margin="20" 
                   Padding="40" HorizontalAlignment="Center" VerticalAlignment="Center"
                   Visibility="Collapsed">
                <Border.Effect>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="8"/>
                </Border.Effect>
                
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="👥" Foreground="#6C757D" FontSize="64" Margin="0,0,0,20"/>
                    <TextBlock Text="لا توجد مستخدمين" FontSize="18" FontWeight="Bold" 
                              Foreground="#6C757D" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="ابدأ بإضافة مستخدم جديد للنظام" FontSize="14" 
                              Foreground="#6C757D" HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>
        </Grid>
    </Grid>
</Page>
