<Page x:Class="AvocatPro.Views.Pages.ComprehensiveSessionsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="إدارة الجلسات الشاملة" Background="#F8FAFC"
      FlowDirection="RightToLeft">

    <Page.Resources>
        <Style TargetType="Button" x:Key="ActionButtonStyle">
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4" Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style TargetType="Button" x:Key="MainButtonStyle">
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="8" Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Page.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان والأزرار الرئيسية -->
        <Border Grid.Row="0" Background="White" CornerRadius="12" Padding="24" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Text="إدارة الجلسات الشاملة" FontSize="24" FontWeight="Bold" Foreground="#1F2937"/>
                    <TextBlock Text="نظام إدارة الجلسات مع التقويم والإحصائيات المتقدمة" FontSize="14" Foreground="#6B7280"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="AddSessionButton" Content="➕ إضافة جلسة جديدة"
                            Background="#6366F1" Foreground="White" 
                            Style="{StaticResource MainButtonStyle}" Click="AddSessionButton_Click"/>
                    <Button x:Name="CalendarViewButton" Content="📅 عرض التقويم"
                            Background="#10B981" Foreground="White" 
                            Style="{StaticResource MainButtonStyle}" Click="CalendarViewButton_Click"/>
                    <Button x:Name="ExportButton" Content="📤 تصدير"
                            Background="#F59E0B" Foreground="White" 
                            Style="{StaticResource MainButtonStyle}" Click="ExportButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- الإحصائيات -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- إجمالي الجلسات -->
            <Border Grid.Column="0" Background="White" CornerRadius="12" Padding="20" Margin="0,0,8,0">
                <Border.Effect>
                    <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <StackPanel Grid.Column="0">
                        <TextBlock x:Name="TotalSessionsCount" Text="0" FontSize="24" FontWeight="Bold" Foreground="#1F2937"/>
                        <TextBlock Text="إجمالي الجلسات" FontSize="11" Foreground="#6B7280"/>
                    </StackPanel>
                    <Border Grid.Column="1" Background="#6366F1" CornerRadius="8" Padding="6">
                        <TextBlock Text="⚖️" FontSize="14" Foreground="White"/>
                    </Border>
                </Grid>
            </Border>

            <!-- جلسات اليوم -->
            <Border Grid.Column="1" Background="White" CornerRadius="12" Padding="20" Margin="4,0">
                <Border.Effect>
                    <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <StackPanel Grid.Column="0">
                        <TextBlock x:Name="TodaySessionsCount" Text="0" FontSize="24" FontWeight="Bold" Foreground="#F59E0B"/>
                        <TextBlock Text="جلسات اليوم" FontSize="11" Foreground="#6B7280"/>
                    </StackPanel>
                    <Border Grid.Column="1" Background="#F59E0B" CornerRadius="8" Padding="6">
                        <TextBlock Text="📅" FontSize="14" Foreground="White"/>
                    </Border>
                </Grid>
            </Border>

            <!-- جلسات مجدولة -->
            <Border Grid.Column="2" Background="White" CornerRadius="12" Padding="20" Margin="4,0">
                <Border.Effect>
                    <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <StackPanel Grid.Column="0">
                        <TextBlock x:Name="ScheduledSessionsCount" Text="0" FontSize="24" FontWeight="Bold" Foreground="#6366F1"/>
                        <TextBlock Text="مجدولة" FontSize="11" Foreground="#6B7280"/>
                    </StackPanel>
                    <Border Grid.Column="1" Background="#6366F1" CornerRadius="8" Padding="6">
                        <TextBlock Text="📋" FontSize="14" Foreground="White"/>
                    </Border>
                </Grid>
            </Border>

            <!-- جلسات مكتملة -->
            <Border Grid.Column="3" Background="White" CornerRadius="12" Padding="20" Margin="4,0">
                <Border.Effect>
                    <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <StackPanel Grid.Column="0">
                        <TextBlock x:Name="CompletedSessionsCount" Text="0" FontSize="24" FontWeight="Bold" Foreground="#10B981"/>
                        <TextBlock Text="مكتملة" FontSize="11" Foreground="#6B7280"/>
                    </StackPanel>
                    <Border Grid.Column="1" Background="#10B981" CornerRadius="8" Padding="6">
                        <TextBlock Text="✅" FontSize="14" Foreground="White"/>
                    </Border>
                </Grid>
            </Border>

            <!-- جلسات مؤجلة -->
            <Border Grid.Column="4" Background="White" CornerRadius="12" Padding="20" Margin="4,0">
                <Border.Effect>
                    <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <StackPanel Grid.Column="0">
                        <TextBlock x:Name="PostponedSessionsCount" Text="0" FontSize="24" FontWeight="Bold" Foreground="#8B5CF6"/>
                        <TextBlock Text="مؤجلة" FontSize="11" Foreground="#6B7280"/>
                    </StackPanel>
                    <Border Grid.Column="1" Background="#8B5CF6" CornerRadius="8" Padding="6">
                        <TextBlock Text="⏸️" FontSize="14" Foreground="White"/>
                    </Border>
                </Grid>
            </Border>

            <!-- إجمالي المصروفات -->
            <Border Grid.Column="5" Background="White" CornerRadius="12" Padding="20" Margin="8,0,0,0">
                <Border.Effect>
                    <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <StackPanel Grid.Column="0">
                        <TextBlock x:Name="TotalExpensesAmount" Text="0" FontSize="20" FontWeight="Bold" Foreground="#EF4444"/>
                        <TextBlock Text="إجمالي المصروفات" FontSize="10" Foreground="#6B7280"/>
                    </StackPanel>
                    <Border Grid.Column="1" Background="#EF4444" CornerRadius="8" Padding="6">
                        <TextBlock Text="💰" FontSize="14" Foreground="White"/>
                    </Border>
                </Grid>
            </Border>
        </Grid>

        <!-- شريط البحث والتصفية -->
        <Border Grid.Row="2" Background="White" CornerRadius="16" Padding="20" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="15" ShadowDepth="0" Opacity="0.3"/>
            </Border.Effect>
            
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- الصف الأول -->
                <Grid Grid.Row="0" Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- البحث -->
                    <Border Grid.Column="0" Background="#F9FAFB" CornerRadius="8" Margin="0,0,12,0">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="🔍" FontSize="16" Foreground="#6B7280"
                                       Margin="12,0,8,0" VerticalAlignment="Center"/>
                            <TextBox x:Name="SearchTextBox" Grid.Column="1" Background="Transparent" BorderThickness="0"
                                     Padding="0,12,12,12" FontSize="14"
                                     Text="البحث في الجلسات..."
                                     Foreground="#9CA3AF" GotFocus="SearchTextBox_GotFocus"
                                     LostFocus="SearchTextBox_LostFocus" TextChanged="SearchTextBox_TextChanged"/>
                        </Grid>
                    </Border>

                    <!-- تصفية حسب الحالة -->
                    <ComboBox x:Name="StatusFilterComboBox" Grid.Column="1" Background="#F9FAFB"
                              BorderBrush="#D1D5DB" BorderThickness="1" Padding="12" FontSize="14"
                              Margin="0,0,12,0" SelectionChanged="StatusFilter_SelectionChanged">
                        <ComboBoxItem Content="جميع الحالات" IsSelected="True"/>
                        <ComboBoxItem Content="مجدولة"/>
                        <ComboBoxItem Content="جارية"/>
                        <ComboBoxItem Content="مكتملة"/>
                        <ComboBoxItem Content="مؤجلة"/>
                        <ComboBoxItem Content="ملغية"/>
                    </ComboBox>

                    <!-- تصفية حسب نوع الجلسة -->
                    <ComboBox x:Name="SessionTypeFilterComboBox" Grid.Column="2" Background="#F9FAFB"
                              BorderBrush="#D1D5DB" BorderThickness="1" Padding="12" FontSize="14"
                              Margin="0,0,12,0" SelectionChanged="SessionTypeFilter_SelectionChanged">
                        <ComboBoxItem Content="جميع الأنواع" IsSelected="True"/>
                    </ComboBox>

                    <!-- تصفية حسب المحكمة -->
                    <ComboBox x:Name="CourtFilterComboBox" Grid.Column="3" Background="#F9FAFB"
                              BorderBrush="#D1D5DB" BorderThickness="1" Padding="12" FontSize="14"
                              Margin="0,0,12,0" SelectionChanged="CourtFilter_SelectionChanged">
                        <ComboBoxItem Content="جميع المحاكم" IsSelected="True"/>
                    </ComboBox>

                    <!-- أزرار الإجراءات -->
                    <StackPanel Grid.Column="4" Orientation="Horizontal">
                        <Button x:Name="RefreshButton" Content="🔄" Background="#6366F1" Foreground="White"
                                BorderThickness="0" Width="40" Height="40" FontSize="16"
                                Cursor="Hand" Margin="0,0,8,0" Click="RefreshButton_Click"
                                ToolTip="تحديث"/>

                        <Button x:Name="PrintButton" Content="🖨️" Background="#10B981" Foreground="White"
                                BorderThickness="0" Width="40" Height="40" FontSize="16"
                                Cursor="Hand" Click="PrintButton_Click"
                                ToolTip="طباعة القائمة"/>
                    </StackPanel>
                </Grid>

                <!-- الصف الثاني -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- تصفية حسب المحامي -->
                    <ComboBox x:Name="LawyerFilterComboBox" Grid.Column="0" Background="#F9FAFB"
                              BorderBrush="#D1D5DB" BorderThickness="1" Padding="12" FontSize="14"
                              Margin="0,0,12,0" SelectionChanged="LawyerFilter_SelectionChanged">
                        <ComboBoxItem Content="جميع المحامين" IsSelected="True"/>
                    </ComboBox>

                    <!-- تصفية حسب الأولوية -->
                    <ComboBox x:Name="PriorityFilterComboBox" Grid.Column="1" Background="#F9FAFB"
                              BorderBrush="#D1D5DB" BorderThickness="1" Padding="12" FontSize="14"
                              Margin="0,0,12,0" SelectionChanged="PriorityFilter_SelectionChanged">
                        <ComboBoxItem Content="جميع الأولويات" IsSelected="True"/>
                        <ComboBoxItem Content="عاجل"/>
                        <ComboBoxItem Content="مهم"/>
                        <ComboBoxItem Content="عادي"/>
                    </ComboBox>

                    <!-- تصفية حسب التاريخ من -->
                    <DatePicker x:Name="DateFromPicker" Grid.Column="2" Background="#F9FAFB"
                                BorderBrush="#D1D5DB" BorderThickness="1" Padding="12" FontSize="14"
                                Margin="0,0,12,0" SelectedDateChanged="DateFilter_Changed"/>

                    <!-- تصفية حسب التاريخ إلى -->
                    <DatePicker x:Name="DateToPicker" Grid.Column="3" Background="#F9FAFB"
                                BorderBrush="#D1D5DB" BorderThickness="1" Padding="12" FontSize="14"
                                Margin="0,0,12,0" SelectedDateChanged="DateFilter_Changed"/>

                    <!-- عرض سريع -->
                    <ComboBox x:Name="QuickViewComboBox" Grid.Column="4" Background="#F9FAFB"
                              BorderBrush="#D1D5DB" BorderThickness="1" Padding="12" FontSize="14"
                              SelectionChanged="QuickView_SelectionChanged">
                        <ComboBoxItem Content="جميع الجلسات" IsSelected="True"/>
                        <ComboBoxItem Content="جلسات اليوم"/>
                        <ComboBoxItem Content="جلسات الأسبوع"/>
                        <ComboBoxItem Content="جلسات الشهر"/>
                        <ComboBoxItem Content="الجلسات القادمة"/>
                        <ComboBoxItem Content="الجلسات المتأخرة"/>
                    </ComboBox>
                </Grid>
            </Grid>
        </Border>

        <!-- جدول الجلسات -->
        <Border Grid.Row="3" Background="White" CornerRadius="16" Padding="0">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="15" ShadowDepth="0" Opacity="0.3"/>
            </Border.Effect>
            
            <DataGrid x:Name="SessionsDataGrid" Background="Transparent" BorderThickness="0"
                      GridLinesVisibility="None" HeadersVisibility="Column"
                      AutoGenerateColumns="False" CanUserAddRows="False"
                      CanUserDeleteRows="False" IsReadOnly="True"
                      SelectionMode="Single" SelectionUnit="FullRow"
                      MouseDoubleClick="SessionsDataGrid_MouseDoubleClick">

                <DataGrid.ColumnHeaderStyle>
                    <Style TargetType="DataGridColumnHeader">
                        <Setter Property="Background" Value="#F8FAFC"/>
                        <Setter Property="Foreground" Value="#374151"/>
                        <Setter Property="FontWeight" Value="SemiBold"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="Padding" Value="16,12"/>
                        <Setter Property="BorderThickness" Value="0,0,0,1"/>
                        <Setter Property="BorderBrush" Value="#E5E7EB"/>
                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                    </Style>
                </DataGrid.ColumnHeaderStyle>

                <DataGrid.Columns>
                    <!-- رقم الجلسة -->
                    <DataGridTextColumn Header="رقم الجلسة" Binding="{Binding SessionNumber}" Width="100"/>
                    
                    <!-- رقم الملف -->
                    <DataGridTextColumn Header="رقم الملف" Binding="{Binding FileNumber}" Width="100"/>
                    
                    <!-- الموكل -->
                    <DataGridTextColumn Header="الموكل" Binding="{Binding Client}" Width="130"/>
                    
                    <!-- المحكمة -->
                    <DataGridTextColumn Header="المحكمة" Binding="{Binding Court}" Width="150"/>
                    
                    <!-- نوع الجلسة -->
                    <DataGridTextColumn Header="نوع الجلسة" Binding="{Binding SessionType}" Width="120"/>
                    
                    <!-- تاريخ الجلسة -->
                    <DataGridTextColumn Header="تاريخ الجلسة" Binding="{Binding SessionDate, StringFormat=dd/MM/yyyy}" Width="100"/>
                    
                    <!-- وقت الجلسة -->
                    <DataGridTextColumn Header="الوقت" Binding="{Binding SessionTimeText}" Width="80"/>
                    
                    <!-- المحامي -->
                    <DataGridTextColumn Header="المحامي" Binding="{Binding AssignedLawyer}" Width="130"/>
                    
                    <!-- الحالة -->
                    <DataGridTemplateColumn Header="الحالة" Width="100">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border Background="{Binding StatusColor}" CornerRadius="12" Padding="8,4">
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <TextBlock Text="{Binding StatusIcon}" FontSize="12" Margin="0,0,4,0"/>
                                        <TextBlock Text="{Binding Status}" Foreground="White" FontWeight="SemiBold" FontSize="11"/>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    
                    <!-- الأولوية -->
                    <DataGridTemplateColumn Header="الأولوية" Width="80">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <TextBlock Text="{Binding PriorityIcon}" FontSize="14" Margin="0,0,4,0"/>
                                    <TextBlock Text="{Binding Priority}" FontSize="12" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    
                    <!-- المتبقي -->
                    <DataGridTextColumn Header="المتبقي" Binding="{Binding DaysUntilSessionText}" Width="80"/>
                    
                    <!-- الإجراءات -->
                    <DataGridTemplateColumn Header="الإجراءات" Width="200">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button Content="✏️" Background="#6366F1" Foreground="White" 
                                            Style="{StaticResource ActionButtonStyle}"
                                            Click="EditSession_Click" ToolTip="تعديل"/>
                                    <Button Content="🗑️" Background="#EF4444" Foreground="White" 
                                            Style="{StaticResource ActionButtonStyle}"
                                            Click="DeleteSession_Click" ToolTip="حذف"/>
                                    <Button Content="📋" Background="#10B981" Foreground="White" 
                                            Style="{StaticResource ActionButtonStyle}"
                                            Click="ViewDetails_Click" ToolTip="التفاصيل"/>
                                    <Button Content="🖨️" Background="#F59E0B" Foreground="White" 
                                            Style="{StaticResource ActionButtonStyle}"
                                            Click="PrintSession_Click" ToolTip="طباعة"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>
    </Grid>
</Page>
