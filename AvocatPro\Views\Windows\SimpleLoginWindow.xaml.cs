using System;
using System.Net.NetworkInformation;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Threading;

namespace AvocatPro.Views.Windows
{
    /// <summary>
    /// نافذة تسجيل الدخول المحسنة مع التحقق من الاتصال
    /// </summary>
    public partial class SimpleLoginWindow : Window
    {
        private DispatcherTimer _connectionTimer;
        private bool _isLoggingIn = false;

        public SimpleLoginWindow()
        {
            InitializeComponent();
            InitializeConnectionMonitoring();
            SetupEventHandlers();
            CheckInitialConnections();
        }

        #region Initialization

        /// <summary>
        /// تهيئة مراقبة الاتصال
        /// </summary>
        private void InitializeConnectionMonitoring()
        {
            _connectionTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(5)
            };
            _connectionTimer.Tick += ConnectionTimer_Tick;
            _connectionTimer.Start();
        }

        /// <summary>
        /// إعداد معالجات الأحداث
        /// </summary>
        private void SetupEventHandlers()
        {
            // التركيز على حقل كلمة المرور عند الضغط على Enter في حقل اسم المستخدم
            UsernameTextBox.KeyDown += (s, e) =>
            {
                if (e.Key == System.Windows.Input.Key.Enter)
                {
                    PasswordBox.Focus();
                }
            };

            // تسجيل الدخول عند الضغط على Enter في حقل كلمة المرور
            PasswordBox.KeyDown += (s, e) =>
            {
                if (e.Key == System.Windows.Input.Key.Enter)
                {
                    Login_Click(this, new RoutedEventArgs());
                }
            };

            // تأثيرات التركيز على الحقول
            UsernameTextBox.GotFocus += (s, e) => HighlightTextBox(s as TextBox);
            UsernameTextBox.LostFocus += (s, e) => UnhighlightTextBox(s as TextBox);
            PasswordBox.GotFocus += (s, e) => HighlightPasswordBox(PasswordBox);
            PasswordBox.LostFocus += (s, e) => UnhighlightPasswordBox(PasswordBox);
        }

        /// <summary>
        /// فحص الاتصالات الأولي
        /// </summary>
        private async void CheckInitialConnections()
        {
            await CheckConnections();
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// تسجيل الدخول
        /// </summary>
        private async void Login_Click(object sender, RoutedEventArgs e)
        {
            if (_isLoggingIn) return;

            try
            {
                _isLoggingIn = true;
                await PerformLogin();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسجيل الدخول: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                _isLoggingIn = false;
                ResetLoginButton();
            }
        }

        /// <summary>
        /// إغلاق التطبيق
        /// </summary>
        private void Close_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد إغلاق التطبيق؟", "تأكيد الإغلاق",
                                        MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                Application.Current.Shutdown();
            }
        }

        /// <summary>
        /// مراقبة الاتصال
        /// </summary>
        private async void ConnectionTimer_Tick(object sender, EventArgs e)
        {
            await CheckConnections();
        }

        #endregion

        #region Login Logic

        /// <summary>
        /// تنفيذ عملية تسجيل الدخول
        /// </summary>
        private async Task PerformLogin()
        {
            // تحديث واجهة زر تسجيل الدخول
            UpdateLoginButton("جاري تسجيل الدخول...", false);

            // التحقق من صحة البيانات
            if (!ValidateLoginData())
            {
                return;
            }

            // محاكاة عملية التحقق (يمكن استبدالها بالتحقق الفعلي)
            await Task.Delay(1500);

            // تحديث الزر
            UpdateLoginButton("تم بنجاح ✓", false);
            await Task.Delay(500);

            // إخفاء نافذة تسجيل الدخول أولاً
            this.Hide();

            // فتح النافذة الرئيسية
            var mainWindow = new ModernMainWindow();
            Application.Current.MainWindow = mainWindow;
            mainWindow.WindowState = WindowState.Maximized;
            mainWindow.Show();
            mainWindow.Activate();

            // إغلاق نافذة تسجيل الدخول
            this.Close();
        }

        /// <summary>
        /// التحقق من صحة بيانات تسجيل الدخول
        /// </summary>
        private bool ValidateLoginData()
        {
            if (string.IsNullOrWhiteSpace(UsernameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المستخدم", "بيانات ناقصة",
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                UsernameTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(PasswordBox.Password))
            {
                MessageBox.Show("يرجى إدخال كلمة المرور", "بيانات ناقصة",
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                PasswordBox.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// تحديث زر تسجيل الدخول
        /// </summary>
        private void UpdateLoginButton(string content, bool isEnabled)
        {
            LoginButton.Content = content;
            LoginButton.IsEnabled = isEnabled;
        }

        /// <summary>
        /// إعادة تعيين زر تسجيل الدخول
        /// </summary>
        private void ResetLoginButton()
        {
            LoginButton.Content = "تسجيل الدخول";
            LoginButton.IsEnabled = true;
        }

        #endregion

        #region Connection Monitoring

        /// <summary>
        /// فحص الاتصالات
        /// </summary>
        private async Task CheckConnections()
        {
            // فحص الاتصال بالإنترنت
            bool internetConnected = await CheckInternetConnection();
            UpdateInternetStatus(internetConnected);

            // فحص الاتصال بالسيرفر
            bool serverConnected = await CheckServerConnection();
            UpdateServerStatus(serverConnected);
        }

        /// <summary>
        /// فحص الاتصال بالإنترنت
        /// </summary>
        private async Task<bool> CheckInternetConnection()
        {
            try
            {
                using (var ping = new Ping())
                {
                    var reply = await ping.SendPingAsync("*******", 3000);
                    return reply.Status == IPStatus.Success;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// فحص الاتصال بالسيرفر
        /// </summary>
        private async Task<bool> CheckServerConnection()
        {
            try
            {
                // محاكاة فحص السيرفر (يمكن استبدالها بالفحص الفعلي)
                await Task.Delay(100);
                return true; // افتراض أن السيرفر متاح دائماً للتجربة
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// تحديث حالة الإنترنت
        /// </summary>
        private void UpdateInternetStatus(bool connected)
        {
            Dispatcher.Invoke(() =>
            {
                if (connected)
                {
                    InternetStatusIndicator.Fill = new SolidColorBrush(Color.FromRgb(16, 185, 129)); // أخضر
                    InternetStatusText.Text = "متصل بالإنترنت";
                }
                else
                {
                    InternetStatusIndicator.Fill = new SolidColorBrush(Color.FromRgb(239, 68, 68)); // أحمر
                    InternetStatusText.Text = "غير متصل";
                }
            });
        }

        /// <summary>
        /// تحديث حالة السيرفر
        /// </summary>
        private void UpdateServerStatus(bool connected)
        {
            Dispatcher.Invoke(() =>
            {
                if (connected)
                {
                    ServerStatusIndicator.Fill = new SolidColorBrush(Color.FromRgb(16, 185, 129)); // أخضر
                    ServerStatusText.Text = "السيرفر نشط";
                }
                else
                {
                    ServerStatusIndicator.Fill = new SolidColorBrush(Color.FromRgb(239, 68, 68)); // أحمر
                    ServerStatusText.Text = "السيرفر غير متاح";
                }
            });
        }

        #endregion

        #region UI Effects

        /// <summary>
        /// تمييز حقل النص عند التركيز
        /// </summary>
        private void HighlightTextBox(TextBox textBox)
        {
            if (textBox?.Parent is Border border)
            {
                border.BorderBrush = new SolidColorBrush(Color.FromRgb(102, 126, 234));
                border.Background = new SolidColorBrush(Colors.White);
            }
        }

        /// <summary>
        /// إلغاء تمييز حقل النص
        /// </summary>
        private void UnhighlightTextBox(TextBox textBox)
        {
            if (textBox?.Parent is Border border)
            {
                border.BorderBrush = new SolidColorBrush(Color.FromRgb(209, 213, 219));
                border.Background = new SolidColorBrush(Color.FromRgb(249, 250, 251));
            }
        }

        /// <summary>
        /// تمييز حقل كلمة المرور عند التركيز
        /// </summary>
        private void HighlightPasswordBox(PasswordBox passwordBox)
        {
            if (passwordBox?.Parent is Border border)
            {
                border.BorderBrush = new SolidColorBrush(Color.FromRgb(102, 126, 234));
                border.Background = new SolidColorBrush(Colors.White);
            }
        }

        /// <summary>
        /// إلغاء تمييز حقل كلمة المرور
        /// </summary>
        private void UnhighlightPasswordBox(PasswordBox passwordBox)
        {
            if (passwordBox?.Parent is Border border)
            {
                border.BorderBrush = new SolidColorBrush(Color.FromRgb(209, 213, 219));
                border.Background = new SolidColorBrush(Color.FromRgb(249, 250, 251));
            }
        }

        #endregion

        #region Cleanup

        /// <summary>
        /// تنظيف الموارد عند إغلاق النافذة
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            _connectionTimer?.Stop();
            base.OnClosed(e);
        }

        #endregion
    }
}
