using System;
using System.Net.NetworkInformation;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Threading;
using AvocatPro.Services;

namespace AvocatPro.Views.Windows
{
    public partial class LoginWindow : Window
    {
        private readonly DispatcherTimer _connectionTimer;
        private bool _isLoggingIn = false;

        public LoginWindow()
        {
            InitializeComponent();
            
            // تهيئة مؤقت فحص الاتصال
            _connectionTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(5) // فحص كل 5 ثواني
            };
            _connectionTimer.Tick += ConnectionTimer_Tick;
            _connectionTimer.Start();

            // فحص الاتصال فوراً
            CheckConnections();

            // تطبيق أنيميشن الدخول
            Loaded += LoginWindow_Loaded;

            // إضافة معالج Enter للدخول السريع
            KeyDown += LoginWindow_KeyDown;

            // تحميل البيانات المحفوظة
            LoadSavedCredentials();
        }

        private void LoadSavedCredentials()
        {
            try
            {
                if (Properties.Settings.Default.RememberLogin)
                {
                    UsernameTextBox.Text = Properties.Settings.Default.SavedUsername ?? "";
                    RememberMeCheckBox.IsChecked = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل البيانات المحفوظة: {ex.Message}");
            }
        }

        private async void LoginWindow_Loaded(object sender, RoutedEventArgs e)
        {
            // أنيميشن دخول النافذة
            Opacity = 0;
            var fadeIn = new System.Windows.Media.Animation.DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = TimeSpan.FromMilliseconds(800),
                EasingFunction = new System.Windows.Media.Animation.CubicEase 
                { 
                    EasingMode = System.Windows.Media.Animation.EasingMode.EaseOut 
                }
            };
            BeginAnimation(OpacityProperty, fadeIn);

            // تركيز على حقل اسم المستخدم
            await Task.Delay(500);
            UsernameTextBox.Focus();
        }

        private void LoginWindow_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && !_isLoggingIn)
            {
                Login_Click(sender, e);
            }
        }

        private async void ConnectionTimer_Tick(object? sender, EventArgs e)
        {
            await CheckConnections();
        }

        private async Task CheckConnections()
        {
            // فحص الاتصال بالإنترنت
            bool internetConnected = await CheckInternetConnection();
            UpdateInternetStatus(internetConnected);

            // فحص الاتصال بالسيرفر
            bool serverConnected = await CheckServerConnection();
            UpdateServerStatus(serverConnected);
        }

        private async Task<bool> CheckInternetConnection()
        {
            try
            {
                using var ping = new Ping();
                var reply = await ping.SendPingAsync("*******", 3000);
                return reply.Status == IPStatus.Success;
            }
            catch
            {
                return false;
            }
        }

        private async Task<bool> CheckServerConnection()
        {
            try
            {
                // محاكاة فحص الاتصال بالسيرفر
                // يمكن استبدال هذا بفحص حقيقي للسيرفر
                await Task.Delay(100);
                
                // فحص قاعدة البيانات أو API
                using var ping = new Ping();
                var reply = await ping.SendPingAsync("localhost", 2000);
                return reply.Status == IPStatus.Success;
            }
            catch
            {
                return false;
            }
        }

        private void UpdateInternetStatus(bool connected)
        {
            Dispatcher.Invoke(() =>
            {
                if (connected)
                {
                    InternetStatusIndicator.Fill = new SolidColorBrush(Color.FromRgb(16, 185, 129)); // أخضر
                    InternetStatusText.Text = "متصل بالإنترنت";
                }
                else
                {
                    InternetStatusIndicator.Fill = new SolidColorBrush(Color.FromRgb(239, 68, 68)); // أحمر
                    InternetStatusText.Text = "غير متصل بالإنترنت";
                }
            });
        }

        private void UpdateServerStatus(bool connected)
        {
            Dispatcher.Invoke(() =>
            {
                if (connected)
                {
                    ServerStatusIndicator.Fill = new SolidColorBrush(Color.FromRgb(16, 185, 129)); // أخضر
                    ServerStatusText.Text = "متصل بالسيرفر";
                }
                else
                {
                    ServerStatusIndicator.Fill = new SolidColorBrush(Color.FromRgb(239, 68, 68)); // أحمر
                    ServerStatusText.Text = "غير متصل بالسيرفر";
                }
            });
        }

        private async void Login_Click(object sender, RoutedEventArgs e)
        {
            if (_isLoggingIn) return;

            var username = UsernameTextBox.Text.Trim();
            var password = PasswordBox.Password;

            // التحقق من صحة البيانات
            if (string.IsNullOrEmpty(username))
            {
                ShowError("يرجى إدخال اسم المستخدم");
                UsernameTextBox.Focus();
                return;
            }

            if (string.IsNullOrEmpty(password))
            {
                ShowError("يرجى إدخال كلمة المرور");
                PasswordBox.Focus();
                return;
            }

            _isLoggingIn = true;
            await PerformLogin(username, password);
        }

        private async Task PerformLogin(string username, string password)
        {
            try
            {
                // تأثير تحميل على الزر
                LoginButton.Content = "جاري تسجيل الدخول...";
                LoginButton.IsEnabled = false;

                // أنيميشن تحميل
                var loadingAnimation = new System.Windows.Media.Animation.DoubleAnimation
                {
                    From = 1,
                    To = 0.7,
                    Duration = TimeSpan.FromMilliseconds(500),
                    AutoReverse = true,
                    RepeatBehavior = System.Windows.Media.Animation.RepeatBehavior.Forever
                };
                LoginButton.BeginAnimation(OpacityProperty, loadingAnimation);

                // محاكاة عملية تسجيل الدخول
                await Task.Delay(2000);

                // التحقق من بيانات الدخول (مؤقت)
                bool loginSuccess = await ValidateCredentials(username, password);

                if (loginSuccess)
                {
                    // حفظ حالة "تذكرني"
                    if (RememberMeCheckBox.IsChecked == true)
                    {
                        SaveUserCredentials(username);
                    }

                    // أنيميشن نجاح
                    LoginButton.Content = "تم بنجاح! ✓";
                    LoginButton.Background = new SolidColorBrush(Color.FromRgb(16, 185, 129));

                    await Task.Delay(1000);

                    // فتح النافذة الرئيسية
                    var defaultUser = new AvocatPro.Models.User
                    {
                        Id = 1,
                        Username = username,
                        FullName = "المحامي الرئيسي",
                        Email = username.Contains("@") ? username : "<EMAIL>"
                    };
                    var mainWindow = new MainWindow(defaultUser);

                    // تعيين النافذة الرئيسية الجديدة
                    Application.Current.MainWindow = mainWindow;

                    // إضافة معالج لإغلاق نافذة تسجيل الدخول عند إغلاق النافذة الرئيسية
                    mainWindow.Closed += (s, e) => Close();

                    mainWindow.Show();

                    // إخفاء نافذة تسجيل الدخول
                    Hide();
                }
                else
                {
                    ShowError("اسم المستخدم أو كلمة المرور غير صحيحة");
                    ResetLoginButton();
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تسجيل الدخول: {ex.Message}");
                ResetLoginButton();
            }
            finally
            {
                _isLoggingIn = false;
            }
        }

        private async Task<bool> ValidateCredentials(string username, string password)
        {
            // محاكاة التحقق من قاعدة البيانات
            await Task.Delay(1000);
            
            // بيانات تجريبية
            return (username.ToLower() == "admin" && password == "123456") ||
                   (username.ToLower() == "lawyer" && password == "lawyer123") ||
                   (username.Contains("@") && password.Length >= 6);
        }

        private void SaveUserCredentials(string username)
        {
            try
            {
                // حفظ اسم المستخدم في إعدادات التطبيق
                Properties.Settings.Default.SavedUsername = username;
                Properties.Settings.Default.RememberLogin = true;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ بيانات المستخدم: {ex.Message}");
            }
        }

        private void ResetLoginButton()
        {
            LoginButton.BeginAnimation(OpacityProperty, null);
            LoginButton.Opacity = 1;
            LoginButton.Content = "تسجيل الدخول";
            LoginButton.IsEnabled = true;
            LoginButton.Background = new SolidColorBrush(Color.FromRgb(0, 0, 139)); // DarkBlue
        }

        private void ShowError(string message)
        {
            MessageBox.Show(message, "خطأ في تسجيل الدخول", 
                           MessageBoxButton.OK, MessageBoxImage.Warning);
        }

        private async void Close_Click(object sender, RoutedEventArgs e)
        {
            // أنيميشن إغلاق
            var fadeOut = new System.Windows.Media.Animation.DoubleAnimation
            {
                From = 1,
                To = 0,
                Duration = TimeSpan.FromMilliseconds(300)
            };
            
            fadeOut.Completed += (s, args) => Application.Current.Shutdown();
            BeginAnimation(OpacityProperty, fadeOut);
        }

        protected override void OnClosed(EventArgs e)
        {
            // تنظيف الموارد
            _connectionTimer?.Stop();
            base.OnClosed(e);
        }

        // إضافة خاصية السحب للنافذة
        protected override void OnMouseLeftButtonDown(MouseButtonEventArgs e)
        {
            base.OnMouseLeftButtonDown(e);
            if (e.ButtonState == MouseButtonState.Pressed)
            {
                DragMove();
            }
        }
    }
}
