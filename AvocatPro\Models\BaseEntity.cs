using System.ComponentModel.DataAnnotations;

namespace AvocatPro.Models;

/// <summary>
/// الكلاس الأساسي لجميع الكيانات في النظام
/// </summary>
public abstract class BaseEntity
{
    [Key]
    public int Id { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.Now;
    
    public DateTime? UpdatedAt { get; set; }
    
    public string? CreatedBy { get; set; }
    
    public string? UpdatedBy { get; set; }
    
    public bool IsDeleted { get; set; } = false;
    
    public DateTime? DeletedAt { get; set; }
    
    public string? DeletedBy { get; set; }
    
    /// <summary>
    /// ملاحظات عامة
    /// </summary>
    public string? Notes { get; set; }
    
    /// <summary>
    /// رقم المرجع الداخلي
    /// </summary>
    public string? InternalReference { get; set; }
}
