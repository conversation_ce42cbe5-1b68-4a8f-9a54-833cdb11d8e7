<?php
/**
 * Smart Lawyers Web Platform - Easy Installation Script
 * منصة المحامين الذكية - سكريبت التثبيت السهل
 * 
 * @version 1.0.0
 * <AUTHOR> Lawyers Team
 */

class SmartLawyersInstaller
{
    private $steps = [
        'requirements' => 'فحص المتطلبات',
        'database' => 'إعداد قاعدة البيانات',
        'environment' => 'تكوين البيئة',
        'dependencies' => 'تثبيت التبعيات',
        'migration' => 'إنشاء الجداول',
        'admin' => 'إنشاء حساب المدير',
        'integrations' => 'إعداد التكاملات',
        'complete' => 'اكتمال التثبيت'
    ];

    private $currentStep = 'requirements';
    private $errors = [];
    private $config = [];

    public function __construct()
    {
        session_start();
        $this->loadConfig();
        $this->handleRequest();
    }

    private function loadConfig()
    {
        if (isset($_SESSION['install_config'])) {
            $this->config = $_SESSION['install_config'];
        }
        
        if (isset($_GET['step']) && array_key_exists($_GET['step'], $this->steps)) {
            $this->currentStep = $_GET['step'];
        }
    }

    private function handleRequest()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->processStep();
        }
    }

    private function processStep()
    {
        switch ($this->currentStep) {
            case 'requirements':
                $this->checkRequirements();
                break;
            case 'database':
                $this->setupDatabase();
                break;
            case 'environment':
                $this->setupEnvironment();
                break;
            case 'dependencies':
                $this->installDependencies();
                break;
            case 'migration':
                $this->runMigrations();
                break;
            case 'admin':
                $this->createAdmin();
                break;
            case 'integrations':
                $this->setupIntegrations();
                break;
        }
    }

    private function checkRequirements()
    {
        $requirements = [
            'PHP >= 8.1' => version_compare(PHP_VERSION, '8.1.0', '>='),
            'OpenSSL Extension' => extension_loaded('openssl'),
            'PDO Extension' => extension_loaded('pdo'),
            'Mbstring Extension' => extension_loaded('mbstring'),
            'Tokenizer Extension' => extension_loaded('tokenizer'),
            'XML Extension' => extension_loaded('xml'),
            'Ctype Extension' => extension_loaded('ctype'),
            'JSON Extension' => extension_loaded('json'),
            'BCMath Extension' => extension_loaded('bcmath'),
            'Fileinfo Extension' => extension_loaded('fileinfo'),
            'GD Extension' => extension_loaded('gd'),
            'cURL Extension' => extension_loaded('curl'),
            'Zip Extension' => extension_loaded('zip'),
        ];

        $permissions = [
            'storage/' => is_writable('storage/'),
            'bootstrap/cache/' => is_writable('bootstrap/cache/'),
            '.env' => is_writable('.') || is_writable('.env'),
        ];

        $allPassed = true;
        foreach (array_merge($requirements, $permissions) as $check => $passed) {
            if (!$passed) {
                $this->errors[] = "فشل في: $check";
                $allPassed = false;
            }
        }

        if ($allPassed && empty($this->errors)) {
            $this->redirectToNext('database');
        }
    }

    private function setupDatabase()
    {
        if (isset($_POST['db_host'])) {
            $this->config['database'] = [
                'host' => $_POST['db_host'],
                'port' => $_POST['db_port'],
                'database' => $_POST['db_database'],
                'username' => $_POST['db_username'],
                'password' => $_POST['db_password'],
            ];

            // Test database connection
            try {
                $pdo = new PDO(
                    "mysql:host={$this->config['database']['host']};port={$this->config['database']['port']}",
                    $this->config['database']['username'],
                    $this->config['database']['password']
                );
                
                // Create database if not exists
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$this->config['database']['database']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                
                $_SESSION['install_config'] = $this->config;
                $this->redirectToNext('environment');
                
            } catch (PDOException $e) {
                $this->errors[] = "خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage();
            }
        }
    }

    private function setupEnvironment()
    {
        if (isset($_POST['app_name'])) {
            $this->config['app'] = [
                'name' => $_POST['app_name'],
                'url' => $_POST['app_url'],
                'timezone' => $_POST['app_timezone'],
                'locale' => $_POST['app_locale'],
            ];

            $this->generateEnvFile();
            $_SESSION['install_config'] = $this->config;
            $this->redirectToNext('dependencies');
        }
    }

    private function installDependencies()
    {
        if (isset($_POST['install_deps'])) {
            // Install Composer dependencies
            $composerOutput = shell_exec('composer install --no-dev --optimize-autoloader 2>&1');
            
            // Install NPM dependencies
            $npmOutput = shell_exec('npm install 2>&1');
            
            // Build assets
            $buildOutput = shell_exec('npm run build 2>&1');
            
            if (strpos($composerOutput, 'error') === false && strpos($npmOutput, 'error') === false) {
                $this->redirectToNext('migration');
            } else {
                $this->errors[] = "خطأ في تثبيت التبعيات";
            }
        }
    }

    private function runMigrations()
    {
        if (isset($_POST['run_migrations'])) {
            // Generate application key
            shell_exec('php artisan key:generate --force');
            
            // Run migrations
            $migrationOutput = shell_exec('php artisan migrate --force 2>&1');
            
            // Seed database
            $seedOutput = shell_exec('php artisan db:seed --force 2>&1');
            
            if (strpos($migrationOutput, 'error') === false) {
                $this->redirectToNext('admin');
            } else {
                $this->errors[] = "خطأ في إنشاء الجداول";
            }
        }
    }

    private function createAdmin()
    {
        if (isset($_POST['admin_name'])) {
            $this->config['admin'] = [
                'name' => $_POST['admin_name'],
                'email' => $_POST['admin_email'],
                'password' => $_POST['admin_password'],
            ];

            // Create admin user via Artisan command
            $command = sprintf(
                'php artisan make:admin "%s" "%s" "%s"',
                $this->config['admin']['name'],
                $this->config['admin']['email'],
                $this->config['admin']['password']
            );
            
            $output = shell_exec($command . ' 2>&1');
            
            $_SESSION['install_config'] = $this->config;
            $this->redirectToNext('integrations');
        }
    }

    private function setupIntegrations()
    {
        if (isset($_POST['setup_integrations'])) {
            $this->config['integrations'] = [
                'google_calendar' => $_POST['google_client_id'] ?? '',
                'whatsapp' => $_POST['whatsapp_token'] ?? '',
                'morocco_apis' => $_POST['morocco_justice_api'] ?? '',
            ];

            $this->updateEnvFile();
            $_SESSION['install_config'] = $this->config;
            $this->redirectToNext('complete');
        }
    }

    private function generateEnvFile()
    {
        $envContent = file_get_contents('.env.example');
        
        // Replace database settings
        $envContent = str_replace('DB_HOST=127.0.0.1', 'DB_HOST=' . $this->config['database']['host'], $envContent);
        $envContent = str_replace('DB_PORT=3306', 'DB_PORT=' . $this->config['database']['port'], $envContent);
        $envContent = str_replace('DB_DATABASE=smartlawyers_web', 'DB_DATABASE=' . $this->config['database']['database'], $envContent);
        $envContent = str_replace('DB_USERNAME=root', 'DB_USERNAME=' . $this->config['database']['username'], $envContent);
        $envContent = str_replace('DB_PASSWORD=', 'DB_PASSWORD=' . $this->config['database']['password'], $envContent);
        
        // Replace app settings
        $envContent = str_replace('APP_NAME="Smart Lawyers Web"', 'APP_NAME="' . $this->config['app']['name'] . '"', $envContent);
        $envContent = str_replace('APP_URL=http://localhost:8000', 'APP_URL=' . $this->config['app']['url'], $envContent);
        $envContent = str_replace('APP_TIMEZONE=Africa/Casablanca', 'APP_TIMEZONE=' . $this->config['app']['timezone'], $envContent);
        $envContent = str_replace('APP_LOCALE=ar', 'APP_LOCALE=' . $this->config['app']['locale'], $envContent);
        
        file_put_contents('.env', $envContent);
    }

    private function updateEnvFile()
    {
        $envContent = file_get_contents('.env');
        
        if (isset($this->config['integrations']['google_calendar'])) {
            $envContent = preg_replace('/GOOGLE_CLIENT_ID=.*/', 'GOOGLE_CLIENT_ID=' . $this->config['integrations']['google_calendar'], $envContent);
        }
        
        if (isset($this->config['integrations']['whatsapp'])) {
            $envContent = preg_replace('/WHATSAPP_TOKEN=.*/', 'WHATSAPP_TOKEN=' . $this->config['integrations']['whatsapp'], $envContent);
        }
        
        file_put_contents('.env', $envContent);
    }

    private function redirectToNext($step)
    {
        header("Location: install.php?step=$step");
        exit;
    }

    public function render()
    {
        include 'install-template.php';
    }
}

// Initialize installer
$installer = new SmartLawyersInstaller();
$installer->render();
?>
