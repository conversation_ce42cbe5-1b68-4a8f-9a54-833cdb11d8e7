﻿using System.IO;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.EntityFrameworkCore;
using AvocatPro.Data;
using AvocatPro.Services;
using AvocatPro.Views;
using AvocatPro.Views.Windows;

namespace AvocatPro;

/// <summary>
/// تطبيق AvocatPro الرئيسي
/// </summary>
public partial class App : Application
{
    private IHost? _host;

    public static IServiceProvider ServiceProvider { get; private set; } = null!;

    protected override async void OnStartup(StartupEventArgs e)
    {
        try
        {
            // إنشاء Host للتطبيق
            _host = CreateHostBuilder().Build();
            ServiceProvider = _host.Services;

            // تهيئة قاعدة البيانات
            await InitializeDatabaseAsync();

            // إنشاء المستخدم الافتراضي
            await CreateDefaultUserAsync();

            // إظهار نافذة تسجيل الدخول البسيطة أولاً
            var loginWindow = new Views.Windows.SimpleLoginWindow();
            MainWindow = loginWindow;
            loginWindow.Show();

            base.OnStartup(e);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في بدء التطبيق: {ex.Message}\n\nتفاصيل الخطأ:\n{ex}",
                          "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown();
        }
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        if (_host != null)
        {
            await _host.StopAsync();
            _host.Dispose();
        }

        base.OnExit(e);
    }

    private static IHostBuilder CreateHostBuilder()
    {
        return Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // تكوين قاعدة البيانات
                services.AddDbContext<AvocatProDbContext>(options =>
                {
                    var connectionString = GetConnectionString();
                    options.UseSqlite(connectionString);
                });

                // تسجيل الخدمات
                services.AddScoped<IAuthenticationService, AuthenticationService>();
                services.AddScoped<IUserService, UserService>();
                services.AddScoped<IClientService, ClientService>();

                // إضافة خدمات أخرى حسب الحاجة
            });
    }

    private static string GetConnectionString()
    {
        var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
        var appFolder = Path.Combine(appDataPath, "AvocatPro");

        if (!Directory.Exists(appFolder))
        {
            Directory.CreateDirectory(appFolder);
        }

        var dbPath = Path.Combine(appFolder, "AvocatPro.db");
        return $"Data Source={dbPath}";
    }

    private async Task InitializeDatabaseAsync()
    {
        try
        {
            using var scope = ServiceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<AvocatProDbContext>();

            // إنشاء قاعدة البيانات إذا لم تكن موجودة
            await context.Database.EnsureCreatedAsync();
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"فشل في تهيئة قاعدة البيانات: {ex.Message}", ex);
        }
    }

    private async Task CreateDefaultUserAsync()
    {
        try
        {
            using var scope = ServiceProvider.CreateScope();
            var userService = scope.ServiceProvider.GetRequiredService<IUserService>();

            // إنشاء المستخدم الافتراضي إذا لم يكن موجوداً
            await userService.CreateDefaultUserIfNotExistsAsync();
        }
        catch (Exception ex)
        {
            // تسجيل الخطأ ولكن لا نوقف التطبيق
            System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء المستخدم الافتراضي: {ex.Message}");
        }
    }

    /// <summary>
    /// تشغيل النافذة الرئيسية الحديثة
    /// </summary>
    public static void ShowModernMainWindow()
    {
        try
        {
            // إغلاق النافذة الحالية إذا كانت موجودة
            Current.MainWindow?.Close();

            // إنشاء وإظهار النافذة الرئيسية الحديثة
            var modernMainWindow = new ModernMainWindow();
            Current.MainWindow = modernMainWindow;
            modernMainWindow.Show();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح النافذة الرئيسية: {ex.Message}",
                           "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// تشغيل النافذة الرئيسية الحديثة مباشرة (للاختبار)
    /// </summary>
    public static void ShowModernMainWindowDirectly()
    {
        try
        {
            var app = new App();
            app.InitializeComponent();

            // تشغيل النافذة الرئيسية مباشرة
            var modernMainWindow = new ModernMainWindow();
            app.MainWindow = modernMainWindow;
            modernMainWindow.Show();

            app.Run();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تشغيل التطبيق: {ex.Message}",
                           "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}

