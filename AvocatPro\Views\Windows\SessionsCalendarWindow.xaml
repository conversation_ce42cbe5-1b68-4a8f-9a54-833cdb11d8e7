<Window x:Class="AvocatPro.Views.Windows.SessionsCalendarWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تقويم الجلسات" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize"
        Background="#F8FAFC" FlowDirection="RightToLeft">

    <Window.Resources>
        <Style TargetType="Button" x:Key="CalendarDayStyle">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E5E7EB"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Margin" Value="1"/>
            <Setter Property="Padding" Value="4"/>
            <Setter Property="VerticalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Stretch" VerticalAlignment="Stretch"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F3F4F6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style TargetType="Button" x:Key="ActionButtonStyle">
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="0,0,8,0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="6" Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان والتنقل -->
        <Border Grid.Row="0" Background="White" CornerRadius="12" Padding="24" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Text="تقويم الجلسات" FontSize="24" FontWeight="Bold" Foreground="#1F2937"/>
                    <TextBlock Text="عرض وإدارة جلسات المحكمة بشكل تقويمي" FontSize="14" Foreground="#6B7280"/>
                </StackPanel>
                
                <!-- التنقل بين الشهور -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,20,0">
                    <Button x:Name="PreviousMonthButton" Content="◀" Background="#6366F1" Foreground="White"
                            Style="{StaticResource ActionButtonStyle}" Click="PreviousMonthButton_Click"/>
                    <TextBlock x:Name="CurrentMonthLabel" Text="يناير 2024" FontSize="18" FontWeight="SemiBold"
                               VerticalAlignment="Center" Margin="15,0"/>
                    <Button x:Name="NextMonthButton" Content="▶" Background="#6366F1" Foreground="White"
                            Style="{StaticResource ActionButtonStyle}" Click="NextMonthButton_Click"/>
                </StackPanel>
                
                <!-- أزرار الإجراءات -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button x:Name="TodayButton" Content="📅 اليوم" Background="#10B981" Foreground="White"
                            Style="{StaticResource ActionButtonStyle}" Click="TodayButton_Click"/>
                    <Button x:Name="AddSessionButton" Content="➕ إضافة جلسة" Background="#F59E0B" Foreground="White"
                            Style="{StaticResource ActionButtonStyle}" Click="AddSessionButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- مفتاح الألوان -->
        <Border Grid.Row="1" Background="White" CornerRadius="12" Padding="20" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
            </Border.Effect>
            
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <StackPanel Orientation="Horizontal" Margin="0,0,30,0">
                    <Border Background="#6366F1" Width="16" Height="16" CornerRadius="8" Margin="0,0,8,0"/>
                    <TextBlock Text="مجدولة" VerticalAlignment="Center" FontSize="12"/>
                </StackPanel>
                <StackPanel Orientation="Horizontal" Margin="0,0,30,0">
                    <Border Background="#F59E0B" Width="16" Height="16" CornerRadius="8" Margin="0,0,8,0"/>
                    <TextBlock Text="جارية" VerticalAlignment="Center" FontSize="12"/>
                </StackPanel>
                <StackPanel Orientation="Horizontal" Margin="0,0,30,0">
                    <Border Background="#10B981" Width="16" Height="16" CornerRadius="8" Margin="0,0,8,0"/>
                    <TextBlock Text="مكتملة" VerticalAlignment="Center" FontSize="12"/>
                </StackPanel>
                <StackPanel Orientation="Horizontal" Margin="0,0,30,0">
                    <Border Background="#8B5CF6" Width="16" Height="16" CornerRadius="8" Margin="0,0,8,0"/>
                    <TextBlock Text="مؤجلة" VerticalAlignment="Center" FontSize="12"/>
                </StackPanel>
                <StackPanel Orientation="Horizontal">
                    <Border Background="#EF4444" Width="16" Height="16" CornerRadius="8" Margin="0,0,8,0"/>
                    <TextBlock Text="ملغية" VerticalAlignment="Center" FontSize="12"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- التقويم -->
        <Border Grid.Row="2" Background="White" CornerRadius="12" Padding="0">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
            </Border.Effect>
            
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- رؤوس أيام الأسبوع -->
                <Grid Grid.Row="0" Background="#F8FAFC">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="الأحد" HorizontalAlignment="Center" VerticalAlignment="Center"
                               FontWeight="SemiBold" FontSize="14" Padding="0,15" Foreground="#374151"/>
                    <TextBlock Grid.Column="1" Text="الاثنين" HorizontalAlignment="Center" VerticalAlignment="Center"
                               FontWeight="SemiBold" FontSize="14" Padding="0,15" Foreground="#374151"/>
                    <TextBlock Grid.Column="2" Text="الثلاثاء" HorizontalAlignment="Center" VerticalAlignment="Center"
                               FontWeight="SemiBold" FontSize="14" Padding="0,15" Foreground="#374151"/>
                    <TextBlock Grid.Column="3" Text="الأربعاء" HorizontalAlignment="Center" VerticalAlignment="Center"
                               FontWeight="SemiBold" FontSize="14" Padding="0,15" Foreground="#374151"/>
                    <TextBlock Grid.Column="4" Text="الخميس" HorizontalAlignment="Center" VerticalAlignment="Center"
                               FontWeight="SemiBold" FontSize="14" Padding="0,15" Foreground="#374151"/>
                    <TextBlock Grid.Column="5" Text="الجمعة" HorizontalAlignment="Center" VerticalAlignment="Center"
                               FontWeight="SemiBold" FontSize="14" Padding="0,15" Foreground="#374151"/>
                    <TextBlock Grid.Column="6" Text="السبت" HorizontalAlignment="Center" VerticalAlignment="Center"
                               FontWeight="SemiBold" FontSize="14" Padding="0,15" Foreground="#374151"/>
                </Grid>

                <!-- أيام الشهر -->
                <Grid x:Name="CalendarGrid" Grid.Row="1">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                </Grid>
            </Grid>
        </Border>

        <!-- تفاصيل اليوم المحدد -->
        <Border Grid.Row="3" Background="White" CornerRadius="12" Padding="20" Margin="0,20,0,0">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Margin="0,0,20,0">
                    <TextBlock x:Name="SelectedDateLabel" Text="اختر يوماً لعرض الجلسات" 
                               FontSize="16" FontWeight="SemiBold" Foreground="#1F2937" Margin="0,0,0,10"/>
                    <TextBlock x:Name="SessionsCountLabel" Text="" FontSize="12" Foreground="#6B7280"/>
                </StackPanel>

                <ScrollViewer Grid.Column="1" HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Disabled">
                    <StackPanel x:Name="DaySessionsPanel" Orientation="Horizontal">
                        <!-- سيتم ملء هذا القسم ديناميكياً بجلسات اليوم المحدد -->
                    </StackPanel>
                </ScrollViewer>
            </Grid>
        </Border>
    </Grid>
</Window>
