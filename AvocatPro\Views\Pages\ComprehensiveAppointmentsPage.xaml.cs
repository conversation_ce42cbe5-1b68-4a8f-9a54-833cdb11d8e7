using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;
using AvocatPro.Models;
using AvocatPro.Services;

namespace AvocatPro.Views.Pages
{
    /// <summary>
    /// صفحة إدارة المواعيد الشاملة
    /// </summary>
    public partial class ComprehensiveAppointmentsPage : Page, INotifyPropertyChanged
    {
        #region Fields

        private readonly AppointmentsService _appointmentsService;
        private readonly NotificationService _notificationService;
        private ObservableCollection<AdvancedAppointmentModel> _allAppointments;
        private ObservableCollection<AdvancedAppointmentModel> _filteredAppointments;
        private AppointmentStatistics _statistics;
        private bool _isSearchPlaceholder = true;

        #endregion

        #region Properties

        public ObservableCollection<AdvancedAppointmentModel> FilteredAppointments
        {
            get => _filteredAppointments;
            set { _filteredAppointments = value; OnPropertyChanged(); }
        }

        public AppointmentStatistics Statistics
        {
            get => _statistics;
            set { _statistics = value; OnPropertyChanged(); }
        }

        #endregion

        #region Constructor

        public ComprehensiveAppointmentsPage()
        {
            InitializeComponent();

            try
            {
                _appointmentsService = new AppointmentsService();
                _notificationService = new NotificationService();
                _allAppointments = new ObservableCollection<AdvancedAppointmentModel>();
                _filteredAppointments = new ObservableCollection<AdvancedAppointmentModel>();
                _statistics = new AppointmentStatistics();

                DataContext = this;

                InitializeData();
                SetupFilters();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة صفحة المواعيد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region Initialization

        private void InitializeData()
        {
            try
            {
                LoadSampleData();
                ApplyFilters();
                UpdateStatistics();
                UpdateStatisticsDisplay();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadSampleData()
        {
            // بيانات تجريبية للمواعيد
            var sampleAppointments = new List<AdvancedAppointmentModel>
            {
                new AdvancedAppointmentModel
                {
                    Id = 1,
                    AppointmentNumber = _appointmentsService.GenerateAppointmentNumber(),
                    Title = "استشارة قانونية - قضية عقارية",
                    Description = "مناقشة تفاصيل قضية النزاع العقاري",
                    AppointmentDate = DateTime.Today,
                    AppointmentTime = new TimeSpan(10, 0, 0),
                    Duration = new TimeSpan(1, 30, 0),
                    AppointmentType = "استشارة قانونية",
                    Location = "المكتب الرئيسي - قاعة الاجتماعات",
                    ClientName = "أحمد محمد العلوي",
                    ClientPhone = "0612345678",
                    ClientEmail = "<EMAIL>",
                    AssignedLawyer = "الأستاذ محمد العلوي",
                    Status = "مجدول",
                    Priority = "مهم",
                    Category = "استشارة",
                    Fees = 500,
                    ReminderEnabled = true,
                    ReminderMinutes = 30,
                    EmailReminderEnabled = true,
                    Notes = "يرجى إحضار جميع الوثائق المتعلقة بالعقار"
                },
                new AdvancedAppointmentModel
                {
                    Id = 2,
                    AppointmentNumber = _appointmentsService.GenerateAppointmentNumber(),
                    Title = "اجتماع مع فريق الدفاع",
                    Description = "مراجعة استراتيجية الدفاع في القضية التجارية",
                    AppointmentDate = DateTime.Today.AddDays(1),
                    AppointmentTime = new TimeSpan(14, 0, 0),
                    Duration = new TimeSpan(2, 0, 0),
                    AppointmentType = "اجتماع عمل",
                    Location = "قاعة المؤتمرات",
                    ClientName = "شركة المغرب التجارية",
                    ClientPhone = "0523456789",
                    ClientEmail = "<EMAIL>",
                    AssignedLawyer = "الأستاذة فاطمة الزهراء",
                    Status = "مجدول",
                    Priority = "عاجل",
                    Category = "اجتماع",
                    Fees = 1000,
                    ReminderEnabled = true,
                    ReminderMinutes = 60,
                    EmailReminderEnabled = true,
                    SmsReminderEnabled = true
                },
                new AdvancedAppointmentModel
                {
                    Id = 3,
                    AppointmentNumber = _appointmentsService.GenerateAppointmentNumber(),
                    Title = "جلسة محكمة - قضية الأحوال الشخصية",
                    Description = "حضور جلسة المحكمة للقضية رقم 2024/123",
                    AppointmentDate = DateTime.Today.AddDays(3),
                    AppointmentTime = new TimeSpan(9, 30, 0),
                    Duration = new TimeSpan(3, 0, 0),
                    AppointmentType = "جلسة محكمة",
                    Location = "محكمة الأسرة بالرباط",
                    ClientName = "خديجة الإدريسي",
                    ClientPhone = "0634567890",
                    ClientEmail = "<EMAIL>",
                    AssignedLawyer = "الأستاذ عبد الرحمن الفاسي",
                    Status = "مجدول",
                    Priority = "عاجل",
                    Category = "محكمة",
                    Fees = 800,
                    ReminderEnabled = true,
                    ReminderMinutes = 1440, // يوم واحد
                    EmailReminderEnabled = true
                },
                new AdvancedAppointmentModel
                {
                    Id = 4,
                    AppointmentNumber = _appointmentsService.GenerateAppointmentNumber(),
                    Title = "توقيع عقد شراكة",
                    Description = "توقيع عقد الشراكة التجارية الجديدة",
                    AppointmentDate = DateTime.Today.AddDays(-1),
                    AppointmentTime = new TimeSpan(11, 0, 0),
                    Duration = new TimeSpan(1, 0, 0),
                    AppointmentType = "توقيع عقد",
                    Location = "مكتب الشريك",
                    ClientName = "يوسف الحسني",
                    ClientPhone = "0645678901",
                    ClientEmail = "<EMAIL>",
                    AssignedLawyer = "الأستاذة عائشة المغربي",
                    Status = "مكتمل",
                    Priority = "عادي",
                    Category = "عقد",
                    Fees = 1200,
                    ReminderSent = true,
                    ReminderSentDate = DateTime.Today.AddDays(-2),
                    Outcome = "تم توقيع العقد بنجاح"
                },
                new AdvancedAppointmentModel
                {
                    Id = 5,
                    AppointmentNumber = _appointmentsService.GenerateAppointmentNumber(),
                    Title = "متابعة قضية العمل",
                    Description = "مراجعة تطورات قضية النزاع العمالي",
                    AppointmentDate = DateTime.Today.AddDays(7),
                    AppointmentTime = new TimeSpan(15, 30, 0),
                    Duration = new TimeSpan(1, 30, 0),
                    AppointmentType = "متابعة قضية",
                    Location = "المكتب الرئيسي",
                    ClientName = "سعيد الأندلسي",
                    ClientPhone = "0656789012",
                    ClientEmail = "<EMAIL>",
                    AssignedLawyer = "الأستاذ أحمد الأندلسي",
                    Status = "مجدول",
                    Priority = "عادي",
                    Category = "متابعة",
                    Fees = 300,
                    ReminderEnabled = true,
                    ReminderMinutes = 120,
                    EmailReminderEnabled = true
                }
            };

            _allAppointments.Clear();
            foreach (var appointment in sampleAppointments)
            {
                _allAppointments.Add(appointment);
            }
        }

        private void SetupFilters()
        {
            try
            {
                // إعداد فلاتر الحالات
                if (_appointmentsService != null)
                {
                    var statuses = _appointmentsService.GetAppointmentStatuses();
                    if (statuses != null && StatusFilterComboBox != null)
                    {
                        foreach (var status in statuses)
                        {
                            StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = status });
                        }
                    }
                }

                // إعداد فلاتر أنواع المواعيد
                if (_appointmentsService != null)
                {
                    var types = _appointmentsService.GetAppointmentTypes();
                    if (types != null && TypeFilterComboBox != null)
                    {
                        foreach (var type in types)
                        {
                            TypeFilterComboBox.Items.Add(new ComboBoxItem { Content = type });
                        }
                    }
                }

                // إعداد فلاتر المحامين
                if (_appointmentsService != null)
                {
                    var lawyers = _appointmentsService.GetLawyers();
                    if (lawyers != null && LawyerFilterComboBox != null)
                    {
                        foreach (var lawyer in lawyers)
                        {
                            LawyerFilterComboBox.Items.Add(new ComboBoxItem { Content = lawyer });
                        }
                    }
                }

                // إعداد فلاتر الأولوية
                if (_appointmentsService != null)
                {
                    var priorities = _appointmentsService.GetPriorityLevels();
                    if (priorities != null && PriorityFilterComboBox != null)
                    {
                        foreach (var priority in priorities)
                        {
                            PriorityFilterComboBox.Items.Add(new ComboBoxItem { Content = priority });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إعداد الفلاتر: {ex.Message}");
            }
        }

        #endregion

        #region Statistics

        private void UpdateStatistics()
        {
            try
            {
                if (_appointmentsService != null && _allAppointments != null)
                {
                    Statistics = _appointmentsService.CalculateStatistics(_allAppointments.ToList());
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الإحصائيات: {ex.Message}");
            }
        }

        private void UpdateStatisticsDisplay()
        {
            try
            {
                if (Statistics != null)
                {
                    if (TotalAppointmentsCount != null)
                        TotalAppointmentsCount.Text = Statistics.TotalAppointments.ToString();
                    if (TodayAppointmentsCount != null)
                        TodayAppointmentsCount.Text = Statistics.TodayAppointments.ToString();
                    if (ScheduledAppointmentsCount != null)
                        ScheduledAppointmentsCount.Text = Statistics.ScheduledAppointments.ToString();
                    if (CompletedAppointmentsCount != null)
                        CompletedAppointmentsCount.Text = Statistics.CompletedAppointments.ToString();
                    if (OverdueAppointmentsCount != null)
                        OverdueAppointmentsCount.Text = Statistics.OverdueAppointments.ToString();
                    if (PendingRemindersCount != null)
                        PendingRemindersCount.Text = Statistics.PendingReminders.ToString();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث عرض الإحصائيات: {ex.Message}");
            }
        }

        #endregion

        #region Filtering

        private void ApplyFilters()
        {
            try
            {
                if (_allAppointments == null || _filteredAppointments == null)
                    return;

                var filtered = _allAppointments.AsEnumerable();

                // تطبيق فلتر البحث
                if (!_isSearchPlaceholder && SearchTextBox != null && !string.IsNullOrWhiteSpace(SearchTextBox.Text))
                {
                    var searchText = SearchTextBox.Text.ToLower();
                    filtered = filtered.Where(a =>
                        a.Title.ToLower().Contains(searchText) ||
                        a.ClientName.ToLower().Contains(searchText) ||
                        a.AppointmentType.ToLower().Contains(searchText) ||
                        a.AssignedLawyer.ToLower().Contains(searchText) ||
                        a.AppointmentNumber.ToLower().Contains(searchText));
                }

                // تطبيق فلتر الحالة
                if (StatusFilterComboBox?.SelectedItem is ComboBoxItem statusItem && 
                    statusItem.Content.ToString() != "جميع الحالات")
                {
                    filtered = filtered.Where(a => a.Status == statusItem.Content.ToString());
                }

                // تطبيق فلتر نوع الموعد
                if (TypeFilterComboBox?.SelectedItem is ComboBoxItem typeItem && 
                    typeItem.Content.ToString() != "جميع الأنواع")
                {
                    filtered = filtered.Where(a => a.AppointmentType == typeItem.Content.ToString());
                }

                // تطبيق فلتر المحامي
                if (LawyerFilterComboBox?.SelectedItem is ComboBoxItem lawyerItem && 
                    lawyerItem.Content.ToString() != "جميع المحامين")
                {
                    filtered = filtered.Where(a => a.AssignedLawyer == lawyerItem.Content.ToString());
                }

                // تطبيق فلتر الأولوية
                if (PriorityFilterComboBox?.SelectedItem is ComboBoxItem priorityItem && 
                    priorityItem.Content.ToString() != "جميع الأولويات")
                {
                    filtered = filtered.Where(a => a.Priority == priorityItem.Content.ToString());
                }

                // تطبيق فلتر التاريخ
                if (FromDatePicker?.SelectedDate != null)
                {
                    filtered = filtered.Where(a => a.AppointmentDate.Date >= FromDatePicker.SelectedDate.Value.Date);
                }

                if (ToDatePicker?.SelectedDate != null)
                {
                    filtered = filtered.Where(a => a.AppointmentDate.Date <= ToDatePicker.SelectedDate.Value.Date);
                }

                // تطبيق العرض السريع
                if (QuickViewComboBox?.SelectedItem is ComboBoxItem quickViewItem)
                {
                    switch (quickViewItem.Content.ToString())
                    {
                        case "مواعيد اليوم":
                            filtered = filtered.Where(a => a.AppointmentDate.Date == DateTime.Today);
                            break;
                        case "مواعيد غداً":
                            filtered = filtered.Where(a => a.AppointmentDate.Date == DateTime.Today.AddDays(1));
                            break;
                        case "مواعيد الأسبوع":
                            var startOfWeek = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
                            var endOfWeek = startOfWeek.AddDays(6);
                            filtered = filtered.Where(a => a.AppointmentDate.Date >= startOfWeek && a.AppointmentDate.Date <= endOfWeek);
                            break;
                        case "مواعيد الشهر":
                            var startOfMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                            var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);
                            filtered = filtered.Where(a => a.AppointmentDate.Date >= startOfMonth && a.AppointmentDate.Date <= endOfMonth);
                            break;
                        case "المواعيد القادمة":
                            filtered = filtered.Where(a => a.AppointmentDateTime > DateTime.Now && a.Status == "مجدول");
                            break;
                        case "المواعيد المتأخرة":
                            filtered = filtered.Where(a => a.IsOverdue);
                            break;
                        case "تحتاج تذكير":
                            filtered = filtered.Where(a => a.NeedsReminder);
                            break;
                    }
                }

                // تحديث المجموعة المفلترة
                FilteredAppointments.Clear();
                foreach (var appointment in filtered.OrderBy(a => a.AppointmentDateTime))
                {
                    FilteredAppointments.Add(appointment);
                }

                if (AppointmentsDataGrid != null)
                    AppointmentsDataGrid.ItemsSource = FilteredAppointments;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق الفلاتر: {ex.Message}");
            }
        }

        #endregion

        #region Event Handlers

        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (_isSearchPlaceholder && sender is TextBox textBox)
            {
                textBox.Text = "";
                textBox.Foreground = System.Windows.Media.Brushes.Black;
                _isSearchPlaceholder = false;
            }
        }

        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (sender is TextBox textBox && string.IsNullOrWhiteSpace(textBox.Text))
            {
                textBox.Text = "البحث في المواعيد...";
                textBox.Foreground = System.Windows.Media.Brushes.Gray;
                _isSearchPlaceholder = true;
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (!_isSearchPlaceholder)
            {
                ApplyFilters();
            }
        }

        private void FilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void DatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void QuickViewComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void AppointmentsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // تفعيل/تعطيل أزرار الإجراءات حسب التحديد
            var hasSelection = AppointmentsDataGrid.SelectedItem != null;
            
            if (EditAppointmentButton != null)
                EditAppointmentButton.IsEnabled = hasSelection;
            if (DeleteAppointmentButton != null)
                DeleteAppointmentButton.IsEnabled = hasSelection;
            if (ViewDetailsButton != null)
                ViewDetailsButton.IsEnabled = hasSelection;
            if (PrintAppointmentButton != null)
                PrintAppointmentButton.IsEnabled = hasSelection;
        }

        #endregion

        #region Button Events

        private void AddAppointmentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addWindow = new AvocatPro.Views.Windows.AdvancedAddAppointmentWindow();
                if (addWindow.ShowDialog() == true)
                {
                    _allAppointments.Add(addWindow.NewAppointment);
                    ApplyFilters();
                    UpdateStatistics();
                    UpdateStatisticsDisplay();
                    MessageBox.Show("تم إضافة الموعد بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CalendarViewButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var calendarWindow = new AvocatPro.Views.Windows.AppointmentsCalendarWindow(_allAppointments.ToList());
                calendarWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SendRemindersButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var appointmentsNeedingReminder = _allAppointments.Where(a => a.NeedsReminder).ToList();
                
                if (!appointmentsNeedingReminder.Any())
                {
                    MessageBox.Show("لا توجد مواعيد تحتاج إلى تذكير حالياً", "تذكيرات", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                var result = MessageBox.Show($"هل تريد إرسال {appointmentsNeedingReminder.Count} تذكير؟", 
                    "تأكيد إرسال التذكيرات", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    await _notificationService.SendBatchRemindersAsync(appointmentsNeedingReminder);
                    UpdateStatistics();
                    UpdateStatisticsDisplay();
                    ApplyFilters();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إرسال التذكيرات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void EditAppointmentButton_Click(object sender, RoutedEventArgs e)
        {
            if (AppointmentsDataGrid.SelectedItem is AdvancedAppointmentModel selectedAppointment)
            {
                try
                {
                    var editWindow = new AvocatPro.Views.Windows.AdvancedAddAppointmentWindow(selectedAppointment);
                    if (editWindow.ShowDialog() == true)
                    {
                        // تحديث الموعد في القائمة
                        var index = _allAppointments.IndexOf(selectedAppointment);
                        if (index >= 0)
                        {
                            _allAppointments[index] = editWindow.NewAppointment;
                            ApplyFilters();
                            UpdateStatistics();
                            UpdateStatisticsDisplay();
                            MessageBox.Show("تم تحديث الموعد بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تعديل الموعد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void DeleteAppointmentButton_Click(object sender, RoutedEventArgs e)
        {
            if (AppointmentsDataGrid.SelectedItem is AdvancedAppointmentModel selectedAppointment)
            {
                var result = MessageBox.Show($"هل أنت متأكد من حذف الموعد: {selectedAppointment.Title}؟", 
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    _allAppointments.Remove(selectedAppointment);
                    ApplyFilters();
                    UpdateStatistics();
                    UpdateStatisticsDisplay();
                    MessageBox.Show("تم حذف الموعد بنجاح", "حذف", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void ViewDetailsButton_Click(object sender, RoutedEventArgs e)
        {
            if (AppointmentsDataGrid.SelectedItem is AdvancedAppointmentModel selectedAppointment)
            {
                MessageBox.Show($"سيتم فتح نافذة تفاصيل الموعد: {selectedAppointment.Title}", "تفاصيل الموعد", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void PrintAppointmentButton_Click(object sender, RoutedEventArgs e)
        {
            if (AppointmentsDataGrid.SelectedItem is AdvancedAppointmentModel selectedAppointment)
            {
                MessageBox.Show($"سيتم طباعة تفاصيل الموعد: {selectedAppointment.Title}", "طباعة", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                InitializeData();
                MessageBox.Show("تم تحديث البيانات بنجاح", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var exportData = _appointmentsService.PrepareExportData(FilteredAppointments.ToList());
                MessageBox.Show("تم تصدير البيانات بنجاح!", "تصدير", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
