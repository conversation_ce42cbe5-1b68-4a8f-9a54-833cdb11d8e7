using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using AvocatPro.Services;
using AvocatPro.Models;

namespace AvocatPro.Views.Pages
{
    public partial class SecurityManagementPage : Page
    {
        private readonly SecurityService _securityService;
        private readonly AuditService _auditService;
        private readonly CloudBackupService _backupService;
        private bool _isLoading = false;

        public SecurityManagementPage()
        {
            InitializeComponent();
            _securityService = new SecurityService();
            _auditService = new AuditService();
            _backupService = new CloudBackupService();
            
            Loaded += SecurityManagementPage_Loaded;
        }

        private async void SecurityManagementPage_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadSecurityDataAsync();
        }

        private async Task LoadSecurityDataAsync()
        {
            if (_isLoading) return;

            _isLoading = true;
            ShowLoading(true);

            try
            {
                // تحميل حالة الأمان
                await LoadSecurityStatusAsync();
                
                // تحميل تنبيهات الأمان
                await LoadSecurityAlertsAsync();
                
                // تحميل إحصائيات النشاط
                await LoadActivityStatisticsAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الأمان: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                ShowLoading(false);
                _isLoading = false;
            }
        }

        private async Task LoadSecurityStatusAsync()
        {
            await Task.Delay(500); // محاكاة التحميل

            // تحديث حالة المصادقة الثنائية
            TwoFactorStatusText.Text = "مفعل";
            TwoFactorStatusText.Foreground = new SolidColorBrush(Colors.Green);
            EnableTwoFactorBtn.Content = "إعدادات المصادقة";

            // تحديث حالة التشفير
            EncryptionStatusText.Text = "مفعل - AES 256";
            EncryptionStatusText.Foreground = new SolidColorBrush(Colors.Green);

            // تحديث حالة النسخ الاحتياطية
            var backupHistory = await _backupService.GetBackupHistoryAsync();
            if (backupHistory.Any())
            {
                var lastBackup = backupHistory.First();
                BackupStatusText.Text = $"آخر نسخة: {lastBackup.CreatedAt:yyyy-MM-dd HH:mm}";
            }

            // تحديث عدد سجلات المراجعة
            var auditLogs = await _auditService.GetAllAuditLogsAsync();
            AuditLogCountText.Text = $"{auditLogs.Count:N0} سجل";
        }

        private async Task LoadSecurityAlertsAsync()
        {
            SecurityAlertsPanel.Children.Clear();

            var alerts = await _auditService.GetSecurityAlertsAsync();
            
            if (!alerts.Any())
            {
                var noAlertsPanel = CreateInfoPanel("✅", "لا توجد تنبيهات أمان حالياً", "#10B981");
                SecurityAlertsPanel.Children.Add(noAlertsPanel);
                return;
            }

            foreach (var alert in alerts.Take(5))
            {
                var alertPanel = CreateSecurityAlertPanel(alert);
                SecurityAlertsPanel.Children.Add(alertPanel);
            }
        }

        private async Task LoadActivityStatisticsAsync()
        {
            ActivityStatsPanel.Children.Clear();

            var statistics = await _auditService.GetAuditStatisticsAsync(DateTime.Now.AddDays(-30), DateTime.Now);

            var stats = new[]
            {
                new { Label = "إجمالي النشاطات", Value = statistics.TotalActivities.ToString("N0"), Icon = "📊" },
                new { Label = "المستخدمين النشطين", Value = statistics.UniqueUsers.ToString("N0"), Icon = "👥" },
                new { Label = "أحداث الأمان", Value = statistics.SecurityEvents.ToString("N0"), Icon = "🔒" },
                new { Label = "أكثر نشاط", Value = statistics.MostCommonAction, Icon = "⭐" }
            };

            foreach (var stat in stats)
            {
                var statPanel = CreateStatPanel(stat.Icon, stat.Label, stat.Value);
                ActivityStatsPanel.Children.Add(statPanel);
            }
        }

        private Border CreateSecurityAlertPanel(SecurityAlert alert)
        {
            var border = new Border
            {
                Margin = new Thickness(0, 0, 0, 10),
                Background = GetAlertBackgroundBrush(alert.Severity),
                Padding = new Thickness(15, 10, 15, 10),
                CornerRadius = new CornerRadius(8)
            };

            var panel = new StackPanel
            {
                Orientation = Orientation.Horizontal
            };

            var iconText = new TextBlock
            {
                Text = GetAlertIcon(alert.Type),
                FontSize = 16,
                Margin = new Thickness(0, 0, 10, 0),
                VerticalAlignment = VerticalAlignment.Center
            };

            var messageText = new TextBlock
            {
                Text = alert.Message,
                FontSize = 14,
                Foreground = new SolidColorBrush(Colors.White),
                VerticalAlignment = VerticalAlignment.Center,
                TextWrapping = TextWrapping.Wrap
            };

            var timeText = new TextBlock
            {
                Text = alert.Timestamp.ToString("HH:mm"),
                FontSize = 12,
                Foreground = new SolidColorBrush(Colors.LightGray),
                Margin = new Thickness(10, 0, 0, 0),
                VerticalAlignment = VerticalAlignment.Center
            };

            panel.Children.Add(iconText);
            panel.Children.Add(messageText);
            panel.Children.Add(timeText);

            border.Child = panel;
            return border;
        }

        private StackPanel CreateStatPanel(string icon, string label, string value)
        {
            var panel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 10)
            };

            var iconText = new TextBlock
            {
                Text = icon,
                FontSize = 16,
                Margin = new Thickness(0, 0, 10, 0),
                VerticalAlignment = VerticalAlignment.Center
            };

            var labelText = new TextBlock
            {
                Text = label,
                FontSize = 14,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#6B7280")),
                VerticalAlignment = VerticalAlignment.Center,
                Width = 120
            };

            var valueText = new TextBlock
            {
                Text = value,
                FontSize = 14,
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#1F2937")),
                VerticalAlignment = VerticalAlignment.Center
            };

            panel.Children.Add(iconText);
            panel.Children.Add(labelText);
            panel.Children.Add(valueText);

            return panel;
        }

        private Border CreateInfoPanel(string icon, string message, string color)
        {
            var border = new Border
            {
                Margin = new Thickness(0, 0, 0, 10),
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
                Padding = new Thickness(15, 10, 15, 10),
                CornerRadius = new CornerRadius(8)
            };

            var panel = new StackPanel
            {
                Orientation = Orientation.Horizontal
            };

            var iconText = new TextBlock
            {
                Text = icon,
                FontSize = 16,
                Margin = new Thickness(0, 0, 10, 0),
                VerticalAlignment = VerticalAlignment.Center
            };

            var messageText = new TextBlock
            {
                Text = message,
                FontSize = 14,
                Foreground = new SolidColorBrush(Colors.White),
                VerticalAlignment = VerticalAlignment.Center
            };

            panel.Children.Add(iconText);
            panel.Children.Add(messageText);

            border.Child = panel;
            return border;
        }

        private Brush GetAlertBackgroundBrush(AlertSeverity severity)
        {
            return severity switch
            {
                AlertSeverity.Critical => new SolidColorBrush((Color)ColorConverter.ConvertFromString("#DC2626")),
                AlertSeverity.High => new SolidColorBrush((Color)ColorConverter.ConvertFromString("#EF4444")),
                AlertSeverity.Medium => new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F59E0B")),
                AlertSeverity.Low => new SolidColorBrush((Color)ColorConverter.ConvertFromString("#3B82F6")),
                _ => new SolidColorBrush((Color)ColorConverter.ConvertFromString("#6B7280"))
            };
        }

        private string GetAlertIcon(SecurityAlertType type)
        {
            return type switch
            {
                SecurityAlertType.MultipleFailedLogins => "🚫",
                SecurityAlertType.SuspiciousActivity => "⚠️",
                SecurityAlertType.UnauthorizedAccess => "🔓",
                SecurityAlertType.DataBreach => "💥",
                SecurityAlertType.SystemAnomaly => "🔍",
                _ => "⚠️"
            };
        }

        private void ShowLoading(bool show)
        {
            LoadingOverlay.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
        }

        // معالجات الأحداث
        private async void Refresh_Click(object sender, RoutedEventArgs e)
        {
            await LoadSecurityDataAsync();
        }

        private void Settings_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم فتح إعدادات الأمان المتقدمة", "إعدادات الأمان", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private async void EnableTwoFactor_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // محاكاة تفعيل المصادقة الثنائية
                var user = new User { Id = 1, Username = "admin" }; // محاكاة المستخدم الحالي
                var result = await _securityService.GenerateTwoFactorCodeAsync(user);

                if (result.Success)
                {
                    MessageBox.Show($"تم إرسال رمز التحقق. {result.Message}", "المصادقة الثنائية", 
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show(result.Message, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تفعيل المصادقة الثنائية: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void EncryptionSettings_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم فتح إعدادات التشفير المتقدمة", "إعدادات التشفير", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private async void CreateBackup_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ShowLoading(true);

                var options = new BackupOptions
                {
                    UserId = 1,
                    BackupType = BackupType.Full,
                    EncryptBackup = true,
                    UploadToCloud = false
                };

                var result = await _backupService.CreateBackupAsync(options);

                if (result.Success)
                {
                    MessageBox.Show($"تم إنشاء النسخة الاحتياطية بنجاح.\nالحجم: {result.Size / 1024 / 1024:F2} MB", 
                                   "نسخة احتياطية", MessageBoxButton.OK, MessageBoxImage.Information);
                    await LoadSecurityStatusAsync();
                }
                else
                {
                    MessageBox.Show(result.Message, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private async void ViewAuditLog_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var logs = await _auditService.GetAllAuditLogsAsync(DateTime.Now.AddDays(-7), DateTime.Now);
                var message = $"آخر 7 أيام:\n";
                
                foreach (var log in logs.Take(5))
                {
                    message += $"• {log.Timestamp:yyyy-MM-dd HH:mm} - {log.Action}\n";
                }

                if (logs.Count > 5)
                {
                    message += $"... و {logs.Count - 5} سجل آخر";
                }

                MessageBox.Show(message, "سجل المراجعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض سجل المراجعة: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveSecuritySettings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // حفظ إعدادات الأمان
                var settings = new
                {
                    AutoLock = AutoLockCheckBox.IsChecked ?? false,
                    LoginNotification = LoginNotificationCheckBox.IsChecked ?? false,
                    DataEncryption = DataEncryptionCheckBox.IsChecked ?? false,
                    AuditLogging = AuditLoggingCheckBox.IsChecked ?? false
                };

                MessageBox.Show("تم حفظ إعدادات الأمان بنجاح", "حفظ الإعدادات", 
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
