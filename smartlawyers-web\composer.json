{"name": "smartlawyers/web-platform", "type": "project", "description": "Smart Lawyers Web Platform - منصة المحامين الذكية", "keywords": ["laravel", "lawyers", "legal", "morocco", "law-office"], "license": "MIT", "require": {"php": "^8.1", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^10.10", "laravel/sanctum": "^3.2", "laravel/tinker": "^2.8", "laravel/socialite": "^5.6", "spatie/laravel-permission": "^5.10", "spatie/laravel-activitylog": "^4.7", "spatie/laravel-backup": "^8.1", "spatie/laravel-pdf": "^1.4", "barryvdh/laravel-dompdf": "^2.0", "maatwebsite/excel": "^3.1", "pusher/pusher-php-server": "^7.2", "laravel/websockets": "^1.14", "google/apiclient": "^2.15", "microsoft/microsoft-graph": "^1.109", "twilio/sdk": "^7.9", "intervention/image": "^2.7", "league/flysystem-aws-s3-v3": "^3.0", "predis/predis": "^2.0", "laravel/horizon": "^5.15", "laravel/telescope": "^4.14", "spatie/laravel-translatable": "^6.5", "mcamara/laravel-localization": "^1.8", "laravel/cashier": "^14.12", "stripe/stripe-php": "^10.15"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}