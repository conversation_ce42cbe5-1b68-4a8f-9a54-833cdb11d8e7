<Page x:Class="AvocatPro.Views.Pages.ModernSessionsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="إدارة الجلسات"
      Background="#F8FAFC"
      FlowDirection="RightToLeft">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- عنوان الصفحة -->
        <Border Grid.Row="0" Background="White" Padding="25" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="⚖️" FontSize="28" Margin="0,0,15,0"/>
                    <StackPanel>
                        <TextBlock Text="إدارة الجلسات" FontSize="24" FontWeight="Bold" 
                                   Foreground="#1F2937" Margin="0,0,0,5"/>
                        <TextBlock Text="إدارة جلسات المحاكم والمرافعات القانونية" FontSize="14" 
                                   Foreground="#6B7280"/>
                    </StackPanel>
                </StackPanel>

                <Button Grid.Column="1" Background="#6366F1" Foreground="White" 
                        BorderThickness="0" Padding="20,12" FontSize="14" FontWeight="SemiBold"
                        Cursor="Hand" Click="AddSession_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="➕" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="إضافة جلسة جديدة"/>
                    </StackPanel>
                </Button>
            </Grid>
        </Border>

        <!-- شريط البحث والتصفية -->
        <Border Grid.Row="1" Background="White" Padding="20" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- مربع البحث -->
                <Border Grid.Column="0" Background="#F9FAFB" BorderBrush="#E5E7EB" 
                        BorderThickness="1" Padding="15,10" Margin="0,0,15,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="🔍" FontSize="16" 
                                   Foreground="#9CA3AF" Margin="0,0,10,0"/>
                        <TextBox Grid.Column="1" Name="SearchTextBox" Background="Transparent" 
                                 BorderThickness="0" FontSize="14" 
                                 TextChanged="SearchTextBox_TextChanged"/>
                        <TextBlock Grid.Column="1" Text="البحث في الجلسات..."
                                   FontSize="14" Foreground="#9CA3AF"
                                   IsHitTestVisible="False"/>
                    </Grid>
                </Border>

                <!-- تصفية حسب الحالة -->
                <ComboBox Grid.Column="1" Name="StatusFilterComboBox" Width="150" 
                          Padding="15,10" FontSize="14" Margin="0,0,15,0"
                          SelectionChanged="StatusFilter_SelectionChanged">
                    <ComboBoxItem Content="جميع الحالات" IsSelected="True"/>
                    <ComboBoxItem Content="مجدولة"/>
                    <ComboBoxItem Content="مكتملة"/>
                    <ComboBoxItem Content="مؤجلة"/>
                    <ComboBoxItem Content="ملغية"/>
                </ComboBox>

                <!-- تصفية حسب المحكمة -->
                <ComboBox Grid.Column="2" Name="CourtFilterComboBox" Width="200" 
                          Padding="15,10" FontSize="14" Margin="0,0,15,0"
                          SelectionChanged="CourtFilter_SelectionChanged">
                    <ComboBoxItem Content="جميع المحاكم" IsSelected="True"/>
                    <ComboBoxItem Content="المحكمة التجارية بالرباط"/>
                    <ComboBoxItem Content="محكمة الأسرة بالدار البيضاء"/>
                    <ComboBoxItem Content="المحكمة الابتدائية بفاس"/>
                    <ComboBoxItem Content="محكمة الاستئناف بطنجة"/>
                </ComboBox>

                <!-- زر التصدير -->
                <Button Grid.Column="3" Background="#10B981" Foreground="White" 
                        BorderThickness="0" Padding="15,10" FontSize="14"
                        Cursor="Hand" Click="Export_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📊" FontSize="14" Margin="0,0,8,0"/>
                        <TextBlock Text="تصدير"/>
                    </StackPanel>
                </Button>
            </Grid>
        </Border>

        <!-- جدول الجلسات -->
        <Border Grid.Row="2" Background="White" Padding="0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- رأس الجدول -->
                <Border Grid.Row="0" Background="#F8FAFC" Padding="20,15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="140"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="رقم الجلسة" FontWeight="SemiBold" 
                                   Foreground="#374151" FontSize="14"/>
                        <TextBlock Grid.Column="1" Text="القضية" FontWeight="SemiBold" 
                                   Foreground="#374151" FontSize="14"/>
                        <TextBlock Grid.Column="2" Text="المحكمة" FontWeight="SemiBold" 
                                   Foreground="#374151" FontSize="14"/>
                        <TextBlock Grid.Column="3" Text="التاريخ" FontWeight="SemiBold" 
                                   Foreground="#374151" FontSize="14"/>
                        <TextBlock Grid.Column="4" Text="الوقت" FontWeight="SemiBold" 
                                   Foreground="#374151" FontSize="14"/>
                        <TextBlock Grid.Column="5" Text="الحالة" FontWeight="SemiBold" 
                                   Foreground="#374151" FontSize="14"/>
                        <TextBlock Grid.Column="6" Text="النوع" FontWeight="SemiBold" 
                                   Foreground="#374151" FontSize="14"/>
                        <TextBlock Grid.Column="7" Text="الإجراءات" FontWeight="SemiBold" 
                                   Foreground="#374151" FontSize="14"/>
                    </Grid>
                </Border>

                <!-- محتوى الجدول -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <ItemsControl Name="SessionsItemsControl">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border BorderBrush="#E5E7EB" BorderThickness="0,0,0,1" 
                                        Padding="20,15" Background="White">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="100"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="150"/>
                                            <ColumnDefinition Width="120"/>
                                            <ColumnDefinition Width="150"/>
                                            <ColumnDefinition Width="100"/>
                                            <ColumnDefinition Width="120"/>
                                            <ColumnDefinition Width="140"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Column="0" Text="{Binding SessionNumber}" 
                                                   Foreground="#6366F1" FontSize="14" FontWeight="SemiBold"/>
                                        <TextBlock Grid.Column="1" Text="{Binding CaseTitle}" 
                                                   Foreground="#1F2937" FontSize="14" FontWeight="Medium"/>
                                        <TextBlock Grid.Column="2" Text="{Binding Court}" 
                                                   Foreground="#6B7280" FontSize="14"/>
                                        <TextBlock Grid.Column="3" Text="{Binding SessionDate}" 
                                                   Foreground="#6B7280" FontSize="14"/>
                                        <TextBlock Grid.Column="4" Text="{Binding SessionTime}" 
                                                   Foreground="#6B7280" FontSize="14"/>
                                        <Border Grid.Column="5" Background="{Binding StatusColor}" 
                                                Padding="8,4" HorizontalAlignment="Center">
                                            <TextBlock Text="{Binding Status}" Foreground="White" 
                                                       FontSize="12" FontWeight="SemiBold"/>
                                        </Border>
                                        <Border Grid.Column="6" Background="{Binding TypeColor}" 
                                                Padding="8,4" HorizontalAlignment="Center">
                                            <TextBlock Text="{Binding SessionType}" Foreground="White" 
                                                       FontSize="12" FontWeight="SemiBold"/>
                                        </Border>
                                        
                                        <StackPanel Grid.Column="7" Orientation="Horizontal" 
                                                    HorizontalAlignment="Center">
                                            <Button Background="#3B82F6" Foreground="White" 
                                                    BorderThickness="0" Padding="8,4" Margin="2"
                                                    FontSize="12" Cursor="Hand"
                                                    Click="ViewSession_Click" Tag="{Binding Id}">
                                                <TextBlock Text="👁️"/>
                                            </Button>
                                            <Button Background="#10B981" Foreground="White" 
                                                    BorderThickness="0" Padding="8,4" Margin="2"
                                                    FontSize="12" Cursor="Hand"
                                                    Click="EditSession_Click" Tag="{Binding Id}">
                                                <TextBlock Text="✏️"/>
                                            </Button>
                                            <Button Background="#F59E0B" Foreground="White" 
                                                    BorderThickness="0" Padding="8,4" Margin="2"
                                                    FontSize="12" Cursor="Hand"
                                                    Click="PostponeSession_Click" Tag="{Binding Id}">
                                                <TextBlock Text="⏰"/>
                                            </Button>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>

                <!-- تذييل الجدول -->
                <Border Grid.Row="2" Background="#F8FAFC" Padding="20,15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Name="SessionsCountLabel" 
                                   Text="إجمالي الجلسات: 0" 
                                   Foreground="#6B7280" FontSize="14"/>

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <Button Content="السابق" Background="#E5E7EB" Foreground="#374151"
                                    BorderThickness="0" Padding="15,8" Margin="5,0"
                                    FontSize="12" Cursor="Hand" Click="PreviousPage_Click"/>
                            <TextBlock Text="صفحة 1 من 1" Foreground="#6B7280" 
                                       FontSize="14" VerticalAlignment="Center" Margin="10,0"/>
                            <Button Content="التالي" Background="#E5E7EB" Foreground="#374151"
                                    BorderThickness="0" Padding="15,8" Margin="5,0"
                                    FontSize="12" Cursor="Hand" Click="NextPage_Click"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </Grid>
        </Border>
    </Grid>
</Page>
