<Window x:Class="AvocatPro.Views.Windows.ComprehensiveAddFileWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة ملف جديد - نظام شامل" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize"
        Background="#F8FAFC" FlowDirection="RightToLeft">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="White" CornerRadius="16" Padding="24" Margin="0,0,0,24">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="20" ShadowDepth="0" Opacity="0.4"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <Border Grid.Column="0" Background="#6366F1" CornerRadius="12" Padding="12" Margin="0,0,16,0">
                    <TextBlock Text="📁" FontSize="24" Foreground="White"/>
                </Border>

                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock x:Name="WindowTitle" Text="إضافة ملف جديد" FontSize="24" FontWeight="Bold" Foreground="#1F2937"/>
                    <TextBlock Text="نظام شامل لإدارة الملفات مع التكامل مع المحاكم الإلكترونية المغربية" FontSize="14" Foreground="#6B7280"/>
                </StackPanel>

                <Button x:Name="CloseButton" Grid.Column="2" Content="✕"
                        Background="#EF4444" Foreground="White" BorderThickness="0"
                        Width="40" Height="40" FontSize="16" FontWeight="Bold"
                        Cursor="Hand" Click="Close_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" CornerRadius="8">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#DC2626"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
            </Grid>
        </Border>

        <!-- محتوى النافذة -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
            <Border Background="White" CornerRadius="16" Padding="32">
                <Border.Effect>
                    <DropShadowEffect Color="#E2E8F0" BlurRadius="15" ShadowDepth="0" Opacity="0.3"/>
                </Border.Effect>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="24"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- العمود الأيسر -->
                    <StackPanel Grid.Column="0">
                        <!-- معلومات أساسية -->
                        <TextBlock Text="📋 المعلومات الأساسية" FontSize="18" FontWeight="Bold"
                                   Foreground="#1F2937" Margin="0,0,0,20"/>

                        <!-- رقم الملف -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="رقم الملف الكامل *" FontSize="14" FontWeight="SemiBold"
                                       Foreground="#374151" Margin="0,0,0,8"/>
                            <Border Background="#F9FAFB" BorderBrush="#D1D5DB" BorderThickness="1" CornerRadius="8">
                                <TextBox x:Name="FileNumberTextBox" Background="Transparent" BorderThickness="0"
                                         Padding="12" FontSize="14" Text="2024/001/مد"/>
                            </Border>
                        </StackPanel>

                        <!-- مرجع المحكمة -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="مرجع المحكمة" FontSize="14" FontWeight="SemiBold"
                                       Foreground="#374151" Margin="0,0,0,8"/>
                            <Border Background="#F9FAFB" BorderBrush="#D1D5DB" BorderThickness="1" CornerRadius="8">
                                <TextBox x:Name="CourtReferenceTextBox" Background="Transparent" BorderThickness="0"
                                         Padding="12" FontSize="14"/>
                            </Border>
                        </StackPanel>

                        <!-- مرجع المكتب -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="مرجع المكتب *" FontSize="14" FontWeight="SemiBold"
                                       Foreground="#374151" Margin="0,0,0,8"/>
                            <Border Background="#F9FAFB" BorderBrush="#D1D5DB" BorderThickness="1" CornerRadius="8">
                                <TextBox x:Name="OfficeReferenceTextBox" Background="Transparent" BorderThickness="0"
                                         Padding="12" FontSize="14"/>
                            </Border>
                        </StackPanel>

                        <!-- نوع الملف -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="نوع الملف *" FontSize="14" FontWeight="SemiBold"
                                       Foreground="#374151" Margin="0,0,0,8"/>
                            <ComboBox x:Name="FileTypeComboBox" Background="#F9FAFB" BorderBrush="#D1D5DB"
                                      BorderThickness="1" Padding="12" FontSize="14">
                                <ComboBoxItem Content="مدني" IsSelected="True"/>
                                <ComboBoxItem Content="تجاري"/>
                                <ComboBoxItem Content="عقاري"/>
                                <ComboBoxItem Content="أسري"/>
                                <ComboBoxItem Content="جنائي"/>
                                <ComboBoxItem Content="إداري"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- المحكمة المختصة -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="المحكمة المختصة *" FontSize="14" FontWeight="SemiBold"
                                       Foreground="#374151" Margin="0,0,0,8"/>
                            <ComboBox x:Name="CourtComboBox" Background="#F9FAFB" BorderBrush="#D1D5DB"
                                      BorderThickness="1" Padding="12" FontSize="14">
                                <ComboBoxItem Content="المحكمة الابتدائية بالرباط"/>
                                <ComboBoxItem Content="المحكمة الابتدائية بالدار البيضاء"/>
                                <ComboBoxItem Content="المحكمة الابتدائية بفاس"/>
                                <ComboBoxItem Content="المحكمة الابتدائية بمراكش"/>
                                <ComboBoxItem Content="المحكمة الابتدائية بطنجة"/>
                                <ComboBoxItem Content="المحكمة التجارية بالدار البيضاء"/>
                                <ComboBoxItem Content="المحكمة التجارية بالرباط"/>
                                <ComboBoxItem Content="محكمة الأسرة بالرباط"/>
                                <ComboBoxItem Content="محكمة الأسرة بالدار البيضاء"/>
                                <ComboBoxItem Content="محكمة الاستئناف بالرباط"/>
                                <ComboBoxItem Content="محكمة الاستئناف بالدار البيضاء"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- نوع القضية -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="نوع القضية *" FontSize="14" FontWeight="SemiBold"
                                       Foreground="#374151" Margin="0,0,0,8"/>
                            <ComboBox x:Name="CaseTypeComboBox" Background="#F9FAFB" BorderBrush="#D1D5DB"
                                      BorderThickness="1" Padding="12" FontSize="14">
                                <ComboBoxItem Content="مدني" IsSelected="True"/>
                                <ComboBoxItem Content="تجاري"/>
                                <ComboBoxItem Content="عقاري"/>
                                <ComboBoxItem Content="أسري"/>
                                <ComboBoxItem Content="جنائي"/>
                                <ComboBoxItem Content="إداري"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- الخصم -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="الخصم *" FontSize="14" FontWeight="SemiBold"
                                       Foreground="#374151" Margin="0,0,0,8"/>
                            <Border Background="#F9FAFB" BorderBrush="#D1D5DB" BorderThickness="1" CornerRadius="8">
                                <TextBox x:Name="OpponentTextBox" Background="Transparent" BorderThickness="0"
                                         Padding="12" FontSize="14"/>
                            </Border>
                        </StackPanel>

                        <!-- موضوع القضية -->
                        <StackPanel Margin="0,0,0,20">
                            <TextBlock Text="موضوع القضية *" FontSize="14" FontWeight="SemiBold"
                                       Foreground="#374151" Margin="0,0,0,8"/>
                            <Border Background="#F9FAFB" BorderBrush="#D1D5DB" BorderThickness="1" CornerRadius="8">
                                <TextBox x:Name="SubjectTextBox" Background="Transparent" BorderThickness="0"
                                         Padding="12" FontSize="14" Height="80" TextWrapping="Wrap"
                                         AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
                            </Border>
                        </StackPanel>

                        <!-- التتبع الإلكتروني -->
                        <TextBlock Text="🌐 التتبع الإلكتروني" FontSize="18" FontWeight="Bold"
                                   Foreground="#1F2937" Margin="0,0,0,20"/>

                        <!-- تفعيل التتبع -->
                        <StackPanel Margin="0,0,0,16">
                            <CheckBox x:Name="ElectronicTrackingCheckBox" Content="تفعيل التتبع الإلكتروني"
                                      FontSize="14" FontWeight="SemiBold" Foreground="#374151"
                                      Checked="ElectronicTracking_Checked" Unchecked="ElectronicTracking_Unchecked"/>
                        </StackPanel>

                        <!-- رمز الملف الإلكتروني -->
                        <StackPanel x:Name="ElectronicCodePanel" Margin="0,0,0,16" IsEnabled="False">
                            <TextBlock Text="رمز الملف الإلكتروني" FontSize="14" FontWeight="SemiBold"
                                       Foreground="#374151" Margin="0,0,0,8"/>
                            <Border Background="#F9FAFB" BorderBrush="#D1D5DB" BorderThickness="1" CornerRadius="8">
                                <TextBox x:Name="ElectronicFileCodeTextBox" Background="Transparent" BorderThickness="0"
                                         Padding="12" FontSize="14"/>
                            </Border>
                        </StackPanel>

                        <!-- سنة الملف -->
                        <StackPanel x:Name="FileYearPanel" Margin="0,0,0,16" IsEnabled="False">
                            <TextBlock Text="سنة الملف" FontSize="14" FontWeight="SemiBold"
                                       Foreground="#374151" Margin="0,0,0,8"/>
                            <Border Background="#F9FAFB" BorderBrush="#D1D5DB" BorderThickness="1" CornerRadius="8">
                                <TextBox x:Name="FileYearTextBox" Background="Transparent" BorderThickness="0"
                                         Padding="12" FontSize="14" Text="2024"/>
                            </Border>
                        </StackPanel>

                        <!-- محكمة الاستئناف -->
                        <StackPanel x:Name="AppealCourtPanel" Margin="0,0,0,16" IsEnabled="False">
                            <TextBlock Text="محكمة الاستئناف" FontSize="14" FontWeight="SemiBold"
                                       Foreground="#374151" Margin="0,0,0,8"/>
                            <ComboBox x:Name="AppealCourtComboBox" Background="#F9FAFB" BorderBrush="#D1D5DB"
                                      BorderThickness="1" Padding="12" FontSize="14">
                                <ComboBoxItem Content="محكمة الاستئناف بالرباط"/>
                                <ComboBoxItem Content="محكمة الاستئناف بالدار البيضاء"/>
                                <ComboBoxItem Content="محكمة الاستئناف بفاس"/>
                                <ComboBoxItem Content="محكمة الاستئناف بمراكش"/>
                                <ComboBoxItem Content="محكمة الاستئناف بطنجة"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- البحث في المحاكم الابتدائية -->
                        <StackPanel x:Name="SearchPrimaryPanel" Margin="0,0,0,16" IsEnabled="False">
                            <CheckBox x:Name="SearchInPrimaryCourtsCheckBox" Content="البحث في المحاكم الابتدائية"
                                      FontSize="14" FontWeight="SemiBold" Foreground="#374151"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- العمود الأيمن -->
                    <StackPanel Grid.Column="2">
                        <!-- معلومات الأطراف -->
                        <TextBlock Text="👥 معلومات الأطراف" FontSize="18" FontWeight="Bold"
                                   Foreground="#1F2937" Margin="0,0,0,20"/>

                        <!-- الموكل -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="الموكل *" FontSize="14" FontWeight="SemiBold"
                                       Foreground="#374151" Margin="0,0,0,8"/>
                            <Border Background="#F9FAFB" BorderBrush="#D1D5DB" BorderThickness="1" CornerRadius="8">
                                <TextBox x:Name="ClientTextBox" Background="Transparent" BorderThickness="0"
                                         Padding="12" FontSize="14"/>
                            </Border>
                        </StackPanel>

                        <!-- المحامي المكلف -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="المحامي المكلف" FontSize="14" FontWeight="SemiBold"
                                       Foreground="#374151" Margin="0,0,0,8"/>
                            <ComboBox x:Name="AssignedLawyerComboBox" Background="#F9FAFB" BorderBrush="#D1D5DB"
                                      BorderThickness="1" Padding="12" FontSize="14">
                                <ComboBoxItem Content="المحامي محمد العلوي"/>
                                <ComboBoxItem Content="المحامية سعاد الإدريسي"/>
                                <ComboBoxItem Content="المحامي عبد الرحمن الفاسي"/>
                                <ComboBoxItem Content="المحامية أمينة المراكشي"/>
                                <ComboBoxItem Content="المحامي رشيد الطنجاوي"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- معلومات الإجراءات -->
                        <TextBlock Text="⚖️ معلومات الإجراءات" FontSize="18" FontWeight="Bold"
                                   Foreground="#1F2937" Margin="0,20,0,20"/>

                        <!-- نوع الإجراء -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="نوع الإجراء" FontSize="14" FontWeight="SemiBold"
                                       Foreground="#374151" Margin="0,0,0,8"/>
                            <ComboBox x:Name="ProcedureTypeComboBox" Background="#F9FAFB" BorderBrush="#D1D5DB"
                                      BorderThickness="1" Padding="12" FontSize="14">
                                <ComboBoxItem Content="مرافعة"/>
                                <ComboBoxItem Content="تحقيق"/>
                                <ComboBoxItem Content="خبرة"/>
                                <ComboBoxItem Content="وساطة"/>
                                <ComboBoxItem Content="دفاع"/>
                                <ComboBoxItem Content="استئناف"/>
                                <ComboBoxItem Content="تنفيذ"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- القرار -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="القرار" FontSize="14" FontWeight="SemiBold"
                                       Foreground="#374151" Margin="0,0,0,8"/>
                            <Border Background="#F9FAFB" BorderBrush="#D1D5DB" BorderThickness="1" CornerRadius="8">
                                <TextBox x:Name="DecisionTextBox" Background="Transparent" BorderThickness="0"
                                         Padding="12" FontSize="14" Height="60" TextWrapping="Wrap"
                                         AcceptsReturn="True"/>
                            </Border>
                        </StackPanel>

                        <!-- تاريخ الجلسة المقبلة -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="تاريخ الجلسة المقبلة" FontSize="14" FontWeight="SemiBold"
                                       Foreground="#374151" Margin="0,0,0,8"/>
                            <Border Background="#F9FAFB" BorderBrush="#D1D5DB" BorderThickness="1" CornerRadius="8">
                                <DatePicker x:Name="NextSessionDatePicker" Background="Transparent" BorderThickness="0"
                                            Padding="12" FontSize="14"/>
                            </Border>
                        </StackPanel>

                        <!-- معلومات إضافية -->
                        <TextBlock Text="📊 معلومات إضافية" FontSize="18" FontWeight="Bold"
                                   Foreground="#1F2937" Margin="0,20,0,20"/>

                        <!-- الحالة -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="حالة الملف" FontSize="14" FontWeight="SemiBold"
                                       Foreground="#374151" Margin="0,0,0,8"/>
                            <ComboBox x:Name="StatusComboBox" Background="#F9FAFB" BorderBrush="#D1D5DB"
                                      BorderThickness="1" Padding="12" FontSize="14">
                                <ComboBoxItem Content="نشط" IsSelected="True"/>
                                <ComboBoxItem Content="في الجلسات"/>
                                <ComboBoxItem Content="مؤرشف"/>
                                <ComboBoxItem Content="مغلق"/>
                                <ComboBoxItem Content="معلق"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- الأولوية -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="أولوية الملف" FontSize="14" FontWeight="SemiBold"
                                       Foreground="#374151" Margin="0,0,0,8"/>
                            <ComboBox x:Name="PriorityComboBox" Background="#F9FAFB" BorderBrush="#D1D5DB"
                                      BorderThickness="1" Padding="12" FontSize="14">
                                <ComboBoxItem Content="عالية"/>
                                <ComboBoxItem Content="متوسطة" IsSelected="True"/>
                                <ComboBoxItem Content="منخفضة"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- القيمة المقدرة -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="القيمة المقدرة (درهم)" FontSize="14" FontWeight="SemiBold"
                                       Foreground="#374151" Margin="0,0,0,8"/>
                            <Border Background="#F9FAFB" BorderBrush="#D1D5DB" BorderThickness="1" CornerRadius="8">
                                <TextBox x:Name="EstimatedValueTextBox" Background="Transparent" BorderThickness="0"
                                         Padding="12" FontSize="14" Text="0"/>
                            </Border>
                        </StackPanel>

                        <!-- ملاحظات -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="ملاحظات" FontSize="14" FontWeight="SemiBold"
                                       Foreground="#374151" Margin="0,0,0,8"/>
                            <Border Background="#F9FAFB" BorderBrush="#D1D5DB" BorderThickness="1" CornerRadius="8">
                                <TextBox x:Name="NotesTextBox" Background="Transparent" BorderThickness="0"
                                         Padding="12" FontSize="14" Height="100" TextWrapping="Wrap"
                                         AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
                            </Border>
                        </StackPanel>

                        <!-- أرشفة الملف -->
                        <StackPanel Margin="0,0,0,16">
                            <CheckBox x:Name="IsArchivedCheckBox" Content="ملف مؤرشف"
                                      FontSize="14" FontWeight="SemiBold" Foreground="#374151"/>
                        </StackPanel>
                    </StackPanel>
                </Grid>
            </Border>
        </ScrollViewer>

        <!-- أزرار الإجراءات -->
        <Border Grid.Row="2" Background="White" CornerRadius="16" Padding="24" Margin="0,24,0,0">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="15" ShadowDepth="0" Opacity="0.3"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- زر التحقق من البيانات -->
                <Button x:Name="ValidateButton" Grid.Column="0" Content="🔍 التحقق من البيانات"
                        Background="#8B5CF6" Foreground="White" BorderThickness="0"
                        Padding="16,12" FontSize="14" FontWeight="SemiBold"
                        Cursor="Hand" Click="Validate_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" CornerRadius="8"
                                                Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#7C3AED"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>

                <!-- أزرار الحفظ والإلغاء -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button x:Name="CancelButton" Content="إلغاء"
                            Background="#6B7280" Foreground="White" BorderThickness="0"
                            Padding="24,12" FontSize="14" FontWeight="SemiBold"
                            Cursor="Hand" Margin="0,0,12,0" Click="Cancel_Click">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" CornerRadius="8"
                                                    Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#4B5563"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>

                    <Button x:Name="SaveButton" Content="💾 حفظ الملف"
                            Background="#10B981" Foreground="White" BorderThickness="0"
                            Padding="24,12" FontSize="14" FontWeight="SemiBold"
                            Cursor="Hand" Click="Save_Click">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" CornerRadius="8"
                                                    Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#059669"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>