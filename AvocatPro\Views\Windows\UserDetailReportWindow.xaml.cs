using System;
using System.Windows;
using AvocatPro.Models;

namespace AvocatPro.Views.Windows
{
    public partial class UserDetailReportWindow : Window
    {
        private readonly User _currentUser;
        private readonly int _userId;

        public UserDetailReportWindow(User currentUser, int userId)
        {
            // محاكاة نافذة تقرير تفصيلي للمستخدم
            _currentUser = currentUser;
            _userId = userId;
            
            // إنشاء نافذة بسيطة
            Title = "تقرير تفصيلي للمستخدم";
            Width = 700;
            Height = 500;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;
            
            var button = new System.Windows.Controls.Button
            {
                Content = "إغلاق",
                Width = 100,
                Height = 30,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };
            button.Click += (s, e) => Close();
            
            Content = button;
        }
    }
}
