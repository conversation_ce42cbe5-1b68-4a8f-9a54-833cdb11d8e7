<Window x:Class="AvocatPro.Views.Windows.AddAppointmentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة موعد جديد" 
        Height="750" Width="950"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">

    <Window.Resources>
        <!-- أنماط النموذج -->
        <Style x:Key="FormLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#333"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>

        <Style x:Key="FormTextBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Style.Triggers>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="BorderBrush" Value="#1976D2"/>
                    <Setter Property="BorderThickness" Value="2"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="FormComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="FormDatePickerStyle" TargetType="DatePicker">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1976D2"/>
            <Setter Property="Margin" Value="0,20,0,15"/>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#1976D2"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1565C0"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#F5F5F5"/>
            <Setter Property="Foreground" Value="#333"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#E0E0E0"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="PriorityButtonStyle" TargetType="RadioButton">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="RadioButton">
                        <Border Name="border" Background="{TemplateBinding Background}" 
                               BorderBrush="#DDD" BorderThickness="1" 
                               CornerRadius="5" Padding="10,5" Margin="2">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsChecked" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="#1976D2"/>
                                <Setter TargetName="border" Property="BorderThickness" Value="2"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#F0F0F0"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- رأس النافذة -->
        <Border Grid.Row="0" Background="#1976D2" Padding="20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📅" FontSize="24" Foreground="White" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="إضافة موعد جديد" FontSize="20" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="جدولة موعد مع تنبيهات ذكية ومتابعة احترافية" FontSize="12" Foreground="#E3F2FD"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- محتوى النموذج -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="30">
            <StackPanel>
                <!-- المعلومات الأساسية -->
                <TextBlock Text="📋 المعلومات الأساسية" Style="{StaticResource SectionHeaderStyle}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="عنوان الموعد *" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="TitleTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="عنوان واضح ومختصر للموعد"/>

                        <TextBlock Text="نوع الموعد *" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="TypeComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="💼 استشارة" Tag="Consultation" IsSelected="True"/>
                            <ComboBoxItem Content="🤝 اجتماع" Tag="Meeting"/>
                            <ComboBoxItem Content="📞 مكالمة هاتفية" Tag="PhoneCall"/>
                            <ComboBoxItem Content="🚗 زيارة ميدانية" Tag="FieldVisit"/>
                            <ComboBoxItem Content="📝 توقيع عقد" Tag="ContractSigning"/>
                            <ComboBoxItem Content="📋 متابعة قضية" Tag="CaseFollowUp"/>
                            <ComboBoxItem Content="👤 موعد شخصي" Tag="Personal"/>
                            <ComboBoxItem Content="🎓 تدريب" Tag="Training"/>
                        </ComboBox>

                        <TextBlock Text="الموكل المرتبط" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="ClientComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="بدون موكل" Tag="0"/>
                            <ComboBoxItem Content="أحمد محمد علي السعيد" Tag="1"/>
                            <ComboBoxItem Content="فاطمة أحمد سالم" Tag="2"/>
                            <ComboBoxItem Content="مريم عبدالله الزهراني" Tag="3"/>
                            <ComboBoxItem Content="شركة النور للتجارة والاستثمار" Tag="4"/>
                            <ComboBoxItem Content="جمعية البر الخيرية" Tag="5"/>
                        </ComboBox>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="تاريخ الموعد *" Style="{StaticResource FormLabelStyle}"/>
                        <DatePicker Name="AppointmentDatePicker" Style="{StaticResource FormDatePickerStyle}"/>

                        <TextBlock Text="وقت البداية *" Style="{StaticResource FormLabelStyle}"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <ComboBox Name="StartHourComboBox" Grid.Column="0" Style="{StaticResource FormComboBoxStyle}">
                                <!-- سيتم ملؤها بالساعات -->
                            </ComboBox>
                            <ComboBox Name="StartMinuteComboBox" Grid.Column="2" Style="{StaticResource FormComboBoxStyle}">
                                <!-- سيتم ملؤها بالدقائق -->
                            </ComboBox>
                        </Grid>

                        <TextBlock Text="وقت النهاية *" Style="{StaticResource FormLabelStyle}"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <ComboBox Name="EndHourComboBox" Grid.Column="0" Style="{StaticResource FormComboBoxStyle}">
                                <!-- سيتم ملؤها بالساعات -->
                            </ComboBox>
                            <ComboBox Name="EndMinuteComboBox" Grid.Column="2" Style="{StaticResource FormComboBoxStyle}">
                                <!-- سيتم ملؤها بالدقائق -->
                            </ComboBox>
                        </Grid>
                    </StackPanel>
                </Grid>

                <!-- الأولوية والحالة -->
                <TextBlock Text="⭐ الأولوية والحالة" Style="{StaticResource SectionHeaderStyle}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="أولوية الموعد" Style="{StaticResource FormLabelStyle}"/>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <RadioButton Name="LowPriorityRadio" Content="🟢 منخفضة" Tag="Low" 
                                        Style="{StaticResource PriorityButtonStyle}" Background="#E8F5E8"/>
                            <RadioButton Name="MediumPriorityRadio" Content="🟡 متوسطة" Tag="Medium" 
                                        Style="{StaticResource PriorityButtonStyle}" Background="#FFF8E1" IsChecked="True"/>
                            <RadioButton Name="HighPriorityRadio" Content="🟠 عالية" Tag="High" 
                                        Style="{StaticResource PriorityButtonStyle}" Background="#FFF3E0"/>
                            <RadioButton Name="UrgentPriorityRadio" Content="🔴 عاجلة" Tag="Urgent" 
                                        Style="{StaticResource PriorityButtonStyle}" Background="#FFEBEE"/>
                        </StackPanel>

                        <TextBlock Text="القضية المرتبطة" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="CaseComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="بدون قضية" Tag="0"/>
                            <ComboBoxItem Content="دعوى مطالبة مالية - CASE-20241201-001" Tag="1"/>
                            <ComboBoxItem Content="قضية عمالية - فصل تعسفي - CASE-20241215-002" Tag="2"/>
                            <ComboBoxItem Content="قضية تجارية - نزاع شراكة - CASE-20241101-003" Tag="3"/>
                            <ComboBoxItem Content="قضية أسرة - نفقة - CASE-20241120-004" Tag="4"/>
                            <ComboBoxItem Content="قضية عقارية - منازعة ملكية - CASE-20241210-005" Tag="5"/>
                            <ComboBoxItem Content="قضية جنائية - اختلاس - CASE-20241205-006" Tag="6"/>
                        </ComboBox>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="حالة الموعد" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="StatusComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="📅 مجدول" Tag="Scheduled" IsSelected="True"/>
                            <ComboBoxItem Content="✅ مؤكد" Tag="Confirmed"/>
                            <ComboBoxItem Content="⏳ جاري" Tag="InProgress"/>
                            <ComboBoxItem Content="✔️ مكتمل" Tag="Completed"/>
                            <ComboBoxItem Content="⏰ مؤجل" Tag="Postponed"/>
                            <ComboBoxItem Content="❌ ملغي" Tag="Cancelled"/>
                        </ComboBox>

                        <TextBlock Text="المكان" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="LocationTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="مكان انعقاد الموعد"/>
                    </StackPanel>
                </Grid>

                <!-- التفاصيل والملاحظات -->
                <TextBlock Text="📝 التفاصيل والملاحظات" Style="{StaticResource SectionHeaderStyle}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="وصف الموعد" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="DescriptionTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Height="80" TextWrapping="Wrap" AcceptsReturn="True"
                                Text="" ToolTip="وصف تفصيلي للموعد"/>

                        <TextBlock Text="الحضور المطلوب" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="RequiredAttendeesTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Height="60" TextWrapping="Wrap" AcceptsReturn="True"
                                Text="" ToolTip="قائمة الأشخاص المطلوب حضورهم"/>

                        <TextBlock Text="التكلفة (ريال)" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="CostTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="تكلفة الموعد"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="ملاحظات الموعد" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="NotesTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Height="80" TextWrapping="Wrap" AcceptsReturn="True"
                                Text="" ToolTip="ملاحظات إضافية"/>

                        <TextBlock Text="رقم الهاتف للتواصل" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="ContactPhoneTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="رقم هاتف للتواصل"/>

                        <TextBlock Text="البريد الإلكتروني" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="ContactEmailTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="بريد إلكتروني للتواصل"/>
                    </StackPanel>
                </Grid>

                <!-- التنبيهات والتكرار -->
                <TextBlock Text="🔔 التنبيهات والتكرار" Style="{StaticResource SectionHeaderStyle}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="وقت التنبيه" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="NotificationComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="5 دقائق قبل الموعد" Tag="5"/>
                            <ComboBoxItem Content="10 دقائق قبل الموعد" Tag="10"/>
                            <ComboBoxItem Content="15 دقيقة قبل الموعد" Tag="15"/>
                            <ComboBoxItem Content="30 دقيقة قبل الموعد" Tag="30" IsSelected="True"/>
                            <ComboBoxItem Content="ساعة قبل الموعد" Tag="60"/>
                            <ComboBoxItem Content="ساعتين قبل الموعد" Tag="120"/>
                            <ComboBoxItem Content="يوم قبل الموعد" Tag="1440"/>
                        </ComboBox>

                        <CheckBox Name="IsPaidCheckBox" Content="تم الدفع" FontSize="14" Margin="0,10,0,15"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <CheckBox Name="IsRecurringCheckBox" Content="موعد متكرر" FontSize="14" 
                                 Checked="IsRecurringCheckBox_Checked" Unchecked="IsRecurringCheckBox_Unchecked"/>
                        
                        <ComboBox Name="RecurrencePatternComboBox" Style="{StaticResource FormComboBoxStyle}"
                                 Visibility="Collapsed" Margin="0,10,0,15">
                            <ComboBoxItem Content="يومياً" Tag="Daily"/>
                            <ComboBoxItem Content="أسبوعياً" Tag="Weekly"/>
                            <ComboBoxItem Content="شهرياً" Tag="Monthly"/>
                            <ComboBoxItem Content="سنوياً" Tag="Yearly"/>
                        </ComboBox>
                    </StackPanel>
                </Grid>
            </StackPanel>
        </ScrollViewer>

        <!-- أزرار الإجراءات -->
        <Border Grid.Row="2" Background="#F8F9FA" Padding="20" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Name="SaveButton" Style="{StaticResource PrimaryButtonStyle}" Click="SaveButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="💾" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ الموعد"/>
                    </StackPanel>
                </Button>
                
                <Button Name="SaveAndNewButton" Style="{StaticResource PrimaryButtonStyle}" 
                       Background="#4CAF50" Click="SaveAndNewButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="➕" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ وإضافة آخر"/>
                    </StackPanel>
                </Button>
                
                <Button Name="SaveAndNotifyButton" Style="{StaticResource PrimaryButtonStyle}" 
                       Background="#FF9800" Click="SaveAndNotifyButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🔔" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ وإرسال تنبيه"/>
                    </StackPanel>
                </Button>
                
                <Button Name="CancelButton" Style="{StaticResource SecondaryButtonStyle}" Click="CancelButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="❌" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="إلغاء"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
