<Page x:Class="AvocatPro.Views.Pages.ModernFinancePage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="الإدارة المالية"
      Background="#F8FAFC"
      FlowDirection="RightToLeft">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- عنوان الصفحة -->
        <Border Grid.Row="0" Background="White" Padding="25" Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="💰" FontSize="28" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="الإدارة المالية" FontSize="24" FontWeight="Bold" 
                               Foreground="#1F2937" Margin="0,0,0,5"/>
                    <TextBlock Text="إدارة الحسابات والمعاملات المالية للمكتب" FontSize="14" 
                               Foreground="#6B7280"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- محتوى الصفحة -->
        <Border Grid.Row="1" Background="White" Padding="40">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <TextBlock Text="💰" FontSize="80" HorizontalAlignment="Center" 
                           Foreground="#E5E7EB" Margin="0,0,0,20"/>
                <TextBlock Text="الإدارة المالية" FontSize="28" FontWeight="Bold" 
                           Foreground="#1F2937" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                <TextBlock Text="هذا القسم قيد التطوير" FontSize="16" 
                           Foreground="#6B7280" HorizontalAlignment="Center" Margin="0,0,0,20"/>
                <TextBlock Text="سيتم إضافة المميزات التالية قريباً:" FontSize="14" 
                           Foreground="#9CA3AF" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                
                <StackPanel Margin="0,10,0,0">
                    <TextBlock Text="• إدارة الإيرادات والمصروفات" FontSize="14" 
                               Foreground="#6B7280" Margin="0,5,0,0"/>
                    <TextBlock Text="• تتبع أتعاب المحاماة" FontSize="14" 
                               Foreground="#6B7280" Margin="0,5,0,0"/>
                    <TextBlock Text="• إدارة الفواتير والمدفوعات" FontSize="14" 
                               Foreground="#6B7280" Margin="0,5,0,0"/>
                    <TextBlock Text="• تقارير مالية شاملة" FontSize="14" 
                               Foreground="#6B7280" Margin="0,5,0,0"/>
                    <TextBlock Text="• ربط مع البنوك والمحاسبة" FontSize="14" 
                               Foreground="#6B7280" Margin="0,5,0,0"/>
                </StackPanel>
            </StackPanel>
        </Border>
    </Grid>
</Page>
