<Page x:Class="AvocatPro.Views.Pages.ReportsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="التقارير والإحصائيات"
      FlowDirection="RightToLeft">

    <Page.Resources>
        <!-- أنماط الصفحة -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1976D2"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>

        <Style x:Key="KPICardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="4" Opacity="0.3" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Effect">
                        <Setter.Value>
                            <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="6" Opacity="0.4" BlurRadius="12"/>
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="ChartCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="25"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="4" Opacity="0.3" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#1976D2"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               CornerRadius="6"
                               Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1565C0"/>
                                <Setter Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="FilterComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Padding" Value="12,10"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Margin" Value="5,0"/>
        </Style>

        <Style x:Key="TabHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
        </Style>

        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1976D2"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="AlertCardStyle" TargetType="Border">
            <Setter Property="Background" Value="#FFF3E0"/>
            <Setter Property="BorderBrush" Value="#FF9800"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="5"/>
        </Style>
    </Page.Resources>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- رأس الصفحة -->
        <Border Grid.Row="0" Background="White" Padding="30,20" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="📊" FontSize="32" Margin="0,0,15,0"/>
                    <StackPanel>
                        <TextBlock Text="التقارير والإحصائيات المتقدمة" Style="{StaticResource HeaderTextStyle}"/>
                        <TextBlock Text="لوحة معلومات شاملة مع تحليلات ذكية ومؤشرات أداء متقدمة" FontSize="14" Foreground="#666"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <ComboBox Name="PeriodFilterComboBox" Style="{StaticResource FilterComboBoxStyle}"
                             SelectionChanged="PeriodFilterComboBox_SelectionChanged">
                        <ComboBoxItem Content="📅 هذا الشهر" Tag="ThisMonth" IsSelected="True"/>
                        <ComboBoxItem Content="📅 الشهر الماضي" Tag="LastMonth"/>
                        <ComboBoxItem Content="📅 آخر 3 أشهر" Tag="Last3Months"/>
                        <ComboBoxItem Content="📅 آخر 6 أشهر" Tag="Last6Months"/>
                        <ComboBoxItem Content="📅 هذا العام" Tag="ThisYear"/>
                        <ComboBoxItem Content="📅 العام الماضي" Tag="LastYear"/>
                        <ComboBoxItem Content="📅 فترة مخصصة" Tag="Custom"/>
                    </ComboBox>
                    
                    <Button Name="RefreshButton" Style="{StaticResource ActionButtonStyle}"
                           Background="#4CAF50" Click="RefreshButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔄" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث"/>
                        </StackPanel>
                    </Button>

                    <Button Name="ExportButton" Style="{StaticResource ActionButtonStyle}"
                           Background="#FF9800" Click="ExportButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📤" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تصدير"/>
                        </StackPanel>
                    </Button>

                    <Button Name="CustomReportButton" Style="{StaticResource ActionButtonStyle}"
                           Background="#9C27B0" Click="CustomReportButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="⚙️" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تقرير مخصص"/>
                        </StackPanel>
                    </Button>

                    <Button Name="PrintDashboardButton" Style="{StaticResource ActionButtonStyle}"
                           Background="#795548" Click="PrintDashboardButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🖨️" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="طباعة"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- مؤشرات الأداء الرئيسية -->
        <Grid Grid.Row="1" Margin="20,0,20,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- إجمالي الإيرادات -->
            <Border Grid.Column="0" Style="{StaticResource KPICardStyle}">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                        <TextBlock Text="💰" FontSize="24" Margin="0,0,10,0"/>
                        <TextBlock Text="إجمالي الإيرادات" FontSize="12" Foreground="#666" VerticalAlignment="Center"/>
                    </StackPanel>
                    <TextBlock Name="TotalRevenuesKPI" Text="0 ريال" FontSize="20" FontWeight="Bold" Foreground="#4CAF50"/>
                    <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                        <TextBlock Name="RevenuesTrendIcon" Text="📈" FontSize="12" Margin="0,0,5,0"/>
                        <TextBlock Name="RevenuesTrendText" Text="+0%" FontSize="11" Foreground="#4CAF50"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- إجمالي المصاريف -->
            <Border Grid.Column="1" Style="{StaticResource KPICardStyle}">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                        <TextBlock Text="💸" FontSize="24" Margin="0,0,10,0"/>
                        <TextBlock Text="إجمالي المصاريف" FontSize="12" Foreground="#666" VerticalAlignment="Center"/>
                    </StackPanel>
                    <TextBlock Name="TotalExpensesKPI" Text="0 ريال" FontSize="20" FontWeight="Bold" Foreground="#F44336"/>
                    <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                        <TextBlock Name="ExpensesTrendIcon" Text="📉" FontSize="12" Margin="0,0,5,0"/>
                        <TextBlock Name="ExpensesTrendText" Text="-0%" FontSize="11" Foreground="#4CAF50"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- صافي الربح -->
            <Border Grid.Column="2" Style="{StaticResource KPICardStyle}">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                        <TextBlock Text="📈" FontSize="24" Margin="0,0,10,0"/>
                        <TextBlock Text="صافي الربح" FontSize="12" Foreground="#666" VerticalAlignment="Center"/>
                    </StackPanel>
                    <TextBlock Name="NetProfitKPI" Text="0 ريال" FontSize="20" FontWeight="Bold" Foreground="#2196F3"/>
                    <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                        <TextBlock Name="ProfitTrendIcon" Text="📈" FontSize="12" Margin="0,0,5,0"/>
                        <TextBlock Name="ProfitTrendText" Text="+0%" FontSize="11" Foreground="#4CAF50"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- عدد القضايا -->
            <Border Grid.Column="3" Style="{StaticResource KPICardStyle}">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                        <TextBlock Text="⚖️" FontSize="24" Margin="0,0,10,0"/>
                        <TextBlock Text="إجمالي القضايا" FontSize="12" Foreground="#666" VerticalAlignment="Center"/>
                    </StackPanel>
                    <TextBlock Name="TotalCasesKPI" Text="0" FontSize="20" FontWeight="Bold" Foreground="#9C27B0"/>
                    <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                        <TextBlock Name="CasesTrendIcon" Text="📈" FontSize="12" Margin="0,0,5,0"/>
                        <TextBlock Name="CasesTrendText" Text="+0%" FontSize="11" Foreground="#4CAF50"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- عدد الموكلين -->
            <Border Grid.Column="4" Style="{StaticResource KPICardStyle}">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                        <TextBlock Text="👥" FontSize="24" Margin="0,0,10,0"/>
                        <TextBlock Text="إجمالي الموكلين" FontSize="12" Foreground="#666" VerticalAlignment="Center"/>
                    </StackPanel>
                    <TextBlock Name="TotalClientsKPI" Text="0" FontSize="20" FontWeight="Bold" Foreground="#FF9800"/>
                    <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                        <TextBlock Name="ClientsTrendIcon" Text="📈" FontSize="12" Margin="0,0,5,0"/>
                        <TextBlock Name="ClientsTrendText" Text="+0%" FontSize="11" Foreground="#4CAF50"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- معدل النجاح -->
            <Border Grid.Column="5" Style="{StaticResource KPICardStyle}">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                        <TextBlock Text="🏆" FontSize="24" Margin="0,0,10,0"/>
                        <TextBlock Text="معدل النجاح" FontSize="12" Foreground="#666" VerticalAlignment="Center"/>
                    </StackPanel>
                    <TextBlock Name="SuccessRateKPI" Text="0%" FontSize="20" FontWeight="Bold" Foreground="#4CAF50"/>
                    <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                        <TextBlock Name="SuccessTrendIcon" Text="📈" FontSize="12" Margin="0,0,5,0"/>
                        <TextBlock Name="SuccessTrendText" Text="+0%" FontSize="11" Foreground="#4CAF50"/>
                    </StackPanel>
                </StackPanel>
            </Border>
        </Grid>

        <!-- المحتوى الرئيسي -->
        <TabControl Grid.Row="2" Margin="20,0,20,20" Background="White">
            <!-- تبويب لوحة المعلومات -->
            <TabItem>
                <TabItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📊" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="لوحة المعلومات" Style="{StaticResource TabHeaderStyle}"/>
                    </StackPanel>
                </TabItem.Header>
                
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- الرسوم البيانية الرئيسية -->
                        <StackPanel Grid.Column="0" Margin="0,0,10,0">
                            <!-- رسم بياني للاتجاهات المالية -->
                            <Border Style="{StaticResource ChartCardStyle}">
                                <StackPanel>
                                    <TextBlock Text="📈 الاتجاهات المالية الشهرية" Style="{StaticResource SectionHeaderStyle}"/>
                                    <ScrollViewer Name="FinancialTrendsChart" Height="300" VerticalScrollBarVisibility="Auto">
                                        <StackPanel Name="FinancialTrendsChartPanel"/>
                                    </ScrollViewer>
                                </StackPanel>
                            </Border>

                            <!-- رسم بياني لأداء القضايا -->
                            <Border Style="{StaticResource ChartCardStyle}">
                                <StackPanel>
                                    <TextBlock Text="⚖️ أداء القضايا" Style="{StaticResource SectionHeaderStyle}"/>
                                    <ScrollViewer Name="CasesPerformanceChart" Height="250" VerticalScrollBarVisibility="Auto">
                                        <StackPanel Name="CasesPerformanceChartPanel"/>
                                    </ScrollViewer>
                                </StackPanel>
                            </Border>

                            <!-- رسم بياني لتوزيع الموكلين -->
                            <Border Style="{StaticResource ChartCardStyle}">
                                <StackPanel>
                                    <TextBlock Text="👥 توزيع الموكلين" Style="{StaticResource SectionHeaderStyle}"/>
                                    <ScrollViewer Name="ClientsDistributionChart" Height="200" VerticalScrollBarVisibility="Auto">
                                        <StackPanel Name="ClientsDistributionChartPanel"/>
                                    </ScrollViewer>
                                </StackPanel>
                            </Border>
                        </StackPanel>

                        <!-- الشريط الجانبي -->
                        <StackPanel Grid.Column="1" Margin="10,0,0,0">
                            <!-- التنبيهات والإشعارات -->
                            <Border Style="{StaticResource ChartCardStyle}">
                                <StackPanel>
                                    <TextBlock Text="🔔 التنبيهات والإشعارات" Style="{StaticResource SectionHeaderStyle}"/>
                                    <ScrollViewer Name="AlertsScrollViewer" Height="200" VerticalScrollBarVisibility="Auto">
                                        <StackPanel Name="AlertsPanel"/>
                                    </ScrollViewer>
                                </StackPanel>
                            </Border>

                            <!-- إحصائيات سريعة -->
                            <Border Style="{StaticResource ChartCardStyle}">
                                <StackPanel>
                                    <TextBlock Text="⚡ إحصائيات سريعة" Style="{StaticResource SectionHeaderStyle}"/>
                                    <StackPanel Name="QuickStatsPanel"/>
                                </StackPanel>
                            </Border>

                            <!-- مؤشرات الأداء -->
                            <Border Style="{StaticResource ChartCardStyle}">
                                <StackPanel>
                                    <TextBlock Text="🎯 مؤشرات الأداء" Style="{StaticResource SectionHeaderStyle}"/>
                                    <StackPanel Name="PerformanceIndicatorsPanel"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </Grid>
                </ScrollViewer>
            </TabItem>

            <!-- تبويب التقارير التفصيلية -->
            <TabItem>
                <TabItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📄" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="التقارير التفصيلية" Style="{StaticResource TabHeaderStyle}"/>
                    </StackPanel>
                </TabItem.Header>
                
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
                    <StackPanel Name="DetailedReportsPanel">
                        <!-- سيتم ملؤها ديناميكياً -->
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- تبويب المقارنات والتحليلات -->
            <TabItem>
                <TabItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="⚖️" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="المقارنات والتحليلات" Style="{StaticResource TabHeaderStyle}"/>
                    </StackPanel>
                </TabItem.Header>
                
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
                    <StackPanel Name="ComparisonsPanel">
                        <!-- سيتم ملؤها ديناميكياً -->
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- تبويب التوقعات والتنبؤات -->
            <TabItem>
                <TabItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🔮" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="التوقعات والتنبؤات" Style="{StaticResource TabHeaderStyle}"/>
                    </StackPanel>
                </TabItem.Header>
                
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
                    <StackPanel Name="ForecastsPanel">
                        <!-- سيتم ملؤها ديناميكياً -->
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>
    </Grid>
</Page>
