using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace AvocatPro.Views.Pages
{
    /// <summary>
    /// صفحة إدارة الملفات الحديثة
    /// </summary>
    public partial class ModernFilesPage : Page
    {
        private ObservableCollection<FileViewModel> _allFiles = new ObservableCollection<FileViewModel>();
        private ObservableCollection<FileViewModel> _filteredFiles = new ObservableCollection<FileViewModel>();

        public ModernFilesPage()
        {
            try
            {
                InitializeComponent();
                InitializeData();
                LoadFiles();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة صفحة الملفات: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تهيئة البيانات الوهمية
        /// </summary>
        private void InitializeData()
        {
            _allFiles = new ObservableCollection<FileViewModel>();
            _filteredFiles = new ObservableCollection<FileViewModel>();

            // إضافة بيانات وهمية للملفات
            var sampleFiles = new List<FileViewModel>
            {
                new FileViewModel
                {
                    Id = 1,
                    FileNumber = "2024/001",
                    Title = "قضية نزاع تجاري - شركة التجارة المغربية",
                    CaseType = "تجاري",
                    TypeColor = "#10B981",
                    ClientName = "شركة التجارة المغربية",
                    Court = "المحكمة التجارية بالرباط",
                    Status = "نشط",
                    StatusColor = "#3B82F6",
                    CreatedDate = "2024-01-15"
                },
                new FileViewModel
                {
                    Id = 2,
                    FileNumber = "2024/002",
                    Title = "قضية طلاق وحضانة",
                    CaseType = "أسري",
                    TypeColor = "#F59E0B",
                    ClientName = "أحمد محمد الحسن",
                    Court = "محكمة الأسرة بالدار البيضاء",
                    Status = "نشط",
                    StatusColor = "#3B82F6",
                    CreatedDate = "2024-02-10"
                },
                new FileViewModel
                {
                    Id = 3,
                    FileNumber = "2024/003",
                    Title = "قضية اعتداء وضرب",
                    CaseType = "جنائي",
                    TypeColor = "#EF4444",
                    ClientName = "فاطمة الزهراء",
                    Court = "المحكمة الابتدائية بفاس",
                    Status = "مؤرشف",
                    StatusColor = "#6B7280",
                    CreatedDate = "2024-01-20"
                },
                new FileViewModel
                {
                    Id = 4,
                    FileNumber = "2024/004",
                    Title = "نزاع عمالي - فصل تعسفي",
                    CaseType = "عمالي",
                    TypeColor = "#8B5CF6",
                    ClientName = "يوسف بن علي",
                    Court = "محكمة الاستئناف بطنجة",
                    Status = "نشط",
                    StatusColor = "#3B82F6",
                    CreatedDate = "2024-03-05"
                },
                new FileViewModel
                {
                    Id = 5,
                    FileNumber = "2024/005",
                    Title = "قضية ميراث وتركة",
                    CaseType = "مدني",
                    TypeColor = "#06B6D4",
                    ClientName = "خديجة المرابط",
                    Court = "المحكمة الابتدائية بأكادير",
                    Status = "مغلق",
                    StatusColor = "#10B981",
                    CreatedDate = "2024-02-28"
                },
                new FileViewModel
                {
                    Id = 6,
                    FileNumber = "2024/006",
                    Title = "عقد شراكة تجارية",
                    CaseType = "تجاري",
                    TypeColor = "#10B981",
                    ClientName = "مؤسسة البناء الحديث",
                    Court = "المحكمة التجارية بمراكش",
                    Status = "نشط",
                    StatusColor = "#3B82F6",
                    CreatedDate = "2024-03-12"
                },
                new FileViewModel
                {
                    Id = 7,
                    FileNumber = "2024/007",
                    Title = "قضية احتيال مالي",
                    CaseType = "جنائي",
                    TypeColor = "#EF4444",
                    ClientName = "شركة التكنولوجيا المتقدمة",
                    Court = "محكمة الجنايات بالرباط",
                    Status = "نشط",
                    StatusColor = "#3B82F6",
                    CreatedDate = "2024-03-18"
                },
                new FileViewModel
                {
                    Id = 8,
                    FileNumber = "2024/008",
                    Title = "نزاع حول ملكية عقارية",
                    CaseType = "مدني",
                    TypeColor = "#06B6D4",
                    ClientName = "مجموعة الاستثمار الذهبي",
                    Court = "المحكمة الابتدائية بالدار البيضاء",
                    Status = "مؤرشف",
                    StatusColor = "#6B7280",
                    CreatedDate = "2024-01-30"
                }
            };

            foreach (var file in sampleFiles)
            {
                _allFiles.Add(file);
                _filteredFiles.Add(file);
            }
        }

        /// <summary>
        /// تحميل قائمة الملفات
        /// </summary>
        private void LoadFiles()
        {
            FilesItemsControl.ItemsSource = _filteredFiles;
            UpdateFilesCount();
        }

        /// <summary>
        /// تحديث عدد الملفات
        /// </summary>
        private void UpdateFilesCount()
        {
            FilesCountLabel.Text = $"إجمالي الملفات: {_filteredFiles.Count}";
        }

        /// <summary>
        /// معالج البحث في النص
        /// </summary>
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterFiles();
        }

        /// <summary>
        /// معالج تصفية حسب الحالة
        /// </summary>
        private void StatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            FilterFiles();
        }

        /// <summary>
        /// معالج تصفية حسب النوع
        /// </summary>
        private void TypeFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            FilterFiles();
        }

        /// <summary>
        /// تصفية الملفات
        /// </summary>
        private void FilterFiles()
        {
            var searchText = SearchTextBox?.Text?.ToLower() ?? "";
            var selectedStatus = (StatusFilterComboBox?.SelectedItem as ComboBoxItem)?.Content?.ToString();
            var selectedType = (TypeFilterComboBox?.SelectedItem as ComboBoxItem)?.Content?.ToString();

            _filteredFiles.Clear();

            var filtered = _allFiles.Where(file =>
            {
                // تصفية النص
                var matchesSearch = string.IsNullOrEmpty(searchText) ||
                                   file.Title.ToLower().Contains(searchText) ||
                                   file.FileNumber.ToLower().Contains(searchText) ||
                                   file.ClientName.ToLower().Contains(searchText);

                // تصفية الحالة
                var matchesStatus = selectedStatus == "جميع الحالات" ||
                                   selectedStatus == file.Status;

                // تصفية النوع
                var matchesType = selectedType == "جميع الأنواع" ||
                                 selectedType == file.CaseType;

                return matchesSearch && matchesStatus && matchesType;
            });

            foreach (var file in filtered)
            {
                _filteredFiles.Add(file);
            }

            UpdateFilesCount();
        }

        /// <summary>
        /// إضافة ملف جديد
        /// </summary>
        private void AddFile_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة إضافة ملف جديد", "إضافة ملف", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// عرض تفاصيل الملف
        /// </summary>
        private void ViewFile_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var fileId = (int)button.Tag;
            MessageBox.Show($"عرض تفاصيل الملف رقم: {fileId}", "تفاصيل الملف", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// تعديل الملف
        /// </summary>
        private void EditFile_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var fileId = (int)button.Tag;
            MessageBox.Show($"تعديل الملف رقم: {fileId}", "تعديل الملف", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// أرشفة الملف
        /// </summary>
        private void ArchiveFile_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var fileId = (int)button.Tag;
            
            var result = MessageBox.Show($"هل أنت متأكد من أرشفة الملف رقم {fileId}؟", 
                                        "تأكيد الأرشفة", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                var fileToArchive = _allFiles.FirstOrDefault(f => f.Id == fileId);
                if (fileToArchive != null)
                {
                    fileToArchive.Status = "مؤرشف";
                    fileToArchive.StatusColor = "#6B7280";
                    FilterFiles();
                    MessageBox.Show("تم أرشفة الملف بنجاح", "أرشفة الملف", 
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        /// <summary>
        /// تصدير البيانات
        /// </summary>
        private void Export_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تصدير قائمة الملفات إلى ملف Excel", "تصدير البيانات", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// الصفحة السابقة
        /// </summary>
        private void PreviousPage_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("الانتقال إلى الصفحة السابقة", "التنقل", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// الصفحة التالية
        /// </summary>
        private void NextPage_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("الانتقال إلى الصفحة التالية", "التنقل", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    /// <summary>
    /// نموذج عرض الملف
    /// </summary>
    public class FileViewModel
    {
        public int Id { get; set; }
        public string FileNumber { get; set; } = "";
        public string Title { get; set; } = "";
        public string CaseType { get; set; } = "";
        public string TypeColor { get; set; } = "";
        public string ClientName { get; set; } = "";
        public string Court { get; set; } = "";
        public string Status { get; set; } = "";
        public string StatusColor { get; set; } = "";
        public string CreatedDate { get; set; } = "";
    }
}
