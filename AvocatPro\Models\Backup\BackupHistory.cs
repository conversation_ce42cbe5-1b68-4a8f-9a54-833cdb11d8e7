using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AvocatPro.Models.Backup
{
    /// <summary>
    /// سجل النسخ الاحتياطية
    /// </summary>
    public class BackupHistory
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// معرف إعداد النسخة الاحتياطية
        /// </summary>
        public int BackupConfigurationId { get; set; }

        /// <summary>
        /// إعداد النسخة الاحتياطية
        /// </summary>
        [ForeignKey(nameof(BackupConfigurationId))]
        public virtual BackupConfiguration BackupConfiguration { get; set; } = null!;

        /// <summary>
        /// اسم النسخة الاحتياطية
        /// </summary>
        [Required]
        [StringLength(200)]
        public string BackupName { get; set; } = string.Empty;

        /// <summary>
        /// نوع النسخة الاحتياطية
        /// </summary>
        public BackupType BackupType { get; set; }

        /// <summary>
        /// حالة النسخة الاحتياطية
        /// </summary>
        public BackupStatus Status { get; set; }

        /// <summary>
        /// تاريخ بداية النسخة الاحتياطية
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// تاريخ انتهاء النسخة الاحتياطية
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// مدة النسخة الاحتياطية بالثواني
        /// </summary>
        public int? Duration { get; set; }

        /// <summary>
        /// حجم النسخة الاحتياطية بالبايت
        /// </summary>
        public long BackupSize { get; set; }

        /// <summary>
        /// حجم النسخة الاحتياطية المضغوطة بالبايت
        /// </summary>
        public long? CompressedSize { get; set; }

        /// <summary>
        /// نسبة الضغط
        /// </summary>
        public decimal? CompressionRatio { get; set; }

        /// <summary>
        /// مسار النسخة الاحتياطية المحلية
        /// </summary>
        [StringLength(500)]
        public string? LocalFilePath { get; set; }

        /// <summary>
        /// معرف الملف في Google Drive
        /// </summary>
        [StringLength(200)]
        public string? GoogleDriveFileId { get; set; }

        /// <summary>
        /// رابط الملف في Google Drive
        /// </summary>
        [StringLength(500)]
        public string? GoogleDriveFileUrl { get; set; }

        /// <summary>
        /// رسالة النتيجة
        /// </summary>
        [StringLength(1000)]
        public string? Message { get; set; }

        /// <summary>
        /// تفاصيل الخطأ (في حالة الفشل)
        /// </summary>
        [StringLength(2000)]
        public string? ErrorDetails { get; set; }

        /// <summary>
        /// عدد الملفات المنسوخة
        /// </summary>
        public int FilesCount { get; set; }

        /// <summary>
        /// عدد الجداول المنسوخة
        /// </summary>
        public int TablesCount { get; set; }

        /// <summary>
        /// عدد السجلات المنسوخة
        /// </summary>
        public int RecordsCount { get; set; }

        /// <summary>
        /// هل تم تشفير النسخة الاحتياطية
        /// </summary>
        public bool IsEncrypted { get; set; }

        /// <summary>
        /// هل تم ضغط النسخة الاحتياطية
        /// </summary>
        public bool IsCompressed { get; set; }

        /// <summary>
        /// معرف المستخدم الذي بدأ النسخة الاحتياطية
        /// </summary>
        public int? InitiatedBy { get; set; }

        /// <summary>
        /// نوع التشغيل (تلقائي أم يدوي)
        /// </summary>
        public BackupTriggerType TriggerType { get; set; }

        /// <summary>
        /// إصدار التطبيق عند إنشاء النسخة
        /// </summary>
        [StringLength(50)]
        public string? ApplicationVersion { get; set; }

        /// <summary>
        /// معلومات النظام
        /// </summary>
        [StringLength(200)]
        public string? SystemInfo { get; set; }

        /// <summary>
        /// تفاصيل إضافية بصيغة JSON
        /// </summary>
        [Column(TypeName = "TEXT")]
        public string? AdditionalDetails { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// هل النسخة الاحتياطية متاحة
        /// </summary>
        public bool IsAvailable { get; set; } = true;

        /// <summary>
        /// تاريخ انتهاء صلاحية النسخة الاحتياطية
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [StringLength(500)]
        public string? Notes { get; set; }

        /// <summary>
        /// تقييم جودة النسخة الاحتياطية
        /// </summary>
        public BackupQuality? Quality { get; set; }

        /// <summary>
        /// معدل النقل بالبايت/ثانية
        /// </summary>
        public long? TransferRate { get; set; }

        /// <summary>
        /// عدد المحاولات
        /// </summary>
        public int AttemptCount { get; set; } = 1;

        /// <summary>
        /// آخر محاولة
        /// </summary>
        public DateTime? LastAttempt { get; set; }

        /// <summary>
        /// حساب المدة
        /// </summary>
        public void CalculateDuration()
        {
            if (EndTime.HasValue)
            {
                Duration = (int)(EndTime.Value - StartTime).TotalSeconds;
            }
        }

        /// <summary>
        /// حساب نسبة الضغط
        /// </summary>
        public void CalculateCompressionRatio()
        {
            if (CompressedSize.HasValue && BackupSize > 0)
            {
                CompressionRatio = Math.Round((decimal)(BackupSize - CompressedSize.Value) / BackupSize * 100, 2);
            }
        }

        /// <summary>
        /// حساب معدل النقل
        /// </summary>
        public void CalculateTransferRate()
        {
            if (Duration.HasValue && Duration.Value > 0)
            {
                TransferRate = BackupSize / Duration.Value;
            }
        }

        /// <summary>
        /// تحديد جودة النسخة الاحتياطية
        /// </summary>
        public void DetermineQuality()
        {
            if (Status != BackupStatus.Completed)
            {
                Quality = BackupQuality.Poor;
                return;
            }

            var score = 0;
            
            // سرعة النسخ
            if (TransferRate.HasValue)
            {
                if (TransferRate.Value > 10_000_000) score += 2; // أكثر من 10 MB/s
                else if (TransferRate.Value > 1_000_000) score += 1; // أكثر من 1 MB/s
            }

            // نسبة الضغط
            if (CompressionRatio.HasValue)
            {
                if (CompressionRatio.Value > 50) score += 2;
                else if (CompressionRatio.Value > 20) score += 1;
            }

            // عدد المحاولات
            if (AttemptCount == 1) score += 1;

            Quality = score switch
            {
                >= 4 => BackupQuality.Excellent,
                >= 2 => BackupQuality.Good,
                >= 1 => BackupQuality.Fair,
                _ => BackupQuality.Poor
            };
        }

        /// <summary>
        /// تحديث حالة النسخة الاحتياطية
        /// </summary>
        public void UpdateStatus(BackupStatus status, string? message = null, string? errorDetails = null)
        {
            Status = status;
            Message = message;
            ErrorDetails = errorDetails;
            
            if (status == BackupStatus.Completed || status == BackupStatus.Failed || status == BackupStatus.Cancelled)
            {
                EndTime = DateTime.Now;
                CalculateDuration();
                CalculateCompressionRatio();
                CalculateTransferRate();
                DetermineQuality();
            }
        }

        /// <summary>
        /// الحصول على حجم النسخة الاحتياطية بصيغة قابلة للقراءة
        /// </summary>
        public string GetFormattedSize()
        {
            return FormatBytes(BackupSize);
        }

        /// <summary>
        /// الحصول على حجم النسخة المضغوطة بصيغة قابلة للقراءة
        /// </summary>
        public string GetFormattedCompressedSize()
        {
            return CompressedSize.HasValue ? FormatBytes(CompressedSize.Value) : "غير متاح";
        }

        /// <summary>
        /// تنسيق الحجم بالبايت
        /// </summary>
        private static string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            
            return $"{number:n1} {suffixes[counter]}";
        }

        /// <summary>
        /// الحصول على معدل النقل بصيغة قابلة للقراءة
        /// </summary>
        public string GetFormattedTransferRate()
        {
            return TransferRate.HasValue ? $"{FormatBytes(TransferRate.Value)}/s" : "غير متاح";
        }
    }

    /// <summary>
    /// نوع مشغل النسخة الاحتياطية
    /// </summary>
    public enum BackupTriggerType
    {
        /// <summary>
        /// يدوي
        /// </summary>
        Manual = 1,
        
        /// <summary>
        /// مجدول
        /// </summary>
        Scheduled = 2,
        
        /// <summary>
        /// تلقائي عند الإغلاق
        /// </summary>
        AutoOnShutdown = 3,
        
        /// <summary>
        /// تلقائي عند التشغيل
        /// </summary>
        AutoOnStartup = 4
    }

    /// <summary>
    /// جودة النسخة الاحتياطية
    /// </summary>
    public enum BackupQuality
    {
        /// <summary>
        /// ضعيفة
        /// </summary>
        Poor = 1,
        
        /// <summary>
        /// مقبولة
        /// </summary>
        Fair = 2,
        
        /// <summary>
        /// جيدة
        /// </summary>
        Good = 3,
        
        /// <summary>
        /// ممتازة
        /// </summary>
        Excellent = 4
    }
}
