# Smart Lawyers Web Platform
## منصة المحامين الذكية - الإصدار الويب

### نظرة عامة
منصة ويب متكاملة لإدارة مكاتب المحاماة مع تكامل كامل مع التطبيق المكتبي وخصائص متقدمة للسوق المغربي.

### الميزات الرئيسية

#### 🔗 التكامل والربط
- ربط مع قاعدة البيانات المشتركة مع التطبيق المكتبي
- تزامن فوري للبيانات بين الويب والتطبيق
- ربط مع التقويم الشخصي (Google Calendar, Outlook)
- تكامل مع WhatsApp Business للتواصل

#### 🇲🇦 الخصائص المغربية
- التكامل مع الإدارات المغربية (العدل، الداخلية)
- التكامل مع المحاكم الإلكترونية المغربية (mahakim.ma)
- قوالب عقود مغربية جاهزة
- حساب الرسوم القضائية المغربية تلقائياً
- تقويم العطل المغربية والمناسبات
- دعم اللهجة المغربية في البحث

#### 📊 لوحة التحكم
- داشبورد أنيق وجذاب
- إحصائيات متقدمة ومخططات تفاعلية
- تقارير شاملة قابلة للتصدير
- واجهة مستخدم حديثة ومتجاوبة

#### 🛠️ التقنيات المستخدمة
- **Backend**: Laravel 10
- **Frontend**: Bootstrap 5 + Vue.js
- **Database**: MySQL/PostgreSQL
- **Real-time**: Laravel WebSockets
- **API**: RESTful API + GraphQL

### متطلبات النظام
- PHP 8.1 أو أحدث
- Composer
- Node.js & NPM
- MySQL 8.0 أو PostgreSQL 13+
- Redis (اختياري للتخزين المؤقت)

### التثبيت السريع والسهل 🚀

#### الطريقة الأولى: التثبيت التلقائي (الأسهل)
1. **تحميل الملفات** وفك الضغط في مجلد الخادم
2. **زيارة الرابط**: `http://your-domain.com/install.php`
3. **اتباع المعالج** خطوة بخطوة
4. **الانتهاء!** - المنصة جاهزة للاستخدام

#### الطريقة الثانية: التثبيت اليدوي
```bash
# 1. تحميل المشروع
git clone https://github.com/your-repo/smartlawyers-web.git
cd smartlawyers-web

# 2. تثبيت التبعيات
composer install --no-dev --optimize-autoloader
npm install --production

# 3. إعداد البيئة
cp .env.example .env
php artisan key:generate

# 4. إعداد قاعدة البيانات
php artisan migrate --seed

# 5. بناء الأصول
npm run build

# 6. تشغيل الخادم
php artisan serve
```

#### متطلبات الخادم
- **PHP**: 8.1 أو أحدث
- **MySQL**: 8.0 أو أحدث
- **Node.js**: 16.0 أو أحدث
- **Composer**: أحدث إصدار

### الإعداد المتقدم

#### إعداد قاعدة البيانات المشتركة
1. تحديث ملف `.env` بمعلومات قاعدة البيانات
2. تشغيل الترحيلات: `php artisan migrate`
3. إضافة البيانات الأولية: `php artisan db:seed`

#### إعداد التكاملات الخارجية
1. **Google Calendar**: إضافة مفاتيح API في `.env`
2. **WhatsApp Business**: تكوين webhook وtoken
3. **المحاكم المغربية**: إعداد شهادات SSL والمفاتيح

### الهيكل التنظيمي
```
smartlawyers-web/
├── app/
│   ├── Http/Controllers/
│   ├── Models/
│   ├── Services/
│   └── Integrations/
├── resources/
│   ├── views/
│   ├── js/
│   └── css/
├── database/
│   ├── migrations/
│   └── seeders/
├── public/
├── routes/
└── config/
```

### الأمان
- مصادقة متعددة العوامل
- تشفير البيانات الحساسة
- حماية CSRF
- تسجيل العمليات الحساسة
- نسخ احتياطية تلقائية

### الدعم والمساعدة
- الوثائق الكاملة: `/docs`
- API Documentation: `/api/docs`
- دعم فني: <EMAIL>
- المجتمع: https://community.smartlawyers.ma

### الترخيص
هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

### المساهمة
نرحب بالمساهمات! يرجى قراءة [CONTRIBUTING.md](CONTRIBUTING.md) للحصول على التفاصيل.

---
© 2024 Smart Lawyers Platform. جميع الحقوق محفوظة.
