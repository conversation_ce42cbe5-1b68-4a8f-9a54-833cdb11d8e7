<Window x:Class="AvocatPro.Views.Windows.AddSessionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة جلسة جديدة" 
        Height="700" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">

    <Window.Resources>
        <!-- أنماط النموذج -->
        <Style x:Key="FormLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#333"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>

        <Style x:Key="FormTextBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Style.Triggers>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="BorderBrush" Value="#1976D2"/>
                    <Setter Property="BorderThickness" Value="2"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="FormComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="FormDatePickerStyle" TargetType="DatePicker">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1976D2"/>
            <Setter Property="Margin" Value="0,20,0,15"/>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#1976D2"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1565C0"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#F5F5F5"/>
            <Setter Property="Foreground" Value="#333"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#E0E0E0"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- رأس النافذة -->
        <Border Grid.Row="0" Background="#1976D2" Padding="20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="⚖️" FontSize="24" Foreground="White" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="إضافة جلسة محكمة جديدة" FontSize="20" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="أدخل بيانات الجلسة والإجراءات بعناية" FontSize="12" Foreground="#E3F2FD"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- محتوى النموذج -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="30">
            <StackPanel>
                <!-- المعلومات الأساسية -->
                <TextBlock Text="📋 المعلومات الأساسية" Style="{StaticResource SectionHeaderStyle}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="القضية المرتبطة *" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="CaseComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <!-- سيتم ملؤها من قاعدة البيانات -->
                        </ComboBox>

                        <TextBlock Text="نوع الجلسة *" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="SessionTypeComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="جلسة عادية" Tag="Regular" IsSelected="True"/>
                            <ComboBoxItem Content="جلسة استعجالية" Tag="Urgent"/>
                            <ComboBoxItem Content="جلسة مرافعة" Tag="Pleading"/>
                            <ComboBoxItem Content="جلسة حكم" Tag="Judgment"/>
                            <ComboBoxItem Content="جلسة تأجيل" Tag="Postponement"/>
                            <ComboBoxItem Content="جلسة تحضيرية" Tag="Preparatory"/>
                            <ComboBoxItem Content="جلسة صلح" Tag="Settlement"/>
                        </ComboBox>

                        <TextBlock Text="نوع الإجراء *" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="ProcedureTypeComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="مرافعة" IsSelected="True"/>
                            <ComboBoxItem Content="تقديم أدلة"/>
                            <ComboBoxItem Content="استجواب شهود"/>
                            <ComboBoxItem Content="خبرة فنية"/>
                            <ComboBoxItem Content="تأجيل"/>
                            <ComboBoxItem Content="حكم"/>
                            <ComboBoxItem Content="صلح"/>
                            <ComboBoxItem Content="إجراء تحضيري"/>
                        </ComboBox>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="تاريخ الجلسة *" Style="{StaticResource FormLabelStyle}"/>
                        <DatePicker Name="SessionDatePicker" Style="{StaticResource FormDatePickerStyle}"/>

                        <TextBlock Text="وقت الجلسة *" Style="{StaticResource FormLabelStyle}"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <ComboBox Name="HourComboBox" Grid.Column="0" Style="{StaticResource FormComboBoxStyle}">
                                <!-- سيتم ملؤها بالساعات -->
                            </ComboBox>
                            <ComboBox Name="MinuteComboBox" Grid.Column="2" Style="{StaticResource FormComboBoxStyle}">
                                <!-- سيتم ملؤها بالدقائق -->
                            </ComboBox>
                        </Grid>

                        <TextBlock Text="حالة الجلسة" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="StatusComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="مجدولة" Tag="Scheduled" IsSelected="True"/>
                            <ComboBoxItem Content="جارية" Tag="InProgress"/>
                            <ComboBoxItem Content="مكتملة" Tag="Completed"/>
                            <ComboBoxItem Content="مؤجلة" Tag="Postponed"/>
                            <ComboBoxItem Content="ملغاة" Tag="Cancelled"/>
                            <ComboBoxItem Content="لم يحضر" Tag="NoShow"/>
                        </ComboBox>
                    </StackPanel>
                </Grid>

                <!-- تفاصيل المحكمة -->
                <TextBlock Text="🏛️ تفاصيل المحكمة" Style="{StaticResource SectionHeaderStyle}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="القاضي المسؤول" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="JudgeTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="اسم القاضي المسؤول عن الجلسة"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="رقم القاعة" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="CourtRoomTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="رقم قاعة المحكمة"/>
                    </StackPanel>

                    <StackPanel Grid.Column="4">
                        <TextBlock Text="المحامي المكلف" Style="{StaticResource FormLabelStyle}"/>
                        <ComboBox Name="LawyerComboBox" Style="{StaticResource FormComboBoxStyle}">
                            <ComboBoxItem Content="د. محمد أحمد الشريف" Tag="1"/>
                            <ComboBoxItem Content="أ. سارة عبدالله النور" Tag="2"/>
                            <ComboBoxItem Content="أ. خالد سالم العتيبي" Tag="3"/>
                            <ComboBoxItem Content="د. نورا محمد الزهراني" Tag="4"/>
                        </ComboBox>
                    </StackPanel>
                </Grid>

                <!-- تفاصيل الجلسة -->
                <TextBlock Text="📝 تفاصيل الجلسة" Style="{StaticResource SectionHeaderStyle}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="القرار المتخذ" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="DecisionTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Height="80" TextWrapping="Wrap" AcceptsReturn="True"
                                Text="" ToolTip="القرار المتخذ في الجلسة"/>

                        <TextBlock Text="الحضور" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="AttendanceTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Height="60" TextWrapping="Wrap" AcceptsReturn="True"
                                Text="" ToolTip="قائمة الحضور"/>

                        <TextBlock Text="مدة الجلسة (دقيقة)" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="DurationTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Text="" ToolTip="مدة الجلسة بالدقائق"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="المرافعات" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="ArgumentsTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Height="80" TextWrapping="Wrap" AcceptsReturn="True"
                                Text="" ToolTip="ملخص المرافعات"/>

                        <TextBlock Text="الأدلة المقدمة" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="EvidenceTextBox" Style="{StaticResource FormTextBoxStyle}" 
                                Height="60" TextWrapping="Wrap" AcceptsReturn="True"
                                Text="" ToolTip="الأدلة المقدمة في الجلسة"/>

                        <TextBlock Text="المصاريف (درهم)" Style="{StaticResource FormLabelStyle}"/>
                        <TextBox Name="ExpensesTextBox" Style="{StaticResource FormTextBoxStyle}"
                                Text="" ToolTip="مصاريف الجلسة"/>
                    </StackPanel>
                </Grid>

                <!-- الجلسة القادمة -->
                <TextBlock Text="📅 الجلسة القادمة" Style="{StaticResource SectionHeaderStyle}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="تاريخ الجلسة القادمة" Style="{StaticResource FormLabelStyle}"/>
                        <DatePicker Name="NextSessionDatePicker" Style="{StaticResource FormDatePickerStyle}"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="وقت الجلسة القادمة" Style="{StaticResource FormLabelStyle}"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <ComboBox Name="NextHourComboBox" Grid.Column="0" Style="{StaticResource FormComboBoxStyle}">
                                <!-- سيتم ملؤها بالساعات -->
                            </ComboBox>
                            <ComboBox Name="NextMinuteComboBox" Grid.Column="2" Style="{StaticResource FormComboBoxStyle}">
                                <!-- سيتم ملؤها بالدقائق -->
                            </ComboBox>
                        </Grid>
                    </StackPanel>
                </Grid>

                <!-- ملاحظات -->
                <TextBlock Text="📄 ملاحظات إضافية" Style="{StaticResource SectionHeaderStyle}"/>
                <TextBox Name="NotesTextBox" Style="{StaticResource FormTextBoxStyle}" 
                        Height="80" TextWrapping="Wrap" AcceptsReturn="True"
                        Text="" ToolTip="ملاحظات إضافية عن الجلسة"/>
            </StackPanel>
        </ScrollViewer>

        <!-- أزرار الإجراءات -->
        <Border Grid.Row="2" Background="#F8F9FA" Padding="20" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Name="SaveButton" Style="{StaticResource PrimaryButtonStyle}" Click="SaveButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="💾" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ الجلسة"/>
                    </StackPanel>
                </Button>
                
                <Button Name="SaveAndNewButton" Style="{StaticResource PrimaryButtonStyle}" 
                       Background="#4CAF50" Click="SaveAndNewButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="➕" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ وإضافة أخرى"/>
                    </StackPanel>
                </Button>
                
                <Button Name="CancelButton" Style="{StaticResource SecondaryButtonStyle}" Click="CancelButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="❌" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="إلغاء"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
