<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- الألوان الديناميكية -->
    <SolidColorBrush x:Key="PrimaryColor" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="SecondaryColor" Color="#F8FAFC"/>
    <SolidColorBrush x:Key="AccentColor" Color="#3B82F6"/>
    <SolidColorBrush x:Key="BackgroundColor" Color="#F8FAFC"/>
    <SolidColorBrush x:Key="SurfaceColor" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="TextColor" Color="#1F2937"/>
    <SolidColorBrush x:Key="TextSecondaryColor" Color="#6B7280"/>
    <SolidColorBrush x:Key="SuccessColor" Color="#10B981"/>
    <SolidColorBrush x:Key="WarningColor" Color="#F59E0B"/>
    <SolidColorBrush x:Key="ErrorColor" Color="#EF4444"/>
    <SolidColorBrush x:Key="InfoColor" Color="#3B82F6"/>

    <!-- الخصائص الديناميكية -->
    <sys:Double x:Key="CardOpacity" xmlns:sys="clr-namespace:System;assembly=mscorlib">1.0</sys:Double>
    <sys:Double x:Key="ShadowOpacity" xmlns:sys="clr-namespace:System;assembly=mscorlib">0.1</sys:Double>
    <CornerRadius x:Key="BorderRadius">12</CornerRadius>

    <!-- أنماط الأزرار المحسنة -->
    <Style x:Key="ModernButton" TargetType="Button">
        <Setter Property="Background" Value="{DynamicResource AccentColor}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="16,10"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border" 
                            Background="{TemplateBinding Background}"
                            CornerRadius="{DynamicResource BorderRadius}"
                            Padding="{TemplateBinding Padding}">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="{DynamicResource ShadowOpacity}" 
                                              BlurRadius="8" ShadowDepth="2"/>
                        </Border.Effect>
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Opacity" Value="0.9"/>
                            <Setter TargetName="border" Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="1.02" ScaleY="1.02"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Opacity" Value="0.5"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- أنماط البطاقات المحسنة -->
    <Style x:Key="ModernCard" TargetType="Border">
        <Setter Property="Background" Value="{DynamicResource SurfaceColor}"/>
        <Setter Property="CornerRadius" Value="{DynamicResource BorderRadius}"/>
        <Setter Property="Padding" Value="20"/>
        <Setter Property="Margin" Value="0,0,0,15"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" Opacity="{DynamicResource ShadowOpacity}" 
                                  BlurRadius="10" ShadowDepth="2"/>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="Black" Opacity="0.15" BlurRadius="15" ShadowDepth="3"/>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- أنماط النصوص المحسنة -->
    <Style x:Key="HeaderText" TargetType="TextBlock">
        <Setter Property="FontSize" Value="24"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{DynamicResource TextColor}"/>
        <Setter Property="Margin" Value="0,0,0,10"/>
    </Style>

    <Style x:Key="SubHeaderText" TargetType="TextBlock">
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{DynamicResource TextColor}"/>
        <Setter Property="Margin" Value="0,0,0,8"/>
    </Style>

    <Style x:Key="BodyText" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="{DynamicResource TextColor}"/>
        <Setter Property="LineHeight" Value="20"/>
    </Style>

    <Style x:Key="CaptionText" TargetType="TextBlock">
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Foreground" Value="{DynamicResource TextSecondaryColor}"/>
    </Style>

    <!-- أنماط حقول الإدخال المحسنة -->
    <Style x:Key="ModernTextBox" TargetType="TextBox">
        <Setter Property="Background" Value="{DynamicResource SurfaceColor}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextColor}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource TextSecondaryColor}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border x:Name="border" 
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8"
                            Padding="{TemplateBinding Padding}">
                        <ScrollViewer x:Name="PART_ContentHost"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="{DynamicResource AccentColor}"/>
                            <Setter TargetName="border" Property="BorderThickness" Value="2"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="{DynamicResource AccentColor}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- أنماط القوائم المنسدلة المحسنة -->
    <Style x:Key="ModernComboBox" TargetType="ComboBox">
        <Setter Property="Background" Value="{DynamicResource SurfaceColor}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextColor}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource TextSecondaryColor}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ComboBox">
                    <Grid>
                        <Border x:Name="border" 
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="8">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <ContentPresenter Grid.Column="0" 
                                                  Margin="{TemplateBinding Padding}"
                                                  VerticalAlignment="Center"
                                                  Content="{TemplateBinding SelectionBoxItem}"/>
                                <Path Grid.Column="1" 
                                      Data="M 0 0 L 4 4 L 8 0 Z" 
                                      Fill="{DynamicResource TextSecondaryColor}"
                                      Margin="0,0,12,0"
                                      VerticalAlignment="Center"/>
                            </Grid>
                        </Border>
                        <Popup x:Name="PART_Popup" 
                               Placement="Bottom" 
                               IsOpen="{TemplateBinding IsDropDownOpen}">
                            <Border Background="{DynamicResource SurfaceColor}"
                                    BorderBrush="{DynamicResource TextSecondaryColor}"
                                    BorderThickness="1"
                                    CornerRadius="8"
                                    MinWidth="{TemplateBinding ActualWidth}">
                                <ScrollViewer>
                                    <ItemsPresenter/>
                                </ScrollViewer>
                            </Border>
                        </Popup>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="{DynamicResource AccentColor}"/>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="{DynamicResource AccentColor}"/>
                            <Setter TargetName="border" Property="BorderThickness" Value="2"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- أنماط مربعات الاختيار المحسنة -->
    <Style x:Key="ModernCheckBox" TargetType="CheckBox">
        <Setter Property="Foreground" Value="{DynamicResource TextColor}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="CheckBox">
                    <StackPanel Orientation="Horizontal">
                        <Border x:Name="checkBorder" 
                                Width="18" Height="18" 
                                Background="{DynamicResource SurfaceColor}"
                                BorderBrush="{DynamicResource TextSecondaryColor}"
                                BorderThickness="2"
                                CornerRadius="4"
                                Margin="0,0,8,0">
                            <Path x:Name="checkMark" 
                                  Data="M 2 6 L 6 10 L 14 2" 
                                  Stroke="{DynamicResource AccentColor}"
                                  StrokeThickness="2"
                                  Visibility="Collapsed"/>
                        </Border>
                        <ContentPresenter VerticalAlignment="Center"/>
                    </StackPanel>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="checkMark" Property="Visibility" Value="Visible"/>
                            <Setter TargetName="checkBorder" Property="Background" Value="{DynamicResource AccentColor}"/>
                            <Setter TargetName="checkBorder" Property="BorderBrush" Value="{DynamicResource AccentColor}"/>
                            <Setter TargetName="checkMark" Property="Stroke" Value="White"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="checkBorder" Property="BorderBrush" Value="{DynamicResource AccentColor}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- أنماط شريط التقدم المحسن -->
    <Style x:Key="ModernProgressBar" TargetType="ProgressBar">
        <Setter Property="Height" Value="8"/>
        <Setter Property="Background" Value="{DynamicResource SecondaryColor}"/>
        <Setter Property="Foreground" Value="{DynamicResource AccentColor}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ProgressBar">
                    <Border Background="{TemplateBinding Background}" 
                            CornerRadius="4">
                        <Border x:Name="PART_Track" 
                                Background="{TemplateBinding Foreground}"
                                CornerRadius="4"
                                HorizontalAlignment="Left"
                                Width="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Value, Converter={StaticResource ProgressBarWidthConverter}}"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- أنماط التمرير المحسنة -->
    <Style x:Key="ModernScrollViewer" TargetType="ScrollViewer">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ScrollViewer">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <ScrollContentPresenter Grid.Column="0" Grid.Row="0"/>
                        
                        <ScrollBar x:Name="PART_VerticalScrollBar"
                                   Grid.Column="1" Grid.Row="0"
                                   Orientation="Vertical"
                                   Width="12"
                                   Background="Transparent"
                                   Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"/>
                        
                        <ScrollBar x:Name="PART_HorizontalScrollBar"
                                   Grid.Column="0" Grid.Row="1"
                                   Orientation="Horizontal"
                                   Height="12"
                                   Background="Transparent"
                                   Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"/>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
