using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using AvocatPro.Models;

namespace AvocatPro.Services;

/// <summary>
/// خدمة طباعة الشيكات والأظرفة
/// </summary>
public class PrintingService
{
    private readonly List<CheckTemplate> _checkTemplates;
    private readonly Dictionary<EnvelopeSize, (double Width, double Height)> _envelopeSizes;

    public PrintingService()
    {
        _checkTemplates = InitializeCheckTemplates();
        _envelopeSizes = InitializeEnvelopeSizes();
    }

    /// <summary>
    /// الحصول على قوالب الشيكات المتاحة
    /// </summary>
    public List<CheckTemplate> GetAvailableCheckTemplates()
    {
        return _checkTemplates.ToList();
    }

    /// <summary>
    /// طباعة شيك
    /// </summary>
    public async Task<bool> PrintCheckAsync(CheckModel check, CheckTemplate template, bool preview = false)
    {
        try
        {
            await Task.CompletedTask;
            
            if (preview)
            {
                MessageBox.Show($"معاينة الشيك:\n\nرقم الشيك: {check.CheckNumber}\nالمستفيد: {check.PayeeName}\nالمبلغ: {check.Amount:N2} درهم\nالمبلغ بالكلمات: {check.AmountInWords}\nالتاريخ: {check.CheckDate:dd/MM/yyyy}\nالبنك: {template.BankName}", 
                    "معاينة الشيك", MessageBoxButton.OK, MessageBoxImage.Information);
                return true;
            }
            else
            {
                MessageBox.Show("تم إرسال الشيك للطباعة بنجاح!\n\nملاحظة: في النسخة الكاملة سيتم طباعة الشيك فعلياً على الطابعة المحددة.", "طباعة", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                return true;
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة الشيك: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
            return false;
        }
    }

    /// <summary>
    /// طباعة ظرف
    /// </summary>
    public async Task<bool> PrintEnvelopeAsync(EnvelopeModel envelope, bool preview = false)
    {
        try
        {
            await Task.CompletedTask;
            
            if (preview)
            {
                var sizeDisplay = GetEnvelopeSizeDisplay(envelope.EnvelopeSize);
                var specialOptions = "";
                if (envelope.IsRegistered) specialOptions += "مسجل ";
                if (envelope.IsUrgent) specialOptions += "عاجل ";
                
                MessageBox.Show($"معاينة الظرف:\n\nالمرسل: {envelope.SenderName}\nعنوان المرسل: {envelope.SenderAddress}, {envelope.SenderCity}\n\nالمستقبل: {envelope.RecipientName}\nعنوان المستقبل: {envelope.RecipientAddress}, {envelope.RecipientCity}\n\nحجم الظرف: {sizeDisplay}\nخيارات خاصة: {specialOptions}\nتعليمات: {envelope.SpecialInstructions}", 
                    "معاينة الظرف", MessageBoxButton.OK, MessageBoxImage.Information);
                return true;
            }
            else
            {
                MessageBox.Show("تم إرسال الظرف للطباعة بنجاح!\n\nملاحظة: في النسخة الكاملة سيتم طباعة الظرف فعلياً على الطابعة المحددة.", "طباعة", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                return true;
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة الظرف: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
            return false;
        }
    }

    /// <summary>
    /// الحصول على عرض حجم الظرف
    /// </summary>
    private string GetEnvelopeSizeDisplay(EnvelopeSize size)
    {
        return size switch
        {
            EnvelopeSize.Standard => "قياسي (220×110 مم)",
            EnvelopeSize.Large => "كبير (324×229 مم)",
            EnvelopeSize.A4 => "A4 (297×210 مم)",
            EnvelopeSize.Custom => "مخصص (250×125 مم)",
            _ => "غير محدد"
        };
    }

    /// <summary>
    /// تهيئة قوالب الشيكات
    /// </summary>
    private List<CheckTemplate> InitializeCheckTemplates()
    {
        return new List<CheckTemplate>
        {
            new CheckTemplate
            {
                Name = "قالب قياسي",
                BankName = "البنك الشعبي",
                Width = 200,
                Height = 85
            },
            new CheckTemplate
            {
                Name = "قالب التجاري وفا بنك",
                BankName = "التجاري وفا بنك",
                Width = 210,
                Height = 90
            },
            new CheckTemplate
            {
                Name = "قالب بنك المغرب",
                BankName = "بنك المغرب",
                Width = 195,
                Height = 80
            },
            new CheckTemplate
            {
                Name = "قالب القرض الفلاحي",
                BankName = "القرض الفلاحي",
                Width = 205,
                Height = 88
            },
            new CheckTemplate
            {
                Name = "قالب البنك المغربي للتجارة الخارجية",
                BankName = "البنك المغربي للتجارة الخارجية",
                Width = 198,
                Height = 82
            }
        };
    }

    /// <summary>
    /// تهيئة أحجام الأظرفة
    /// </summary>
    private Dictionary<EnvelopeSize, (double Width, double Height)> InitializeEnvelopeSizes()
    {
        return new Dictionary<EnvelopeSize, (double Width, double Height)>
        {
            { EnvelopeSize.Standard, (220, 110) },
            { EnvelopeSize.Large, (324, 229) },
            { EnvelopeSize.A4, (297, 210) },
            { EnvelopeSize.Custom, (250, 125) }
        };
    }
}
