<Page x:Class="AvocatPro.Views.Pages.FinancialPageNew"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="إدارة المالية"
      FlowDirection="RightToLeft">

    <Page.Resources>
        <!-- أنماط الصفحة -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1976D2"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>

        <Style x:Key="StatCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#1976D2"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5,0"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1565C0"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="FontSize" Value="14"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F0F0F0"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="FilterComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Margin" Value="5,0"/>
        </Style>

        <Style x:Key="FilterDatePickerStyle" TargetType="DatePicker">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Margin" Value="5,0"/>
        </Style>

        <Style x:Key="TabHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
        </Style>

        <Style x:Key="ChartCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Page.Resources>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- رأس الصفحة -->
        <Border Grid.Row="0" Background="White" Padding="30,20" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="💰" FontSize="32" Margin="0,0,15,0"/>
                    <StackPanel>
                        <TextBlock Text="إدارة المالية والمحاسبة" Style="{StaticResource HeaderTextStyle}"/>
                        <TextBlock Text="إدارة شاملة للمصاريف والإيرادات مع تقارير مالية متقدمة" FontSize="14" Foreground="#666"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Name="AddExpenseButton" Style="{StaticResource PrimaryButtonStyle}" 
                           Background="#F44336" Click="AddExpenseButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="💸" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="إضافة مصروف"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Name="AddRevenueButton" Style="{StaticResource PrimaryButtonStyle}" 
                           Background="#4CAF50" Click="AddRevenueButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="💰" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="إضافة إيراد"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Name="GenerateReportButton" Style="{StaticResource PrimaryButtonStyle}" 
                           Background="#FF9800" Click="GenerateReportButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📊" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تقرير مالي"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Name="RefreshButton" Style="{StaticResource PrimaryButtonStyle}" 
                           Background="#9C27B0" Click="RefreshButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔄" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- بطاقات الإحصائيات المالية -->
        <Grid Grid.Row="1" Margin="20,0,20,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Border Grid.Column="0" Style="{StaticResource StatCardStyle}">
                <StackPanel>
                    <TextBlock Text="💰 إجمالي الإيرادات" FontSize="12" Foreground="#666" Margin="0,0,0,8"/>
                    <TextBlock Name="TotalRevenuesTextBlock" Text="0 ريال" FontSize="20" FontWeight="Bold" Foreground="#4CAF50"/>
                </StackPanel>
            </Border>

            <Border Grid.Column="1" Style="{StaticResource StatCardStyle}">
                <StackPanel>
                    <TextBlock Text="💸 إجمالي المصاريف" FontSize="12" Foreground="#666" Margin="0,0,0,8"/>
                    <TextBlock Name="TotalExpensesTextBlock" Text="0 ريال" FontSize="20" FontWeight="Bold" Foreground="#F44336"/>
                </StackPanel>
            </Border>

            <Border Grid.Column="2" Style="{StaticResource StatCardStyle}">
                <StackPanel>
                    <TextBlock Text="📈 صافي الربح" FontSize="12" Foreground="#666" Margin="0,0,0,8"/>
                    <TextBlock Name="NetProfitTextBlock" Text="0 ريال" FontSize="20" FontWeight="Bold" Foreground="#2196F3"/>
                </StackPanel>
            </Border>

            <Border Grid.Column="3" Style="{StaticResource StatCardStyle}">
                <StackPanel>
                    <TextBlock Text="⏳ إيرادات معلقة" FontSize="12" Foreground="#666" Margin="0,0,0,8"/>
                    <TextBlock Name="PendingRevenuesTextBlock" Text="0 ريال" FontSize="20" FontWeight="Bold" Foreground="#FF9800"/>
                </StackPanel>
            </Border>

            <Border Grid.Column="4" Style="{StaticResource StatCardStyle}">
                <StackPanel>
                    <TextBlock Text="❌ إيرادات متأخرة" FontSize="12" Foreground="#666" Margin="0,0,0,8"/>
                    <TextBlock Name="OverdueRevenuesTextBlock" Text="0 ريال" FontSize="20" FontWeight="Bold" Foreground="#E91E63"/>
                </StackPanel>
            </Border>

            <Border Grid.Column="5" Style="{StaticResource StatCardStyle}">
                <StackPanel>
                    <TextBlock Text="🔄 مصاريف قابلة للاسترداد" FontSize="12" Foreground="#666" Margin="0,0,0,8"/>
                    <TextBlock Name="ReimbursableExpensesTextBlock" Text="0 ريال" FontSize="20" FontWeight="Bold" Foreground="#9C27B0"/>
                </StackPanel>
            </Border>
        </Grid>

        <!-- شريط التحكم والتصفية -->
        <Border Grid.Row="2" Background="White" Padding="20" Margin="20,0,20,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- مرشحات الفترة -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="الفترة:" FontSize="14" FontWeight="Bold" 
                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                    
                    <ComboBox Name="PeriodFilterComboBox" Style="{StaticResource FilterComboBoxStyle}"
                             SelectionChanged="PeriodFilterComboBox_SelectionChanged">
                        <ComboBoxItem Content="هذا الشهر" Tag="ThisMonth" IsSelected="True"/>
                        <ComboBoxItem Content="الشهر الماضي" Tag="LastMonth"/>
                        <ComboBoxItem Content="آخر 3 أشهر" Tag="Last3Months"/>
                        <ComboBoxItem Content="آخر 6 أشهر" Tag="Last6Months"/>
                        <ComboBoxItem Content="هذا العام" Tag="ThisYear"/>
                        <ComboBoxItem Content="العام الماضي" Tag="LastYear"/>
                        <ComboBoxItem Content="فترة مخصصة" Tag="Custom"/>
                    </ComboBox>

                    <DatePicker Name="StartDatePicker" Style="{StaticResource FilterDatePickerStyle}"
                               Visibility="Collapsed" SelectedDateChanged="DatePicker_SelectedDateChanged"/>

                    <TextBlock Text="إلى" FontSize="12" VerticalAlignment="Center"
                              Margin="10,0" Visibility="Collapsed" Name="ToLabel"/>

                    <DatePicker Name="EndDatePicker" Style="{StaticResource FilterDatePickerStyle}"
                               Visibility="Collapsed" SelectedDateChanged="DatePicker_SelectedDateChanged"/>
                </StackPanel>

                <!-- مرشحات إضافية -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                    <ComboBox Name="CategoryFilterComboBox" Style="{StaticResource FilterComboBoxStyle}"
                             SelectionChanged="FilterComboBox_SelectionChanged">
                        <ComboBoxItem Content="جميع الفئات" Tag="All" IsSelected="True"/>
                        <ComboBoxItem Content="🏢 مصاريف إدارية" Tag="Administrative"/>
                        <ComboBoxItem Content="⚖️ مصاريف قانونية" Tag="Legal"/>
                        <ComboBoxItem Content="⚙️ مصاريف تشغيلية" Tag="Operational"/>
                        <ComboBoxItem Content="💻 مصاريف تقنية" Tag="Technology"/>
                        <ComboBoxItem Content="📢 مصاريف تسويق" Tag="Marketing"/>
                        <ComboBoxItem Content="✈️ مصاريف سفر" Tag="Travel"/>
                    </ComboBox>

                    <ComboBox Name="StatusFilterComboBox" Style="{StaticResource FilterComboBoxStyle}"
                             SelectionChanged="FilterComboBox_SelectionChanged">
                        <ComboBoxItem Content="جميع الحالات" Tag="All" IsSelected="True"/>
                        <ComboBoxItem Content="⏳ في الانتظار" Tag="Pending"/>
                        <ComboBoxItem Content="✅ مدفوع" Tag="Paid"/>
                        <ComboBoxItem Content="🔵 مدفوع جزئياً" Tag="PartiallyPaid"/>
                        <ComboBoxItem Content="❌ متأخر" Tag="Overdue"/>
                        <ComboBoxItem Content="🚫 ملغي" Tag="Cancelled"/>
                    </ComboBox>
                </StackPanel>

                <!-- أزرار الإجراءات -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Name="ExportButton" Style="{StaticResource PrimaryButtonStyle}" 
                           Background="#607D8B" Click="ExportButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📤" FontSize="14" Margin="0,0,5,0"/>
                            <TextBlock Text="تصدير"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Name="PrintButton" Style="{StaticResource PrimaryButtonStyle}" 
                           Background="#795548" Click="PrintButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🖨️" FontSize="14" Margin="0,0,5,0"/>
                            <TextBlock Text="طباعة"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="3" Margin="20,0,20,20">
            <TabControl Background="White">
                <!-- تبويب المصاريف -->
                <TabItem>
                    <TabItem.Header>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="💸" FontSize="16" Margin="0,0,5,0"/>
                            <TextBlock Text="المصاريف" Style="{StaticResource TabHeaderStyle}"/>
                        </StackPanel>
                    </TabItem.Header>
                    
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- جدول المصاريف -->
                        <DataGrid Name="ExpensesDataGrid" Grid.Row="0" 
                                 AutoGenerateColumns="False" 
                                 CanUserAddRows="False"
                                 CanUserDeleteRows="False"
                                 IsReadOnly="True"
                                 GridLinesVisibility="Horizontal"
                                 HeadersVisibility="Column"
                                 AlternatingRowBackground="#F9F9F9"
                                 RowHeight="45"
                                 FontSize="12">
                            
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="المرجع" Binding="{Binding Reference}" Width="120"/>
                                <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="200"/>
                                <DataGridTextColumn Header="المبلغ" Binding="{Binding AmountDisplay}" Width="100"/>
                                <DataGridTextColumn Header="التاريخ" Binding="{Binding ExpenseDateDisplay}" Width="100"/>
                                <DataGridTextColumn Header="الفئة" Binding="{Binding CategoryDisplay}" Width="120"/>
                                <DataGridTextColumn Header="النوع" Binding="{Binding TypeDisplay}" Width="120"/>
                                <DataGridTextColumn Header="المورد" Binding="{Binding VendorDisplay}" Width="120"/>
                                <DataGridTextColumn Header="الحالة" Binding="{Binding PaymentStatusDisplay}" Width="100"/>
                                <DataGridTextColumn Header="المرفقات" Binding="{Binding AttachmentsDisplay}" Width="100"/>
                                
                                <DataGridTemplateColumn Header="الإجراءات" Width="200">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                <Button Style="{StaticResource ActionButtonStyle}" 
                                                       ToolTip="عرض التفاصيل" 
                                                       Click="ViewExpenseButton_Click" 
                                                       Tag="{Binding Id}">
                                                    <TextBlock Text="👁️" FontSize="14"/>
                                                </Button>
                                                <Button Style="{StaticResource ActionButtonStyle}" 
                                                       ToolTip="تعديل" 
                                                       Click="EditExpenseButton_Click" 
                                                       Tag="{Binding Id}">
                                                    <TextBlock Text="✏️" FontSize="14"/>
                                                </Button>
                                                <Button Style="{StaticResource ActionButtonStyle}" 
                                                       ToolTip="المرفقات" 
                                                       Click="ViewAttachmentsButton_Click" 
                                                       Tag="{Binding Id}">
                                                    <TextBlock Text="📎" FontSize="14"/>
                                                </Button>
                                                <Button Style="{StaticResource ActionButtonStyle}" 
                                                       ToolTip="تسديد" 
                                                       Click="PayExpenseButton_Click" 
                                                       Tag="{Binding Id}">
                                                    <TextBlock Text="💳" FontSize="14"/>
                                                </Button>
                                                <Button Style="{StaticResource ActionButtonStyle}" 
                                                       ToolTip="حذف" 
                                                       Click="DeleteExpenseButton_Click" 
                                                       Tag="{Binding Id}">
                                                    <TextBlock Text="🗑️" FontSize="14" Foreground="Red"/>
                                                </Button>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>

                        <!-- تذييل جدول المصاريف -->
                        <Border Grid.Row="1" Background="#F8F9FA" Padding="15" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
                            <TextBlock Name="ExpensesStatusTextBlock" Text="جاري التحميل..." FontSize="12" Foreground="#666"/>
                        </Border>
                    </Grid>
                </TabItem>

                <!-- تبويب الإيرادات -->
                <TabItem>
                    <TabItem.Header>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="💰" FontSize="16" Margin="0,0,5,0"/>
                            <TextBlock Text="الإيرادات" Style="{StaticResource TabHeaderStyle}"/>
                        </StackPanel>
                    </TabItem.Header>
                    
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- جدول الإيرادات -->
                        <DataGrid Name="RevenuesDataGrid" Grid.Row="0" 
                                 AutoGenerateColumns="False" 
                                 CanUserAddRows="False"
                                 CanUserDeleteRows="False"
                                 IsReadOnly="True"
                                 GridLinesVisibility="Horizontal"
                                 HeadersVisibility="Column"
                                 AlternatingRowBackground="#F9F9F9"
                                 RowHeight="45"
                                 FontSize="12">
                            
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="المرجع" Binding="{Binding Reference}" Width="120"/>
                                <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="200"/>
                                <DataGridTextColumn Header="المبلغ" Binding="{Binding AmountDisplay}" Width="100"/>
                                <DataGridTextColumn Header="التاريخ" Binding="{Binding RevenueDateDisplay}" Width="100"/>
                                <DataGridTextColumn Header="النوع" Binding="{Binding TypeDisplay}" Width="120"/>
                                <DataGridTextColumn Header="الموكل" Binding="{Binding ClientName}" Width="150"/>
                                <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumberDisplay}" Width="120"/>
                                <DataGridTextColumn Header="الحالة" Binding="{Binding PaymentStatusDisplay}" Width="100"/>
                                <DataGridTextColumn Header="تاريخ الاستحقاق" Binding="{Binding DueDateDisplay}" Width="120"/>
                                
                                <DataGridTemplateColumn Header="الإجراءات" Width="200">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                <Button Style="{StaticResource ActionButtonStyle}" 
                                                       ToolTip="عرض التفاصيل" 
                                                       Click="ViewRevenueButton_Click" 
                                                       Tag="{Binding Id}">
                                                    <TextBlock Text="👁️" FontSize="14"/>
                                                </Button>
                                                <Button Style="{StaticResource ActionButtonStyle}" 
                                                       ToolTip="تعديل" 
                                                       Click="EditRevenueButton_Click" 
                                                       Tag="{Binding Id}">
                                                    <TextBlock Text="✏️" FontSize="14"/>
                                                </Button>
                                                <Button Style="{StaticResource ActionButtonStyle}" 
                                                       ToolTip="إرسال فاتورة" 
                                                       Click="SendInvoiceButton_Click" 
                                                       Tag="{Binding Id}">
                                                    <TextBlock Text="📧" FontSize="14"/>
                                                </Button>
                                                <Button Style="{StaticResource ActionButtonStyle}" 
                                                       ToolTip="تحصيل" 
                                                       Click="CollectRevenueButton_Click" 
                                                       Tag="{Binding Id}">
                                                    <TextBlock Text="💰" FontSize="14"/>
                                                </Button>
                                                <Button Style="{StaticResource ActionButtonStyle}" 
                                                       ToolTip="حذف" 
                                                       Click="DeleteRevenueButton_Click" 
                                                       Tag="{Binding Id}">
                                                    <TextBlock Text="🗑️" FontSize="14" Foreground="Red"/>
                                                </Button>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>

                        <!-- تذييل جدول الإيرادات -->
                        <Border Grid.Row="1" Background="#F8F9FA" Padding="15" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
                            <TextBlock Name="RevenuesStatusTextBlock" Text="جاري التحميل..." FontSize="12" Foreground="#666"/>
                        </Border>
                    </Grid>
                </TabItem>

                <!-- تبويب التقارير والإحصائيات -->
                <TabItem>
                    <TabItem.Header>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📊" FontSize="16" Margin="0,0,5,0"/>
                            <TextBlock Text="التقارير والإحصائيات" Style="{StaticResource TabHeaderStyle}"/>
                        </StackPanel>
                    </TabItem.Header>
                    
                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- رسم بياني للإيرادات والمصاريف الشهرية -->
                            <Border Grid.Column="0" Grid.Row="0" Style="{StaticResource ChartCardStyle}">
                                <StackPanel>
                                    <TextBlock Text="📈 الإيرادات والمصاريف الشهرية" FontSize="16" FontWeight="Bold" 
                                              Foreground="#1976D2" Margin="0,0,0,15"/>
                                    <ScrollViewer Name="MonthlyChart" Height="200" VerticalScrollBarVisibility="Auto">
                                        <StackPanel Name="MonthlyChartPanel"/>
                                    </ScrollViewer>
                                </StackPanel>
                            </Border>

                            <!-- رسم بياني لتوزيع المصاريف -->
                            <Border Grid.Column="1" Grid.Row="0" Style="{StaticResource ChartCardStyle}">
                                <StackPanel>
                                    <TextBlock Text="🥧 توزيع المصاريف حسب الفئة" FontSize="16" FontWeight="Bold" 
                                              Foreground="#1976D2" Margin="0,0,0,15"/>
                                    <ScrollViewer Name="ExpenseCategoryChart" Height="200" VerticalScrollBarVisibility="Auto">
                                        <StackPanel Name="ExpenseCategoryChartPanel"/>
                                    </ScrollViewer>
                                </StackPanel>
                            </Border>

                            <!-- رسم بياني لتوزيع الإيرادات -->
                            <Border Grid.Column="0" Grid.Row="1" Style="{StaticResource ChartCardStyle}">
                                <StackPanel>
                                    <TextBlock Text="💰 توزيع الإيرادات حسب النوع" FontSize="16" FontWeight="Bold" 
                                              Foreground="#1976D2" Margin="0,0,0,15"/>
                                    <ScrollViewer Name="RevenueTypeChart" Height="200" VerticalScrollBarVisibility="Auto">
                                        <StackPanel Name="RevenueTypeChartPanel"/>
                                    </ScrollViewer>
                                </StackPanel>
                            </Border>

                            <!-- ملخص الربحية -->
                            <Border Grid.Column="1" Grid.Row="1" Style="{StaticResource ChartCardStyle}">
                                <StackPanel>
                                    <TextBlock Text="📊 ملخص الربحية" FontSize="16" FontWeight="Bold" 
                                              Foreground="#1976D2" Margin="0,0,0,15"/>
                                    <StackPanel Name="ProfitabilitySummaryPanel">
                                        <!-- سيتم ملؤها ديناميكياً -->
                                    </StackPanel>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </ScrollViewer>
                </TabItem>
            </TabControl>
        </Grid>
    </Grid>
</Page>
