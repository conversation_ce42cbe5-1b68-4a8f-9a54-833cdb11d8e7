<Page x:Class="AvocatPro.Views.Pages.ComprehensiveClientsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="إدارة الموكلين الشاملة"
      Background="#F5F7FA"
      FlowDirection="RightToLeft">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان والإجراءات -->
        <Border Grid.Row="0" Background="White" CornerRadius="16" Padding="24" Margin="0,0,0,24">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="20" ShadowDepth="0" Opacity="0.4"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Border Background="#6366F1" CornerRadius="12" Padding="12" Margin="0,0,16,0">
                        <TextBlock Text="👥" FontSize="24" Foreground="White"/>
                    </Border>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock Text="إدارة الموكلين الشاملة" FontSize="28" FontWeight="Bold" 
                                   Foreground="#1F2937" Margin="0,0,0,4"/>
                        <TextBlock Text="إدارة شاملة لقاعدة بيانات الموكلين مع جميع الميزات المتقدمة" FontSize="14" 
                                   Foreground="#6B7280"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Background="#6366F1" Foreground="White" BorderThickness="0" 
                            Padding="20,12" FontSize="14" FontWeight="SemiBold" Cursor="Hand" 
                            Click="AddClient_Click" Margin="0,0,12,0">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}" CornerRadius="12" 
                                        Padding="{TemplateBinding Padding}">
                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                            </ControlTemplate>
                        </Button.Template>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="➕" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="إضافة موكل"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Background="#10B981" Foreground="White" BorderThickness="0" 
                            Padding="16,12" FontSize="14" Cursor="Hand" Click="ExportClients_Click" Margin="0,0,12,0">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}" CornerRadius="12" 
                                        Padding="{TemplateBinding Padding}">
                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                            </ControlTemplate>
                        </Button.Template>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📊" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تصدير"/>
                        </StackPanel>
                    </Button>

                    <Button Background="#8B5CF6" Foreground="White" BorderThickness="0" 
                            Padding="16,12" FontSize="14" Cursor="Hand" Click="PrintClients_Click">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}" CornerRadius="12" 
                                        Padding="{TemplateBinding Padding}">
                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                            </ControlTemplate>
                        </Button.Template>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🖨️" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="طباعة"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- بطاقات الإحصائيات -->
        <Grid Grid.Row="1" Margin="0,0,0,24">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- إجمالي الموكلين -->
            <Border Grid.Column="0" Background="White" CornerRadius="16" Padding="20" Margin="0,0,12,0">
                <Border.Effect>
                    <DropShadowEffect Color="#E2E8F0" BlurRadius="15" ShadowDepth="0" Opacity="0.3"/>
                </Border.Effect>
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="إجمالي الموكلين" FontSize="14" Foreground="#6B7280" Margin="0,0,0,8"/>
                            <TextBlock x:Name="TotalClientsText" Text="0" FontSize="32" FontWeight="Bold" Foreground="#1F2937"/>
                        </StackPanel>
                        <Border Grid.Column="1" Background="#EEF2FF" CornerRadius="12" Padding="12">
                            <TextBlock Text="👥" FontSize="20" Foreground="#6366F1"/>
                        </Border>
                    </Grid>
                    <TextBlock x:Name="TotalClientsChangeText" Text="↗️ +0% من الشهر الماضي" FontSize="12" Foreground="#10B981" Margin="0,8,0,0"/>
                </StackPanel>
            </Border>

            <!-- الموكلين النشطين -->
            <Border Grid.Column="1" Background="White" CornerRadius="16" Padding="20" Margin="6,0">
                <Border.Effect>
                    <DropShadowEffect Color="#E2E8F0" BlurRadius="15" ShadowDepth="0" Opacity="0.3"/>
                </Border.Effect>
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="الموكلين النشطين" FontSize="14" Foreground="#6B7280" Margin="0,0,0,8"/>
                            <TextBlock x:Name="ActiveClientsText" Text="0" FontSize="32" FontWeight="Bold" Foreground="#1F2937"/>
                        </StackPanel>
                        <Border Grid.Column="1" Background="#ECFDF5" CornerRadius="12" Padding="12">
                            <TextBlock Text="✅" FontSize="20" Foreground="#10B981"/>
                        </Border>
                    </Grid>
                    <TextBlock x:Name="ActiveClientsPercentText" Text="0% من إجمالي الموكلين" FontSize="12" Foreground="#6B7280" Margin="0,8,0,0"/>
                </StackPanel>
            </Border>

            <!-- الشركات -->
            <Border Grid.Column="2" Background="White" CornerRadius="16" Padding="20" Margin="6,0">
                <Border.Effect>
                    <DropShadowEffect Color="#E2E8F0" BlurRadius="15" ShadowDepth="0" Opacity="0.3"/>
                </Border.Effect>
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="الشركات" FontSize="14" Foreground="#6B7280" Margin="0,0,0,8"/>
                            <TextBlock x:Name="CompaniesText" Text="0" FontSize="32" FontWeight="Bold" Foreground="#1F2937"/>
                        </StackPanel>
                        <Border Grid.Column="1" Background="#FEF3C7" CornerRadius="12" Padding="12">
                            <TextBlock Text="🏢" FontSize="20" Foreground="#F59E0B"/>
                        </Border>
                    </Grid>
                    <TextBlock x:Name="CompaniesPercentText" Text="0% من إجمالي الموكلين" FontSize="12" Foreground="#6B7280" Margin="0,8,0,0"/>
                </StackPanel>
            </Border>

            <!-- القضايا النشطة -->
            <Border Grid.Column="3" Background="White" CornerRadius="16" Padding="20" Margin="12,0,0,0">
                <Border.Effect>
                    <DropShadowEffect Color="#E2E8F0" BlurRadius="15" ShadowDepth="0" Opacity="0.3"/>
                </Border.Effect>
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="القضايا النشطة" FontSize="14" Foreground="#6B7280" Margin="0,0,0,8"/>
                            <TextBlock x:Name="ActiveCasesText" Text="0" FontSize="32" FontWeight="Bold" Foreground="#1F2937"/>
                        </StackPanel>
                        <Border Grid.Column="1" Background="#FEE2E2" CornerRadius="12" Padding="12">
                            <TextBlock Text="⚖️" FontSize="20" Foreground="#EF4444"/>
                        </Border>
                    </Grid>
                    <TextBlock x:Name="ActiveCasesChangeText" Text="↗️ +0 قضايا جديدة" FontSize="12" Foreground="#10B981" Margin="0,8,0,0"/>
                </StackPanel>
            </Border>
        </Grid>

        <!-- شريط البحث والتصفية -->
        <Border Grid.Row="2" Background="White" CornerRadius="16" Padding="24" Margin="0,0,0,24">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="20" ShadowDepth="0" Opacity="0.4"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- مربع البحث -->
                <Grid Grid.Column="0" Margin="0,0,16,0">
                    <TextBox x:Name="SearchTextBox" Background="#F8FAFC" BorderBrush="#E2E8F0" 
                             BorderThickness="1" Padding="16,12" FontSize="14" 
                             TextChanged="SearchTextBox_TextChanged">
                        <TextBox.Template>
                            <ControlTemplate TargetType="TextBox">
                                <Border Background="{TemplateBinding Background}" 
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        CornerRadius="12">
                                    <ScrollViewer x:Name="PART_ContentHost" 
                                                  Margin="{TemplateBinding Padding}"
                                                  VerticalAlignment="Center"/>
                                </Border>
                            </ControlTemplate>
                        </TextBox.Template>
                    </TextBox>
                    <TextBlock Text="🔍 البحث في الموكلين (الاسم، الهاتف، البريد)..." FontSize="14" Foreground="#9CA3AF" 
                               Margin="20,0,0,0" VerticalAlignment="Center" IsHitTestVisible="False">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock">
                                <Setter Property="Visibility" Value="Collapsed"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding Text, ElementName=SearchTextBox}" Value="">
                                        <Setter Property="Visibility" Value="Visible"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </TextBlock.Style>
                    </TextBlock>
                </Grid>

                <!-- تصفية نوع الموكل -->
                <ComboBox Grid.Column="1" x:Name="ClientTypeFilter" FontSize="14" Padding="16,12" 
                          Margin="0,0,16,0" SelectionChanged="ClientTypeFilter_SelectionChanged">
                    <ComboBoxItem Content="جميع الأنواع"/>
                    <ComboBoxItem Content="أفراد"/>
                    <ComboBoxItem Content="شركات"/>
                </ComboBox>

                <!-- تصفية الحالة -->
                <ComboBox Grid.Column="2" x:Name="StatusFilter" FontSize="14" Padding="16,12" 
                          Margin="0,0,16,0" SelectionChanged="StatusFilter_SelectionChanged">
                    <ComboBoxItem Content="جميع الحالات"/>
                    <ComboBoxItem Content="نشط"/>
                    <ComboBoxItem Content="غير نشط"/>
                </ComboBox>

                <!-- زر التحديث -->
                <Button Grid.Column="3" Background="#F3F4F6" Foreground="#374151" BorderThickness="0" 
                        Padding="16,12" FontSize="14" Cursor="Hand" Click="RefreshClients_Click">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}" CornerRadius="12" 
                                    Padding="{TemplateBinding Padding}">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                        </ControlTemplate>
                    </Button.Template>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🔄" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="تحديث"/>
                    </StackPanel>
                </Button>
            </Grid>
        </Border>

        <!-- جدول الموكلين -->
        <Border Grid.Row="3" Background="White" CornerRadius="16" Padding="24">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="20" ShadowDepth="0" Opacity="0.4"/>
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- عنوان الجدول -->
                <Grid Grid.Row="0" Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="قائمة الموكلين" FontSize="20" FontWeight="Bold" 
                               Foreground="#1F2937" VerticalAlignment="Center"/>
                    
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button Background="#F59E0B" Foreground="White" BorderThickness="0" 
                                Padding="12,8" FontSize="12" Cursor="Hand" Click="ExportExcel_Click" Margin="0,0,8,0">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}" CornerRadius="8" 
                                            Padding="{TemplateBinding Padding}">
                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                </ControlTemplate>
                            </Button.Template>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📋" FontSize="14" Margin="0,0,6,0"/>
                                <TextBlock Text="Excel"/>
                            </StackPanel>
                        </Button>

                        <Button Background="#DC2626" Foreground="White" BorderThickness="0" 
                                Padding="12,8" FontSize="12" Cursor="Hand" Click="ExportPdf_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}" CornerRadius="8" 
                                            Padding="{TemplateBinding Padding}">
                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                </ControlTemplate>
                            </Button.Template>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📄" FontSize="14" Margin="0,0,6,0"/>
                                <TextBlock Text="PDF"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>

                <!-- الجدول -->
                <DataGrid Grid.Row="1" x:Name="ClientsDataGrid" AutoGenerateColumns="False"
                          CanUserAddRows="False" CanUserDeleteRows="False" CanUserReorderColumns="True"
                          GridLinesVisibility="None" HeadersVisibility="Column"
                          Background="Transparent" BorderThickness="0"
                          RowBackground="Transparent" AlternatingRowBackground="#F9FAFB"
                          FontSize="14" RowHeight="70" SelectionMode="Single">

                    <DataGrid.ColumnHeaderStyle>
                        <Style TargetType="DataGridColumnHeader">
                            <Setter Property="Background" Value="#F8FAFC"/>
                            <Setter Property="Foreground" Value="#374151"/>
                            <Setter Property="FontWeight" Value="SemiBold"/>
                            <Setter Property="FontSize" Value="14"/>
                            <Setter Property="Padding" Value="16,12"/>
                            <Setter Property="BorderThickness" Value="0,0,0,1"/>
                            <Setter Property="BorderBrush" Value="#E5E7EB"/>
                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                        </Style>
                    </DataGrid.ColumnHeaderStyle>

                    <DataGrid.CellStyle>
                        <Style TargetType="DataGridCell">
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="Padding" Value="16,8"/>
                            <Setter Property="VerticalAlignment" Value="Center"/>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="DataGridCell">
                                        <Border Background="{TemplateBinding Background}"
                                                Padding="{TemplateBinding Padding}">
                                            <ContentPresenter VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </DataGrid.CellStyle>

                    <DataGrid.Columns>
                        <!-- الصورة الشخصية -->
                        <DataGridTemplateColumn Header="الصورة" Width="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border Background="#6366F1" CornerRadius="25" Width="50" Height="50">
                                        <TextBlock Text="{Binding InitialLetter}" FontSize="18" FontWeight="Bold"
                                                   Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- اسم الموكل -->
                        <DataGridTemplateColumn Header="اسم الموكل" Width="220">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel>
                                        <TextBlock Text="{Binding Name}" FontWeight="SemiBold" FontSize="15" Foreground="#1F2937"/>
                                        <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                                            <Border Background="#EEF2FF" CornerRadius="8" Padding="6,2" Margin="0,0,4,0">
                                                <TextBlock Text="{Binding ClientType}" FontSize="11" Foreground="#6366F1" FontWeight="Medium"/>
                                            </Border>
                                            <TextBlock Text="{Binding OfficeRef}" FontSize="11" Foreground="#6B7280"/>
                                        </StackPanel>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- معلومات الاتصال -->
                        <DataGridTemplateColumn Header="معلومات الاتصال" Width="250">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel>
                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                                            <TextBlock Text="📞" FontSize="12" Margin="0,0,6,0"/>
                                            <TextBlock Text="{Binding Phone}" FontSize="13" Foreground="#374151"/>
                                        </StackPanel>
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="📧" FontSize="12" Margin="0,0,6,0"/>
                                            <TextBlock Text="{Binding Email}" FontSize="13" Foreground="#374151"/>
                                        </StackPanel>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- العنوان -->
                        <DataGridTemplateColumn Header="العنوان" Width="200">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="📍" FontSize="12" Margin="0,0,6,0"/>
                                        <TextBlock Text="{Binding Address}" FontSize="13" Foreground="#374151"
                                                   TextWrapping="Wrap" MaxWidth="180"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- عدد القضايا -->
                        <DataGridTemplateColumn Header="القضايا" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border Background="#EEF2FF" CornerRadius="12" Padding="8,4">
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="{Binding CasesCount}" FontWeight="Bold" FontSize="16"
                                                       Foreground="#6366F1" HorizontalAlignment="Center"/>
                                            <TextBlock Text="قضية" FontSize="10" Foreground="#6366F1" HorizontalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- تاريخ الإضافة -->
                        <DataGridTemplateColumn Header="تاريخ الإضافة" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel>
                                        <TextBlock Text="{Binding CreatedDate, StringFormat=dd/MM/yyyy}" FontSize="13" Foreground="#374151"/>
                                        <TextBlock Text="{Binding CreatedDate, StringFormat=HH:mm}" FontSize="11" Foreground="#6B7280"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- الحالة -->
                        <DataGridTemplateColumn Header="الحالة" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="12" Padding="12,6">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Status}" Value="نشط">
                                                        <Setter Property="Background" Value="#ECFDF5"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="غير نشط">
                                                        <Setter Property="Background" Value="#FEF2F2"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock Text="{Binding Status}" FontSize="12" FontWeight="SemiBold"
                                                   HorizontalAlignment="Center">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Status}" Value="نشط">
                                                            <Setter Property="Foreground" Value="#10B981"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="غير نشط">
                                                            <Setter Property="Foreground" Value="#EF4444"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- الإجراءات -->
                        <DataGridTemplateColumn Header="الإجراءات" Width="180">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button Background="#3B82F6" Foreground="White" BorderThickness="0"
                                                Padding="8,6" Margin="0,0,4,0" Cursor="Hand"
                                                Click="ViewClient_Click" Tag="{Binding}" ToolTip="عرض التفاصيل">
                                            <Button.Template>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}" CornerRadius="6"
                                                            Padding="{TemplateBinding Padding}">
                                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Button.Template>
                                            <TextBlock Text="👁️" FontSize="12"/>
                                        </Button>

                                        <Button Background="#F59E0B" Foreground="White" BorderThickness="0"
                                                Padding="8,6" Margin="2,0" Cursor="Hand"
                                                Click="EditClient_Click" Tag="{Binding}" ToolTip="تعديل">
                                            <Button.Template>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}" CornerRadius="6"
                                                            Padding="{TemplateBinding Padding}">
                                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Button.Template>
                                            <TextBlock Text="✏️" FontSize="12"/>
                                        </Button>

                                        <Button Background="#10B981" Foreground="White" BorderThickness="0"
                                                Padding="8,6" Margin="2,0" Cursor="Hand"
                                                Click="ViewCases_Click" Tag="{Binding}" ToolTip="عرض القضايا">
                                            <Button.Template>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}" CornerRadius="6"
                                                            Padding="{TemplateBinding Padding}">
                                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Button.Template>
                                            <TextBlock Text="⚖️" FontSize="12"/>
                                        </Button>

                                        <Button Background="#EF4444" Foreground="White" BorderThickness="0"
                                                Padding="8,6" Margin="4,0,0,0" Cursor="Hand"
                                                Click="DeleteClient_Click" Tag="{Binding}" ToolTip="حذف">
                                            <Button.Template>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}" CornerRadius="6"
                                                            Padding="{TemplateBinding Padding}">
                                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Button.Template>
                                            <TextBlock Text="🗑️" FontSize="12"/>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- شريط التنقل -->
                <Grid Grid.Row="2" Margin="0,20,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" x:Name="ResultsCountText" Text="عرض 0-0 من 0 موكل"
                               FontSize="14" Foreground="#6B7280" VerticalAlignment="Center"/>

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button Background="#F3F4F6" Foreground="#374151" BorderThickness="0"
                                Padding="12,8" FontSize="14" Cursor="Hand" Click="PreviousPage_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}" CornerRadius="8"
                                            Padding="{TemplateBinding Padding}">
                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                </ControlTemplate>
                            </Button.Template>
                            <TextBlock Text="السابق"/>
                        </Button>

                        <TextBlock x:Name="CurrentPageText" Text="1" FontSize="14" FontWeight="Bold" Foreground="#6366F1"
                                   Margin="16,0" VerticalAlignment="Center"/>

                        <Button Background="#F3F4F6" Foreground="#374151" BorderThickness="0"
                                Padding="12,8" FontSize="14" Cursor="Hand" Click="NextPage_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}" CornerRadius="8"
                                            Padding="{TemplateBinding Padding}">
                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                </ControlTemplate>
                            </Button.Template>
                            <TextBlock Text="التالي"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </Grid>
        </Border>
    </Grid>
</Page>
