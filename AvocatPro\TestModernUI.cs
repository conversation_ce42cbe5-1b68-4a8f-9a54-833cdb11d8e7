using System;
using System.Windows;
using AvocatPro.Views.Windows;

namespace AvocatPro
{
    /// <summary>
    /// فئة لاختبار الواجهة الحديثة
    /// </summary>
    public static class TestModernUI
    {
        /// <summary>
        /// تشغيل النافذة الرئيسية الحديثة للاختبار
        /// </summary>
        [STAThread]
        public static void Main()
        {
            try
            {
                // إنشاء تطبيق WPF
                var app = new Application();
                
                // تطبيق الأنماط
                app.Resources.MergedDictionaries.Add(
                    new ResourceDictionary 
                    { 
                        Source = new Uri("Resources/Styles/ModernStyles.xaml", UriKind.Relative) 
                    });

                // إنشاء النافذة الرئيسية الحديثة
                var modernMainWindow = new ModernMainWindow();
                
                // تشغيل التطبيق
                app.Run(modernMainWindow);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل الواجهة الحديثة: {ex.Message}\n\nتفاصيل الخطأ:\n{ex}", 
                               "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// اختبار صفحة لوحة التحكم فقط
        /// </summary>
        public static void TestDashboardPage()
        {
            try
            {
                var app = new Application();
                
                // تطبيق الأنماط
                app.Resources.MergedDictionaries.Add(
                    new ResourceDictionary 
                    { 
                        Source = new Uri("Resources/Styles/ModernStyles.xaml", UriKind.Relative) 
                    });

                // إنشاء نافذة بسيطة لعرض لوحة التحكم
                var testWindow = new Window
                {
                    Title = "اختبار لوحة التحكم الحديثة",
                    Width = 1200,
                    Height = 800,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen,
                    FlowDirection = FlowDirection.RightToLeft
                };

                var frame = new System.Windows.Controls.Frame();
                testWindow.Content = frame;
                
                // تحميل صفحة لوحة التحكم
                frame.Navigate(new AvocatPro.Views.Pages.ModernDashboardPage());
                
                app.Run(testWindow);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار لوحة التحكم: {ex.Message}", 
                               "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// اختبار القائمة الجانبية فقط
        /// </summary>
        public static void TestSidebar()
        {
            try
            {
                var app = new Application();
                
                // تطبيق الأنماط
                app.Resources.MergedDictionaries.Add(
                    new ResourceDictionary 
                    { 
                        Source = new Uri("Resources/Styles/ModernStyles.xaml", UriKind.Relative) 
                    });

                // إنشاء نافذة بسيطة لعرض القائمة الجانبية
                var testWindow = new Window
                {
                    Title = "اختبار القائمة الجانبية الحديثة",
                    Width = 400,
                    Height = 700,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen,
                    FlowDirection = FlowDirection.RightToLeft,
                    Background = System.Windows.Media.Brushes.LightGray
                };

                // إضافة القائمة الجانبية
                var sidebar = new AvocatPro.Views.Controls.ModernSidebarControl();
                testWindow.Content = sidebar;
                
                app.Run(testWindow);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار القائمة الجانبية: {ex.Message}", 
                               "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
