using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using AvocatPro.Models;
using AvocatPro.Services;
using AvocatPro.Helpers;
using RelayCommand = AvocatPro.Helpers.RelayCommand;

namespace AvocatPro.ViewModels;

/// <summary>
/// ViewModel لصفحة إدارة الموكلين
/// </summary>
public class ClientsPageViewModel : INotifyPropertyChanged
{
    private readonly IClientService _clientService;
    private readonly User _currentUser;
    
    private bool _isLoading = false;
    private string _searchTerm = string.Empty;
    private ClientType? _selectedClientType = null;
    private ClientStatus? _selectedClientStatus = null;
    private int _currentPage = 1;
    private int _pageSize = 20;
    private int _totalCount = 0;
    private ClientStatistics? _statistics;

    public ClientsPageViewModel(IClientService clientService, User currentUser)
    {
        _clientService = clientService;
        _currentUser = currentUser;
        
        Clients = new ObservableCollection<ClientDisplayModel>();
        
        // إنشاء الأوامر
        LoadClientsCommand = new RelayCommand(async () => await LoadClientsAsync());
        SearchCommand = new RelayCommand(async () => await SearchClientsAsync());
        AddClientCommand = new RelayCommand(() => AddClient());
        EditClientCommand = new RelayCommand<int>(id => EditClient(id));
        ViewClientCommand = new RelayCommand<int>(id => ViewClient(id));
        DeleteClientCommand = new RelayCommand<int>(async id => await DeleteClientAsync(id));
        ExportCommand = new RelayCommand(async () => await ExportClientsAsync());
        RefreshCommand = new RelayCommand(async () => await RefreshAsync());
        PreviousPageCommand = new RelayCommand(async () => await PreviousPageAsync(), () => CanGoPreviousPage);
        NextPageCommand = new RelayCommand(async () => await NextPageAsync(), () => CanGoNextPage);
        
        // تحميل البيانات الأولية
        _ = Task.Run(async () => await InitializeAsync());
    }

    #region Properties

    public ObservableCollection<ClientDisplayModel> Clients { get; }

    public bool IsLoading
    {
        get => _isLoading;
        set
        {
            if (SetProperty(ref _isLoading, value))
            {
                OnPropertyChanged(nameof(CanGoPreviousPage));
                OnPropertyChanged(nameof(CanGoNextPage));
            }
        }
    }

    public string SearchTerm
    {
        get => _searchTerm;
        set => SetProperty(ref _searchTerm, value);
    }

    public ClientType? SelectedClientType
    {
        get => _selectedClientType;
        set => SetProperty(ref _selectedClientType, value);
    }

    public ClientStatus? SelectedClientStatus
    {
        get => _selectedClientStatus;
        set => SetProperty(ref _selectedClientStatus, value);
    }

    public int CurrentPage
    {
        get => _currentPage;
        set
        {
            if (SetProperty(ref _currentPage, value))
            {
                OnPropertyChanged(nameof(CanGoPreviousPage));
                OnPropertyChanged(nameof(CanGoNextPage));
                OnPropertyChanged(nameof(PageInfo));
            }
        }
    }

    public int TotalCount
    {
        get => _totalCount;
        set
        {
            if (SetProperty(ref _totalCount, value))
            {
                OnPropertyChanged(nameof(TotalPages));
                OnPropertyChanged(nameof(CanGoNextPage));
                OnPropertyChanged(nameof(PageInfo));
                OnPropertyChanged(nameof(ResultsInfo));
            }
        }
    }

    public int TotalPages => (int)Math.Ceiling((double)TotalCount / _pageSize);

    public bool CanGoPreviousPage => !IsLoading && CurrentPage > 1;
    public bool CanGoNextPage => !IsLoading && CurrentPage < TotalPages;

    public string PageInfo => $"صفحة {CurrentPage} من {TotalPages}";
    public string ResultsInfo => $"عرض {Math.Min((CurrentPage - 1) * _pageSize + 1, TotalCount)} - {Math.Min(CurrentPage * _pageSize, TotalCount)} من {TotalCount} موكل";

    public ClientStatistics? Statistics
    {
        get => _statistics;
        set => SetProperty(ref _statistics, value);
    }

    #endregion

    #region Commands

    public ICommand LoadClientsCommand { get; }
    public ICommand SearchCommand { get; }
    public ICommand AddClientCommand { get; }
    public ICommand EditClientCommand { get; }
    public ICommand ViewClientCommand { get; }
    public ICommand DeleteClientCommand { get; }
    public ICommand ExportCommand { get; }
    public ICommand RefreshCommand { get; }
    public ICommand PreviousPageCommand { get; }
    public ICommand NextPageCommand { get; }

    #endregion

    #region Methods

    private async Task InitializeAsync()
    {
        await LoadStatisticsAsync();
        await LoadClientsAsync();
    }

    private async Task LoadClientsAsync()
    {
        try
        {
            IsLoading = true;

            var result = await _clientService.GetClientsPagedAsync(
                CurrentPage, _pageSize, SearchTerm, SelectedClientType, SelectedClientStatus);

            Clients.Clear();
            foreach (var client in result.Items)
            {
                Clients.Add(new ClientDisplayModel(client));
            }

            TotalCount = result.TotalCount;
        }
        catch (Exception ex)
        {
            // TODO: إظهار رسالة خطأ
            System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الموكلين: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task LoadStatisticsAsync()
    {
        try
        {
            Statistics = await _clientService.GetClientStatisticsAsync();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الإحصائيات: {ex.Message}");
        }
    }

    private async Task SearchClientsAsync()
    {
        CurrentPage = 1;
        await LoadClientsAsync();
    }

    private void AddClient()
    {
        ClientFormRequested?.Invoke(null);
    }

    private void EditClient(int clientId)
    {
        ClientFormRequested?.Invoke(clientId);
    }

    private void ViewClient(int clientId)
    {
        ClientViewRequested?.Invoke(clientId);
    }

    private async Task DeleteClientAsync(int clientId)
    {
        try
        {
            var client = Clients.FirstOrDefault(c => c.Id == clientId);
            if (client == null) return;

            var confirmed = await ConfirmDeleteAsync(client.FullName);
            if (!confirmed) return;

            IsLoading = true;

            var success = await _clientService.DeleteClientAsync(clientId, _currentUser.Username);
            if (success)
            {
                await LoadClientsAsync();
                await LoadStatisticsAsync();
                ClientDeleted?.Invoke(clientId);
            }
        }
        catch (Exception ex)
        {
            ErrorOccurred?.Invoke($"خطأ في حذف الموكل: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task ExportClientsAsync()
    {
        try
        {
            IsLoading = true;
            
            var allClients = await _clientService.GetAllClientsAsync();
            ExportRequested?.Invoke(allClients);
        }
        catch (Exception ex)
        {
            ErrorOccurred?.Invoke($"خطأ في تصدير البيانات: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task RefreshAsync()
    {
        await LoadStatisticsAsync();
        await LoadClientsAsync();
    }

    private async Task PreviousPageAsync()
    {
        if (CanGoPreviousPage)
        {
            CurrentPage--;
            await LoadClientsAsync();
        }
    }

    private async Task NextPageAsync()
    {
        if (CanGoNextPage)
        {
            CurrentPage++;
            await LoadClientsAsync();
        }
    }

    private async Task<bool> ConfirmDeleteAsync(string clientName)
    {
        // TODO: إظهار نافذة تأكيد
        return await Task.FromResult(true);
    }

    public async Task OnClientTypeFilterChanged(ClientType? clientType)
    {
        SelectedClientType = clientType;
        CurrentPage = 1;
        await LoadClientsAsync();
    }

    public async Task OnClientStatusFilterChanged(ClientStatus? status)
    {
        SelectedClientStatus = status;
        CurrentPage = 1;
        await LoadClientsAsync();
    }

    #endregion

    #region Events

    public event Action<int?>? ClientFormRequested;
    public event Action<int>? ClientViewRequested;
    public event Action<int>? ClientDeleted;
    public event Action<List<Client>>? ExportRequested;
    public event Action<string>? ErrorOccurred;

    #endregion

    #region INotifyPropertyChanged

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value))
            return false;

        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }

    #endregion
}

/// <summary>
/// نموذج عرض الموكل
/// </summary>
public class ClientDisplayModel
{
    public ClientDisplayModel(Client client)
    {
        Id = client.Id;
        OfficeReference = client.OfficeReference;
        FullName = client.FullName;
        Type = client.Type;
        Phone = client.Phone;
        Email = client.Email;
        City = client.City;
        Status = client.Status;
        CreatedAt = client.CreatedAt;
    }

    public int Id { get; set; }
    public string OfficeReference { get; set; }
    public string FullName { get; set; }
    public ClientType Type { get; set; }
    public string Phone { get; set; }
    public string? Email { get; set; }
    public string? City { get; set; }
    public ClientStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }

    public string TypeDisplay => Type switch
    {
        ClientType.Individual => "فرد",
        ClientType.Company => "شركة",
        ClientType.Government => "حكومية",
        ClientType.Association => "جمعية",
        _ => "غير محدد"
    };

    public string StatusDisplay => Status switch
    {
        ClientStatus.Active => "نشط",
        ClientStatus.Inactive => "غير نشط",
        ClientStatus.Blocked => "محظور",
        ClientStatus.Deceased => "متوفى",
        _ => "غير محدد"
    };

    public string StatusColor => Status switch
    {
        ClientStatus.Active => "#4CAF50",
        ClientStatus.Inactive => "#FF9800",
        ClientStatus.Blocked => "#F44336",
        ClientStatus.Deceased => "#9E9E9E",
        _ => "#9E9E9E"
    };
}
