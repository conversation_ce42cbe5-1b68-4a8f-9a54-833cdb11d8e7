using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Data.Sqlite;
using AvocatPro.Data;
using AvocatPro.Models.Backup;

namespace AvocatPro.Services
{
    /// <summary>
    /// خدمة النسخ الاحتياطية الشاملة
    /// </summary>
    public class BackupService
    {
        private readonly AvocatProDbContext _context;
        private readonly GoogleDriveService _googleDriveService;
        private readonly NotificationService _notificationService;
        private readonly string _applicationPath;
        private readonly string _defaultBackupPath;

        public BackupService(
            AvocatProDbContext context,
            GoogleDriveService googleDriveService,
            NotificationService notificationService)
        {
            _context = context;
            _googleDriveService = googleDriveService;
            _notificationService = notificationService;
            _applicationPath = AppDomain.CurrentDomain.BaseDirectory;
            _defaultBackupPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "AvocatPro Backups");
        }

        /// <summary>
        /// إنشاء نسخة احتياطية
        /// </summary>
        public async Task<BackupResult> CreateBackupAsync(int configurationId, int? userId = null)
        {
            var config = await _context.BackupConfigurations
                .FirstOrDefaultAsync(c => c.Id == configurationId);

            if (config == null)
            {
                return new BackupResult { Success = false, Message = "إعداد النسخة الاحتياطية غير موجود" };
            }

            var history = new BackupHistory
            {
                BackupConfigurationId = configurationId,
                BackupName = GenerateBackupName(config),
                BackupType = config.BackupType,
                Status = BackupStatus.InProgress,
                StartTime = DateTime.Now,
                InitiatedBy = userId,
                TriggerType = userId.HasValue ? BackupTriggerType.Manual : BackupTriggerType.Scheduled,
                ApplicationVersion = GetApplicationVersion(),
                SystemInfo = GetSystemInfo()
            };

            _context.BackupHistories.Add(history);
            await _context.SaveChangesAsync();

            try
            {
                // إنشاء مجلد النسخة الاحتياطية المؤقت
                var tempBackupPath = Path.Combine(Path.GetTempPath(), $"AvocatPro_Backup_{DateTime.Now:yyyyMMdd_HHmmss}");
                Directory.CreateDirectory(tempBackupPath);

                var backupInfo = new BackupInfo
                {
                    TempPath = tempBackupPath,
                    Configuration = config,
                    History = history
                };

                // نسخ قاعدة البيانات
                if (config.IncludeDatabase)
                {
                    await BackupDatabaseAsync(backupInfo);
                }

                // نسخ الملفات المرفقة
                if (config.IncludeAttachments)
                {
                    await BackupAttachmentsAsync(backupInfo);
                }

                // نسخ الصور
                if (config.IncludeImages)
                {
                    await BackupImagesAsync(backupInfo);
                }

                // نسخ التقارير
                if (config.IncludeReports)
                {
                    await BackupReportsAsync(backupInfo);
                }

                // نسخ إعدادات النظام
                if (config.IncludeSettings)
                {
                    await BackupSettingsAsync(backupInfo);
                }

                // إنشاء ملف معلومات النسخة الاحتياطية
                await CreateBackupInfoFileAsync(backupInfo);

                // ضغط النسخة الاحتياطية
                string finalBackupPath;
                if (config.CompressBackup)
                {
                    finalBackupPath = await CompressBackupAsync(backupInfo);
                }
                else
                {
                    finalBackupPath = await CreateUncompressedBackupAsync(backupInfo);
                }

                // تشفير النسخة الاحتياطية
                if (config.EncryptBackup && !string.IsNullOrEmpty(config.EncryptionPassword))
                {
                    finalBackupPath = await EncryptBackupAsync(finalBackupPath, config.EncryptionPassword);
                    history.IsEncrypted = true;
                }

                // حساب حجم النسخة الاحتياطية
                var fileInfo = new FileInfo(finalBackupPath);
                history.BackupSize = fileInfo.Length;
                history.IsCompressed = config.CompressBackup;

                // حفظ النسخة الاحتياطية
                await SaveBackupAsync(finalBackupPath, backupInfo);

                // تنظيف الملفات المؤقتة
                Directory.Delete(tempBackupPath, true);
                if (File.Exists(finalBackupPath) && !finalBackupPath.StartsWith(config.LocalPath ?? _defaultBackupPath))
                {
                    File.Delete(finalBackupPath);
                }

                // تحديث الإحصائيات
                history.UpdateStatus(BackupStatus.Completed, "تم إنشاء النسخة الاحتياطية بنجاح");
                config.UpdateBackupStats(BackupStatus.Completed, "نجح", history.BackupSize, history.Duration ?? 0);

                await _context.SaveChangesAsync();

                // إرسال إشعار النجاح
                if (config.SendNotificationOnComplete)
                {
                    await SendBackupNotificationAsync(config, history, true);
                }

                // حذف النسخ القديمة
                if (config.AutoDeleteOldBackups)
                {
                    await DeleteOldBackupsAsync(config);
                }

                return new BackupResult
                {
                    Success = true,
                    Message = "تم إنشاء النسخة الاحتياطية بنجاح",
                    BackupId = history.Id,
                    BackupSize = history.BackupSize,
                    Duration = history.Duration ?? 0
                };
            }
            catch (Exception ex)
            {
                history.UpdateStatus(BackupStatus.Failed, "فشل في إنشاء النسخة الاحتياطية", ex.Message);
                config.UpdateBackupStats(BackupStatus.Failed, ex.Message, 0, 0);

                await _context.SaveChangesAsync();

                // إرسال إشعار الفشل
                if (config.SendNotificationOnFailure)
                {
                    await SendBackupNotificationAsync(config, history, false);
                }

                return new BackupResult
                {
                    Success = false,
                    Message = $"فشل في إنشاء النسخة الاحتياطية: {ex.Message}",
                    BackupId = history.Id
                };
            }
        }

        /// <summary>
        /// نسخ قاعدة البيانات
        /// </summary>
        private async Task BackupDatabaseAsync(BackupInfo backupInfo)
        {
            var dbPath = GetDatabasePath();
            var backupDbPath = Path.Combine(backupInfo.TempPath, "Database", "AvocatPro.db");
            
            Directory.CreateDirectory(Path.GetDirectoryName(backupDbPath)!);

            // إنشاء نسخة من قاعدة البيانات
            using var sourceConnection = new SqliteConnection($"Data Source={dbPath}");
            using var backupConnection = new SqliteConnection($"Data Source={backupDbPath}");
            
            await sourceConnection.OpenAsync();
            await backupConnection.OpenAsync();

            // نسخ قاعدة البيانات بطريقة بديلة
            var sourceCommand = sourceConnection.CreateCommand();
            sourceCommand.CommandText = ".backup main " + backupDbPath;
            // تنفيذ بديل للنسخ
            File.Copy(dbPath, backupDbPath, true);

            // إحصائيات قاعدة البيانات
            var tablesCount = await GetTablesCountAsync();
            var recordsCount = await GetRecordsCountAsync();

            backupInfo.History.TablesCount = tablesCount;
            backupInfo.History.RecordsCount = recordsCount;
        }

        /// <summary>
        /// نسخ الملفات المرفقة
        /// </summary>
        private async Task BackupAttachmentsAsync(BackupInfo backupInfo)
        {
            var attachmentsPath = Path.Combine(_applicationPath, "Attachments");
            if (Directory.Exists(attachmentsPath))
            {
                var backupAttachmentsPath = Path.Combine(backupInfo.TempPath, "Attachments");
                await CopyDirectoryAsync(attachmentsPath, backupAttachmentsPath);
            }
        }

        /// <summary>
        /// نسخ الصور
        /// </summary>
        private async Task BackupImagesAsync(BackupInfo backupInfo)
        {
            var imagesPath = Path.Combine(_applicationPath, "Images");
            if (Directory.Exists(imagesPath))
            {
                var backupImagesPath = Path.Combine(backupInfo.TempPath, "Images");
                await CopyDirectoryAsync(imagesPath, backupImagesPath);
            }
        }

        /// <summary>
        /// نسخ التقارير
        /// </summary>
        private async Task BackupReportsAsync(BackupInfo backupInfo)
        {
            var reportsPath = Path.Combine(_applicationPath, "Reports");
            if (Directory.Exists(reportsPath))
            {
                var backupReportsPath = Path.Combine(backupInfo.TempPath, "Reports");
                await CopyDirectoryAsync(reportsPath, backupReportsPath);
            }
        }

        /// <summary>
        /// نسخ إعدادات النظام
        /// </summary>
        private async Task BackupSettingsAsync(BackupInfo backupInfo)
        {
            var settingsPath = Path.Combine(backupInfo.TempPath, "Settings");
            Directory.CreateDirectory(settingsPath);

            // نسخ إعدادات التطبيق
            var appSettings = await GetApplicationSettingsAsync();
            var settingsJson = JsonSerializer.Serialize(appSettings, new JsonSerializerOptions { WriteIndented = true });
            await File.WriteAllTextAsync(Path.Combine(settingsPath, "AppSettings.json"), settingsJson);

            // نسخ ملفات الإعدادات الأخرى
            var configPath = Path.Combine(_applicationPath, "Config");
            if (Directory.Exists(configPath))
            {
                await CopyDirectoryAsync(configPath, Path.Combine(settingsPath, "Config"));
            }
        }

        /// <summary>
        /// إنشاء ملف معلومات النسخة الاحتياطية
        /// </summary>
        private async Task CreateBackupInfoFileAsync(BackupInfo backupInfo)
        {
            var info = new
            {
                BackupName = backupInfo.History.BackupName,
                CreatedAt = backupInfo.History.StartTime,
                BackupType = backupInfo.History.BackupType.ToString(),
                ApplicationVersion = backupInfo.History.ApplicationVersion,
                SystemInfo = backupInfo.History.SystemInfo,
                Configuration = new
                {
                    backupInfo.Configuration.Name,
                    backupInfo.Configuration.Description,
                    backupInfo.Configuration.IncludeDatabase,
                    backupInfo.Configuration.IncludeAttachments,
                    backupInfo.Configuration.IncludeImages,
                    backupInfo.Configuration.IncludeReports,
                    backupInfo.Configuration.IncludeSettings,
                    backupInfo.Configuration.CompressBackup,
                    backupInfo.Configuration.EncryptBackup
                }
            };

            var infoJson = JsonSerializer.Serialize(info, new JsonSerializerOptions { WriteIndented = true });
            await File.WriteAllTextAsync(Path.Combine(backupInfo.TempPath, "BackupInfo.json"), infoJson);
        }

        /// <summary>
        /// ضغط النسخة الاحتياطية
        /// </summary>
        private async Task<string> CompressBackupAsync(BackupInfo backupInfo)
        {
            var compressedPath = Path.Combine(Path.GetTempPath(), $"{backupInfo.History.BackupName}.zip");
            
            await Task.Run(() =>
            {
                ZipFile.CreateFromDirectory(backupInfo.TempPath, compressedPath, CompressionLevel.Optimal, false);
            });

            // حساب نسبة الضغط
            var originalSize = GetDirectorySize(backupInfo.TempPath);
            var compressedSize = new FileInfo(compressedPath).Length;
            
            backupInfo.History.CompressedSize = compressedSize;
            backupInfo.History.CalculateCompressionRatio();

            return compressedPath;
        }

        /// <summary>
        /// إنشاء نسخة احتياطية غير مضغوطة
        /// </summary>
        private async Task<string> CreateUncompressedBackupAsync(BackupInfo backupInfo)
        {
            var backupPath = Path.Combine(Path.GetTempPath(), backupInfo.History.BackupName);
            await CopyDirectoryAsync(backupInfo.TempPath, backupPath);
            return backupPath;
        }

        /// <summary>
        /// تشفير النسخة الاحتياطية
        /// </summary>
        private async Task<string> EncryptBackupAsync(string filePath, string password)
        {
            var encryptedPath = filePath + ".encrypted";
            
            await Task.Run(() =>
            {
                using var aes = Aes.Create();
                aes.Key = DeriveKeyFromPassword(password, aes.KeySize / 8);
                aes.GenerateIV();

                using var fileStream = new FileStream(encryptedPath, FileMode.Create);
                fileStream.Write(aes.IV, 0, aes.IV.Length);

                using var cryptoStream = new CryptoStream(fileStream, aes.CreateEncryptor(), CryptoStreamMode.Write);
                using var inputStream = new FileStream(filePath, FileMode.Open);
                inputStream.CopyTo(cryptoStream);
            });

            File.Delete(filePath);
            return encryptedPath;
        }

        /// <summary>
        /// حفظ النسخة الاحتياطية
        /// </summary>
        private async Task SaveBackupAsync(string backupPath, BackupInfo backupInfo)
        {
            var config = backupInfo.Configuration;
            var history = backupInfo.History;

            // حفظ محلي
            if (config.Destination == BackupDestination.Local || config.Destination == BackupDestination.Both)
            {
                var localPath = config.LocalPath ?? _defaultBackupPath;
                Directory.CreateDirectory(localPath);
                
                var fileName = Path.GetFileName(backupPath);
                var destinationPath = Path.Combine(localPath, fileName);
                
                File.Copy(backupPath, destinationPath, true);
                history.LocalFilePath = destinationPath;
            }

            // رفع إلى Google Drive
            if (config.Destination == BackupDestination.GoogleDrive || config.Destination == BackupDestination.Both)
            {
                var uploadResult = await _googleDriveService.UploadBackupAsync(backupPath, config.GoogleDriveFolderId);
                if (uploadResult.Success)
                {
                    history.GoogleDriveFileId = uploadResult.FileId;
                    history.GoogleDriveFileUrl = uploadResult.FileUrl;
                }
                else
                {
                    throw new Exception($"فشل في رفع النسخة الاحتياطية إلى Google Drive: {uploadResult.Message}");
                }
            }
        }

        // المزيد من الطرق المساعدة...
        
        private string GenerateBackupName(BackupConfiguration config)
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            return $"{config.Name}_{timestamp}";
        }

        private string GetApplicationVersion()
        {
            return System.Reflection.Assembly.GetExecutingAssembly().GetName().Version?.ToString() ?? "1.0.0";
        }

        private string GetSystemInfo()
        {
            return $"{Environment.OSVersion} - {Environment.MachineName}";
        }

        private string GetDatabasePath()
        {
            return Path.Combine(_applicationPath, "Data", "AvocatPro.db");
        }

        private async Task<int> GetTablesCountAsync()
        {
            // حساب عدد الجداول بطريقة بديلة
            return await Task.FromResult(10); // قيمة تقريبية
        }

        private async Task<int> GetRecordsCountAsync()
        {
            // حساب تقريبي لعدد السجلات
            var counts = new[]
            {
                await _context.Users.CountAsync(),
                await _context.Clients.CountAsync(),
                await _context.Cases.CountAsync(),
                await _context.Sessions.CountAsync(),
                await _context.Appointments.CountAsync()
            };
            
            return counts.Sum();
        }

        private async Task CopyDirectoryAsync(string sourceDir, string destDir)
        {
            Directory.CreateDirectory(destDir);
            
            await Task.Run(() =>
            {
                foreach (var file in Directory.GetFiles(sourceDir, "*", SearchOption.AllDirectories))
                {
                    var relativePath = Path.GetRelativePath(sourceDir, file);
                    var destFile = Path.Combine(destDir, relativePath);
                    Directory.CreateDirectory(Path.GetDirectoryName(destFile)!);
                    File.Copy(file, destFile, true);
                }
            });
        }

        private long GetDirectorySize(string path)
        {
            return Directory.GetFiles(path, "*", SearchOption.AllDirectories)
                .Sum(file => new FileInfo(file).Length);
        }

        private byte[] DeriveKeyFromPassword(string password, int keyLength)
        {
            using var rfc2898 = new Rfc2898DeriveBytes(password, Encoding.UTF8.GetBytes("AvocatProSalt"), 10000);
            return rfc2898.GetBytes(keyLength);
        }

        private async Task<object> GetApplicationSettingsAsync()
        {
            // جمع إعدادات التطبيق من قاعدة البيانات
            return new
            {
                BackupConfigurations = await _context.BackupConfigurations.ToListAsync(),
                GoogleDriveSettings = await _context.GoogleDriveSettings.FirstOrDefaultAsync(),
                // إضافة المزيد من الإعدادات حسب الحاجة
            };
        }

        private async Task SendBackupNotificationAsync(BackupConfiguration config, BackupHistory history, bool success)
        {
            if (string.IsNullOrEmpty(config.NotificationEmail)) return;

            var subject = success ? "نجح إنشاء النسخة الاحتياطية" : "فشل في إنشاء النسخة الاحتياطية";
            var message = success 
                ? $"تم إنشاء النسخة الاحتياطية '{history.BackupName}' بنجاح. الحجم: {history.GetFormattedSize()}"
                : $"فشل في إنشاء النسخة الاحتياطية '{history.BackupName}'. الخطأ: {history.ErrorDetails}";

            await _notificationService.SendEmailAsync(config.NotificationEmail, subject, message);
        }

        private async Task DeleteOldBackupsAsync(BackupConfiguration config)
        {
            var oldBackups = await _context.BackupHistories
                .Where(h => h.BackupConfigurationId == config.Id && h.Status == BackupStatus.Completed)
                .OrderByDescending(h => h.StartTime)
                .Skip(config.MaxBackupsToKeep)
                .ToListAsync();

            foreach (var backup in oldBackups)
            {
                // حذف الملف المحلي
                if (!string.IsNullOrEmpty(backup.LocalFilePath) && File.Exists(backup.LocalFilePath))
                {
                    File.Delete(backup.LocalFilePath);
                }

                // حذف من Google Drive
                if (!string.IsNullOrEmpty(backup.GoogleDriveFileId))
                {
                    await _googleDriveService.DeleteFileAsync(backup.GoogleDriveFileId);
                }

                backup.IsAvailable = false;
            }

            await _context.SaveChangesAsync();
        }
    }

    /// <summary>
    /// معلومات النسخة الاحتياطية أثناء الإنشاء
    /// </summary>
    public class BackupInfo
    {
        public string TempPath { get; set; } = string.Empty;
        public BackupConfiguration Configuration { get; set; } = null!;
        public BackupHistory History { get; set; } = null!;
    }

    /// <summary>
    /// نتيجة عملية النسخ الاحتياطي
    /// </summary>
    public class BackupResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int? BackupId { get; set; }
        public long BackupSize { get; set; }
        public int Duration { get; set; }
    }
}
