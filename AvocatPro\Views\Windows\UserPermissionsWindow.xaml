<Window x:Class="AvocatPro.Views.Windows.UserPermissionsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة صلاحيات المستخدم"
        Width="800" Height="600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
            <TextBlock Text="🔑" FontSize="24" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <StackPanel>
                <TextBlock Text="إدارة صلاحيات المستخدم" FontSize="20" FontWeight="Bold"/>
                <TextBlock Name="UserNameText" Text="اسم المستخدم" FontSize="14" Foreground="Gray"/>
            </StackPanel>
        </StackPanel>

        <!-- المحتوى -->
        <TabControl Grid.Row="1">
            <TabItem Header="الصلاحيات الفردية">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- الصلاحيات المتاحة -->
                    <GroupBox Grid.Column="0" Header="الصلاحيات المتاحة" Margin="0,0,10,0">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <TextBox Name="AvailablePermissionsSearchBox" Grid.Row="0" 
                                     Text="🔍 البحث في الصلاحيات..." Padding="10,8" Margin="0,0,0,10"/>

                            <TreeView Name="AvailablePermissionsTreeView" Grid.Row="1"/>
                        </Grid>
                    </GroupBox>

                    <!-- الصلاحيات الممنوحة -->
                    <GroupBox Grid.Column="1" Header="الصلاحيات الممنوحة" Margin="10,0,0,0">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <TextBox Name="GrantedPermissionsSearchBox" Grid.Row="0" 
                                     Text="🔍 البحث في الصلاحيات الممنوحة..." Padding="10,8" Margin="0,0,0,10"/>

                            <ListBox Name="GrantedPermissionsListBox" Grid.Row="1"/>
                        </Grid>
                    </GroupBox>
                </Grid>
            </TabItem>

            <TabItem Header="مجموعات الصلاحيات">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- المجموعات المتاحة -->
                    <GroupBox Grid.Column="0" Header="مجموعات الصلاحيات المتاحة" Margin="0,0,10,0">
                        <ListBox Name="AvailableGroupsListBox"/>
                    </GroupBox>

                    <!-- المجموعات المضافة -->
                    <GroupBox Grid.Column="1" Header="المجموعات المضافة للمستخدم" Margin="10,0,0,0">
                        <ListBox Name="UserGroupsListBox"/>
                    </GroupBox>
                </Grid>
            </TabItem>

            <TabItem Header="سجل الصلاحيات">
                <DataGrid Name="PermissionHistoryDataGrid" 
                          AutoGenerateColumns="False" 
                          IsReadOnly="True"
                          GridLinesVisibility="None"
                          HeadersVisibility="Column">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="التاريخ" Binding="{Binding Date}" Width="120"/>
                        <DataGridTextColumn Header="العملية" Binding="{Binding Action}" Width="100"/>
                        <DataGridTextColumn Header="الصلاحية" Binding="{Binding Permission}" Width="200"/>
                        <DataGridTextColumn Header="بواسطة" Binding="{Binding GrantedBy}" Width="150"/>
                        <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="*"/>
                    </DataGrid.Columns>
                </DataGrid>
            </TabItem>
        </TabControl>

        <!-- الأزرار -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Name="GrantPermissionButton" Content="➕ منح صلاحية" Padding="15,8" Margin="5" 
                    Background="#4CAF50" Foreground="White" BorderThickness="0" 
                    Click="GrantPermissionButton_Click"/>
            <Button Name="RevokePermissionButton" Content="➖ إلغاء صلاحية" Padding="15,8" Margin="5" 
                    Background="#F44336" Foreground="White" BorderThickness="0" 
                    Click="RevokePermissionButton_Click"/>
            <Button Name="AddToGroupButton" Content="👥 إضافة لمجموعة" Padding="15,8" Margin="5" 
                    Background="#2196F3" Foreground="White" BorderThickness="0" 
                    Click="AddToGroupButton_Click"/>
            <Button Name="RemoveFromGroupButton" Content="👥 إزالة من مجموعة" Padding="15,8" Margin="5" 
                    Background="#FF9800" Foreground="White" BorderThickness="0" 
                    Click="RemoveFromGroupButton_Click"/>
            <Button Name="CloseButton" Content="❌ إغلاق" Padding="15,8" Margin="5" 
                    Background="#9E9E9E" Foreground="White" BorderThickness="0" 
                    Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
