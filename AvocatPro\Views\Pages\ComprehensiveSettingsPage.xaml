<Page x:Class="AvocatPro.Views.Pages.ComprehensiveSettingsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="الإعدادات الشاملة" Background="#F8FAFC"
      FlowDirection="RightToLeft">

    <Page.Resources>
        <Style TargetType="TextBox">
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#D1D5DB"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Style.Triggers>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="BorderBrush" Value="#6366F1"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style TargetType="ComboBox">
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#D1D5DB"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style TargetType="CheckBox">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <Style TargetType="Label">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#374151"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>

        <Style TargetType="Button" x:Key="ActionButtonStyle">
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="8" Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Page.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Background="White" CornerRadius="12" Padding="24" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Text="الإعدادات الشاملة" FontSize="24" FontWeight="Bold" Foreground="#1F2937"/>
                    <TextBlock Text="تحكم كامل في جميع إعدادات البرنامج" FontSize="14" Foreground="#6B7280"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="ImportButton" Content="📥 استيراد" Background="#8B5CF6" Foreground="White" 
                            Style="{StaticResource ActionButtonStyle}" Click="ImportButton_Click"/>
                    <Button x:Name="ExportButton" Content="📤 تصدير" Background="#10B981" Foreground="White" 
                            Style="{StaticResource ActionButtonStyle}" Click="ExportButton_Click"/>
                    <Button x:Name="ResetButton" Content="🔄 إعادة تعيين" Background="#EF4444" Foreground="White" 
                            Style="{StaticResource ActionButtonStyle}" Click="ResetButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <TabControl Background="Transparent" BorderThickness="0">
                
                <!-- تبويب معلومات المكتب -->
                <TabItem Header="🏢 معلومات المكتب" FontSize="14" FontWeight="SemiBold">
                    <Border Background="White" CornerRadius="12" Padding="24" Margin="0,10">
                        <Border.Effect>
                            <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                        </Border.Effect>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- العمود الأول -->
                            <StackPanel Grid.Column="0">
                                <Label Content="اسم المكتب *"/>
                                <TextBox x:Name="OfficeNameTextBox"/>
                                
                                <Label Content="عنوان المكتب"/>
                                <TextBox x:Name="OfficeAddressTextBox" Height="60" TextWrapping="Wrap" AcceptsReturn="True"/>
                                
                                <Label Content="هاتف المكتب"/>
                                <TextBox x:Name="OfficePhoneTextBox"/>
                                
                                <Label Content="بريد المكتب الإلكتروني"/>
                                <TextBox x:Name="OfficeEmailTextBox"/>
                            </StackPanel>
                            
                            <!-- العمود الثاني -->
                            <StackPanel Grid.Column="2">
                                <Label Content="موقع المكتب الإلكتروني"/>
                                <TextBox x:Name="OfficeWebsiteTextBox"/>
                                
                                <Label Content="رقم الترخيص"/>
                                <TextBox x:Name="OfficeLicenseTextBox"/>
                                
                                <Label Content="شعار المكتب"/>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBox x:Name="OfficeLogoTextBox" Grid.Column="0" IsReadOnly="True"/>
                                    <Button Grid.Column="1" Content="📁" Width="40" Height="35" 
                                            Background="#6366F1" Foreground="White" BorderThickness="0"
                                            Click="BrowseLogoButton_Click" Margin="5,0,0,15"/>
                                </Grid>
                                
                                <Border Background="#F3F4F6" CornerRadius="8" Padding="15" Margin="0,10,0,0">
                                    <StackPanel>
                                        <TextBlock Text="معاينة الشعار" FontWeight="SemiBold" Margin="0,0,0,10"/>
                                        <Image x:Name="LogoPreview" MaxHeight="100" MaxWidth="200" 
                                               Stretch="Uniform" HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                            </StackPanel>
                        </Grid>
                    </Border>
                </TabItem>
                
                <!-- تبويب إعدادات الواجهة -->
                <TabItem Header="🎨 إعدادات الواجهة" FontSize="14" FontWeight="SemiBold">
                    <Border Background="White" CornerRadius="12" Padding="24" Margin="0,10">
                        <Border.Effect>
                            <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                        </Border.Effect>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- العمود الأول -->
                            <StackPanel Grid.Column="0">
                                <Label Content="اللغة"/>
                                <ComboBox x:Name="LanguageComboBox"/>
                                
                                <Label Content="السمة"/>
                                <ComboBox x:Name="ThemeComboBox"/>
                                
                                <Label Content="اللون الأساسي"/>
                                <ComboBox x:Name="PrimaryColorComboBox"/>
                                
                                <Label Content="اللون الثانوي"/>
                                <ComboBox x:Name="SecondaryColorComboBox"/>
                                
                                <Label Content="نوع الخط"/>
                                <ComboBox x:Name="FontFamilyComboBox"/>
                            </StackPanel>
                            
                            <!-- العمود الثاني -->
                            <StackPanel Grid.Column="2">
                                <Label Content="حجم الخط"/>
                                <Slider x:Name="FontSizeSlider" Minimum="10" Maximum="20" Value="14" 
                                        TickFrequency="1" IsSnapToTickEnabled="True" Margin="0,0,0,15"/>
                                <TextBlock x:Name="FontSizeLabel" Text="14" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                                
                                <Label Content="شفافية النافذة"/>
                                <Slider x:Name="OpacitySlider" Minimum="0.7" Maximum="1.0" Value="1.0" 
                                        TickFrequency="0.1" IsSnapToTickEnabled="True" Margin="0,0,0,15"/>
                                <TextBlock x:Name="OpacityLabel" Text="100%" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                                
                                <CheckBox x:Name="EnableAnimationsCheckBox" Content="تفعيل الحركات"/>
                                <CheckBox x:Name="EnableSoundsCheckBox" Content="تفعيل الأصوات"/>
                                
                                <Border Background="#F3F4F6" CornerRadius="8" Padding="15" Margin="0,20,0,0">
                                    <StackPanel>
                                        <TextBlock Text="معاينة الألوان" FontWeight="SemiBold" Margin="0,0,0,10"/>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <Border x:Name="PrimaryColorPreview" Grid.Column="0" Height="30" 
                                                    Background="#6366F1" CornerRadius="4" Margin="0,0,5,0"/>
                                            <Border x:Name="SecondaryColorPreview" Grid.Column="1" Height="30" 
                                                    Background="#10B981" CornerRadius="4" Margin="5,0,0,0"/>
                                        </Grid>
                                    </StackPanel>
                                </Border>
                            </StackPanel>
                        </Grid>
                    </Border>
                </TabItem>
                
                <!-- تبويب إدارة القوائم -->
                <TabItem Header="📋 إدارة القوائم" FontSize="14" FontWeight="SemiBold">
                    <Border Background="White" CornerRadius="12" Padding="24" Margin="0,10">
                        <Border.Effect>
                            <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                        </Border.Effect>
                        
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <!-- أزرار الإدارة -->
                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
                                <Button x:Name="AddMenuItemButton" Content="➕ إضافة قسم" Background="#10B981" Foreground="White" 
                                        Style="{StaticResource ActionButtonStyle}" Click="AddMenuItemButton_Click"/>
                                <Button x:Name="EditMenuItemButton" Content="✏️ تعديل" Background="#6366F1" Foreground="White" 
                                        Style="{StaticResource ActionButtonStyle}" Click="EditMenuItemButton_Click"/>
                                <Button x:Name="DeleteMenuItemButton" Content="🗑️ حذف" Background="#EF4444" Foreground="White" 
                                        Style="{StaticResource ActionButtonStyle}" Click="DeleteMenuItemButton_Click"/>
                                <Button x:Name="ToggleVisibilityButton" Content="👁️ إخفاء/إظهار" Background="#F59E0B" Foreground="White" 
                                        Style="{StaticResource ActionButtonStyle}" Click="ToggleVisibilityButton_Click"/>
                            </StackPanel>
                            
                            <!-- قائمة العناصر -->
                            <DataGrid x:Name="MenuItemsDataGrid" Grid.Row="1" Background="Transparent" BorderThickness="0"
                                      GridLinesVisibility="None" HeadersVisibility="Column"
                                      AutoGenerateColumns="False" CanUserAddRows="False"
                                      CanUserDeleteRows="False" IsReadOnly="True"
                                      SelectionMode="Single" SelectionUnit="FullRow">

                                <DataGrid.ColumnHeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#F8FAFC"/>
                                        <Setter Property="Foreground" Value="#374151"/>
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="FontSize" Value="14"/>
                                        <Setter Property="Padding" Value="16,12"/>
                                        <Setter Property="BorderThickness" Value="0,0,0,1"/>
                                        <Setter Property="BorderBrush" Value="#E5E7EB"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    </Style>
                                </DataGrid.ColumnHeaderStyle>

                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="المفتاح" Binding="{Binding Key}" Width="120"/>
                                    <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="200"/>
                                    <DataGridTextColumn Header="الأيقونة" Binding="{Binding Icon}" Width="100"/>
                                    <DataGridCheckBoxColumn Header="مرئي" Binding="{Binding IsVisible}" Width="80"/>
                                    <DataGridTextColumn Header="الترتيب" Binding="{Binding Order}" Width="80"/>
                                </DataGrid.Columns>
                            </DataGrid>
                            
                            <!-- معاينة القائمة -->
                            <Border Grid.Row="2" Background="#F3F4F6" CornerRadius="8" Padding="15" Margin="0,20,0,0">
                                <StackPanel>
                                    <TextBlock Text="معاينة القائمة الجانبية" FontWeight="SemiBold" Margin="0,0,0,10"/>
                                    <ScrollViewer x:Name="MenuPreviewScrollViewer" MaxHeight="200" VerticalScrollBarVisibility="Auto">
                                        <StackPanel x:Name="MenuPreviewPanel"/>
                                    </ScrollViewer>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </Border>
                </TabItem>
                
                <!-- تبويب إعدادات البريد الإلكتروني -->
                <TabItem Header="📧 البريد الإلكتروني" FontSize="14" FontWeight="SemiBold">
                    <Border Background="White" CornerRadius="12" Padding="24" Margin="0,10">
                        <Border.Effect>
                            <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                        </Border.Effect>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- العمود الأول -->
                            <StackPanel Grid.Column="0">
                                <Label Content="خادم SMTP"/>
                                <TextBox x:Name="SmtpServerTextBox"/>
                                
                                <Label Content="منفذ SMTP"/>
                                <TextBox x:Name="SmtpPortTextBox"/>
                                
                                <Label Content="اسم المستخدم"/>
                                <TextBox x:Name="SmtpUsernameTextBox"/>
                                
                                <Label Content="كلمة المرور"/>
                                <PasswordBox x:Name="SmtpPasswordBox" Padding="12,8" FontSize="14" 
                                             BorderBrush="#D1D5DB" BorderThickness="1" Margin="0,0,0,15"/>
                                
                                <CheckBox x:Name="EnableSslCheckBox" Content="تفعيل SSL"/>
                            </StackPanel>
                            
                            <!-- العمود الثاني -->
                            <StackPanel Grid.Column="2">
                                <Label Content="البريد المرسل"/>
                                <TextBox x:Name="FromEmailTextBox"/>
                                
                                <Label Content="اسم المرسل"/>
                                <TextBox x:Name="FromNameTextBox"/>
                                
                                <Button x:Name="TestEmailButton" Content="🧪 اختبار الإعدادات" 
                                        Background="#6366F1" Foreground="White" 
                                        Style="{StaticResource ActionButtonStyle}" 
                                        Click="TestEmailButton_Click" Margin="0,20,0,0"/>
                                
                                <Border Background="#F3F4F6" CornerRadius="8" Padding="15" Margin="0,20,0,0">
                                    <StackPanel>
                                        <TextBlock Text="إعدادات شائعة" FontWeight="SemiBold" Margin="0,0,0,10"/>
                                        <Button Content="Gmail" Click="SetGmailSettings_Click" Margin="0,0,0,5"/>
                                        <Button Content="Outlook" Click="SetOutlookSettings_Click" Margin="0,0,0,5"/>
                                        <Button Content="Yahoo" Click="SetYahooSettings_Click"/>
                                    </StackPanel>
                                </Border>
                            </StackPanel>
                        </Grid>
                    </Border>
                </TabItem>
                
                <!-- تبويب قاعدة البيانات -->
                <TabItem Header="🗄️ قاعدة البيانات" FontSize="14" FontWeight="SemiBold">
                    <Border Background="White" CornerRadius="12" Padding="24" Margin="0,10">
                        <Border.Effect>
                            <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                        </Border.Effect>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- العمود الأول -->
                            <StackPanel Grid.Column="0">
                                <Label Content="نوع قاعدة البيانات"/>
                                <ComboBox x:Name="DatabaseTypeComboBox"/>
                                
                                <Label Content="سلسلة الاتصال"/>
                                <TextBox x:Name="ConnectionStringTextBox" Height="60" TextWrapping="Wrap" AcceptsReturn="True"/>
                                
                                <Button x:Name="TestConnectionButton" Content="🔗 اختبار الاتصال" 
                                        Background="#10B981" Foreground="White" 
                                        Style="{StaticResource ActionButtonStyle}" 
                                        Click="TestConnectionButton_Click"/>
                            </StackPanel>
                            
                            <!-- العمود الثاني -->
                            <StackPanel Grid.Column="2">
                                <Label Content="النسخ الاحتياطي"/>
                                <CheckBox x:Name="AutoBackupCheckBox" Content="نسخ احتياطي تلقائي"/>
                                
                                <Label Content="فترة النسخ الاحتياطي (ساعات)"/>
                                <TextBox x:Name="BackupIntervalTextBox"/>
                                
                                <Label Content="مسار النسخ الاحتياطي"/>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBox x:Name="BackupPathTextBox" Grid.Column="0"/>
                                    <Button Grid.Column="1" Content="📁" Width="40" Height="35" 
                                            Background="#6366F1" Foreground="White" BorderThickness="0"
                                            Click="BrowseBackupPathButton_Click" Margin="5,0,0,15"/>
                                </Grid>
                                
                                <Label Content="عدد النسخ الاحتياطية المحفوظة"/>
                                <TextBox x:Name="MaxBackupFilesTextBox"/>
                                
                                <StackPanel Orientation="Horizontal" Margin="0,20,0,0">
                                    <Button x:Name="CreateBackupButton" Content="💾 إنشاء نسخة احتياطية" 
                                            Background="#F59E0B" Foreground="White" 
                                            Style="{StaticResource ActionButtonStyle}" 
                                            Click="CreateBackupButton_Click"/>
                                    <Button x:Name="RestoreBackupButton" Content="📥 استعادة نسخة احتياطية" 
                                            Background="#8B5CF6" Foreground="White" 
                                            Style="{StaticResource ActionButtonStyle}" 
                                            Click="RestoreBackupButton_Click"/>
                                </StackPanel>
                            </StackPanel>
                        </Grid>
                    </Border>
                </TabItem>
            </TabControl>
        </ScrollViewer>

        <!-- أزرار الحفظ -->
        <Border Grid.Row="2" Background="White" CornerRadius="12" Padding="20" Margin="0,20,0,0">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
            </Border.Effect>
            
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="SaveButton" Content="💾 حفظ الإعدادات" Background="#10B981" Foreground="White" 
                        Style="{StaticResource ActionButtonStyle}" Click="SaveButton_Click" Padding="24,12" FontSize="16"/>
                <Button x:Name="ApplyButton" Content="✅ تطبيق" Background="#6366F1" Foreground="White" 
                        Style="{StaticResource ActionButtonStyle}" Click="ApplyButton_Click" Padding="24,12" FontSize="16"/>
                <Button x:Name="CancelButton" Content="❌ إلغاء" Background="#6B7280" Foreground="White" 
                        Style="{StaticResource ActionButtonStyle}" Click="CancelButton_Click" Padding="24,12" FontSize="16"/>
            </StackPanel>
        </Border>
    </Grid>
</Page>
