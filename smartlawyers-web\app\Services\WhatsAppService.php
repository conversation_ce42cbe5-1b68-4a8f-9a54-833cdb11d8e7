<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class WhatsAppService
{
    private $token;
    private $phoneNumberId;
    private $baseUrl;

    public function __construct()
    {
        $this->token = config('services.whatsapp.token');
        $this->phoneNumberId = config('services.whatsapp.phone_number_id');
        $this->baseUrl = 'https://graph.facebook.com/v18.0';
    }

    /**
     * Send a text message
     */
    public function sendTextMessage($to, $message)
    {
        try {
            $response = Http::withToken($this->token)
                ->post("{$this->baseUrl}/{$this->phoneNumberId}/messages", [
                    'messaging_product' => 'whatsapp',
                    'to' => $this->formatPhoneNumber($to),
                    'type' => 'text',
                    'text' => [
                        'body' => $message
                    ]
                ]);

            if ($response->successful()) {
                Log::info('WhatsApp message sent successfully', [
                    'to' => $to,
                    'message_id' => $response->json('messages.0.id')
                ]);
                return $response->json();
            }

            Log::error('Failed to send WhatsApp message', [
                'to' => $to,
                'response' => $response->json()
            ]);

            return false;
        } catch (Exception $e) {
            Log::error('WhatsApp service error', [
                'error' => $e->getMessage(),
                'to' => $to
            ]);
            return false;
        }
    }

    /**
     * Send a template message
     */
    public function sendTemplateMessage($to, $templateName, $languageCode = 'ar', $parameters = [])
    {
        try {
            $payload = [
                'messaging_product' => 'whatsapp',
                'to' => $this->formatPhoneNumber($to),
                'type' => 'template',
                'template' => [
                    'name' => $templateName,
                    'language' => [
                        'code' => $languageCode
                    ]
                ]
            ];

            if (!empty($parameters)) {
                $payload['template']['components'] = [
                    [
                        'type' => 'body',
                        'parameters' => array_map(function ($param) {
                            return ['type' => 'text', 'text' => $param];
                        }, $parameters)
                    ]
                ];
            }

            $response = Http::withToken($this->token)
                ->post("{$this->baseUrl}/{$this->phoneNumberId}/messages", $payload);

            if ($response->successful()) {
                Log::info('WhatsApp template message sent successfully', [
                    'to' => $to,
                    'template' => $templateName,
                    'message_id' => $response->json('messages.0.id')
                ]);
                return $response->json();
            }

            Log::error('Failed to send WhatsApp template message', [
                'to' => $to,
                'template' => $templateName,
                'response' => $response->json()
            ]);

            return false;
        } catch (Exception $e) {
            Log::error('WhatsApp template service error', [
                'error' => $e->getMessage(),
                'to' => $to,
                'template' => $templateName
            ]);
            return false;
        }
    }

    /**
     * Send appointment reminder
     */
    public function sendAppointmentReminder($to, $clientName, $appointmentDate, $appointmentTime, $lawyerName)
    {
        $message = "مرحباً {$clientName}،\n\n";
        $message .= "نذكركم بموعدكم القادم:\n";
        $message .= "📅 التاريخ: {$appointmentDate}\n";
        $message .= "🕐 الوقت: {$appointmentTime}\n";
        $message .= "👨‍💼 المحامي: {$lawyerName}\n\n";
        $message .= "يرجى التأكيد أو إعلامنا في حالة عدم التمكن من الحضور.\n\n";
        $message .= "شكراً لكم\nمكتب المحاماة";

        return $this->sendTextMessage($to, $message);
    }

    /**
     * Send court session reminder
     */
    public function sendCourtSessionReminder($to, $clientName, $sessionDate, $sessionTime, $courtName, $caseNumber)
    {
        $message = "عزيزي/عزيزتي {$clientName}،\n\n";
        $message .= "نذكركم بجلسة المحكمة القادمة:\n";
        $message .= "⚖️ المحكمة: {$courtName}\n";
        $message .= "📋 رقم القضية: {$caseNumber}\n";
        $message .= "📅 التاريخ: {$sessionDate}\n";
        $message .= "🕐 الوقت: {$sessionTime}\n\n";
        $message .= "يرجى الحضور في الوقت المحدد مع إحضار الوثائق المطلوبة.\n\n";
        $message .= "مع تحيات\nمكتب المحاماة";

        return $this->sendTextMessage($to, $message);
    }

    /**
     * Send document ready notification
     */
    public function sendDocumentReadyNotification($to, $clientName, $documentType)
    {
        $message = "مرحباً {$clientName}،\n\n";
        $message .= "نود إعلامكم بأن الوثيقة التالية جاهزة للاستلام:\n";
        $message .= "📄 نوع الوثيقة: {$documentType}\n\n";
        $message .= "يمكنكم زيارة المكتب لاستلامها أو طلب إرسالها إلكترونياً.\n\n";
        $message .= "شكراً لكم\nمكتب المحاماة";

        return $this->sendTextMessage($to, $message);
    }

    /**
     * Send payment reminder
     */
    public function sendPaymentReminder($to, $clientName, $amount, $dueDate, $invoiceNumber)
    {
        $message = "عزيزي/عزيزتي {$clientName}،\n\n";
        $message .= "نذكركم بالمبلغ المستحق:\n";
        $message .= "💰 المبلغ: {$amount} درهم\n";
        $message .= "📋 رقم الفاتورة: {$invoiceNumber}\n";
        $message .= "📅 تاريخ الاستحقاق: {$dueDate}\n\n";
        $message .= "يرجى تسديد المبلغ في أقرب وقت ممكن.\n\n";
        $message .= "شكراً لكم\nمكتب المحاماة";

        return $this->sendTextMessage($to, $message);
    }

    /**
     * Send case update notification
     */
    public function sendCaseUpdateNotification($to, $clientName, $caseNumber, $updateMessage)
    {
        $message = "عزيزي/عزيزتي {$clientName}،\n\n";
        $message .= "تحديث بخصوص قضيتكم رقم: {$caseNumber}\n\n";
        $message .= "📝 التحديث: {$updateMessage}\n\n";
        $message .= "للمزيد من التفاصيل، يرجى التواصل معنا.\n\n";
        $message .= "مع تحيات\nمكتب المحاماة";

        return $this->sendTextMessage($to, $message);
    }

    /**
     * Send welcome message to new client
     */
    public function sendWelcomeMessage($to, $clientName)
    {
        $message = "مرحباً بكم {$clientName}،\n\n";
        $message .= "نرحب بكم في مكتب المحاماة ونتطلع للعمل معكم.\n\n";
        $message .= "خدماتنا متاحة على مدار الساعة:\n";
        $message .= "📞 للاستفسارات العاجلة\n";
        $message .= "💬 للتواصل عبر WhatsApp\n";
        $message .= "🌐 موقعنا الإلكتروني\n\n";
        $message .= "شكراً لثقتكم بنا\nفريق مكتب المحاماة";

        return $this->sendTextMessage($to, $message);
    }

    /**
     * Handle incoming webhook
     */
    public function handleWebhook($payload)
    {
        try {
            if (isset($payload['entry'][0]['changes'][0]['value']['messages'])) {
                $messages = $payload['entry'][0]['changes'][0]['value']['messages'];
                
                foreach ($messages as $message) {
                    $this->processIncomingMessage($message);
                }
            }

            if (isset($payload['entry'][0]['changes'][0]['value']['statuses'])) {
                $statuses = $payload['entry'][0]['changes'][0]['value']['statuses'];
                
                foreach ($statuses as $status) {
                    $this->processMessageStatus($status);
                }
            }

            return true;
        } catch (Exception $e) {
            Log::error('WhatsApp webhook processing error', [
                'error' => $e->getMessage(),
                'payload' => $payload
            ]);
            return false;
        }
    }

    /**
     * Process incoming message
     */
    private function processIncomingMessage($message)
    {
        $from = $message['from'];
        $messageId = $message['id'];
        $timestamp = $message['timestamp'];

        if (isset($message['text'])) {
            $text = $message['text']['body'];
            
            // Store message in database
            \App\Models\WhatsAppMessage::create([
                'message_id' => $messageId,
                'from' => $from,
                'to' => $this->phoneNumberId,
                'type' => 'text',
                'content' => $text,
                'direction' => 'incoming',
                'timestamp' => $timestamp,
                'status' => 'received'
            ]);

            // Auto-reply logic can be added here
            $this->handleAutoReply($from, $text);
        }
    }

    /**
     * Process message status update
     */
    private function processMessageStatus($status)
    {
        $messageId = $status['id'];
        $statusType = $status['status'];
        $timestamp = $status['timestamp'];

        // Update message status in database
        \App\Models\WhatsAppMessage::where('message_id', $messageId)
            ->update([
                'status' => $statusType,
                'status_timestamp' => $timestamp
            ]);
    }

    /**
     * Handle auto-reply
     */
    private function handleAutoReply($from, $text)
    {
        $text = strtolower(trim($text));

        // Simple auto-reply logic
        if (in_array($text, ['مرحبا', 'السلام عليكم', 'hello', 'hi'])) {
            $reply = "مرحباً بكم في مكتب المحاماة. كيف يمكننا مساعدتكم؟\n\n";
            $reply .= "للحصول على المساعدة:\n";
            $reply .= "1️⃣ اكتب 'موعد' لحجز موعد\n";
            $reply .= "2️⃣ اكتب 'استفسار' للاستفسارات العامة\n";
            $reply .= "3️⃣ اكتب 'طوارئ' للحالات العاجلة";
            
            $this->sendTextMessage($from, $reply);
        } elseif (in_array($text, ['موعد', 'حجز موعد', 'appointment'])) {
            $reply = "لحجز موعد، يرجى التواصل معنا على:\n";
            $reply .= "📞 الهاتف: " . config('app.phone') . "\n";
            $reply .= "🌐 الموقع: " . config('app.url') . "\n";
            $reply .= "أو يمكنكم ترك رقم هاتفكم وسنتواصل معكم.";
            
            $this->sendTextMessage($from, $reply);
        }
    }

    /**
     * Format phone number for WhatsApp API
     */
    private function formatPhoneNumber($phone)
    {
        // Remove any non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Add Morocco country code if not present
        if (!str_starts_with($phone, '212')) {
            if (str_starts_with($phone, '0')) {
                $phone = '212' . substr($phone, 1);
            } else {
                $phone = '212' . $phone;
            }
        }
        
        return $phone;
    }

    /**
     * Verify webhook
     */
    public function verifyWebhook($mode, $token, $challenge)
    {
        $verifyToken = config('services.whatsapp.webhook_verify_token');
        
        if ($mode === 'subscribe' && $token === $verifyToken) {
            return $challenge;
        }
        
        return false;
    }
}
