<Window x:Class="AvocatPro.Views.Windows.AttachmentsViewerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="عارض المرفقات والوثائق" 
        Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        ResizeMode="CanResize">

    <Window.Resources>
        <!-- أنماط النافذة -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1976D2"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="AttachmentCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#1976D2"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="3"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1565C0"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="CloseButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#F44336"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#D32F2F"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- رأس النافذة -->
        <Border Grid.Row="0" Background="#1976D2" Padding="20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📎" FontSize="24" Foreground="White" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="عارض المرفقات والوثائق" FontSize="18" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Name="DocumentInfoTextBlock" Text="عرض وإدارة جميع المرفقات والوثائق المالية" FontSize="12" Foreground="#E3F2FD"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- قائمة المرفقات -->
            <Border Grid.Column="0" Background="White" CornerRadius="8" Padding="15" Margin="0,0,10,0">
                <StackPanel>
                    <TextBlock Text="📋 قائمة المرفقات" Style="{StaticResource HeaderTextStyle}"/>
                    
                    <ScrollViewer VerticalScrollBarVisibility="Auto" Height="400">
                        <StackPanel Name="AttachmentsListPanel">
                            <!-- سيتم ملؤها ديناميكياً -->
                        </StackPanel>
                    </ScrollViewer>

                    <!-- أزرار الإجراءات -->
                    <StackPanel Margin="0,15,0,0">
                        <Button Name="AddAttachmentButton" Style="{StaticResource ActionButtonStyle}" 
                               Background="#4CAF50" Click="AddAttachmentButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="➕" FontSize="14" Margin="0,0,5,0"/>
                                <TextBlock Text="إضافة مرفق"/>
                            </StackPanel>
                        </Button>
                        
                        <Button Name="ScanDocumentButton" Style="{StaticResource ActionButtonStyle}" 
                               Background="#FF9800" Click="ScanDocumentButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📷" FontSize="14" Margin="0,0,5,0"/>
                                <TextBlock Text="مسح ضوئي"/>
                            </StackPanel>
                        </Button>
                        
                        <Button Name="DownloadAllButton" Style="{StaticResource ActionButtonStyle}" 
                               Background="#9C27B0" Click="DownloadAllButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📥" FontSize="14" Margin="0,0,5,0"/>
                                <TextBlock Text="تحميل الكل"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- منطقة المعاينة -->
            <Border Grid.Column="1" Background="White" CornerRadius="8" Padding="20" Margin="10,0,0,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- معلومات الملف المحدد -->
                    <StackPanel Grid.Row="0" Margin="0,0,0,15">
                        <TextBlock Text="📄 معاينة الملف" Style="{StaticResource HeaderTextStyle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم الملف:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Name="FileNameTextBlock" Text="لم يتم اختيار ملف" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="نوع الملف:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Name="FileTypeTextBlock" Text="-" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="حجم الملف:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Name="FileSizeTextBlock" Text="-" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="تاريخ الإضافة:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="3" Grid.Column="1" Name="DateAddedTextBlock" Text="-" Margin="0,0,0,5"/>
                        </Grid>
                    </StackPanel>

                    <!-- منطقة المعاينة -->
                    <Border Grid.Row="1" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="5">
                        <ScrollViewer Name="PreviewScrollViewer" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
                            <Grid Name="PreviewGrid">
                                <!-- محتوى المعاينة -->
                                <StackPanel Name="NoPreviewPanel" HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <TextBlock Text="📄" FontSize="48" HorizontalAlignment="Center" Foreground="#CCC"/>
                                    <TextBlock Text="اختر ملفاً لمعاينته" FontSize="16" HorizontalAlignment="Center" Foreground="#999" Margin="0,10,0,0"/>
                                </StackPanel>

                                <!-- معاينة الصور -->
                                <Image Name="ImagePreview" Visibility="Collapsed" Stretch="Uniform"/>

                                <!-- معاينة النصوص -->
                                <TextBox Name="TextPreview" Visibility="Collapsed" IsReadOnly="True" 
                                        TextWrapping="Wrap" AcceptsReturn="True" 
                                        VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto"
                                        Background="Transparent" BorderThickness="0"/>

                                <!-- معاينة PDF -->
                                <StackPanel Name="PdfPreview" Visibility="Collapsed" HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <TextBlock Text="📄" FontSize="48" HorizontalAlignment="Center" Foreground="#1976D2"/>
                                    <TextBlock Text="ملف PDF" FontSize="16" HorizontalAlignment="Center" FontWeight="Bold" Margin="0,10,0,0"/>
                                    <TextBlock Text="انقر على 'فتح' لعرض الملف في التطبيق المناسب" FontSize="12" HorizontalAlignment="Center" Foreground="#666" Margin="0,5,0,0"/>
                                </StackPanel>

                                <!-- معاينة الملفات الأخرى -->
                                <StackPanel Name="OtherFilePreview" Visibility="Collapsed" HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <TextBlock Name="FileIconTextBlock" Text="📄" FontSize="48" HorizontalAlignment="Center" Foreground="#666"/>
                                    <TextBlock Name="FileTypeDisplayTextBlock" Text="ملف" FontSize="16" HorizontalAlignment="Center" FontWeight="Bold" Margin="0,10,0,0"/>
                                    <TextBlock Text="انقر على 'فتح' لعرض الملف في التطبيق المناسب" FontSize="12" HorizontalAlignment="Center" Foreground="#666" Margin="0,5,0,0"/>
                                </StackPanel>
                            </Grid>
                        </ScrollViewer>
                    </Border>

                    <!-- أزرار إجراءات الملف -->
                    <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15,0,0">
                        <Button Name="OpenFileButton" Style="{StaticResource ActionButtonStyle}" 
                               Click="OpenFileButton_Click" IsEnabled="False">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="👁️" FontSize="14" Margin="0,0,5,0"/>
                                <TextBlock Text="فتح"/>
                            </StackPanel>
                        </Button>
                        
                        <Button Name="DownloadFileButton" Style="{StaticResource ActionButtonStyle}" 
                               Background="#4CAF50" Click="DownloadFileButton_Click" IsEnabled="False">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📥" FontSize="14" Margin="0,0,5,0"/>
                                <TextBlock Text="تحميل"/>
                            </StackPanel>
                        </Button>
                        
                        <Button Name="PrintFileButton" Style="{StaticResource ActionButtonStyle}" 
                               Background="#FF9800" Click="PrintFileButton_Click" IsEnabled="False">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🖨️" FontSize="14" Margin="0,0,5,0"/>
                                <TextBlock Text="طباعة"/>
                            </StackPanel>
                        </Button>
                        
                        <Button Name="ShareFileButton" Style="{StaticResource ActionButtonStyle}" 
                               Background="#9C27B0" Click="ShareFileButton_Click" IsEnabled="False">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📤" FontSize="14" Margin="0,0,5,0"/>
                                <TextBlock Text="مشاركة"/>
                            </StackPanel>
                        </Button>
                        
                        <Button Name="DeleteFileButton" Style="{StaticResource ActionButtonStyle}" 
                               Background="#F44336" Click="DeleteFileButton_Click" IsEnabled="False">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🗑️" FontSize="14" Margin="0,0,5,0"/>
                                <TextBlock Text="حذف"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>

        <!-- تذييل النافذة -->
        <Border Grid.Row="2" Background="White" Padding="15" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Name="StatusTextBlock" Text="جاهز" FontSize="12" 
                              Foreground="#666" VerticalAlignment="Center"/>
                </StackPanel>

                <Button Name="CloseButton" Grid.Column="1" Style="{StaticResource CloseButtonStyle}" Click="CloseButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="❌" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="إغلاق"/>
                    </StackPanel>
                </Button>
            </Grid>
        </Border>
    </Grid>
</Window>
