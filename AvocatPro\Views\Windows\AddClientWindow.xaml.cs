using System;
using System.Windows;
using System.Windows.Controls;
using AvocatPro.Models;

namespace AvocatPro.Views.Windows;

public partial class AddClientWindow : Window
{
    public Client? NewClient { get; private set; }
    public bool SaveAndAddAnother { get; private set; }

    public AddClientWindow()
    {
        InitializeComponent();
        GenerateOfficeReference();
    }

    public AddClientWindow(Client clientToEdit) : this()
    {
        Title = "تعديل بيانات الموكل";
        LoadClientData(clientToEdit);
    }

    private void GenerateOfficeReference()
    {
        // توليد مرجع تلقائي للمكتب
        var reference = $"CLT-{DateTime.Now:yyyyMMdd}-{new Random().Next(1000, 9999)}";
        OfficeReferenceTextBox.Text = reference;
    }

    private void LoadClientData(Client client)
    {
        FullNameTextBox.Text = client.FullName;
        IdentityNumberTextBox.Text = client.IdentityNumber ?? "";
        OfficeReferenceTextBox.Text = client.OfficeReference ?? "";
        FileReferenceTextBox.Text = client.FileReference ?? "";
        PrimaryPhoneTextBox.Text = client.Phone;
        SecondaryPhoneTextBox.Text = client.Phone2 ?? "";
        EmailTextBox.Text = client.Email;
        AddressTextBox.Text = client.Address ?? "";
        ProfessionTextBox.Text = client.Profession ?? "";
        NotesTextBox.Text = client.Notes ?? "";

        // تعيين القيم في ComboBox
        SetComboBoxValue(ClientTypeComboBox, client.Type.ToString());
        SetComboBoxValue(StatusComboBox, client.Status.ToString());
        
        if (!string.IsNullOrEmpty(client.Nationality))
            SetComboBoxValue(NationalityComboBox, client.Nationality);
        
        if (!string.IsNullOrEmpty(client.City))
            SetComboBoxValue(CityComboBox, client.City);
    }

    private void SetComboBoxValue(ComboBox comboBox, string value)
    {
        foreach (ComboBoxItem item in comboBox.Items)
        {
            if (item.Tag?.ToString() == value || item.Content?.ToString() == value)
            {
                comboBox.SelectedItem = item;
                break;
            }
        }
    }

    private bool ValidateForm()
    {
        var errors = new System.Collections.Generic.List<string>();

        if (string.IsNullOrWhiteSpace(FullNameTextBox.Text))
            errors.Add("الاسم الكامل مطلوب");

        if (string.IsNullOrWhiteSpace(PrimaryPhoneTextBox.Text))
            errors.Add("رقم الهاتف الأساسي مطلوب");

        if (!string.IsNullOrWhiteSpace(EmailTextBox.Text) && !IsValidEmail(EmailTextBox.Text))
            errors.Add("البريد الإلكتروني غير صحيح");

        if (errors.Count > 0)
        {
            MessageBox.Show($"يرجى تصحيح الأخطاء التالية:\n\n{string.Join("\n", errors)}", 
                           "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
            return false;
        }

        return true;
    }

    private bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    private Client CreateClientFromForm()
    {
        var clientType = Enum.Parse<ClientType>((ClientTypeComboBox.SelectedItem as ComboBoxItem)?.Tag?.ToString() ?? "Individual");
        var clientStatus = Enum.Parse<ClientStatus>((StatusComboBox.SelectedItem as ComboBoxItem)?.Tag?.ToString() ?? "Active");

        return new Client
        {
            FullName = FullNameTextBox.Text.Trim(),
            IdentityNumber = IdentityNumberTextBox.Text.Trim(),
            OfficeReference = OfficeReferenceTextBox.Text.Trim(),
            FileReference = FileReferenceTextBox.Text.Trim(),
            Type = clientType,
            Phone = PrimaryPhoneTextBox.Text.Trim(),
            Phone2 = SecondaryPhoneTextBox.Text.Trim(),
            Email = EmailTextBox.Text.Trim(),
            Address = AddressTextBox.Text.Trim(),
            City = (CityComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString(),
            Nationality = (NationalityComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString(),
            Profession = ProfessionTextBox.Text.Trim(),
            Status = clientStatus,
            Notes = NotesTextBox.Text.Trim(),
            CreatedAt = DateTime.Now,
            UpdatedAt = DateTime.Now
        };
    }

    private void SaveButton_Click(object sender, RoutedEventArgs e)
    {
        if (!ValidateForm()) return;

        try
        {
            NewClient = CreateClientFromForm();
            SaveAndAddAnother = false;
            
            MessageBox.Show("تم حفظ بيانات الموكل بنجاح!", "نجح الحفظ", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
            
            DialogResult = true;
            Close();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء حفظ البيانات:\n{ex.Message}", "خطأ", 
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void SaveAndNewButton_Click(object sender, RoutedEventArgs e)
    {
        if (!ValidateForm()) return;

        try
        {
            NewClient = CreateClientFromForm();
            SaveAndAddAnother = true;
            
            MessageBox.Show("تم حفظ بيانات الموكل بنجاح!\nيمكنك الآن إضافة موكل آخر.", "نجح الحفظ", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
            
            DialogResult = true;
            Close();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء حفظ البيانات:\n{ex.Message}", "خطأ", 
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("هل أنت متأكد من إلغاء العملية؟\nسيتم فقدان جميع البيانات المدخلة.", 
                                   "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Question);
        
        if (result == MessageBoxResult.Yes)
        {
            DialogResult = false;
            Close();
        }
    }
}
