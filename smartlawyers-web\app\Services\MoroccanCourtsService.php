<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Exception;

class MoroccanCourtsService
{
    private $apiKey;
    private $baseUrl;
    private $certificatePath;
    private $privateKeyPath;

    public function __construct()
    {
        $this->apiKey = config('services.mahakim.api_key');
        $this->baseUrl = config('services.mahakim.api_url', 'https://api.mahakim.ma');
        $this->certificatePath = config('services.mahakim.certificate_path');
        $this->privateKeyPath = config('services.mahakim.private_key_path');
    }

    /**
     * Get list of all Moroccan courts
     */
    public function getCourts()
    {
        try {
            $cacheKey = 'moroccan_courts_list';
            
            return Cache::remember($cacheKey, 3600, function () {
                $response = $this->makeSecureRequest('GET', '/courts');
                
                if ($response && isset($response['data'])) {
                    return $response['data'];
                }
                
                // Fallback to static list if API is unavailable
                return $this->getStaticCourtsList();
            });
        } catch (Exception $e) {
            Log::error('Failed to fetch courts list', ['error' => $e->getMessage()]);
            return $this->getStaticCourtsList();
        }
    }

    /**
     * Search for case information
     */
    public function searchCase($caseNumber, $year = null, $courtId = null)
    {
        try {
            $params = [
                'case_number' => $caseNumber,
                'year' => $year ?: date('Y'),
                'court_id' => $courtId
            ];

            $response = $this->makeSecureRequest('GET', '/cases/search', $params);
            
            if ($response && isset($response['data'])) {
                return [
                    'success' => true,
                    'data' => $response['data']
                ];
            }
            
            return [
                'success' => false,
                'message' => 'لم يتم العثور على القضية'
            ];
        } catch (Exception $e) {
            Log::error('Case search failed', [
                'case_number' => $caseNumber,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => 'خطأ في البحث عن القضية'
            ];
        }
    }

    /**
     * Get case sessions/hearings
     */
    public function getCaseSessions($caseId)
    {
        try {
            $response = $this->makeSecureRequest('GET', "/cases/{$caseId}/sessions");
            
            if ($response && isset($response['data'])) {
                return [
                    'success' => true,
                    'sessions' => $response['data']
                ];
            }
            
            return [
                'success' => false,
                'message' => 'لم يتم العثور على جلسات للقضية'
            ];
        } catch (Exception $e) {
            Log::error('Failed to fetch case sessions', [
                'case_id' => $caseId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => 'خطأ في جلب جلسات القضية'
            ];
        }
    }

    /**
     * Get case documents
     */
    public function getCaseDocuments($caseId)
    {
        try {
            $response = $this->makeSecureRequest('GET', "/cases/{$caseId}/documents");
            
            if ($response && isset($response['data'])) {
                return [
                    'success' => true,
                    'documents' => $response['data']
                ];
            }
            
            return [
                'success' => false,
                'message' => 'لم يتم العثور على وثائق للقضية'
            ];
        } catch (Exception $e) {
            Log::error('Failed to fetch case documents', [
                'case_id' => $caseId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => 'خطأ في جلب وثائق القضية'
            ];
        }
    }

    /**
     * Submit electronic filing
     */
    public function submitElectronicFiling($caseId, $documentType, $documentContent, $metadata = [])
    {
        try {
            $payload = [
                'case_id' => $caseId,
                'document_type' => $documentType,
                'content' => base64_encode($documentContent),
                'metadata' => $metadata,
                'submission_date' => now()->toISOString()
            ];

            $response = $this->makeSecureRequest('POST', '/filings/submit', $payload);
            
            if ($response && isset($response['filing_id'])) {
                return [
                    'success' => true,
                    'filing_id' => $response['filing_id'],
                    'reference_number' => $response['reference_number'] ?? null
                ];
            }
            
            return [
                'success' => false,
                'message' => 'فشل في تقديم الملف الإلكتروني'
            ];
        } catch (Exception $e) {
            Log::error('Electronic filing submission failed', [
                'case_id' => $caseId,
                'document_type' => $documentType,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => 'خطأ في تقديم الملف الإلكتروني'
            ];
        }
    }

    /**
     * Get court fees calculation
     */
    public function calculateCourtFees($caseType, $claimAmount = null, $courtLevel = 'first_instance')
    {
        try {
            $params = [
                'case_type' => $caseType,
                'claim_amount' => $claimAmount,
                'court_level' => $courtLevel
            ];

            $response = $this->makeSecureRequest('GET', '/fees/calculate', $params);
            
            if ($response && isset($response['fees'])) {
                return [
                    'success' => true,
                    'fees' => $response['fees']
                ];
            }
            
            // Fallback to static calculation
            return $this->calculateStaticFees($caseType, $claimAmount, $courtLevel);
        } catch (Exception $e) {
            Log::error('Court fees calculation failed', [
                'case_type' => $caseType,
                'claim_amount' => $claimAmount,
                'error' => $e->getMessage()
            ]);
            
            return $this->calculateStaticFees($caseType, $claimAmount, $courtLevel);
        }
    }

    /**
     * Get lawyer registration status
     */
    public function verifyLawyerRegistration($barNumber, $barAssociation)
    {
        try {
            $params = [
                'bar_number' => $barNumber,
                'bar_association' => $barAssociation
            ];

            $response = $this->makeSecureRequest('GET', '/lawyers/verify', $params);
            
            if ($response && isset($response['status'])) {
                return [
                    'success' => true,
                    'status' => $response['status'],
                    'details' => $response['details'] ?? []
                ];
            }
            
            return [
                'success' => false,
                'message' => 'لم يتم العثور على المحامي'
            ];
        } catch (Exception $e) {
            Log::error('Lawyer verification failed', [
                'bar_number' => $barNumber,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => 'خطأ في التحقق من المحامي'
            ];
        }
    }

    /**
     * Get court calendar/schedule
     */
    public function getCourtCalendar($courtId, $startDate, $endDate)
    {
        try {
            $params = [
                'court_id' => $courtId,
                'start_date' => $startDate,
                'end_date' => $endDate
            ];

            $response = $this->makeSecureRequest('GET', '/courts/calendar', $params);
            
            if ($response && isset($response['calendar'])) {
                return [
                    'success' => true,
                    'calendar' => $response['calendar']
                ];
            }
            
            return [
                'success' => false,
                'message' => 'لم يتم العثور على جدول المحكمة'
            ];
        } catch (Exception $e) {
            Log::error('Court calendar fetch failed', [
                'court_id' => $courtId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => 'خطأ في جلب جدول المحكمة'
            ];
        }
    }

    /**
     * Make secure API request with certificates
     */
    private function makeSecureRequest($method, $endpoint, $data = [])
    {
        $url = $this->baseUrl . $endpoint;
        
        $options = [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            ],
            'timeout' => 30,
            'verify' => true
        ];

        // Add SSL certificates if available
        if ($this->certificatePath && file_exists($this->certificatePath)) {
            $options['cert'] = $this->certificatePath;
        }
        
        if ($this->privateKeyPath && file_exists($this->privateKeyPath)) {
            $options['ssl_key'] = $this->privateKeyPath;
        }

        $http = Http::withOptions($options);

        if ($method === 'GET' && !empty($data)) {
            $response = $http->get($url, $data);
        } elseif ($method === 'POST') {
            $response = $http->post($url, $data);
        } else {
            $response = $http->send($method, $url, ['json' => $data]);
        }

        if ($response->successful()) {
            return $response->json();
        }

        Log::warning('Moroccan courts API request failed', [
            'method' => $method,
            'endpoint' => $endpoint,
            'status' => $response->status(),
            'response' => $response->body()
        ]);

        return null;
    }

    /**
     * Static courts list as fallback
     */
    private function getStaticCourtsList()
    {
        return [
            ['id' => 'casa_appeal', 'name' => 'محكمة الاستئناف بالدار البيضاء', 'type' => 'appeal', 'city' => 'الدار البيضاء'],
            ['id' => 'rabat_appeal', 'name' => 'محكمة الاستئناف بالرباط', 'type' => 'appeal', 'city' => 'الرباط'],
            ['id' => 'fes_appeal', 'name' => 'محكمة الاستئناف بفاس', 'type' => 'appeal', 'city' => 'فاس'],
            ['id' => 'marrakech_appeal', 'name' => 'محكمة الاستئناف بمراكش', 'type' => 'appeal', 'city' => 'مراكش'],
            ['id' => 'casa_first', 'name' => 'المحكمة الابتدائية بالدار البيضاء', 'type' => 'first_instance', 'city' => 'الدار البيضاء'],
            ['id' => 'rabat_first', 'name' => 'المحكمة الابتدائية بالرباط', 'type' => 'first_instance', 'city' => 'الرباط'],
            ['id' => 'supreme_court', 'name' => 'محكمة النقض', 'type' => 'supreme', 'city' => 'الرباط'],
            ['id' => 'constitutional_court', 'name' => 'المحكمة الدستورية', 'type' => 'constitutional', 'city' => 'الرباط'],
        ];
    }

    /**
     * Static fees calculation as fallback
     */
    private function calculateStaticFees($caseType, $claimAmount, $courtLevel)
    {
        $baseFees = [
            'civil' => ['first_instance' => 100, 'appeal' => 200, 'supreme' => 500],
            'commercial' => ['first_instance' => 150, 'appeal' => 300, 'supreme' => 750],
            'criminal' => ['first_instance' => 50, 'appeal' => 100, 'supreme' => 250],
            'administrative' => ['first_instance' => 200, 'appeal' => 400, 'supreme' => 1000],
            'family' => ['first_instance' => 80, 'appeal' => 160, 'supreme' => 400],
        ];

        $baseFee = $baseFees[$caseType][$courtLevel] ?? 100;
        
        // Calculate percentage-based fees for high-value claims
        $percentageFee = 0;
        if ($claimAmount && $claimAmount > 10000) {
            $percentageFee = $claimAmount * 0.001; // 0.1%
        }

        $totalFees = $baseFee + $percentageFee;

        return [
            'success' => true,
            'fees' => [
                'base_fee' => $baseFee,
                'percentage_fee' => $percentageFee,
                'total_fees' => $totalFees,
                'currency' => 'MAD',
                'calculation_date' => now()->toDateString()
            ]
        ];
    }
}
