﻿#pragma checksum "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "71B7EFA664C123A4AC1ECE841F6EFC529CC78AD1"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Pages {
    
    
    /// <summary>
    /// SessionsPageNew
    /// </summary>
    public partial class SessionsPageNew : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 115 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddSessionButton;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalSessionsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TodaySessionsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WeekSessionsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MonthSessionsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CompletedSessionsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 190 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrevMonthButton;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentMonthTextBlock;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NextMonthButton;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SessionTypeFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SessionStatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 233 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 241 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintButton;
        
        #line default
        #line hidden
        
        
        #line 249 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewModeButton;
        
        #line default
        #line hidden
        
        
        #line 252 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ViewModeIcon;
        
        #line default
        #line hidden
        
        
        #line 253 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ViewModeText;
        
        #line default
        #line hidden
        
        
        #line 267 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border CalendarView;
        
        #line default
        #line hidden
        
        
        #line 296 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.UniformGrid CalendarGrid;
        
        #line default
        #line hidden
        
        
        #line 301 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border TableView;
        
        #line default
        #line hidden
        
        
        #line 315 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SessionsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 381 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/pages/sessionspagenew.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AddSessionButton = ((System.Windows.Controls.Button)(target));
            
            #line 115 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
            this.AddSessionButton.Click += new System.Windows.RoutedEventHandler(this.AddSessionButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 123 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.TotalSessionsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.TodaySessionsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.WeekSessionsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.MonthSessionsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.CompletedSessionsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.PrevMonthButton = ((System.Windows.Controls.Button)(target));
            
            #line 191 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
            this.PrevMonthButton.Click += new System.Windows.RoutedEventHandler(this.PrevMonthButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.CurrentMonthTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.NextMonthButton = ((System.Windows.Controls.Button)(target));
            
            #line 200 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
            this.NextMonthButton.Click += new System.Windows.RoutedEventHandler(this.NextMonthButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.SessionTypeFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 208 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
            this.SessionTypeFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 12:
            this.SessionStatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 220 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
            this.SessionStatusFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 13:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 234 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.PrintButton = ((System.Windows.Controls.Button)(target));
            
            #line 242 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
            this.PrintButton.Click += new System.Windows.RoutedEventHandler(this.PrintButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.ViewModeButton = ((System.Windows.Controls.Button)(target));
            
            #line 250 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
            this.ViewModeButton.Click += new System.Windows.RoutedEventHandler(this.ViewModeButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.ViewModeIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.ViewModeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.CalendarView = ((System.Windows.Controls.Border)(target));
            return;
            case 19:
            this.CalendarGrid = ((System.Windows.Controls.Primitives.UniformGrid)(target));
            return;
            case 20:
            this.TableView = ((System.Windows.Controls.Border)(target));
            return;
            case 21:
            this.SessionsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 27:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 22:
            
            #line 344 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewSessionButton_Click);
            
            #line default
            #line hidden
            break;
            case 23:
            
            #line 350 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditSessionButton_Click);
            
            #line default
            #line hidden
            break;
            case 24:
            
            #line 356 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DocumentsButton_Click);
            
            #line default
            #line hidden
            break;
            case 25:
            
            #line 362 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NotifyButton_Click);
            
            #line default
            #line hidden
            break;
            case 26:
            
            #line 368 "..\..\..\..\..\Views\Pages\SessionsPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteSessionButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

