﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>

    <!-- معلومات التطبيق -->
    <AssemblyTitle>AvocatPro - نظام إدارة مكتب المحاماة</AssemblyTitle>
    <AssemblyDescription>نظام احترافي شامل لإدارة مكاتب المحاماة في المغرب</AssemblyDescription>
    <AssemblyCompany>AvocatPro Solutions</AssemblyCompany>
    <AssemblyProduct>AvocatPro</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <AssemblyFileVersion>*******</AssemblyFileVersion>
    <Copyright>© 2025 AvocatPro Solutions. جميع الحقوق محفوظة.</Copyright>

    <!-- إعدادات Windows -->
    <StartupObject>AvocatPro.App</StartupObject>
  </PropertyGroup>

  <ItemGroup>
    <!-- قواعد البيانات -->
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="6.0.25" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.25" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.25" />

    <!-- واجهة المستخدم -->
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
    <PackageReference Include="FontAwesome.WPF" Version="*******" />
    <PackageReference Include="ModernWpfUI" Version="0.9.6" />

    <!-- التقارير والتصدير -->
    <PackageReference Include="iTextSharp" Version="5.5.13.3" />
    <PackageReference Include="EPPlus" Version="6.2.10" />
    <PackageReference Include="QuestPDF" Version="2023.12.6" />

    <!-- الأدوات المساعدة -->
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="AutoMapper" Version="12.0.1" />

    <!-- التحقق من البيانات -->
    <PackageReference Include="FluentValidation" Version="11.8.0" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.8.0" />

    <!-- MVVM -->
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="6.0.1" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Models\" />
    <Folder Include="ViewModels\" />
    <Folder Include="Views\" />
    <Folder Include="Services\" />
    <Folder Include="Data\" />
    <Folder Include="Helpers\" />
    <Folder Include="Resources\" />
    <Folder Include="Styles\" />
    <Folder Include="Controls\" />
    <Folder Include="Converters\" />
    <Folder Include="Validators\" />
    <Folder Include="Reports\" />
    <Folder Include="Migrations\" />
  </ItemGroup>

</Project>
