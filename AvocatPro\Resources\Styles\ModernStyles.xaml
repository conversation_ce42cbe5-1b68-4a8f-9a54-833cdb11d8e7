<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- الألوان الأساسية للنظام -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="#6366F1"/>
    <SolidColorBrush x:Key="PrimaryHoverBrush" Color="#5B5FE8"/>
    <SolidColorBrush x:Key="SecondaryBrush" Color="#F3F4F6"/>
    <SolidColorBrush x:Key="AccentBrush" Color="#10B981"/>
    <SolidColorBrush x:Key="DangerBrush" Color="#EF4444"/>
    <SolidColorBrush x:Key="WarningBrush" Color="#F59E0B"/>
    <SolidColorBrush x:Key="InfoBrush" Color="#3B82F6"/>
    
    <!-- ألوان النصوص -->
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="#1F2937"/>
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="#6B7280"/>
    <SolidColorBrush x:Key="TextMutedBrush" Color="#9CA3AF"/>
    <SolidColorBrush x:Key="TextLightBrush" Color="#F9FAFB"/>
    
    <!-- ألوان الخلفيات -->
    <SolidColorBrush x:Key="BackgroundPrimaryBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="BackgroundSecondaryBrush" Color="#F8FAFC"/>
    <SolidColorBrush x:Key="BackgroundMutedBrush" Color="#F3F4F6"/>
    
    <!-- ألوان الحدود -->
    <SolidColorBrush x:Key="BorderBrush" Color="#E5E7EB"/>
    <SolidColorBrush x:Key="BorderLightBrush" Color="#F3F4F6"/>

    <!-- أنماط البطاقات الحديثة -->
    <Style x:Key="ModernCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource BackgroundPrimaryBrush}"/>
        <Setter Property="CornerRadius" Value="16"/>
        <Setter Property="Padding" Value="24"/>
        <Setter Property="Margin" Value="12"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" Opacity="0.08" BlurRadius="24" ShadowDepth="4"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- أنماط الأزرار الحديثة -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="24,12"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border" 
                            Background="{TemplateBinding Background}"
                            CornerRadius="12"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource PrimaryHoverBrush}"/>
                            <Setter TargetName="border" Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="1.02" ScaleY="1.02"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource TextMutedBrush}"/>
                            <Setter Property="Foreground" Value="White"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- زر ثانوي -->
    <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
        <Setter Property="Background" Value="{StaticResource SecondaryBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource BorderBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- زر الخطر -->
    <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
        <Setter Property="Background" Value="{StaticResource DangerBrush}"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#DC2626"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- أنماط مربعات النص الحديثة -->
    <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
        <Setter Property="Background" Value="{StaticResource BackgroundPrimaryBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="Padding" Value="16,12"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border x:Name="border" 
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="12"
                            Padding="{TemplateBinding Padding}">
                        <ScrollViewer x:Name="PART_ContentHost" 
                                      Focusable="false" 
                                      HorizontalScrollBarVisibility="Hidden" 
                                      VerticalScrollBarVisibility="Hidden"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                            <Setter TargetName="border" Property="Background" Value="#FEFEFE"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource BackgroundMutedBrush}"/>
                            <Setter Property="Foreground" Value="{StaticResource TextMutedBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- أنماط ComboBox الحديثة -->
    <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
        <Setter Property="Background" Value="{StaticResource BackgroundPrimaryBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="Padding" Value="16,12"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Height" Value="48"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ComboBox">
                    <Grid>
                        <Border x:Name="border" 
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="12">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <ContentPresenter Grid.Column="0" 
                                                  Content="{TemplateBinding SelectionBoxItem}"
                                                  ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                                  Margin="{TemplateBinding Padding}"
                                                  VerticalAlignment="Center"/>
                                
                                <Border Grid.Column="1" 
                                        Background="{StaticResource SecondaryBrush}"
                                        CornerRadius="8"
                                        Width="32" Height="32"
                                        Margin="8">
                                    <TextBlock Text="▼" FontSize="10" 
                                               Foreground="{StaticResource TextSecondaryBrush}"
                                               HorizontalAlignment="Center" 
                                               VerticalAlignment="Center"/>
                                </Border>
                            </Grid>
                        </Border>
                        
                        <Popup x:Name="PART_Popup" 
                               Placement="Bottom" 
                               IsOpen="{TemplateBinding IsDropDownOpen}"
                               AllowsTransparency="True">
                            <Border Background="{StaticResource BackgroundPrimaryBrush}"
                                    BorderBrush="{StaticResource BorderBrush}"
                                    BorderThickness="1"
                                    CornerRadius="12"
                                    Margin="0,4,0,0">
                                <Border.Effect>
                                    <DropShadowEffect Color="Black" Opacity="0.15" BlurRadius="20" ShadowDepth="4"/>
                                </Border.Effect>
                                <ScrollViewer MaxHeight="200">
                                    <ItemsPresenter/>
                                </ScrollViewer>
                            </Border>
                        </Popup>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- أنماط DataGrid الحديثة -->
    <Style x:Key="ModernDataGridStyle" TargetType="DataGrid">
        <Setter Property="Background" Value="{StaticResource BackgroundPrimaryBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="GridLinesVisibility" Value="None"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="CanUserAddRows" Value="False"/>
        <Setter Property="CanUserDeleteRows" Value="False"/>
        <Setter Property="CanUserReorderColumns" Value="True"/>
        <Setter Property="CanUserResizeColumns" Value="True"/>
        <Setter Property="CanUserSortColumns" Value="True"/>
        <Setter Property="SelectionMode" Value="Single"/>
        <Setter Property="SelectionUnit" Value="FullRow"/>
        <Setter Property="AlternatingRowBackground" Value="{StaticResource BackgroundSecondaryBrush}"/>
        <Setter Property="RowHeight" Value="48"/>
        <Setter Property="FontSize" Value="14"/>
    </Style>

    <!-- أنماط عناوين الصفحات -->
    <Style x:Key="PageTitleStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="32"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
    </Style>

    <Style x:Key="SectionTitleStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="20"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="Margin" Value="0,0,0,12"/>
    </Style>

    <Style x:Key="SubtitleStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
        <Setter Property="Margin" Value="0,0,0,8"/>
    </Style>

    <!-- أنماط الأيقونات -->
    <Style x:Key="IconStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="20"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="Margin" Value="0,0,8,0"/>
    </Style>

    <!-- أنماط الفواصل -->
    <Style x:Key="SeparatorStyle" TargetType="Border">
        <Setter Property="Height" Value="1"/>
        <Setter Property="Background" Value="{StaticResource BorderBrush}"/>
        <Setter Property="Margin" Value="0,16"/>
    </Style>

    <!-- أنماط الإشعارات -->
    <Style x:Key="SuccessNotificationStyle" TargetType="Border">
        <Setter Property="Background" Value="#ECFDF5"/>
        <Setter Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="0,8"/>
    </Style>

    <Style x:Key="ErrorNotificationStyle" TargetType="Border">
        <Setter Property="Background" Value="#FEF2F2"/>
        <Setter Property="BorderBrush" Value="{StaticResource DangerBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="0,8"/>
    </Style>

    <Style x:Key="WarningNotificationStyle" TargetType="Border">
        <Setter Property="Background" Value="#FFFBEB"/>
        <Setter Property="BorderBrush" Value="{StaticResource WarningBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="0,8"/>
    </Style>

    <Style x:Key="InfoNotificationStyle" TargetType="Border">
        <Setter Property="Background" Value="#EFF6FF"/>
        <Setter Property="BorderBrush" Value="{StaticResource InfoBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="0,8"/>
    </Style>

</ResourceDictionary>
