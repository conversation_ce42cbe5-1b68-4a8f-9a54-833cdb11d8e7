<?php
/**
 * Smart Lawyers Web Platform - Easy Installation Script
 * منصة المحامين الذكية - سكريبت التثبيت السهل
 * 
 * @version 1.0.0
 * <AUTHOR> Lawyers Team
 */

class SmartLawyersInstaller
{
    private $steps = [
        'requirements' => 'فحص المتطلبات',
        'environment' => 'تكوين البيئة',
        'complete' => 'اكتمال التثبيت'
    ];

    private $currentStep = 'requirements';
    private $errors = [];
    private $config = [];

    public function __construct()
    {
        session_start();
        $this->loadConfig();
        $this->handleRequest();
    }

    private function loadConfig()
    {
        if (isset($_SESSION['install_config'])) {
            $this->config = $_SESSION['install_config'];
        }
        
        if (isset($_GET['step']) && array_key_exists($_GET['step'], $this->steps)) {
            $this->currentStep = $_GET['step'];
        }
    }

    private function handleRequest()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->processStep();
        }
    }

    private function processStep()
    {
        switch ($this->currentStep) {
            case 'requirements':
                $this->checkRequirements();
                break;
            case 'environment':
                $this->setupEnvironment();
                break;
        }
    }

    private function checkRequirements()
    {
        $requirements = [
            'PHP >= 8.1' => version_compare(PHP_VERSION, '8.1.0', '>='),
            'OpenSSL Extension' => extension_loaded('openssl'),
            'PDO Extension' => extension_loaded('pdo'),
            'Mbstring Extension' => extension_loaded('mbstring'),
            'Tokenizer Extension' => extension_loaded('tokenizer'),
            'XML Extension' => extension_loaded('xml'),
            'Ctype Extension' => extension_loaded('ctype'),
            'JSON Extension' => extension_loaded('json'),
            'BCMath Extension' => extension_loaded('bcmath'),
            'Fileinfo Extension' => extension_loaded('fileinfo'),
            'GD Extension' => extension_loaded('gd'),
            'cURL Extension' => extension_loaded('curl'),
            'Zip Extension' => extension_loaded('zip'),
        ];

        $permissions = [
            'storage/' => is_writable('storage/'),
            'bootstrap/cache/' => is_writable('bootstrap/cache/'),
            '.env' => is_writable('.') || is_writable('.env'),
        ];

        $allPassed = true;
        foreach (array_merge($requirements, $permissions) as $check => $passed) {
            if (!$passed) {
                $this->errors[] = "فشل في: $check";
                $allPassed = false;
            }
        }

        if ($allPassed && empty($this->errors)) {
            $this->redirectToNext('environment');
        }
    }



    private function setupEnvironment()
    {
        if (isset($_POST['app_name'])) {
            $this->config['app'] = [
                'name' => $_POST['app_name'],
                'url' => $_POST['app_url'],
                'timezone' => $_POST['app_timezone'],
                'locale' => $_POST['app_locale'],
            ];

            $this->generateEnvFile();
            $_SESSION['install_config'] = $this->config;
            $this->redirectToNext('complete');
        }
    }



    private function generateEnvFile()
    {
        if (!file_exists('.env.example')) {
            // Create a simple .env file
            $envContent = 'APP_NAME="' . ($this->config['app']['name'] ?? 'Smart Lawyers Web') . '"' . "\n";
            $envContent .= 'APP_ENV=local' . "\n";
            $envContent .= 'APP_KEY=base64:' . base64_encode(random_bytes(32)) . "\n";
            $envContent .= 'APP_DEBUG=true' . "\n";
            $envContent .= 'APP_URL=' . ($this->config['app']['url'] ?? 'http://localhost:8000') . "\n";
            $envContent .= 'APP_TIMEZONE=' . ($this->config['app']['timezone'] ?? 'Africa/Casablanca') . "\n";
            $envContent .= 'APP_LOCALE=' . ($this->config['app']['locale'] ?? 'ar') . "\n";
        } else {
            $envContent = file_get_contents('.env.example');

            // Replace app settings
            $envContent = str_replace('APP_NAME="Smart Lawyers Web"', 'APP_NAME="' . $this->config['app']['name'] . '"', $envContent);
            $envContent = str_replace('APP_URL=http://localhost:8000', 'APP_URL=' . $this->config['app']['url'], $envContent);
            $envContent = str_replace('APP_TIMEZONE=Africa/Casablanca', 'APP_TIMEZONE=' . $this->config['app']['timezone'], $envContent);
            $envContent = str_replace('APP_LOCALE=ar', 'APP_LOCALE=' . $this->config['app']['locale'], $envContent);
        }

        file_put_contents('.env', $envContent);
    }



    private function redirectToNext($step)
    {
        header("Location: install.php?step=$step");
        exit;
    }

    public function render()
    {
        include 'install-template.php';
    }
}

// Initialize installer
$installer = new SmartLawyersInstaller();
$installer->render();
?>
