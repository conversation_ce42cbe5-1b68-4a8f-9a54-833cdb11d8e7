using System;
using System.Windows;
using AvocatPro.Models;

namespace AvocatPro.Views.Windows
{
    public partial class UserReportsWindow : Window
    {
        private readonly User _currentUser;

        public UserReportsWindow(User currentUser)
        {
            // محاكاة نافذة تقارير المستخدمين
            _currentUser = currentUser;
            
            // إنشاء نافذة بسيطة
            Title = "تقارير المستخدمين";
            Width = 800;
            Height = 600;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;
            
            var button = new System.Windows.Controls.Button
            {
                Content = "إغلاق",
                Width = 100,
                Height = 30,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };
            button.Click += (s, e) => Close();
            
            Content = button;
        }
    }
}
