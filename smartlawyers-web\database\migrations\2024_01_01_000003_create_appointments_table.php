<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('appointments', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            $table->foreignId('client_id')->constrained('clients')->onDelete('cascade');
            $table->foreignId('case_id')->nullable()->constrained('legal_cases')->onDelete('set null');
            $table->foreignId('lawyer_id')->constrained('users');
            $table->datetime('appointment_date');
            $table->datetime('end_time')->nullable();
            $table->integer('duration_minutes')->default(60);
            $table->enum('type', [
                'consultation', 'meeting', 'court_hearing', 
                'document_signing', 'follow_up', 'other'
            ])->default('consultation');
            $table->enum('status', [
                'scheduled', 'confirmed', 'in_progress', 
                'completed', 'cancelled', 'no_show'
            ])->default('scheduled');
            $table->enum('priority', ['low', 'medium', 'high'])->default('medium');
            $table->string('location')->nullable();
            $table->string('meeting_link')->nullable(); // For online meetings
            $table->text('preparation_notes')->nullable();
            $table->text('meeting_notes')->nullable();
            $table->text('follow_up_actions')->nullable();
            $table->boolean('reminder_sent')->default(false);
            $table->datetime('reminder_sent_at')->nullable();
            $table->boolean('confirmation_required')->default(true);
            $table->datetime('confirmed_at')->nullable();
            $table->foreignId('confirmed_by')->nullable()->constrained('users');
            $table->text('cancellation_reason')->nullable();
            $table->datetime('cancelled_at')->nullable();
            $table->foreignId('cancelled_by')->nullable()->constrained('users');
            $table->string('google_calendar_event_id')->nullable();
            $table->string('outlook_event_id')->nullable();
            $table->json('attendees')->nullable();
            $table->json('custom_fields')->nullable();
            $table->foreignId('created_by')->nullable()->constrained('users');
            $table->foreignId('updated_by')->nullable()->constrained('users');
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['client_id', 'appointment_date']);
            $table->index(['lawyer_id', 'appointment_date']);
            $table->index('appointment_date');
            $table->index('status');
            $table->index('type');
            $table->index('priority');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('appointments');
    }
};
