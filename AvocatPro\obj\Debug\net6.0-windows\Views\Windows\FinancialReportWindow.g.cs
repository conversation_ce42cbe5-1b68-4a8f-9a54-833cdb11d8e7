﻿#pragma checksum "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "54F69F6C4684719888780AD455C4397396C6E68B"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Windows {
    
    
    /// <summary>
    /// FinancialReportWindow
    /// </summary>
    public partial class FinancialReportWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 111 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PeriodComboBox;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker StartDatePicker;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ToLabel;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker EndDatePicker;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ReportTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button GenerateReportButton;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportPdfButton;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportExcelButton;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintReportButton;
        
        #line default
        #line hidden
        
        
        #line 213 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SummaryTotalRevenuesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SummaryTotalExpensesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 231 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SummaryNetProfitTextBlock;
        
        #line default
        #line hidden
        
        
        #line 240 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SummaryProfitMarginTextBlock;
        
        #line default
        #line hidden
        
        
        #line 257 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer MonthlyTrendChart;
        
        #line default
        #line hidden
        
        
        #line 258 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel MonthlyTrendChartPanel;
        
        #line default
        #line hidden
        
        
        #line 267 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer CategoryDistributionChart;
        
        #line default
        #line hidden
        
        
        #line 268 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel CategoryDistributionChartPanel;
        
        #line default
        #line hidden
        
        
        #line 278 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PerformanceAnalysisPanel;
        
        #line default
        #line hidden
        
        
        #line 297 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DetailedReportPanel;
        
        #line default
        #line hidden
        
        
        #line 321 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel MonthlyComparisonPanel;
        
        #line default
        #line hidden
        
        
        #line 332 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel YearlyComparisonPanel;
        
        #line default
        #line hidden
        
        
        #line 358 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ForecastPanel;
        
        #line default
        #line hidden
        
        
        #line 377 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReportStatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 381 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/windows/financialreportwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PeriodComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 112 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
            this.PeriodComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PeriodComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.StartDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 123 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
            this.StartDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DatePicker_SelectedDateChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ToLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.EndDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 129 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
            this.EndDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DatePicker_SelectedDateChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ReportTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 138 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
            this.ReportTypeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ReportTypeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.GenerateReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 151 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
            this.GenerateReportButton.Click += new System.Windows.RoutedEventHandler(this.GenerateReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ExportPdfButton = ((System.Windows.Controls.Button)(target));
            
            #line 159 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
            this.ExportPdfButton.Click += new System.Windows.RoutedEventHandler(this.ExportPdfButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ExportExcelButton = ((System.Windows.Controls.Button)(target));
            
            #line 167 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
            this.ExportExcelButton.Click += new System.Windows.RoutedEventHandler(this.ExportExcelButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.PrintReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 175 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
            this.PrintReportButton.Click += new System.Windows.RoutedEventHandler(this.PrintReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.SummaryTotalRevenuesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.SummaryTotalExpensesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.SummaryNetProfitTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.SummaryProfitMarginTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.MonthlyTrendChart = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 15:
            this.MonthlyTrendChartPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 16:
            this.CategoryDistributionChart = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 17:
            this.CategoryDistributionChartPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 18:
            this.PerformanceAnalysisPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 19:
            this.DetailedReportPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 20:
            this.MonthlyComparisonPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 21:
            this.YearlyComparisonPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 22:
            this.ForecastPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 23:
            this.ReportStatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 382 "..\..\..\..\..\Views\Windows\FinancialReportWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

