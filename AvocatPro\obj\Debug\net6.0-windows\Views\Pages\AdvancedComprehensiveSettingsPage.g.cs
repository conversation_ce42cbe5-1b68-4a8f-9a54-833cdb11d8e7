﻿#pragma checksum "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "7EF0BCD9D00B5DB1797A42B5C6635835A1F26BFE"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Pages {
    
    
    /// <summary>
    /// AdvancedComprehensiveSettingsPage
    /// </summary>
    public partial class AdvancedComprehensiveSettingsPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 230 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DarkModeToggleButton;
        
        #line default
        #line hidden
        
        
        #line 233 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveAllButton;
        
        #line default
        #line hidden
        
        
        #line 236 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetAllButton;
        
        #line default
        #line hidden
        
        
        #line 239 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestConnectionButton;
        
        #line default
        #line hidden
        
        
        #line 248 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl SettingsTabControl;
        
        #line default
        #line hidden
        
        
        #line 293 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OfficeNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 297 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LicenseNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 300 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MainPhoneTextBox;
        
        #line default
        #line hidden
        
        
        #line 305 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OfficeAddressTextBox;
        
        #line default
        #line hidden
        
        
        #line 309 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OfficialEmailTextBox;
        
        #line default
        #line hidden
        
        
        #line 312 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox WebsiteTextBox;
        
        #line default
        #line hidden
        
        
        #line 329 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image LogoPreview;
        
        #line default
        #line hidden
        
        
        #line 335 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrimaryColorButton;
        
        #line default
        #line hidden
        
        
        #line 337 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle PrimaryColorPreview;
        
        #line default
        #line hidden
        
        
        #line 343 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SecondaryColorButton;
        
        #line default
        #line hidden
        
        
        #line 345 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle SecondaryColorPreview;
        
        #line default
        #line hidden
        
        
        #line 370 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SmtpServerTextBox;
        
        #line default
        #line hidden
        
        
        #line 374 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SmtpPortTextBox;
        
        #line default
        #line hidden
        
        
        #line 377 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableSslCheckBox;
        
        #line default
        #line hidden
        
        
        #line 383 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox EmailUsernameTextBox;
        
        #line default
        #line hidden
        
        
        #line 386 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox EmailPasswordBox;
        
        #line default
        #line hidden
        
        
        #line 412 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableSoundNotificationsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 414 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AppointmentSoundCheckBox;
        
        #line default
        #line hidden
        
        
        #line 416 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EmailSoundCheckBox;
        
        #line default
        #line hidden
        
        
        #line 418 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox SystemSoundCheckBox;
        
        #line default
        #line hidden
        
        
        #line 424 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider VolumeSlider;
        
        #line default
        #line hidden
        
        
        #line 426 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock VolumeLabel;
        
        #line default
        #line hidden
        
        
        #line 443 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableVisualNotificationsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 445 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox PopupNotificationsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 447 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox TaskbarNotificationsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 449 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox FlashingNotificationsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 455 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotificationDurationTextBox;
        
        #line default
        #line hidden
        
        
        #line 459 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox NotificationPositionComboBox;
        
        #line default
        #line hidden
        
        
        #line 488 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton LightModeRadio;
        
        #line default
        #line hidden
        
        
        #line 491 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton DarkModeRadio;
        
        #line default
        #line hidden
        
        
        #line 494 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton AutoModeRadio;
        
        #line default
        #line hidden
        
        
        #line 501 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FontSizeComboBox;
        
        #line default
        #line hidden
        
        
        #line 509 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FontFamilyComboBox;
        
        #line default
        #line hidden
        
        
        #line 529 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox IconSizeComboBox;
        
        #line default
        #line hidden
        
        
        #line 536 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowIconLabelsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 538 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AnimatedIconsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 572 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DatabaseTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 583 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DatabaseNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 589 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ServerAddressTextBox;
        
        #line default
        #line hidden
        
        
        #line 593 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DatabasePortTextBox;
        
        #line default
        #line hidden
        
        
        #line 608 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DatabaseUsernameTextBox;
        
        #line default
        #line hidden
        
        
        #line 611 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IntegratedSecurityCheckBox;
        
        #line default
        #line hidden
        
        
        #line 617 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox DatabasePasswordBox;
        
        #line default
        #line hidden
        
        
        #line 635 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ConnectionTimeoutTextBox;
        
        #line default
        #line hidden
        
        
        #line 639 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaxConnectionsTextBox;
        
        #line default
        #line hidden
        
        
        #line 644 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableConnectionPoolingCheckBox;
        
        #line default
        #line hidden
        
        
        #line 646 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableQueryCachingCheckBox;
        
        #line default
        #line hidden
        
        
        #line 648 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableAutoBackupCheckBox;
        
        #line default
        #line hidden
        
        
        #line 673 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox InterfaceLanguageComboBox;
        
        #line default
        #line hidden
        
        
        #line 681 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TextDirectionComboBox;
        
        #line default
        #line hidden
        
        
        #line 689 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TimezoneComboBox;
        
        #line default
        #line hidden
        
        
        #line 697 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DateFormatComboBox;
        
        #line default
        #line hidden
        
        
        #line 716 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CurrencyComboBox;
        
        #line default
        #line hidden
        
        
        #line 725 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CurrencySymbolTextBox;
        
        #line default
        #line hidden
        
        
        #line 731 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CurrencyPositionComboBox;
        
        #line default
        #line hidden
        
        
        #line 737 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DecimalPlacesComboBox;
        
        #line default
        #line hidden
        
        
        #line 792 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableLoggingCheckBox;
        
        #line default
        #line hidden
        
        
        #line 794 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox LogErrorsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 796 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox LogWarningsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 798 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox LogInfoCheckBox;
        
        #line default
        #line hidden
        
        
        #line 804 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LogLevelComboBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/pages/advancedcomprehensivesettingspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DarkModeToggleButton = ((System.Windows.Controls.Button)(target));
            
            #line 232 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
            this.DarkModeToggleButton.Click += new System.Windows.RoutedEventHandler(this.DarkModeToggle_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SaveAllButton = ((System.Windows.Controls.Button)(target));
            
            #line 235 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
            this.SaveAllButton.Click += new System.Windows.RoutedEventHandler(this.SaveAllSettings_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ResetAllButton = ((System.Windows.Controls.Button)(target));
            
            #line 238 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
            this.ResetAllButton.Click += new System.Windows.RoutedEventHandler(this.ResetAllSettings_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.TestConnectionButton = ((System.Windows.Controls.Button)(target));
            
            #line 241 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
            this.TestConnectionButton.Click += new System.Windows.RoutedEventHandler(this.TestConnection_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.SettingsTabControl = ((System.Windows.Controls.TabControl)(target));
            return;
            case 6:
            this.OfficeNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.LicenseNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.MainPhoneTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.OfficeAddressTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.OfficialEmailTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.WebsiteTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            
            #line 328 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectLogo_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.LogoPreview = ((System.Windows.Controls.Image)(target));
            return;
            case 14:
            this.PrimaryColorButton = ((System.Windows.Controls.Button)(target));
            
            #line 336 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
            this.PrimaryColorButton.Click += new System.Windows.RoutedEventHandler(this.SelectPrimaryColor_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.PrimaryColorPreview = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 16:
            this.SecondaryColorButton = ((System.Windows.Controls.Button)(target));
            
            #line 344 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
            this.SecondaryColorButton.Click += new System.Windows.RoutedEventHandler(this.SelectSecondaryColor_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.SecondaryColorPreview = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 18:
            this.SmtpServerTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.SmtpPortTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.EnableSslCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 21:
            this.EmailUsernameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 22:
            this.EmailPasswordBox = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 23:
            
            #line 389 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TestEmailConnection_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.EnableSoundNotificationsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 25:
            this.AppointmentSoundCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 26:
            this.EmailSoundCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 27:
            this.SystemSoundCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 28:
            this.VolumeSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 29:
            this.VolumeLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            
            #line 430 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TestSound_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            this.EnableVisualNotificationsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 32:
            this.PopupNotificationsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 33:
            this.TaskbarNotificationsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 34:
            this.FlashingNotificationsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 35:
            this.NotificationDurationTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 36:
            this.NotificationPositionComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 37:
            this.LightModeRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 38:
            this.DarkModeRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 39:
            this.AutoModeRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 40:
            this.FontSizeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 41:
            this.FontFamilyComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 42:
            this.IconSizeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 43:
            this.ShowIconLabelsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 44:
            this.AnimatedIconsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 45:
            
            #line 544 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CustomizeMenuNames_Click);
            
            #line default
            #line hidden
            return;
            case 46:
            
            #line 546 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ChangeIconSet_Click);
            
            #line default
            #line hidden
            return;
            case 47:
            
            #line 548 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ResetAppearance_Click);
            
            #line default
            #line hidden
            return;
            case 48:
            this.DatabaseTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 573 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
            this.DatabaseTypeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DatabaseType_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 49:
            this.DatabaseNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 50:
            this.ServerAddressTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 51:
            this.DatabasePortTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 52:
            this.DatabaseUsernameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 53:
            this.IntegratedSecurityCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 54:
            this.DatabasePasswordBox = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 55:
            
            #line 621 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TestDatabaseConnection_Click);
            
            #line default
            #line hidden
            return;
            case 56:
            this.ConnectionTimeoutTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 57:
            this.MaxConnectionsTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 58:
            this.EnableConnectionPoolingCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 59:
            this.EnableQueryCachingCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 60:
            this.EnableAutoBackupCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 61:
            this.InterfaceLanguageComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 62:
            this.TextDirectionComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 63:
            this.TimezoneComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 64:
            this.DateFormatComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 65:
            this.CurrencyComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 66:
            this.CurrencySymbolTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 67:
            this.CurrencyPositionComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 68:
            this.DecimalPlacesComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 69:
            
            #line 766 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RunSystemDiagnostics_Click);
            
            #line default
            #line hidden
            return;
            case 70:
            
            #line 768 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CheckDatabaseIntegrity_Click);
            
            #line default
            #line hidden
            return;
            case 71:
            
            #line 770 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CheckMissingFiles_Click);
            
            #line default
            #line hidden
            return;
            case 72:
            
            #line 775 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CleanTempFiles_Click);
            
            #line default
            #line hidden
            return;
            case 73:
            
            #line 777 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AnalyzePerformance_Click);
            
            #line default
            #line hidden
            return;
            case 74:
            
            #line 779 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateSystemReport_Click);
            
            #line default
            #line hidden
            return;
            case 75:
            this.EnableLoggingCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 76:
            this.LogErrorsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 77:
            this.LogWarningsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 78:
            this.LogInfoCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 79:
            this.LogLevelComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 80:
            
            #line 812 "..\..\..\..\..\Views\Pages\AdvancedComprehensiveSettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewLogs_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

