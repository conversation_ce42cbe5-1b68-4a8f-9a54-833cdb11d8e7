using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using AvocatPro.Models;
using AvocatPro.Services;

namespace AvocatPro.Views.Windows
{
    /// <summary>
    /// نافذة إضافة/تعديل الملفات الشاملة مع التكامل مع المحاكم الإلكترونية
    /// </summary>
    public partial class ComprehensiveAddFileWindow : Window
    {
        #region Properties

        public ComprehensiveFileModel NewFile { get; set; }
        private ComprehensiveFileModel _existingFile;
        private MoroccanCourtsIntegrationService _courtsService;
        private bool _isEditMode = false;

        #endregion

        #region Constructors

        /// <summary>
        /// إنشاء ملف جديد
        /// </summary>
        public ComprehensiveAddFileWindow()
        {
            InitializeComponent();
            InitializeNewFile();
            InitializeServices();
            SetupUI();
        }

        /// <summary>
        /// تعديل ملف موجود
        /// </summary>
        public ComprehensiveAddFileWindow(ComprehensiveFileModel existingFile)
        {
            InitializeComponent();
            _existingFile = existingFile;
            _isEditMode = true;
            InitializeExistingFile();
            InitializeServices();
            SetupUI();
        }

        #endregion

        #region Initialization

        /// <summary>
        /// تهيئة ملف جديد
        /// </summary>
        private void InitializeNewFile()
        {
            NewFile = new ComprehensiveFileModel();
            DataContext = NewFile;
        }

        /// <summary>
        /// تهيئة ملف موجود للتعديل
        /// </summary>
        private void InitializeExistingFile()
        {
            NewFile = new ComprehensiveFileModel
            {
                Id = _existingFile.Id,
                FileNumber = _existingFile.FileNumber,
                CourtReference = _existingFile.CourtReference,
                OfficeReference = _existingFile.OfficeReference,
                FileType = _existingFile.FileType,
                Court = _existingFile.Court,
                CaseType = _existingFile.CaseType,
                Opponent = _existingFile.Opponent,
                Subject = _existingFile.Subject,
                Client = _existingFile.Client,
                AssignedLawyer = _existingFile.AssignedLawyer,
                ProcedureType = _existingFile.ProcedureType,
                Decision = _existingFile.Decision,
                NextSessionDate = _existingFile.NextSessionDate,
                Status = _existingFile.Status,
                Priority = _existingFile.Priority,
                Notes = _existingFile.Notes,
                IsArchived = _existingFile.IsArchived,
                IsElectronicTracking = _existingFile.IsElectronicTracking,
                ElectronicFileCode = _existingFile.ElectronicFileCode,
                FileYear = _existingFile.FileYear,
                AppealCourt = _existingFile.AppealCourt,
                SearchInPrimaryCourts = _existingFile.SearchInPrimaryCourts,
                EstimatedValue = _existingFile.EstimatedValue,
                CreatedDate = _existingFile.CreatedDate,
                LastUpdateDate = _existingFile.LastUpdateDate
            };
            
            DataContext = NewFile;
        }

        /// <summary>
        /// تهيئة الخدمات
        /// </summary>
        private void InitializeServices()
        {
            _courtsService = new MoroccanCourtsIntegrationService();
        }

        /// <summary>
        /// إعداد واجهة المستخدم
        /// </summary>
        private void SetupUI()
        {
            if (_isEditMode)
            {
                WindowTitle.Text = "تعديل الملف";
                SaveButton.Content = "💾 تحديث الملف";
                
                // تعبئة البيانات في الحقول
                PopulateFields();
            }
            else
            {
                WindowTitle.Text = "إضافة ملف جديد";
                SaveButton.Content = "💾 حفظ الملف";
                
                // تعيين القيم الافتراضية
                SetDefaultValues();
            }
            
            // تحديث حالة التتبع الإلكتروني
            UpdateElectronicTrackingUI();
        }

        /// <summary>
        /// تعبئة الحقول بالبيانات الموجودة
        /// </summary>
        private void PopulateFields()
        {
            FileNumberTextBox.Text = NewFile.FileNumber;
            CourtReferenceTextBox.Text = NewFile.CourtReference;
            OfficeReferenceTextBox.Text = NewFile.OfficeReference;
            OpponentTextBox.Text = NewFile.Opponent;
            SubjectTextBox.Text = NewFile.Subject;
            ClientTextBox.Text = NewFile.Client;
            DecisionTextBox.Text = NewFile.Decision;
            NotesTextBox.Text = NewFile.Notes;
            ElectronicFileCodeTextBox.Text = NewFile.ElectronicFileCode;
            FileYearTextBox.Text = NewFile.FileYear.ToString();
            EstimatedValueTextBox.Text = NewFile.EstimatedValue.ToString();
            
            NextSessionDatePicker.SelectedDate = NewFile.NextSessionDate != default ? NewFile.NextSessionDate : null;
            ElectronicTrackingCheckBox.IsChecked = NewFile.IsElectronicTracking;
            SearchInPrimaryCourtsCheckBox.IsChecked = NewFile.SearchInPrimaryCourts;
            IsArchivedCheckBox.IsChecked = NewFile.IsArchived;
            
            // تحديد العناصر في القوائم المنسدلة
            SetComboBoxSelection(FileTypeComboBox, NewFile.FileType);
            SetComboBoxSelection(CaseTypeComboBox, NewFile.CaseType);
            SetComboBoxSelection(StatusComboBox, NewFile.Status);
            SetComboBoxSelection(PriorityComboBox, NewFile.Priority);
            SetComboBoxSelection(ProcedureTypeComboBox, NewFile.ProcedureType);
            SetComboBoxSelection(CourtComboBox, NewFile.Court);
            SetComboBoxSelection(AppealCourtComboBox, NewFile.AppealCourt);
            SetComboBoxSelection(AssignedLawyerComboBox, NewFile.AssignedLawyer);
        }

        /// <summary>
        /// تعيين القيم الافتراضية
        /// </summary>
        private void SetDefaultValues()
        {
            FileYearTextBox.Text = DateTime.Now.Year.ToString();
            NextSessionDatePicker.SelectedDate = DateTime.Now.AddDays(30);
            EstimatedValueTextBox.Text = "0";
        }

        /// <summary>
        /// تحديد عنصر في القائمة المنسدلة
        /// </summary>
        private void SetComboBoxSelection(ComboBox comboBox, string value)
        {
            if (string.IsNullOrEmpty(value)) return;
            
            foreach (ComboBoxItem item in comboBox.Items)
            {
                if (item.Content.ToString() == value)
                {
                    comboBox.SelectedItem = item;
                    break;
                }
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// تفعيل/إلغاء التتبع الإلكتروني
        /// </summary>
        private void ElectronicTracking_Checked(object sender, RoutedEventArgs e)
        {
            UpdateElectronicTrackingUI();
        }

        private void ElectronicTracking_Unchecked(object sender, RoutedEventArgs e)
        {
            UpdateElectronicTrackingUI();
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        private async void Validate_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ValidateButton.Content = "⏳ جاري التحقق...";
                ValidateButton.IsEnabled = false;

                if (!ElectronicTrackingCheckBox.IsChecked == true)
                {
                    MessageBox.Show("التتبع الإلكتروني غير مفعل", "تنبيه", 
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                var fileNumber = FileNumberTextBox.Text;
                var fileCode = ElectronicFileCodeTextBox.Text;
                var year = int.TryParse(FileYearTextBox.Text, out var y) ? y : DateTime.Now.Year;

                if (string.IsNullOrWhiteSpace(fileCode))
                {
                    MessageBox.Show("يرجى إدخال رمز الملف الإلكتروني", "بيانات ناقصة", 
                                   MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = await _courtsService.ValidateFileDataAsync(fileNumber, fileCode, year);
                
                if (result.IsValid)
                {
                    MessageBox.Show("✅ البيانات صحيحة ومتطابقة مع النظام الإلكتروني للمحاكم", "تحقق ناجح", 
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    var message = $"❌ {result.ErrorMessage}";
                    if (result.Suggestions != null && result.Suggestions.Count > 0)
                    {
                        message += "\n\nاقتراحات:\n" + string.Join("\n", result.Suggestions);
                    }
                    
                    MessageBox.Show(message, "فشل التحقق", 
                                   MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التحقق: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                ValidateButton.Content = "🔍 التحقق من البيانات";
                ValidateButton.IsEnabled = true;
            }
        }

        /// <summary>
        /// حفظ الملف
        /// </summary>
        private void Save_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات المطلوبة
                if (!ValidateRequiredFields())
                    return;

                // تحديث بيانات الملف من الحقول
                UpdateFileFromFields();

                // تعيين التواريخ
                if (!_isEditMode)
                {
                    NewFile.CreatedDate = DateTime.Now;
                }
                NewFile.LastUpdateDate = DateTime.Now;

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void Close_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// تحديث واجهة التتبع الإلكتروني
        /// </summary>
        private void UpdateElectronicTrackingUI()
        {
            var isEnabled = ElectronicTrackingCheckBox.IsChecked == true;
            
            ElectronicCodePanel.IsEnabled = isEnabled;
            FileYearPanel.IsEnabled = isEnabled;
            AppealCourtPanel.IsEnabled = isEnabled;
            SearchPrimaryPanel.IsEnabled = isEnabled;
            ValidateButton.IsEnabled = isEnabled;
        }

        /// <summary>
        /// التحقق من الحقول المطلوبة
        /// </summary>
        private bool ValidateRequiredFields()
        {
            if (string.IsNullOrWhiteSpace(FileNumberTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الملف", "بيانات ناقصة", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                FileNumberTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(OfficeReferenceTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال مرجع المكتب", "بيانات ناقصة", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                OfficeReferenceTextBox.Focus();
                return false;
            }

            if (FileTypeComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع الملف", "بيانات ناقصة", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                FileTypeComboBox.Focus();
                return false;
            }

            if (CourtComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار المحكمة المختصة", "بيانات ناقصة", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                CourtComboBox.Focus();
                return false;
            }

            if (CaseTypeComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع القضية", "بيانات ناقصة", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                CaseTypeComboBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(OpponentTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الخصم", "بيانات ناقصة", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                OpponentTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(SubjectTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال موضوع القضية", "بيانات ناقصة", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                SubjectTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(ClientTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الموكل", "بيانات ناقصة", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                ClientTextBox.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// تحديث بيانات الملف من الحقول
        /// </summary>
        private void UpdateFileFromFields()
        {
            NewFile.FileNumber = FileNumberTextBox.Text.Trim();
            NewFile.CourtReference = CourtReferenceTextBox.Text.Trim();
            NewFile.OfficeReference = OfficeReferenceTextBox.Text.Trim();
            NewFile.FileType = GetComboBoxValue(FileTypeComboBox);
            NewFile.Court = GetComboBoxValue(CourtComboBox);
            NewFile.CaseType = GetComboBoxValue(CaseTypeComboBox);
            NewFile.Opponent = OpponentTextBox.Text.Trim();
            NewFile.Subject = SubjectTextBox.Text.Trim();
            NewFile.Client = ClientTextBox.Text.Trim();
            NewFile.AssignedLawyer = GetComboBoxValue(AssignedLawyerComboBox);
            NewFile.ProcedureType = GetComboBoxValue(ProcedureTypeComboBox);
            NewFile.Decision = DecisionTextBox.Text.Trim();
            NewFile.Status = GetComboBoxValue(StatusComboBox);
            NewFile.Priority = GetComboBoxValue(PriorityComboBox);
            NewFile.Notes = NotesTextBox.Text.Trim();
            NewFile.IsArchived = IsArchivedCheckBox.IsChecked == true;
            NewFile.IsElectronicTracking = ElectronicTrackingCheckBox.IsChecked == true;
            NewFile.ElectronicFileCode = ElectronicFileCodeTextBox.Text.Trim();
            NewFile.AppealCourt = GetComboBoxValue(AppealCourtComboBox);
            NewFile.SearchInPrimaryCourts = SearchInPrimaryCourtsCheckBox.IsChecked == true;

            // تحويل السنة
            if (int.TryParse(FileYearTextBox.Text, out var year))
                NewFile.FileYear = year;

            // تحويل القيمة المقدرة
            if (decimal.TryParse(EstimatedValueTextBox.Text, out var value))
                NewFile.EstimatedValue = value;

            // تاريخ الجلسة المقبلة
            if (NextSessionDatePicker.SelectedDate.HasValue)
                NewFile.NextSessionDate = NextSessionDatePicker.SelectedDate.Value;
        }

        /// <summary>
        /// الحصول على قيمة القائمة المنسدلة
        /// </summary>
        private string GetComboBoxValue(ComboBox comboBox)
        {
            return (comboBox.SelectedItem as ComboBoxItem)?.Content?.ToString() ?? string.Empty;
        }

        #endregion
    }
}
