using System;

namespace AvocatPro.Models;

/// <summary>
/// نموذج عرض الجلسات في الجدول والتقويم
/// </summary>
public class SessionDisplayModel
{
    public int Id { get; set; }
    public int CaseId { get; set; }
    public string CaseTitle { get; set; } = "";
    public string CaseReference { get; set; } = "";
    public DateTime SessionDate { get; set; }
    public TimeSpan SessionTime { get; set; }
    public SessionType Type { get; set; }
    public string? ProcedureType { get; set; }
    public string? Decision { get; set; }
    public DateTime? NextSessionDate { get; set; }
    public TimeSpan? NextSessionTime { get; set; }
    public SessionStatus Status { get; set; }
    public string? AssignedLawyerName { get; set; }
    public string? Judge { get; set; }
    public string? CourtRoom { get; set; }
    public string? SessionNotes { get; set; }
    public string? Attendance { get; set; }
    public string? Arguments { get; set; }
    public string? Evidence { get; set; }
    public decimal? Expenses { get; set; }
    public int? Duration { get; set; }
    public bool IsNotified { get; set; }
    public DateTime? NotificationDate { get; set; }
    public string ClientName { get; set; } = "";
    public string Court { get; set; } = "";
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    // خصائص العرض
    public string TypeDisplay => Type switch
    {
        SessionType.Regular => "جلسة عادية",
        SessionType.Urgent => "جلسة استعجالية",
        SessionType.Pleading => "جلسة مرافعة",
        SessionType.Judgment => "جلسة حكم",
        SessionType.Postponement => "جلسة تأجيل",
        SessionType.Preparatory => "جلسة تحضيرية",
        SessionType.Settlement => "جلسة صلح",
        _ => "غير محدد"
    };

    public string StatusDisplay => Status switch
    {
        SessionStatus.Scheduled => "مجدولة",
        SessionStatus.InProgress => "جارية",
        SessionStatus.Completed => "مكتملة",
        SessionStatus.Postponed => "مؤجلة",
        SessionStatus.Cancelled => "ملغاة",
        SessionStatus.NoShow => "لم يحضر",
        _ => "غير محدد"
    };

    public string SessionDateDisplay => SessionDate.ToString("dd/MM/yyyy");
    public string SessionTimeDisplay => SessionTime.ToString(@"hh\:mm");
    public string SessionDateTimeDisplay => $"{SessionDateDisplay} - {SessionTimeDisplay}";
    public string NextSessionDateDisplay => NextSessionDate?.ToString("dd/MM/yyyy") ?? "غير محدد";
    public string NextSessionTimeDisplay => NextSessionTime?.ToString(@"hh\:mm") ?? "";
    public string NextSessionDateTimeDisplay => NextSessionDate.HasValue ? 
        $"{NextSessionDateDisplay} - {NextSessionTimeDisplay}" : "غير محدد";
    
    public string ExpensesDisplay => Expenses?.ToString("N2") + " ريال" ?? "غير محدد";
    public string DurationDisplay => Duration.HasValue ? $"{Duration} دقيقة" : "غير محدد";
    public string CreatedAtDisplay => CreatedAt.ToString("dd/MM/yyyy");
    
    public string ProcedureTypeDisplay => ProcedureType ?? "غير محدد";
    public string DecisionDisplay => Decision ?? "لم يتخذ قرار بعد";
    public string JudgeDisplay => Judge ?? "غير محدد";
    public string CourtRoomDisplay => CourtRoom ?? "غير محدد";
    public string SessionNotesDisplay => SessionNotes ?? "لا توجد ملاحظات";
    public string AttendanceDisplay => Attendance ?? "غير محدد";
    public string ArgumentsDisplay => Arguments ?? "لا توجد مرافعات";
    public string EvidenceDisplay => Evidence ?? "لا توجد أدلة";
    public string AssignedLawyerDisplay => AssignedLawyerName ?? "غير محدد";
    
    public string NotificationStatusDisplay => IsNotified ? "تم التنبيه" : "لم يتم التنبيه";
    public string NotificationDateDisplay => NotificationDate?.ToString("dd/MM/yyyy HH:mm") ?? "غير محدد";

    // خصائص للتقويم
    public string CalendarTitle => $"{TypeDisplay} - {CaseTitle}";
    public string CalendarDescription => $"المحامي: {AssignedLawyerDisplay}\nالقاضي: {JudgeDisplay}\nالقاعة: {CourtRoomDisplay}";
    
    // خصائص الألوان للتقويم
    public string StatusColor => Status switch
    {
        SessionStatus.Scheduled => "#2196F3", // أزرق
        SessionStatus.InProgress => "#FF9800", // برتقالي
        SessionStatus.Completed => "#4CAF50", // أخضر
        SessionStatus.Postponed => "#FFC107", // أصفر
        SessionStatus.Cancelled => "#F44336", // أحمر
        SessionStatus.NoShow => "#9E9E9E", // رمادي
        _ => "#607D8B"
    };

    public string TypeColor => Type switch
    {
        SessionType.Regular => "#2196F3",
        SessionType.Urgent => "#F44336",
        SessionType.Pleading => "#9C27B0",
        SessionType.Judgment => "#4CAF50",
        SessionType.Postponement => "#FF9800",
        SessionType.Preparatory => "#00BCD4",
        SessionType.Settlement => "#8BC34A",
        _ => "#607D8B"
    };

    // خصائص للإحصائيات
    public string MonthYear => SessionDate.ToString("yyyy-MM");
    public string Year => SessionDate.ToString("yyyy");
    public string Month => SessionDate.ToString("MM");
    public string DayOfWeek => SessionDate.ToString("dddd", new System.Globalization.CultureInfo("ar-SA"));
    public int WeekOfYear => System.Globalization.CultureInfo.CurrentCulture.Calendar.GetWeekOfYear(
        SessionDate, System.Globalization.CalendarWeekRule.FirstDay, System.DayOfWeek.Saturday);
}
