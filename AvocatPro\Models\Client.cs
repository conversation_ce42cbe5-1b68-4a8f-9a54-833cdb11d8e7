using System.ComponentModel.DataAnnotations;

namespace AvocatPro.Models;

/// <summary>
/// نموذج الموكل
/// </summary>
public class Client : BaseEntity
{
    /// <summary>
    /// مرجع المكتب للموكل
    /// </summary>
    [Required]
    [StringLength(20)]
    public string OfficeReference { get; set; } = string.Empty;

    /// <summary>
    /// مرجع الملف
    /// </summary>
    [StringLength(50)]
    public string? FileReference { get; set; }
    
    /// <summary>
    /// الاسم الكامل
    /// </summary>
    [Required]
    [StringLength(100)]
    public string FullName { get; set; } = string.Empty;
    
    /// <summary>
    /// نوع الموكل
    /// </summary>
    public ClientType Type { get; set; } = ClientType.Individual;
    
    /// <summary>
    /// رقم البطاقة الوطنية أو السجل التجاري
    /// </summary>
    [StringLength(20)]
    public string? IdentityNumber { get; set; }
    
    /// <summary>
    /// رقم الهاتف الأساسي
    /// </summary>
    [Required]
    [StringLength(20)]
    public string Phone { get; set; } = string.Empty;
    
    /// <summary>
    /// رقم هاتف إضافي
    /// </summary>
    [StringLength(20)]
    public string? Phone2 { get; set; }
    
    /// <summary>
    /// البريد الإلكتروني
    /// </summary>
    [EmailAddress]
    [StringLength(100)]
    public string? Email { get; set; }
    
    /// <summary>
    /// العنوان الكامل
    /// </summary>
    [StringLength(500)]
    public string? Address { get; set; }
    
    /// <summary>
    /// المدينة
    /// </summary>
    [StringLength(50)]
    public string? City { get; set; }
    
    /// <summary>
    /// المنطقة/الإقليم
    /// </summary>
    [StringLength(50)]
    public string? Region { get; set; }
    
    /// <summary>
    /// الرمز البريدي
    /// </summary>
    [StringLength(10)]
    public string? PostalCode { get; set; }
    
    /// <summary>
    /// الجنسية
    /// </summary>
    [StringLength(50)]
    public string? Nationality { get; set; } = "مغربية";
    
    /// <summary>
    /// تاريخ الميلاد
    /// </summary>
    public DateTime? DateOfBirth { get; set; }
    
    /// <summary>
    /// الجنس
    /// </summary>
    public Gender? Gender { get; set; }
    
    /// <summary>
    /// المهنة
    /// </summary>
    [StringLength(100)]
    public string? Profession { get; set; }
    
    /// <summary>
    /// الحالة الاجتماعية
    /// </summary>
    public MaritalStatus? MaritalStatus { get; set; }
    
    /// <summary>
    /// اسم الشركة (للشركات)
    /// </summary>
    [StringLength(200)]
    public string? CompanyName { get; set; }
    
    /// <summary>
    /// رقم السجل التجاري (للشركات)
    /// </summary>
    [StringLength(50)]
    public string? CommercialRegister { get; set; }
    
    /// <summary>
    /// رقم الضريبة (للشركات)
    /// </summary>
    [StringLength(50)]
    public string? TaxNumber { get; set; }
    
    /// <summary>
    /// الممثل القانوني (للشركات)
    /// </summary>
    [StringLength(100)]
    public string? LegalRepresentative { get; set; }
    
    /// <summary>
    /// حالة الموكل
    /// </summary>
    public ClientStatus Status { get; set; } = ClientStatus.Active;
    
    /// <summary>
    /// مصدر الموكل
    /// </summary>
    [StringLength(100)]
    public string? Source { get; set; }
    
    /// <summary>
    /// تقييم الموكل
    /// </summary>
    public int? Rating { get; set; }
    
    /// <summary>
    /// الصورة الشخصية أو شعار الشركة
    /// </summary>
    public string? Photo { get; set; }
    
    /// <summary>
    /// المرفقات (JSON)
    /// </summary>
    public string? Attachments { get; set; }
    
    // Navigation Properties
    public virtual ICollection<Case> Cases { get; set; } = new List<Case>();
    public virtual ICollection<Appointment> Appointments { get; set; } = new List<Appointment>();
    public virtual ICollection<ClientContact> Contacts { get; set; } = new List<ClientContact>();
    public virtual ICollection<ClientDocument> Documents { get; set; } = new List<ClientDocument>();
}

/// <summary>
/// نوع الموكل
/// </summary>
public enum ClientType
{
    /// <summary>
    /// فرد
    /// </summary>
    Individual = 1,
    
    /// <summary>
    /// شركة
    /// </summary>
    Company = 2,
    
    /// <summary>
    /// مؤسسة حكومية
    /// </summary>
    Government = 3,
    
    /// <summary>
    /// جمعية
    /// </summary>
    Association = 4
}

/// <summary>
/// الجنس
/// </summary>
public enum Gender
{
    /// <summary>
    /// ذكر
    /// </summary>
    Male = 1,
    
    /// <summary>
    /// أنثى
    /// </summary>
    Female = 2
}

/// <summary>
/// الحالة الاجتماعية
/// </summary>
public enum MaritalStatus
{
    /// <summary>
    /// أعزب/عزباء
    /// </summary>
    Single = 1,
    
    /// <summary>
    /// متزوج/متزوجة
    /// </summary>
    Married = 2,
    
    /// <summary>
    /// مطلق/مطلقة
    /// </summary>
    Divorced = 3,
    
    /// <summary>
    /// أرمل/أرملة
    /// </summary>
    Widowed = 4
}

/// <summary>
/// حالة الموكل
/// </summary>
public enum ClientStatus
{
    /// <summary>
    /// نشط
    /// </summary>
    Active = 1,
    
    /// <summary>
    /// غير نشط
    /// </summary>
    Inactive = 2,
    
    /// <summary>
    /// محظور
    /// </summary>
    Blocked = 3,
    
    /// <summary>
    /// متوفى
    /// </summary>
    Deceased = 4
}
