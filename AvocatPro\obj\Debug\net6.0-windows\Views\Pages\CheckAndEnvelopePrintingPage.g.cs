﻿#pragma checksum "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "EA744D7362FFC1CE1DC5E09A4BC22B2F23DDFF8A"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Pages {
    
    
    /// <summary>
    /// CheckAndEnvelopePrintingPage
    /// </summary>
    public partial class CheckAndEnvelopePrintingPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 59 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CheckNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker CheckDatePicker;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PayeeNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox BankComboBox;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AmountWordsTextBox;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AccountNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SignatureTextBox;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MemoTextBox;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CrossedCheckBox;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CrossedTextTextBox;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CheckTemplateComboBox;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PreviewCheckButton;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintCheckButton;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveCheckTemplateButton;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearCheckFormButton;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SenderNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SenderCityTextBox;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SenderAddressTextBox;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RecipientNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RecipientCityTextBox;
        
        #line default
        #line hidden
        
        
        #line 221 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RecipientAddressTextBox;
        
        #line default
        #line hidden
        
        
        #line 230 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RegisteredCheckBox;
        
        #line default
        #line hidden
        
        
        #line 231 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox UrgentCheckBox;
        
        #line default
        #line hidden
        
        
        #line 235 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SpecialInstructionsTextBox;
        
        #line default
        #line hidden
        
        
        #line 247 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox EnvelopeSizeComboBox;
        
        #line default
        #line hidden
        
        
        #line 252 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PreviewEnvelopeButton;
        
        #line default
        #line hidden
        
        
        #line 256 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintEnvelopeButton;
        
        #line default
        #line hidden
        
        
        #line 260 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveEnvelopeTemplateButton;
        
        #line default
        #line hidden
        
        
        #line 264 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearEnvelopeFormButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/pages/checkandenvelopeprintingpage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CheckNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.CheckDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 3:
            this.PayeeNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.AmountTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 78 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
            this.AmountTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.AmountTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BankComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            this.AmountWordsTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.AccountNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.SignatureTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.MemoTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.CrossedCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 115 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
            this.CrossedCheckBox.Checked += new System.Windows.RoutedEventHandler(this.CrossedCheckBox_Checked);
            
            #line default
            #line hidden
            
            #line 115 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
            this.CrossedCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.CrossedCheckBox_Unchecked);
            
            #line default
            #line hidden
            return;
            case 11:
            this.CrossedTextTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.CheckTemplateComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 13:
            this.PreviewCheckButton = ((System.Windows.Controls.Button)(target));
            
            #line 136 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
            this.PreviewCheckButton.Click += new System.Windows.RoutedEventHandler(this.PreviewCheckButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.PrintCheckButton = ((System.Windows.Controls.Button)(target));
            
            #line 140 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
            this.PrintCheckButton.Click += new System.Windows.RoutedEventHandler(this.PrintCheckButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.SaveCheckTemplateButton = ((System.Windows.Controls.Button)(target));
            
            #line 144 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
            this.SaveCheckTemplateButton.Click += new System.Windows.RoutedEventHandler(this.SaveCheckTemplateButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.ClearCheckFormButton = ((System.Windows.Controls.Button)(target));
            
            #line 148 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
            this.ClearCheckFormButton.Click += new System.Windows.RoutedEventHandler(this.ClearCheckFormButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.SenderNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 18:
            this.SenderCityTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.SenderAddressTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.RecipientNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.RecipientCityTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 22:
            this.RecipientAddressTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 23:
            this.RegisteredCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 24:
            this.UrgentCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 25:
            this.SpecialInstructionsTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 26:
            this.EnvelopeSizeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 27:
            this.PreviewEnvelopeButton = ((System.Windows.Controls.Button)(target));
            
            #line 254 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
            this.PreviewEnvelopeButton.Click += new System.Windows.RoutedEventHandler(this.PreviewEnvelopeButton_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.PrintEnvelopeButton = ((System.Windows.Controls.Button)(target));
            
            #line 258 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
            this.PrintEnvelopeButton.Click += new System.Windows.RoutedEventHandler(this.PrintEnvelopeButton_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.SaveEnvelopeTemplateButton = ((System.Windows.Controls.Button)(target));
            
            #line 262 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
            this.SaveEnvelopeTemplateButton.Click += new System.Windows.RoutedEventHandler(this.SaveEnvelopeTemplateButton_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            this.ClearEnvelopeFormButton = ((System.Windows.Controls.Button)(target));
            
            #line 266 "..\..\..\..\..\Views\Pages\CheckAndEnvelopePrintingPage.xaml"
            this.ClearEnvelopeFormButton.Click += new System.Windows.RoutedEventHandler(this.ClearEnvelopeFormButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

