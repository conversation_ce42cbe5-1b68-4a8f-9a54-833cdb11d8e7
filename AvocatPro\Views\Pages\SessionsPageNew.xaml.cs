using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using AvocatPro.Models;
using AvocatPro.Views.Windows;

namespace AvocatPro.Views.Pages;

public partial class SessionsPageNew : Page
{
    private readonly User _currentUser;
    private readonly ObservableCollection<SessionDisplayModel> _allSessions;
    private readonly ObservableCollection<SessionDisplayModel> _filteredSessions;
    private DateTime _currentMonth;
    private bool _isCalendarView = true;

    public SessionsPageNew(User currentUser)
    {
        InitializeComponent();
        _currentUser = currentUser;
        _allSessions = new ObservableCollection<SessionDisplayModel>();
        _filteredSessions = new ObservableCollection<SessionDisplayModel>();
        _currentMonth = DateTime.Now;
        
        this.Loaded += SessionsPageNew_Loaded;
    }

    private void SessionsPageNew_Loaded(object sender, RoutedEventArgs e)
    {
        try
        {
            if (SessionsDataGrid != null)
            {
                SessionsDataGrid.ItemsSource = _filteredSessions;
            }
            
            LoadSampleData();
            UpdateStatistics();
            UpdateCurrentMonthDisplay();
            BuildCalendar();
            UpdateStatusText();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل صفحة الجلسات: {ex.Message}", "خطأ", 
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void LoadSampleData()
    {
        var sampleSessions = new List<SessionDisplayModel>
        {
            new SessionDisplayModel
            {
                Id = 1,
                CaseId = 1,
                CaseTitle = "دعوى مطالبة مالية",
                CaseReference = "CASE-20241201-001",
                SessionDate = DateTime.Now.AddDays(2),
                SessionTime = new TimeSpan(9, 0, 0),
                Type = SessionType.Pleading,
                ProcedureType = "مرافعة",
                Decision = null,
                NextSessionDate = DateTime.Now.AddDays(16),
                NextSessionTime = new TimeSpan(10, 0, 0),
                Status = SessionStatus.Scheduled,
                AssignedLawyerName = "د. محمد أحمد الشريف",
                Judge = "القاضي أحمد العتيبي",
                CourtRoom = "قاعة 3",
                ClientName = "أحمد محمد علي السعيد",
                Court = "المحكمة العامة بالرياض",
                Duration = 60,
                Expenses = 500
            },
            new SessionDisplayModel
            {
                Id = 2,
                CaseId = 2,
                CaseTitle = "قضية عمالية - فصل تعسفي",
                CaseReference = "CASE-20241215-002",
                SessionDate = DateTime.Now.AddDays(5),
                SessionTime = new TimeSpan(11, 0, 0),
                Type = SessionType.Regular,
                ProcedureType = "تقديم أدلة",
                Decision = null,
                NextSessionDate = DateTime.Now.AddDays(19),
                NextSessionTime = new TimeSpan(9, 30, 0),
                Status = SessionStatus.Scheduled,
                AssignedLawyerName = "أ. سارة عبدالله النور",
                Judge = "القاضي فاطمة الزهراني",
                CourtRoom = "قاعة 1",
                ClientName = "فاطمة أحمد سالم",
                Court = "محكمة العمل بالرياض",
                Duration = 45,
                Expenses = 300
            },
            new SessionDisplayModel
            {
                Id = 3,
                CaseId = 4,
                CaseTitle = "قضية أسرة - نفقة",
                CaseReference = "CASE-20241120-004",
                SessionDate = DateTime.Now.AddDays(-3),
                SessionTime = new TimeSpan(10, 30, 0),
                Type = SessionType.Judgment,
                ProcedureType = "حكم",
                Decision = "الحكم بالنفقة الشهرية 3000 ريال",
                NextSessionDate = null,
                NextSessionTime = null,
                Status = SessionStatus.Completed,
                AssignedLawyerName = "د. نورا محمد الزهراني",
                Judge = "القاضي محمد السالم",
                CourtRoom = "قاعة 2",
                ClientName = "مريم عبدالله الزهراني",
                Court = "محكمة الأحوال الشخصية",
                Duration = 30,
                Expenses = 200
            },
            new SessionDisplayModel
            {
                Id = 4,
                CaseId = 6,
                CaseTitle = "قضية جنائية - اختلاس",
                CaseReference = "CASE-20241205-006",
                SessionDate = DateTime.Now.AddDays(1),
                SessionTime = new TimeSpan(14, 0, 0),
                Type = SessionType.Urgent,
                ProcedureType = "استجواب شهود",
                Decision = null,
                NextSessionDate = DateTime.Now.AddDays(8),
                NextSessionTime = new TimeSpan(9, 0, 0),
                Status = SessionStatus.Scheduled,
                AssignedLawyerName = "أ. خالد سالم العتيبي",
                Judge = "القاضي عبدالله الغامدي",
                CourtRoom = "قاعة 5",
                ClientName = "جمعية البر الخيرية",
                Court = "المحكمة الجزائية بالرياض",
                Duration = 90,
                Expenses = 800
            },
            new SessionDisplayModel
            {
                Id = 5,
                CaseId = 1,
                CaseTitle = "دعوى مطالبة مالية",
                CaseReference = "CASE-20241201-001",
                SessionDate = DateTime.Now.AddDays(-10),
                SessionTime = new TimeSpan(9, 0, 0),
                Type = SessionType.Preparatory,
                ProcedureType = "إجراء تحضيري",
                Decision = "تأجيل الجلسة لتقديم مستندات إضافية",
                NextSessionDate = DateTime.Now.AddDays(2),
                NextSessionTime = new TimeSpan(9, 0, 0),
                Status = SessionStatus.Completed,
                AssignedLawyerName = "د. محمد أحمد الشريف",
                Judge = "القاضي أحمد العتيبي",
                CourtRoom = "قاعة 3",
                ClientName = "أحمد محمد علي السعيد",
                Court = "المحكمة العامة بالرياض",
                Duration = 30,
                Expenses = 200
            },
            new SessionDisplayModel
            {
                Id = 6,
                CaseId = 3,
                CaseTitle = "قضية تجارية - نزاع شراكة",
                CaseReference = "CASE-20241101-003",
                SessionDate = DateTime.Now.AddDays(7),
                SessionTime = new TimeSpan(15, 30, 0),
                Type = SessionType.Settlement,
                ProcedureType = "صلح",
                Decision = null,
                NextSessionDate = null,
                NextSessionTime = null,
                Status = SessionStatus.Scheduled,
                AssignedLawyerName = "أ. خالد سالم العتيبي",
                Judge = "القاضي سعد الحربي",
                CourtRoom = "قاعة 4",
                ClientName = "شركة النور للتجارة والاستثمار",
                Court = "المحكمة التجارية بجدة",
                Duration = 120,
                Expenses = 1000
            }
        };

        _allSessions.Clear();
        _filteredSessions.Clear();
        
        foreach (var session in sampleSessions)
        {
            _allSessions.Add(session);
            _filteredSessions.Add(session);
        }
    }

    private void UpdateStatistics()
    {
        try
        {
            var today = DateTime.Today;
            var startOfWeek = today.AddDays(-(int)today.DayOfWeek + (int)DayOfWeek.Saturday);
            var endOfWeek = startOfWeek.AddDays(6);
            var startOfMonth = new DateTime(today.Year, today.Month, 1);
            var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);

            if (TotalSessionsTextBlock != null)
            {
                TotalSessionsTextBlock.Text = _allSessions.Count.ToString();
                TodaySessionsTextBlock.Text = _allSessions.Count(s => s.SessionDate.Date == today).ToString();
                WeekSessionsTextBlock.Text = _allSessions.Count(s => s.SessionDate.Date >= startOfWeek && s.SessionDate.Date <= endOfWeek).ToString();
                MonthSessionsTextBlock.Text = _allSessions.Count(s => s.SessionDate.Date >= startOfMonth && s.SessionDate.Date <= endOfMonth).ToString();
                CompletedSessionsTextBlock.Text = _allSessions.Count(s => s.Status == SessionStatus.Completed).ToString();
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الإحصائيات: {ex.Message}");
        }
    }

    private void UpdateCurrentMonthDisplay()
    {
        try
        {
            if (CurrentMonthTextBlock != null)
            {
                var culture = new CultureInfo("ar-SA");
                CurrentMonthTextBlock.Text = _currentMonth.ToString("MMMM yyyy", culture);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث عرض الشهر: {ex.Message}");
        }
    }

    private void UpdateStatusText()
    {
        try
        {
            if (StatusTextBlock != null)
            {
                StatusTextBlock.Text = $"عرض {_filteredSessions.Count} من أصل {_allSessions.Count} جلسة";
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث نص الحالة: {ex.Message}");
        }
    }

    private void ApplyFilters()
    {
        try
        {
            var selectedType = (SessionTypeFilterComboBox?.SelectedItem as ComboBoxItem)?.Tag?.ToString();
            var selectedStatus = (SessionStatusFilterComboBox?.SelectedItem as ComboBoxItem)?.Tag?.ToString();

            var filtered = _allSessions.Where(session =>
            {
                var matchesType = selectedType == "All" || selectedType == null ||
                                 session.Type.ToString() == selectedType;

                var matchesStatus = selectedStatus == "All" || selectedStatus == null ||
                                   session.Status.ToString() == selectedStatus;

                return matchesType && matchesStatus;
            });

            _filteredSessions.Clear();
            foreach (var session in filtered)
            {
                _filteredSessions.Add(session);
            }

            if (_isCalendarView)
            {
                BuildCalendar();
            }
            
            UpdateStatusText();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق المرشحات: {ex.Message}");
        }
    }

    private void BuildCalendar()
    {
        try
        {
            if (CalendarGrid == null) return;

            CalendarGrid.Children.Clear();

            var firstDayOfMonth = new DateTime(_currentMonth.Year, _currentMonth.Month, 1);
            var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);

            // البداية من السبت (أول يوم في الأسبوع العربي)
            var startDate = firstDayOfMonth;
            while (startDate.DayOfWeek != DayOfWeek.Saturday)
            {
                startDate = startDate.AddDays(-1);
            }

            // إنشاء 42 يوم (6 أسابيع × 7 أيام)
            for (int i = 0; i < 42; i++)
            {
                var currentDate = startDate.AddDays(i);
                var dayBorder = CreateCalendarDay(currentDate, firstDayOfMonth, lastDayOfMonth);
                CalendarGrid.Children.Add(dayBorder);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في بناء التقويم: {ex.Message}");
        }
    }

    private Border CreateCalendarDay(DateTime date, DateTime firstDayOfMonth, DateTime lastDayOfMonth)
    {
        var dayBorder = new Border
        {
            Background = new SolidColorBrush(Colors.White),
            BorderBrush = new SolidColorBrush(Color.FromRgb(224, 224, 224)),
            BorderThickness = new Thickness(1),
            Margin = new Thickness(1),
            Padding = new Thickness(5),
            Height = 80,
            Cursor = System.Windows.Input.Cursors.Hand,
            Tag = date
        };

        var stackPanel = new StackPanel();

        // رقم اليوم
        var dayNumber = new TextBlock
        {
            Text = date.Day.ToString(),
            FontWeight = FontWeights.Bold,
            HorizontalAlignment = HorizontalAlignment.Left,
            Margin = new Thickness(0, 0, 0, 2)
        };

        // تلوين الأيام حسب الشهر
        if (date < firstDayOfMonth || date > lastDayOfMonth)
        {
            dayNumber.Foreground = new SolidColorBrush(Colors.LightGray);
            dayBorder.Background = new SolidColorBrush(Color.FromRgb(250, 250, 250));
        }
        else if (date.Date == DateTime.Today)
        {
            dayNumber.Foreground = new SolidColorBrush(Colors.White);
            dayBorder.Background = new SolidColorBrush(Color.FromRgb(25, 118, 210));
        }
        else if (date.DayOfWeek == DayOfWeek.Friday)
        {
            dayNumber.Foreground = new SolidColorBrush(Color.FromRgb(244, 67, 54));
        }
        else
        {
            dayNumber.Foreground = new SolidColorBrush(Color.FromRgb(33, 33, 33));
        }

        stackPanel.Children.Add(dayNumber);

        // إضافة الجلسات لهذا اليوم
        var sessionsForDay = _filteredSessions.Where(s => s.SessionDate.Date == date.Date).Take(3);
        foreach (var session in sessionsForDay)
        {
            var sessionBorder = new Border
            {
                CornerRadius = new CornerRadius(3),
                Padding = new Thickness(3, 1, 3, 1),
                Margin = new Thickness(0, 1, 0, 1),
                Cursor = System.Windows.Input.Cursors.Hand,
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(session.StatusColor)),
                ToolTip = $"{session.CalendarTitle}\n{session.SessionTimeDisplay}\n{session.CalendarDescription}"
            };

            var sessionText = new TextBlock
            {
                Text = $"{session.SessionTimeDisplay} {session.TypeDisplay}",
                FontSize = 9,
                Foreground = new SolidColorBrush(Colors.White),
                TextTrimming = TextTrimming.CharacterEllipsis
            };

            sessionBorder.Child = sessionText;
            stackPanel.Children.Add(sessionBorder);
        }

        // إضافة مؤشر إذا كان هناك جلسات أكثر
        var remainingSessions = _filteredSessions.Count(s => s.SessionDate.Date == date.Date) - 3;
        if (remainingSessions > 0)
        {
            var moreText = new TextBlock
            {
                Text = $"+{remainingSessions} أخرى",
                FontSize = 8,
                Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102)),
                HorizontalAlignment = HorizontalAlignment.Center
            };
            stackPanel.Children.Add(moreText);
        }

        dayBorder.Child = stackPanel;
        dayBorder.MouseLeftButtonUp += (s, e) => DayBorder_Click(date);

        return dayBorder;
    }

    private void DayBorder_Click(DateTime date)
    {
        var sessionsForDay = _filteredSessions.Where(s => s.SessionDate.Date == date.Date).ToList();

        if (sessionsForDay.Count == 0)
        {
            MessageBox.Show($"لا توجد جلسات في يوم {date:dd/MM/yyyy}", "جلسات اليوم",
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }
        else
        {
            var message = $"جلسات يوم {date:dd/MM/yyyy}:\n\n";
            foreach (var session in sessionsForDay)
            {
                message += $"• {session.SessionTimeDisplay} - {session.CaseTitle}\n";
                message += $"  النوع: {session.TypeDisplay} | الحالة: {session.StatusDisplay}\n";
                message += $"  المحامي: {session.AssignedLawyerDisplay}\n\n";
            }

            MessageBox.Show(message, "جلسات اليوم", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    // معالجات الأحداث
    private void AddSessionButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var addWindow = new AddSessionWindow();
            if (addWindow.ShowDialog() == true)
            {
                if (addWindow.NewSession != null)
                {
                    var newSessionDisplay = new SessionDisplayModel
                    {
                        Id = _allSessions.Count + 1,
                        CaseId = addWindow.NewSession.CaseId,
                        CaseTitle = "قضية جديدة",
                        CaseReference = "REF-NEW",
                        SessionDate = addWindow.NewSession.SessionDate,
                        SessionTime = addWindow.NewSession.SessionTime,
                        Type = addWindow.NewSession.Type,
                        ProcedureType = addWindow.NewSession.ProcedureType,
                        Decision = addWindow.NewSession.Decision,
                        NextSessionDate = addWindow.NewSession.NextSessionDate,
                        NextSessionTime = addWindow.NewSession.NextSessionTime,
                        Status = addWindow.NewSession.Status,
                        AssignedLawyerName = "محامي جديد",
                        Judge = addWindow.NewSession.Judge,
                        CourtRoom = addWindow.NewSession.CourtRoom,
                        SessionNotes = addWindow.NewSession.SessionNotes,
                        Attendance = addWindow.NewSession.Attendance,
                        Arguments = addWindow.NewSession.Arguments,
                        Evidence = addWindow.NewSession.Evidence,
                        Duration = addWindow.NewSession.Duration,
                        Expenses = addWindow.NewSession.Expenses,
                        ClientName = "موكل جديد",
                        Court = "محكمة جديدة",
                        CreatedAt = DateTime.Now
                    };

                    _allSessions.Add(newSessionDisplay);
                    ApplyFilters();
                    UpdateStatistics();

                    MessageBox.Show("تم إضافة الجلسة بنجاح!", "نجح الحفظ",
                                   MessageBoxButton.OK, MessageBoxImage.Information);

                    if (addWindow.SaveAndAddAnother)
                    {
                        AddSessionButton_Click(sender, e);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إضافة الجلسة: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void RefreshButton_Click(object sender, RoutedEventArgs e)
    {
        LoadSampleData();
        UpdateStatistics();
        BuildCalendar();
        UpdateStatusText();
        MessageBox.Show("تم تحديث البيانات بنجاح!", "تحديث",
                       MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void PrevMonthButton_Click(object sender, RoutedEventArgs e)
    {
        _currentMonth = _currentMonth.AddMonths(-1);
        UpdateCurrentMonthDisplay();
        BuildCalendar();
    }

    private void NextMonthButton_Click(object sender, RoutedEventArgs e)
    {
        _currentMonth = _currentMonth.AddMonths(1);
        UpdateCurrentMonthDisplay();
        BuildCalendar();
    }

    private void FilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        ApplyFilters();
    }

    private void ViewModeButton_Click(object sender, RoutedEventArgs e)
    {
        _isCalendarView = !_isCalendarView;

        if (_isCalendarView)
        {
            CalendarView.Visibility = Visibility.Visible;
            TableView.Visibility = Visibility.Collapsed;
            ViewModeIcon.Text = "📋";
            ViewModeText.Text = "عرض الجدول";
        }
        else
        {
            CalendarView.Visibility = Visibility.Collapsed;
            TableView.Visibility = Visibility.Visible;
            ViewModeIcon.Text = "📅";
            ViewModeText.Text = "عرض التقويم";
        }
    }

    private void ExportButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var dialog = new Microsoft.Win32.SaveFileDialog
            {
                Filter = "Excel Files (*.xlsx)|*.xlsx|PDF Files (*.pdf)|*.pdf|CSV Files (*.csv)|*.csv",
                DefaultExt = "xlsx",
                FileName = $"جلسات_المحكمة_{DateTime.Now:yyyyMMdd}"
            };

            if (dialog.ShowDialog() == true)
            {
                MessageBox.Show($"تم تصدير {_filteredSessions.Count} جلسة بنجاح إلى:\n{dialog.FileName}",
                               "نجح التصدير", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void PrintButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            MessageBox.Show($"تم إرسال تقرير الجلسات للطباعة\n\nعدد الجلسات: {_filteredSessions.Count}",
                           "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ViewSessionButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int sessionId)
        {
            var session = _allSessions.FirstOrDefault(s => s.Id == sessionId);
            if (session != null)
            {
                MessageBox.Show($"تفاصيل الجلسة:\n\n" +
                               $"القضية: {session.CaseTitle}\n" +
                               $"التاريخ والوقت: {session.SessionDateTimeDisplay}\n" +
                               $"النوع: {session.TypeDisplay}\n" +
                               $"نوع الإجراء: {session.ProcedureTypeDisplay}\n" +
                               $"الحالة: {session.StatusDisplay}\n" +
                               $"المحامي: {session.AssignedLawyerDisplay}\n" +
                               $"القاضي: {session.JudgeDisplay}\n" +
                               $"القاعة: {session.CourtRoomDisplay}\n" +
                               $"القرار: {session.DecisionDisplay}\n" +
                               $"المدة: {session.DurationDisplay}\n" +
                               $"المصاريف: {session.ExpensesDisplay}\n" +
                               $"الجلسة القادمة: {session.NextSessionDateTimeDisplay}",
                               "تفاصيل الجلسة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }

    private void EditSessionButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int sessionId)
        {
            MessageBox.Show($"سيتم فتح نافذة تعديل الجلسة رقم: {sessionId}", "تعديل الجلسة",
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void DocumentsButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int sessionId)
        {
            MessageBox.Show($"عرض مرفقات الجلسة رقم: {sessionId}\n\nسيتم تطوير هذه الميزة في التحديث القادم",
                           "مرفقات الجلسة", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void NotifyButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int sessionId)
        {
            var session = _allSessions.FirstOrDefault(s => s.Id == sessionId);
            if (session != null)
            {
                MessageBox.Show($"تم إرسال تنبيه للجلسة:\n\n" +
                               $"القضية: {session.CaseTitle}\n" +
                               $"التاريخ والوقت: {session.SessionDateTimeDisplay}\n" +
                               $"المحامي: {session.AssignedLawyerDisplay}",
                               "تم إرسال التنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }

    private void DeleteSessionButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is int sessionId)
        {
            var session = _allSessions.FirstOrDefault(s => s.Id == sessionId);
            if (session != null)
            {
                var result = MessageBox.Show($"هل تريد حذف الجلسة:\n{session.CaseTitle} - {session.SessionDateTimeDisplay}؟\n\nلا يمكن التراجع عن هذا الإجراء!",
                                           "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    _allSessions.Remove(session);
                    ApplyFilters();
                    UpdateStatistics();

                    MessageBox.Show("تم حذف الجلسة بنجاح!", "نجح الحذف",
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }
    }
}
