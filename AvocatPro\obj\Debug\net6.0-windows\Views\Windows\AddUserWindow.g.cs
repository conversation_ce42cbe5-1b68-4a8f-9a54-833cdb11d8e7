﻿#pragma checksum "..\..\..\..\..\Views\Windows\AddUserWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "AFA278DEC1146038B8E670A375960AAA913272DE"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using FontAwesome.WPF;
using FontAwesome.WPF.Converters;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Windows {
    
    
    /// <summary>
    /// AddUserWindow
    /// </summary>
    public partial class AddUserWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 123 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UsernameTextBox;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FirstNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PhoneTextBox;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox EmailTextBox;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LastNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DepartmentComboBox;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox PasswordBox;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RequirePasswordChangeCheckBox;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox ConfirmPasswordBox;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox TwoFactorCheckBox;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox AvailableRolesListBox;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddRoleBtn;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RemoveRoleBtn;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox SelectedRolesListBox;
        
        #line default
        #line hidden
        
        
        #line 243 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsActiveCheckBox;
        
        #line default
        #line hidden
        
        
        #line 246 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EmailNotificationsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 249 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox SystemNotificationsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 255 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SessionTimeoutTextBox;
        
        #line default
        #line hidden
        
        
        #line 259 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 270 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveBtn;
        
        #line default
        #line hidden
        
        
        #line 278 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveAndAddBtn;
        
        #line default
        #line hidden
        
        
        #line 286 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelBtn;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/windows/adduserwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 103 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseBtn_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.UsernameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.FirstNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.PhoneTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.EmailTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.LastNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.DepartmentComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.PasswordBox = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 9:
            this.RequirePasswordChangeCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 10:
            this.ConfirmPasswordBox = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 11:
            this.TwoFactorCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 12:
            this.AvailableRolesListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 13:
            this.AddRoleBtn = ((System.Windows.Controls.Button)(target));
            
            #line 217 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
            this.AddRoleBtn.Click += new System.Windows.RoutedEventHandler(this.AddRole_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.RemoveRoleBtn = ((System.Windows.Controls.Button)(target));
            
            #line 220 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
            this.RemoveRoleBtn.Click += new System.Windows.RoutedEventHandler(this.RemoveRole_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.SelectedRolesListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 16:
            this.IsActiveCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 17:
            this.EmailNotificationsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 18:
            this.SystemNotificationsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 19:
            this.SessionTimeoutTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.SaveBtn = ((System.Windows.Controls.Button)(target));
            
            #line 271 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
            this.SaveBtn.Click += new System.Windows.RoutedEventHandler(this.SaveBtn_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.SaveAndAddBtn = ((System.Windows.Controls.Button)(target));
            
            #line 279 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
            this.SaveAndAddBtn.Click += new System.Windows.RoutedEventHandler(this.SaveAndAddBtn_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.CancelBtn = ((System.Windows.Controls.Button)(target));
            
            #line 287 "..\..\..\..\..\Views\Windows\AddUserWindow.xaml"
            this.CancelBtn.Click += new System.Windows.RoutedEventHandler(this.CancelBtn_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

