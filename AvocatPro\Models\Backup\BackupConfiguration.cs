using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AvocatPro.Models.Backup
{
    /// <summary>
    /// إعدادات النسخ الاحتياطية
    /// </summary>
    public class BackupConfiguration
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// اسم الإعداد
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// وصف الإعداد
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// نوع النسخة الاحتياطية
        /// </summary>
        public BackupType BackupType { get; set; }

        /// <summary>
        /// مكان الحفظ
        /// </summary>
        public BackupDestination Destination { get; set; }

        /// <summary>
        /// مسار الحفظ المحلي
        /// </summary>
        [StringLength(500)]
        public string? LocalPath { get; set; }

        /// <summary>
        /// معرف مجلد Google Drive
        /// </summary>
        [StringLength(200)]
        public string? GoogleDriveFolderId { get; set; }

        /// <summary>
        /// تكرار النسخ الاحتياطية
        /// </summary>
        public BackupFrequency Frequency { get; set; }

        /// <summary>
        /// وقت تنفيذ النسخة الاحتياطية
        /// </summary>
        public TimeSpan ScheduledTime { get; set; }

        /// <summary>
        /// أيام الأسبوع للنسخ الأسبوعي
        /// </summary>
        public DaysOfWeek? WeeklyDays { get; set; }

        /// <summary>
        /// يوم الشهر للنسخ الشهري
        /// </summary>
        public int? MonthlyDay { get; set; }

        /// <summary>
        /// هل النسخة الاحتياطية مفعلة
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// تضمين قاعدة البيانات
        /// </summary>
        public bool IncludeDatabase { get; set; } = true;

        /// <summary>
        /// تضمين الملفات المرفقة
        /// </summary>
        public bool IncludeAttachments { get; set; } = true;

        /// <summary>
        /// تضمين الصور
        /// </summary>
        public bool IncludeImages { get; set; } = true;

        /// <summary>
        /// تضمين التقارير
        /// </summary>
        public bool IncludeReports { get; set; } = true;

        /// <summary>
        /// تضمين إعدادات النظام
        /// </summary>
        public bool IncludeSettings { get; set; } = true;

        /// <summary>
        /// ضغط النسخة الاحتياطية
        /// </summary>
        public bool CompressBackup { get; set; } = true;

        /// <summary>
        /// تشفير النسخة الاحتياطية
        /// </summary>
        public bool EncryptBackup { get; set; } = false;

        /// <summary>
        /// كلمة مرور التشفير (مشفرة)
        /// </summary>
        [StringLength(500)]
        public string? EncryptionPassword { get; set; }

        /// <summary>
        /// الحد الأقصى لعدد النسخ المحفوظة
        /// </summary>
        public int MaxBackupsToKeep { get; set; } = 10;

        /// <summary>
        /// حذف النسخ القديمة تلقائياً
        /// </summary>
        public bool AutoDeleteOldBackups { get; set; } = true;

        /// <summary>
        /// إرسال إشعار عند اكتمال النسخة
        /// </summary>
        public bool SendNotificationOnComplete { get; set; } = true;

        /// <summary>
        /// إرسال إشعار عند فشل النسخة
        /// </summary>
        public bool SendNotificationOnFailure { get; set; } = true;

        /// <summary>
        /// البريد الإلكتروني للإشعارات
        /// </summary>
        [StringLength(200)]
        public string? NotificationEmail { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// معرف المستخدم المنشئ
        /// </summary>
        public int CreatedBy { get; set; }

        /// <summary>
        /// معرف آخر مستخدم قام بالتحديث
        /// </summary>
        public int UpdatedBy { get; set; }

        /// <summary>
        /// آخر نسخة احتياطية تم تنفيذها
        /// </summary>
        public DateTime? LastBackupDate { get; set; }

        /// <summary>
        /// تاريخ النسخة الاحتياطية التالية
        /// </summary>
        public DateTime? NextBackupDate { get; set; }

        /// <summary>
        /// حالة آخر نسخة احتياطية
        /// </summary>
        public BackupStatus? LastBackupStatus { get; set; }

        /// <summary>
        /// رسالة آخر نسخة احتياطية
        /// </summary>
        [StringLength(1000)]
        public string? LastBackupMessage { get; set; }

        /// <summary>
        /// حجم آخر نسخة احتياطية بالبايت
        /// </summary>
        public long? LastBackupSize { get; set; }

        /// <summary>
        /// مدة آخر نسخة احتياطية بالثواني
        /// </summary>
        public int? LastBackupDuration { get; set; }

        /// <summary>
        /// عدد النسخ الاحتياطية الناجحة
        /// </summary>
        public int SuccessfulBackupsCount { get; set; } = 0;

        /// <summary>
        /// عدد النسخ الاحتياطية الفاشلة
        /// </summary>
        public int FailedBackupsCount { get; set; } = 0;

        /// <summary>
        /// إجمالي حجم النسخ الاحتياطية
        /// </summary>
        public long TotalBackupSize { get; set; } = 0;

        /// <summary>
        /// حساب تاريخ النسخة الاحتياطية التالية
        /// </summary>
        public DateTime CalculateNextBackupDate()
        {
            var baseDate = LastBackupDate ?? DateTime.Now;
            
            return Frequency switch
            {
                BackupFrequency.Daily => baseDate.Date.AddDays(1).Add(ScheduledTime),
                BackupFrequency.Weekly => CalculateNextWeeklyDate(baseDate),
                BackupFrequency.Monthly => CalculateNextMonthlyDate(baseDate),
                BackupFrequency.Manual => DateTime.MaxValue,
                _ => DateTime.MaxValue
            };
        }

        /// <summary>
        /// حساب تاريخ النسخة الأسبوعية التالية
        /// </summary>
        private DateTime CalculateNextWeeklyDate(DateTime baseDate)
        {
            if (!WeeklyDays.HasValue) return DateTime.MaxValue;

            var nextDate = baseDate.Date.AddDays(1);
            while (!WeeklyDays.Value.HasFlag((DaysOfWeek)(1 << (int)nextDate.DayOfWeek)))
            {
                nextDate = nextDate.AddDays(1);
            }
            
            return nextDate.Add(ScheduledTime);
        }

        /// <summary>
        /// حساب تاريخ النسخة الشهرية التالية
        /// </summary>
        private DateTime CalculateNextMonthlyDate(DateTime baseDate)
        {
            if (!MonthlyDay.HasValue) return DateTime.MaxValue;

            var nextMonth = baseDate.AddMonths(1);
            var daysInMonth = DateTime.DaysInMonth(nextMonth.Year, nextMonth.Month);
            var day = Math.Min(MonthlyDay.Value, daysInMonth);
            
            return new DateTime(nextMonth.Year, nextMonth.Month, day).Add(ScheduledTime);
        }

        /// <summary>
        /// تحديث إحصائيات النسخة الاحتياطية
        /// </summary>
        public void UpdateBackupStats(BackupStatus status, string? message, long size, int duration)
        {
            LastBackupDate = DateTime.Now;
            LastBackupStatus = status;
            LastBackupMessage = message;
            LastBackupSize = size;
            LastBackupDuration = duration;
            
            if (status == BackupStatus.Completed)
            {
                SuccessfulBackupsCount++;
                TotalBackupSize += size;
            }
            else if (status == BackupStatus.Failed)
            {
                FailedBackupsCount++;
            }
            
            NextBackupDate = CalculateNextBackupDate();
            UpdatedAt = DateTime.Now;
        }
    }

    /// <summary>
    /// نوع النسخة الاحتياطية
    /// </summary>
    public enum BackupType
    {
        /// <summary>
        /// نسخة كاملة
        /// </summary>
        Full = 1,
        
        /// <summary>
        /// نسخة تزايدية
        /// </summary>
        Incremental = 2,
        
        /// <summary>
        /// نسخة تفاضلية
        /// </summary>
        Differential = 3
    }

    /// <summary>
    /// وجهة النسخة الاحتياطية
    /// </summary>
    public enum BackupDestination
    {
        /// <summary>
        /// محلي (سطح المكتب أو مجلد محدد)
        /// </summary>
        Local = 1,
        
        /// <summary>
        /// Google Drive
        /// </summary>
        GoogleDrive = 2,
        
        /// <summary>
        /// كلاهما
        /// </summary>
        Both = 3
    }

    /// <summary>
    /// تكرار النسخ الاحتياطية
    /// </summary>
    public enum BackupFrequency
    {
        /// <summary>
        /// يدوي
        /// </summary>
        Manual = 0,
        
        /// <summary>
        /// يومي
        /// </summary>
        Daily = 1,
        
        /// <summary>
        /// أسبوعي
        /// </summary>
        Weekly = 2,
        
        /// <summary>
        /// شهري
        /// </summary>
        Monthly = 3
    }

    /// <summary>
    /// أيام الأسبوع
    /// </summary>
    [Flags]
    public enum DaysOfWeek
    {
        Sunday = 1,
        Monday = 2,
        Tuesday = 4,
        Wednesday = 8,
        Thursday = 16,
        Friday = 32,
        Saturday = 64
    }

    /// <summary>
    /// حالة النسخة الاحتياطية
    /// </summary>
    public enum BackupStatus
    {
        /// <summary>
        /// في الانتظار
        /// </summary>
        Pending = 1,
        
        /// <summary>
        /// قيد التنفيذ
        /// </summary>
        InProgress = 2,
        
        /// <summary>
        /// مكتملة
        /// </summary>
        Completed = 3,
        
        /// <summary>
        /// فاشلة
        /// </summary>
        Failed = 4,
        
        /// <summary>
        /// ملغاة
        /// </summary>
        Cancelled = 5
    }
}
