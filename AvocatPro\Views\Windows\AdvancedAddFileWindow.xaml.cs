using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using AvocatPro.Models;
using AvocatPro.Services;

namespace AvocatPro.Views.Windows
{
    /// <summary>
    /// نافذة إضافة/تعديل الملف المتقدمة
    /// </summary>
    public partial class AdvancedAddFileWindow : Window
    {
        #region Private Fields

        private readonly MoroccanCourtsService _courtsService;
        private AdvancedFileModel? _existingFile;
        private bool _isEditMode = false;

        #endregion

        #region Properties

        public AdvancedFileModel NewFile { get; private set; }
        public bool DialogResult { get; private set; } = false;

        #endregion

        #region Constructors

        /// <summary>
        /// إنشاء ملف جديد
        /// </summary>
        public AdvancedAddFileWindow()
        {
            InitializeComponent();
            _courtsService = new MoroccanCourtsService();
            NewFile = new AdvancedFileModel();
            InitializeControls();
            WindowTitle.Text = "إضافة ملف جديد";
        }

        /// <summary>
        /// تعديل ملف موجود
        /// </summary>
        public AdvancedAddFileWindow(AdvancedFileModel existingFile)
        {
            InitializeComponent();
            _courtsService = new MoroccanCourtsService();
            _existingFile = existingFile;
            _isEditMode = true;
            NewFile = new AdvancedFileModel();
            InitializeControls();
            LoadFileData();
            WindowTitle.Text = "تعديل الملف";
        }

        #endregion

        #region Initialization

        /// <summary>
        /// تهيئة العناصر
        /// </summary>
        private void InitializeControls()
        {
            try
            {
                // تحميل أنواع الملفات
                FileTypeComboBox.ItemsSource = _courtsService.GetFileTypes();
                FileTypeComboBox.SelectedIndex = 0;

                // تحميل المحاكم
                CourtComboBox.ItemsSource = _courtsService.GetMoroccanCourts();

                // تحميل أنواع القضايا
                CaseTypeComboBox.ItemsSource = _courtsService.GetCaseTypes();
                CaseTypeComboBox.SelectedIndex = 0;

                // تحميل أنواع الإجراءات
                ProcedureTypeComboBox.ItemsSource = _courtsService.GetProcedureTypes();

                // تحميل الحالات
                StatusComboBox.ItemsSource = _courtsService.GetFileStatuses();
                StatusComboBox.SelectedIndex = 0;

                // تحميل الأولويات
                PriorityComboBox.ItemsSource = _courtsService.GetPriorities();
                PriorityComboBox.SelectedIndex = 2; // عادي

                // تحميل محاكم الاستئناف
                var appealCourts = _courtsService.GetMoroccanCourts()
                    .Where(c => c.Contains("استئناف"))
                    .ToList();
                AppealCourtComboBox.ItemsSource = appealCourts;

                // تحميل العملاء (يمكن تحميلها من قاعدة البيانات)
                LoadClients();

                // تحميل المحامين
                LoadLawyers();

                // تعيين السنة الحالية
                ElectronicYearTextBox.Text = DateTime.Now.Year.ToString();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النافذة: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل قائمة العملاء
        /// </summary>
        private void LoadClients()
        {
            // في التطبيق الحقيقي، ستتم قراءة البيانات من قاعدة البيانات
            var clients = new[]
            {
                "أحمد محمد علي",
                "فاطمة الزهراء",
                "محمد الحسن",
                "عائشة بنت محمد",
                "يوسف الإدريسي",
                "خديجة العلوي",
                "عبد الرحمن الفاسي"
            };

            ClientComboBox.ItemsSource = clients;
        }

        /// <summary>
        /// تحميل قائمة المحامين
        /// </summary>
        private void LoadLawyers()
        {
            // في التطبيق الحقيقي، ستتم قراءة البيانات من قاعدة البيانات
            var lawyers = new[]
            {
                "الأستاذ محمد العلوي",
                "الأستاذة فاطمة الزهراء",
                "الأستاذ عبد الرحمن الفاسي",
                "الأستاذة خديجة الإدريسي",
                "الأستاذ يوسف الحسني"
            };

            LawyerComboBox.ItemsSource = lawyers;
        }

        /// <summary>
        /// تحميل بيانات الملف للتعديل
        /// </summary>
        private void LoadFileData()
        {
            if (_existingFile == null) return;

            try
            {
                FileNumberTextBox.Text = _existingFile.FileNumber;
                OfficeReferenceTextBox.Text = _existingFile.OfficeReference;
                CourtReferenceTextBox.Text = _existingFile.CourtReference;
                FileTypeComboBox.Text = _existingFile.FileType;
                CourtComboBox.Text = _existingFile.Court;
                CaseTypeComboBox.Text = _existingFile.CaseType;
                OpponentTextBox.Text = _existingFile.Opponent;
                SubjectTextBox.Text = _existingFile.Subject;
                ClientComboBox.Text = _existingFile.Client;
                LawyerComboBox.Text = _existingFile.Lawyer;
                FileValueTextBox.Text = _existingFile.FileValue.ToString();
                PriorityComboBox.Text = _existingFile.Priority;
                StatusComboBox.Text = _existingFile.Status;
                CategoryTextBox.Text = _existingFile.Category;
                ProcedureTypeComboBox.Text = _existingFile.ProcedureType;
                DecisionTextBox.Text = _existingFile.Decision;
                NextSessionDatePicker.SelectedDate = _existingFile.NextSessionDate;
                NotesTextBox.Text = _existingFile.Notes;

                // بيانات التتبع الإلكتروني
                ElectronicTrackingCheckBox.IsChecked = _existingFile.IsElectronicTracking;
                ElectronicFileNumberTextBox.Text = _existingFile.ElectronicFileNumber;
                ElectronicFileCodeTextBox.Text = _existingFile.ElectronicFileCode;
                ElectronicYearTextBox.Text = _existingFile.ElectronicYear.ToString();
                AppealCourtComboBox.Text = _existingFile.AppealCourt;
                SearchInPrimaryCourtsCheckBox.IsChecked = _existingFile.SearchInPrimaryCourts;

                UpdateSyncStatus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الملف: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// تفعيل/إلغاء تفعيل التتبع الإلكتروني
        /// </summary>
        private void ElectronicTrackingCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            ElectronicTrackingPanel.IsEnabled = true;
        }

        private void ElectronicTrackingCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            ElectronicTrackingPanel.IsEnabled = false;
        }

        /// <summary>
        /// البحث في المحاكم الإلكترونية
        /// </summary>
        private async void SearchElectronicButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateElectronicData())
                    return;

                SearchElectronicButton.IsEnabled = false;
                SearchElectronicButton.Content = "🔄 جاري البحث...";
                SyncStatusTextBlock.Text = "حالة المزامنة: قيد البحث...";

                var result = await _courtsService.SearchFileAsync(
                    ElectronicFileNumberTextBox.Text,
                    ElectronicFileCodeTextBox.Text,
                    int.Parse(ElectronicYearTextBox.Text),
                    CourtComboBox.Text,
                    SearchInPrimaryCourtsCheckBox.IsChecked == true
                );

                if (result.IsFound)
                {
                    SyncStatusTextBlock.Text = "حالة المزامنة: تم العثور على الملف ✅";
                    SyncStatusTextBlock.Foreground = System.Windows.Media.Brushes.Green;

                    // تحديث البيانات
                    if (result.NextSessionDate.HasValue)
                        NextSessionDatePicker.SelectedDate = result.NextSessionDate;
                    if (!string.IsNullOrEmpty(result.Decision))
                        DecisionTextBox.Text = result.Decision;

                    MessageBox.Show("تم العثور على الملف وتحديث البيانات بنجاح!", "نجح البحث", 
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    SyncStatusTextBlock.Text = "حالة المزامنة: لم يتم العثور على الملف ❌";
                    SyncStatusTextBlock.Foreground = System.Windows.Media.Brushes.Red;

                    MessageBox.Show($"لم يتم العثور على الملف.\n{result.ErrorMessage}", "فشل البحث", 
                                   MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                SyncStatusTextBlock.Text = "حالة المزامنة: خطأ في البحث ❌";
                SyncStatusTextBlock.Foreground = System.Windows.Media.Brushes.Red;
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                SearchElectronicButton.IsEnabled = true;
                SearchElectronicButton.Content = "🔍 البحث في المحاكم الإلكترونية";
            }
        }

        /// <summary>
        /// مزامنة البيانات
        /// </summary>
        private void SyncButton_Click(object sender, RoutedEventArgs e)
        {
            if (ElectronicTrackingCheckBox.IsChecked == true)
            {
                SearchElectronicButton_Click(sender, e);
            }
            else
            {
                MessageBox.Show("يجب تفعيل التتبع الإلكتروني أولاً", "تنبيه",
                               MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// حفظ الملف
        /// </summary>
        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateData())
                    return;

                SaveFileData();
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الملف: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        #endregion

        #region Validation

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        private bool ValidateData()
        {
            if (string.IsNullOrWhiteSpace(FileNumberTextBox.Text))
            {
                MessageBox.Show("يجب إدخال رقم الملف", "خطأ في البيانات", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                FileNumberTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(OfficeReferenceTextBox.Text))
            {
                MessageBox.Show("يجب إدخال مرجع المكتب", "خطأ في البيانات", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                OfficeReferenceTextBox.Focus();
                return false;
            }

            if (FileTypeComboBox.SelectedItem == null)
            {
                MessageBox.Show("يجب اختيار نوع الملف", "خطأ في البيانات", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                FileTypeComboBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(CourtComboBox.Text))
            {
                MessageBox.Show("يجب اختيار المحكمة المختصة", "خطأ في البيانات", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                CourtComboBox.Focus();
                return false;
            }

            if (CaseTypeComboBox.SelectedItem == null)
            {
                MessageBox.Show("يجب اختيار نوع القضية", "خطأ في البيانات", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                CaseTypeComboBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(SubjectTextBox.Text))
            {
                MessageBox.Show("يجب إدخال موضوع القضية", "خطأ في البيانات", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                SubjectTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(ClientComboBox.Text))
            {
                MessageBox.Show("يجب اختيار الموكّل", "خطأ في البيانات", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                ClientComboBox.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// التحقق من صحة بيانات التتبع الإلكتروني
        /// </summary>
        private bool ValidateElectronicData()
        {
            if (string.IsNullOrWhiteSpace(ElectronicFileNumberTextBox.Text))
            {
                MessageBox.Show("يجب إدخال الرقم الكامل للملف", "خطأ في البيانات", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                ElectronicFileNumberTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(ElectronicFileCodeTextBox.Text))
            {
                MessageBox.Show("يجب إدخال رمز الملف", "خطأ في البيانات", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                ElectronicFileCodeTextBox.Focus();
                return false;
            }

            if (!int.TryParse(ElectronicYearTextBox.Text, out int year) || year < 2000 || year > DateTime.Now.Year + 1)
            {
                MessageBox.Show("يجب إدخال سنة صحيحة", "خطأ في البيانات", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                ElectronicYearTextBox.Focus();
                return false;
            }

            return true;
        }

        #endregion

        #region Data Management

        /// <summary>
        /// حفظ بيانات الملف
        /// </summary>
        private void SaveFileData()
        {
            NewFile.FileNumber = FileNumberTextBox.Text.Trim();
            NewFile.OfficeReference = OfficeReferenceTextBox.Text.Trim();
            NewFile.CourtReference = CourtReferenceTextBox.Text.Trim();
            NewFile.FileType = FileTypeComboBox.Text;
            NewFile.Court = CourtComboBox.Text;
            NewFile.CaseType = CaseTypeComboBox.Text;
            NewFile.Opponent = OpponentTextBox.Text.Trim();
            NewFile.Subject = SubjectTextBox.Text.Trim();
            NewFile.Client = ClientComboBox.Text;
            NewFile.Lawyer = LawyerComboBox.Text;
            NewFile.Category = CategoryTextBox.Text.Trim();
            NewFile.ProcedureType = ProcedureTypeComboBox.Text;
            NewFile.Decision = DecisionTextBox.Text.Trim();
            NewFile.NextSessionDate = NextSessionDatePicker.SelectedDate;
            NewFile.Notes = NotesTextBox.Text.Trim();
            NewFile.Status = StatusComboBox.Text;
            NewFile.Priority = PriorityComboBox.Text;

            // قيمة الملف
            if (decimal.TryParse(FileValueTextBox.Text, out decimal fileValue))
                NewFile.FileValue = fileValue;

            // بيانات التتبع الإلكتروني
            NewFile.IsElectronicTracking = ElectronicTrackingCheckBox.IsChecked == true;
            if (NewFile.IsElectronicTracking)
            {
                NewFile.ElectronicFileNumber = ElectronicFileNumberTextBox.Text.Trim();
                NewFile.ElectronicFileCode = ElectronicFileCodeTextBox.Text.Trim();
                if (int.TryParse(ElectronicYearTextBox.Text, out int year))
                    NewFile.ElectronicYear = year;
                NewFile.AppealCourt = AppealCourtComboBox.Text;
                NewFile.SearchInPrimaryCourts = SearchInPrimaryCourtsCheckBox.IsChecked == true;
            }

            // تحديث التواريخ
            if (_isEditMode && _existingFile != null)
            {
                NewFile.Id = _existingFile.Id;
                NewFile.CreatedDate = _existingFile.CreatedDate;
                NewFile.LastSyncDate = _existingFile.LastSyncDate;
                NewFile.SyncStatus = _existingFile.SyncStatus;
            }
            
            NewFile.LastUpdated = DateTime.Now;
        }

        /// <summary>
        /// تحديث حالة المزامنة
        /// </summary>
        private void UpdateSyncStatus()
        {
            if (_existingFile == null) return;

            var statusText = $"حالة المزامنة: {_existingFile.SyncStatus}";
            if (_existingFile.LastSyncDate.HasValue)
                statusText += $" - آخر مزامنة: {_existingFile.LastSyncDate.Value:dd/MM/yyyy HH:mm}";

            SyncStatusTextBlock.Text = statusText;
            SyncStatusTextBlock.Foreground = _existingFile.SyncStatus == "متزامن" 
                ? System.Windows.Media.Brushes.Green 
                : System.Windows.Media.Brushes.Red;
        }

        #endregion
    }
}
