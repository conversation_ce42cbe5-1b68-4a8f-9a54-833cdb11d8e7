using System;
using System.ComponentModel.DataAnnotations;

namespace AvocatPro.Models.Backup
{
    /// <summary>
    /// إعدادات Google Drive
    /// </summary>
    public class GoogleDriveSettings
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// معرف العميل (Client ID)
        /// </summary>
        [Required]
        [StringLength(200)]
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// سر العميل (Client Secret) - مشفر
        /// </summary>
        [Required]
        [StringLength(500)]
        public string ClientSecret { get; set; } = string.Empty;

        /// <summary>
        /// رمز التحديث (Refresh Token) - مشفر
        /// </summary>
        [StringLength(500)]
        public string? RefreshToken { get; set; }

        /// <summary>
        /// رمز الوصول (Access Token) - مشفر
        /// </summary>
        [StringLength(500)]
        public string? AccessToken { get; set; }

        /// <summary>
        /// تاريخ انتهاء رمز الوصول
        /// </summary>
        public DateTime? AccessTokenExpiry { get; set; }

        /// <summary>
        /// معرف المجلد الجذر للنسخ الاحتياطية
        /// </summary>
        [StringLength(200)]
        public string? RootFolderId { get; set; }

        /// <summary>
        /// اسم المجلد الجذر للنسخ الاحتياطية
        /// </summary>
        [StringLength(100)]
        public string RootFolderName { get; set; } = "AvocatPro Backups";

        /// <summary>
        /// هل تم تفعيل Google Drive
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// هل تم التحقق من الاتصال
        /// </summary>
        public bool IsAuthenticated { get; set; } = false;

        /// <summary>
        /// آخر تحقق من الاتصال
        /// </summary>
        public DateTime? LastAuthenticationCheck { get; set; }

        /// <summary>
        /// معلومات المستخدم في Google Drive
        /// </summary>
        [StringLength(200)]
        public string? UserEmail { get; set; }

        /// <summary>
        /// اسم المستخدم في Google Drive
        /// </summary>
        [StringLength(100)]
        public string? UserName { get; set; }

        /// <summary>
        /// المساحة المتاحة في Google Drive (بالبايت)
        /// </summary>
        public long? AvailableSpace { get; set; }

        /// <summary>
        /// المساحة المستخدمة في Google Drive (بالبايت)
        /// </summary>
        public long? UsedSpace { get; set; }

        /// <summary>
        /// إجمالي المساحة في Google Drive (بالبايت)
        /// </summary>
        public long? TotalSpace { get; set; }

        /// <summary>
        /// آخر تحديث لمعلومات المساحة
        /// </summary>
        public DateTime? LastSpaceUpdate { get; set; }

        /// <summary>
        /// الحد الأقصى لحجم الملف الواحد (بالبايت)
        /// </summary>
        public long MaxFileSize { get; set; } = 5_368_709_120; // 5 GB

        /// <summary>
        /// عدد المحاولات عند فشل الرفع
        /// </summary>
        public int RetryAttempts { get; set; } = 3;

        /// <summary>
        /// مهلة الاتصال بالثواني
        /// </summary>
        public int TimeoutSeconds { get; set; } = 300; // 5 دقائق

        /// <summary>
        /// حجم الجزء للرفع المتقطع (بالبايت)
        /// </summary>
        public int ChunkSize { get; set; } = 8_388_608; // 8 MB

        /// <summary>
        /// تفعيل الرفع المتقطع للملفات الكبيرة
        /// </summary>
        public bool EnableResumableUpload { get; set; } = true;

        /// <summary>
        /// تفعيل ضغط الملفات قبل الرفع
        /// </summary>
        public bool EnableCompression { get; set; } = true;

        /// <summary>
        /// تفعيل التشفير قبل الرفع
        /// </summary>
        public bool EnableEncryption { get; set; } = false;

        /// <summary>
        /// إنشاء مجلدات فرعية حسب التاريخ
        /// </summary>
        public bool CreateDateFolders { get; set; } = true;

        /// <summary>
        /// تنسيق مجلدات التاريخ
        /// </summary>
        [StringLength(50)]
        public string DateFolderFormat { get; set; } = "yyyy/MM";

        /// <summary>
        /// حذف النسخ القديمة تلقائياً من Google Drive
        /// </summary>
        public bool AutoDeleteOldBackups { get; set; } = true;

        /// <summary>
        /// عدد النسخ المحفوظة في Google Drive
        /// </summary>
        public int BackupsToKeepInDrive { get; set; } = 10;

        /// <summary>
        /// تفعيل مشاركة النسخ الاحتياطية
        /// </summary>
        public bool EnableSharing { get; set; } = false;

        /// <summary>
        /// قائمة البريد الإلكتروني للمشاركة
        /// </summary>
        [StringLength(1000)]
        public string? SharedEmails { get; set; }

        /// <summary>
        /// نوع الإذن للمشاركة
        /// </summary>
        [StringLength(20)]
        public string SharePermission { get; set; } = "reader";

        /// <summary>
        /// تفعيل الإشعارات عند الرفع
        /// </summary>
        public bool EnableUploadNotifications { get; set; } = true;

        /// <summary>
        /// تفعيل التحقق من التكامل
        /// </summary>
        public bool EnableIntegrityCheck { get; set; } = true;

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// معرف المستخدم المنشئ
        /// </summary>
        public int CreatedBy { get; set; }

        /// <summary>
        /// معرف آخر مستخدم قام بالتحديث
        /// </summary>
        public int UpdatedBy { get; set; }

        /// <summary>
        /// آخر رسالة خطأ
        /// </summary>
        [StringLength(500)]
        public string? LastError { get; set; }

        /// <summary>
        /// تاريخ آخر خطأ
        /// </summary>
        public DateTime? LastErrorDate { get; set; }

        /// <summary>
        /// عدد النسخ الاحتياطية المرفوعة بنجاح
        /// </summary>
        public int SuccessfulUploads { get; set; } = 0;

        /// <summary>
        /// عدد النسخ الاحتياطية الفاشلة
        /// </summary>
        public int FailedUploads { get; set; } = 0;

        /// <summary>
        /// إجمالي البيانات المرفوعة (بالبايت)
        /// </summary>
        public long TotalDataUploaded { get; set; } = 0;

        /// <summary>
        /// متوسط سرعة الرفع (بايت/ثانية)
        /// </summary>
        public long? AverageUploadSpeed { get; set; }

        /// <summary>
        /// آخر نسخة احتياطية مرفوعة
        /// </summary>
        public DateTime? LastUploadDate { get; set; }

        /// <summary>
        /// حجم آخر نسخة احتياطية مرفوعة
        /// </summary>
        public long? LastUploadSize { get; set; }

        /// <summary>
        /// مدة آخر رفع بالثواني
        /// </summary>
        public int? LastUploadDuration { get; set; }

        /// <summary>
        /// التحقق من صحة رمز الوصول
        /// </summary>
        public bool IsAccessTokenValid()
        {
            return !string.IsNullOrEmpty(AccessToken) && 
                   AccessTokenExpiry.HasValue && 
                   AccessTokenExpiry.Value > DateTime.Now.AddMinutes(5);
        }

        /// <summary>
        /// التحقق من توفر المساحة
        /// </summary>
        public bool HasSufficientSpace(long requiredSize)
        {
            return AvailableSpace.HasValue && AvailableSpace.Value >= requiredSize;
        }

        /// <summary>
        /// الحصول على المساحة المتاحة بصيغة قابلة للقراءة
        /// </summary>
        public string GetFormattedAvailableSpace()
        {
            return AvailableSpace.HasValue ? FormatBytes(AvailableSpace.Value) : "غير معروف";
        }

        /// <summary>
        /// الحصول على المساحة المستخدمة بصيغة قابلة للقراءة
        /// </summary>
        public string GetFormattedUsedSpace()
        {
            return UsedSpace.HasValue ? FormatBytes(UsedSpace.Value) : "غير معروف";
        }

        /// <summary>
        /// الحصول على إجمالي المساحة بصيغة قابلة للقراءة
        /// </summary>
        public string GetFormattedTotalSpace()
        {
            return TotalSpace.HasValue ? FormatBytes(TotalSpace.Value) : "غير معروف";
        }

        /// <summary>
        /// تنسيق الحجم بالبايت
        /// </summary>
        private static string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            
            return $"{number:n1} {suffixes[counter]}";
        }

        /// <summary>
        /// تحديث إحصائيات الرفع
        /// </summary>
        public void UpdateUploadStats(bool success, long size, int duration)
        {
            if (success)
            {
                SuccessfulUploads++;
                TotalDataUploaded += size;
                LastUploadDate = DateTime.Now;
                LastUploadSize = size;
                LastUploadDuration = duration;
                
                // حساب متوسط السرعة
                if (duration > 0)
                {
                    var currentSpeed = size / duration;
                    AverageUploadSpeed = AverageUploadSpeed.HasValue 
                        ? (AverageUploadSpeed.Value + currentSpeed) / 2 
                        : currentSpeed;
                }
            }
            else
            {
                FailedUploads++;
            }
            
            UpdatedAt = DateTime.Now;
        }

        /// <summary>
        /// تحديث معلومات المساحة
        /// </summary>
        public void UpdateSpaceInfo(long available, long used, long total)
        {
            AvailableSpace = available;
            UsedSpace = used;
            TotalSpace = total;
            LastSpaceUpdate = DateTime.Now;
            UpdatedAt = DateTime.Now;
        }

        /// <summary>
        /// تسجيل خطأ
        /// </summary>
        public void LogError(string error)
        {
            LastError = error;
            LastErrorDate = DateTime.Now;
            UpdatedAt = DateTime.Now;
        }
    }
}
