﻿#pragma checksum "..\..\..\..\Views\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "341371C16EDA9C7F8F9352969E30FEE2558425EA"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 172 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DashboardButton;
        
        #line default
        #line hidden
        
        
        #line 184 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClientsButton;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CasesButton;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SessionsButton;
        
        #line default
        #line hidden
        
        
        #line 223 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AppointmentsButton;
        
        #line default
        #line hidden
        
        
        #line 236 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FinanceButton;
        
        #line default
        #line hidden
        
        
        #line 249 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ReportsButton;
        
        #line default
        #line hidden
        
        
        #line 262 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button UserManagementButton;
        
        #line default
        #line hidden
        
        
        #line 273 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ComprehensiveBackupButton;
        
        #line default
        #line hidden
        
        
        #line 286 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SmartAssistantButton;
        
        #line default
        #line hidden
        
        
        #line 297 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InteractiveDashboardButton;
        
        #line default
        #line hidden
        
        
        #line 311 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SettingsButton;
        
        #line default
        #line hidden
        
        
        #line 331 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserInfoTextBlock;
        
        #line default
        #line hidden
        
        
        #line 339 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LogoutButton;
        
        #line default
        #line hidden
        
        
        #line 376 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PageTitleTextBlock;
        
        #line default
        #line hidden
        
        
        #line 378 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PageSubtitleTextBlock;
        
        #line default
        #line hidden
        
        
        #line 390 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox QuickSearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 417 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Frame MainContentFrame;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DashboardButton = ((System.Windows.Controls.Button)(target));
            
            #line 173 "..\..\..\..\Views\MainWindow.xaml"
            this.DashboardButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ClientsButton = ((System.Windows.Controls.Button)(target));
            
            #line 185 "..\..\..\..\Views\MainWindow.xaml"
            this.ClientsButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.CasesButton = ((System.Windows.Controls.Button)(target));
            
            #line 198 "..\..\..\..\Views\MainWindow.xaml"
            this.CasesButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.SessionsButton = ((System.Windows.Controls.Button)(target));
            
            #line 211 "..\..\..\..\Views\MainWindow.xaml"
            this.SessionsButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.AppointmentsButton = ((System.Windows.Controls.Button)(target));
            
            #line 224 "..\..\..\..\Views\MainWindow.xaml"
            this.AppointmentsButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.FinanceButton = ((System.Windows.Controls.Button)(target));
            
            #line 237 "..\..\..\..\Views\MainWindow.xaml"
            this.FinanceButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ReportsButton = ((System.Windows.Controls.Button)(target));
            
            #line 250 "..\..\..\..\Views\MainWindow.xaml"
            this.ReportsButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.UserManagementButton = ((System.Windows.Controls.Button)(target));
            
            #line 263 "..\..\..\..\Views\MainWindow.xaml"
            this.UserManagementButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.ComprehensiveBackupButton = ((System.Windows.Controls.Button)(target));
            
            #line 274 "..\..\..\..\Views\MainWindow.xaml"
            this.ComprehensiveBackupButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.SmartAssistantButton = ((System.Windows.Controls.Button)(target));
            
            #line 287 "..\..\..\..\Views\MainWindow.xaml"
            this.SmartAssistantButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.InteractiveDashboardButton = ((System.Windows.Controls.Button)(target));
            
            #line 298 "..\..\..\..\Views\MainWindow.xaml"
            this.InteractiveDashboardButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.SettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 312 "..\..\..\..\Views\MainWindow.xaml"
            this.SettingsButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.UserInfoTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.LogoutButton = ((System.Windows.Controls.Button)(target));
            
            #line 340 "..\..\..\..\Views\MainWindow.xaml"
            this.LogoutButton.Click += new System.Windows.RoutedEventHandler(this.LogoutButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.PageTitleTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.PageSubtitleTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.QuickSearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 393 "..\..\..\..\Views\MainWindow.xaml"
            this.QuickSearchTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.QuickSearchTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 393 "..\..\..\..\Views\MainWindow.xaml"
            this.QuickSearchTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.QuickSearchTextBox_LostFocus);
            
            #line default
            #line hidden
            
            #line 394 "..\..\..\..\Views\MainWindow.xaml"
            this.QuickSearchTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.QuickSearchTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 18:
            this.MainContentFrame = ((System.Windows.Controls.Frame)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

