using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using Microsoft.Win32;
using AvocatPro.Services;
using AvocatPro.Models;
using AvocatPro.Views.Windows;

namespace AvocatPro.Views.Pages
{
    public partial class AdvancedUISettingsPage : Page
    {
        private readonly ThemeService _themeService;
        private readonly KeyboardShortcutService _shortcutService;
        private readonly AnimationService _animationService;
        private bool _isLoading = false;
        private bool _isUpdatingColors = false;

        public AdvancedUISettingsPage()
        {
            InitializeComponent();
            _themeService = new ThemeService();
            _shortcutService = new KeyboardShortcutService();
            _animationService = new AnimationService();
            
            Loaded += AdvancedUISettingsPage_Loaded;
        }

        private async void AdvancedUISettingsPage_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadCurrentSettingsAsync();
            LoadShortcuts();
            
            // تسجيل النافذة للاختصارات
            var window = Window.GetWindow(this);
            if (window != null)
            {
                _shortcutService.RegisterWindow(window);
            }
        }

        private async Task LoadCurrentSettingsAsync()
        {
            _isLoading = true;
            ShowLoading(true);

            try
            {
                // تحميل إعدادات الثيم الحالية
                var currentTheme = _themeService.GetCurrentTheme();
                
                // تحديث واجهة المستخدم
                UpdateThemeModeSelection(currentTheme.Mode);
                UpdateColorPreviews(currentTheme);
                
                await Task.Delay(500); // محاكاة التحميل
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                ShowLoading(false);
                _isLoading = false;
            }
        }

        private void UpdateThemeModeSelection(ThemeMode mode)
        {
            foreach (ComboBoxItem item in ThemeModeComboBox.Items)
            {
                if (item.Tag?.ToString() == mode.ToString())
                {
                    ThemeModeComboBox.SelectedItem = item;
                    break;
                }
            }
        }

        private void UpdateColorPreviews(ThemeSettings theme)
        {
            _isUpdatingColors = true;

            try
            {
                // تحديث معاينات الألوان
                PrimaryColorPreview.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(theme.PrimaryColor));
                PrimaryColorTextBox.Text = theme.PrimaryColor;

                SecondaryColorPreview.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(theme.SecondaryColor));
                SecondaryColorTextBox.Text = theme.SecondaryColor;

                AccentColorPreview.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(theme.AccentColor));
                AccentColorTextBox.Text = theme.AccentColor;

                SuccessColorPreview.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(theme.SuccessColor));
                WarningColorPreview.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(theme.WarningColor));
                ErrorColorPreview.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(theme.ErrorColor));
                InfoColorPreview.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(theme.InfoColor));
            }
            finally
            {
                _isUpdatingColors = false;
            }
        }

        private void LoadShortcuts()
        {
            ShortcutsPanel.Children.Clear();

            var shortcuts = _shortcutService.GetAllShortcuts().Take(8); // أهم 8 اختصارات

            foreach (var shortcut in shortcuts)
            {
                var shortcutPanel = CreateShortcutPanel(shortcut);
                ShortcutsPanel.Children.Add(shortcutPanel);
            }
        }

        private StackPanel CreateShortcutPanel(KeyboardShortcut shortcut)
        {
            var panel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 8)
            };

            var keyCombo = GetKeyComboString(shortcut);
            var keyText = new TextBlock
            {
                Text = keyCombo,
                FontSize = 12,
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#3B82F6")),
                Width = 120,
                VerticalAlignment = VerticalAlignment.Center
            };

            var descriptionText = new TextBlock
            {
                Text = shortcut.Description,
                FontSize = 12,
                Foreground = (Brush)FindResource("TextSecondaryColor"),
                VerticalAlignment = VerticalAlignment.Center
            };

            panel.Children.Add(keyText);
            panel.Children.Add(descriptionText);

            return panel;
        }

        private string GetKeyComboString(KeyboardShortcut shortcut)
        {
            var combo = "";
            
            if (shortcut.Modifiers.HasFlag(ModifierKeys.Control))
                combo += "Ctrl + ";
            if (shortcut.Modifiers.HasFlag(ModifierKeys.Alt))
                combo += "Alt + ";
            if (shortcut.Modifiers.HasFlag(ModifierKeys.Shift))
                combo += "Shift + ";
                
            combo += shortcut.Key.ToString();
            
            return combo;
        }

        private void ShowLoading(bool show)
        {
            LoadingOverlay.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
        }

        // معالجات أحداث الثيم
        private void ThemeMode_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isLoading || ThemeModeComboBox.SelectedItem is not ComboBoxItem selectedItem) return;

            var mode = Enum.Parse<ThemeMode>(selectedItem.Tag.ToString()!);
            _themeService.SetThemeMode(mode);
        }

        private void PredefinedTheme_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isLoading || PredefinedThemeComboBox.SelectedItem is not ComboBoxItem selectedItem) return;

            var themeName = selectedItem.Tag.ToString()!;
            _themeService.ApplyPredefinedTheme(themeName);
            
            // تحديث معاينات الألوان
            var currentTheme = _themeService.GetCurrentTheme();
            UpdateColorPreviews(currentTheme);
        }

        // معالجات أحداث الألوان
        private void ColorPreview_Click(object sender, MouseButtonEventArgs e)
        {
            if (sender is Border border && border.Tag is string colorType)
            {
                // إنشاء نافذة بسيطة لاختيار اللون
                var colorWindow = new ColorPickerWindow();
                if (colorWindow.ShowDialog() == true)
                {
                    var hexColor = colorWindow.SelectedColor;
                    UpdateColorPreview(colorType, hexColor);
                    ApplyColorChange(colorType, hexColor);
                }
            }
        }

        private void ColorTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_isUpdatingColors || sender is not TextBox textBox || textBox.Tag is not string colorType) return;

            var colorText = textBox.Text;
            if (IsValidHexColor(colorText))
            {
                UpdateColorPreview(colorType, colorText);
                ApplyColorChange(colorType, colorText);
            }
        }

        private void UpdateColorPreview(string colorType, string hexColor)
        {
            try
            {
                var color = (Color)ColorConverter.ConvertFromString(hexColor);
                var brush = new SolidColorBrush(color);

                switch (colorType)
                {
                    case "Primary":
                        PrimaryColorPreview.Background = brush;
                        if (!_isUpdatingColors) PrimaryColorTextBox.Text = hexColor;
                        break;
                    case "Secondary":
                        SecondaryColorPreview.Background = brush;
                        if (!_isUpdatingColors) SecondaryColorTextBox.Text = hexColor;
                        break;
                    case "Accent":
                        AccentColorPreview.Background = brush;
                        if (!_isUpdatingColors) AccentColorTextBox.Text = hexColor;
                        break;
                    case "Success":
                        SuccessColorPreview.Background = brush;
                        break;
                    case "Warning":
                        WarningColorPreview.Background = brush;
                        break;
                    case "Error":
                        ErrorColorPreview.Background = brush;
                        break;
                    case "Info":
                        InfoColorPreview.Background = brush;
                        break;
                }
            }
            catch
            {
                // تجاهل الألوان غير الصحيحة
            }
        }

        private void ApplyColorChange(string colorType, string hexColor)
        {
            // تطبيق التغيير فوراً
            switch (colorType)
            {
                case "Accent":
                    _themeService.SetAccentColor(hexColor);
                    break;
                // يمكن إضافة المزيد من الألوان هنا
            }
        }

        private bool IsValidHexColor(string color)
        {
            if (string.IsNullOrEmpty(color) || !color.StartsWith("#") || color.Length != 7)
                return false;

            try
            {
                ColorConverter.ConvertFromString(color);
                return true;
            }
            catch
            {
                return false;
            }
        }

        // معالجات أحداث الأنيميشن
        private void AnimationSettings_Changed(object sender, RoutedEventArgs e)
        {
            // حفظ إعدادات الأنيميشن
            SaveAnimationSettings();
        }

        private void TransitionType_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // حفظ نوع الانتقال المحدد
            SaveAnimationSettings();
        }

        private async void TestTransition_Click(object sender, RoutedEventArgs e)
        {
            if (TransitionTypeComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                var transitionType = Enum.Parse<PageTransitionType>(selectedItem.Tag.ToString()!);
                
                // إنشاء عنصر تجريبي للاختبار
                var testElement = new Border
                {
                    Width = 200,
                    Height = 100,
                    Background = new SolidColorBrush(Colors.LightBlue),
                    CornerRadius = new CornerRadius(10),
                    Child = new TextBlock
                    {
                        Text = "اختبار الانتقال",
                        HorizontalAlignment = HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Center,
                        FontSize = 16,
                        FontWeight = FontWeights.SemiBold
                    }
                };

                // تطبيق الأنيميشن
                await _animationService.AnimateElementEntry(testElement, ElementAnimationType.FadeIn);
                
                MessageBox.Show("تم اختبار الانتقال بنجاح!", "اختبار الانتقال", 
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        // معالجات أحداث الاختصارات
        private void ShortcutSettings_Changed(object sender, RoutedEventArgs e)
        {
            // حفظ إعدادات الاختصارات
            SaveShortcutSettings();
        }

        private void ViewAllShortcuts_Click(object sender, RoutedEventArgs e)
        {
            var helpText = _shortcutService.GetShortcutsHelpText();
            MessageBox.Show(helpText, "جميع اختصارات لوحة المفاتيح", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // حفظ الإعدادات
        private async void Save_Click(object sender, RoutedEventArgs e)
        {
            ShowLoading(true);
            
            try
            {
                // حفظ جميع الإعدادات
                SaveAnimationSettings();
                SaveShortcutSettings();
                
                await Task.Delay(1000); // محاكاة الحفظ
                
                MessageBox.Show("تم حفظ جميع الإعدادات بنجاح!", "حفظ الإعدادات", 
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private void Reset_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟", 
                                        "إعادة تعيين", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                // إعادة تعيين إلى الثيم الافتراضي
                _themeService.ApplyPredefinedTheme("Light");
                
                // إعادة تحميل الإعدادات
                LoadCurrentSettingsAsync();
                
                MessageBox.Show("تم إعادة تعيين جميع الإعدادات بنجاح!", "إعادة تعيين", 
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void SaveAnimationSettings()
        {
            // حفظ إعدادات الأنيميشن في ملف الإعدادات
            var settings = new
            {
                EnableAnimations = EnableAnimationsCheckBox.IsChecked ?? true,
                EnableTransitions = EnableTransitionsCheckBox.IsChecked ?? true,
                EnableHoverEffects = EnableHoverEffectsCheckBox.IsChecked ?? true,
                TransitionType = (TransitionTypeComboBox.SelectedItem as ComboBoxItem)?.Tag?.ToString() ?? "SlideLeft"
            };
            
            // يمكن حفظ هذه الإعدادات في ملف JSON أو قاعدة البيانات
            System.Diagnostics.Debug.WriteLine($"حفظ إعدادات الأنيميشن: {settings}");
        }

        private void SaveShortcutSettings()
        {
            // حفظ إعدادات الاختصارات
            var enableShortcuts = EnableShortcutsCheckBox.IsChecked ?? true;
            
            // يمكن حفظ هذه الإعدادات في ملف JSON أو قاعدة البيانات
            System.Diagnostics.Debug.WriteLine($"حفظ إعدادات الاختصارات: تفعيل = {enableShortcuts}");
        }
    }
}
