﻿#pragma checksum "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "785FA27FA6488FD895C5ABD01C5ED47E0522C91D"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Pages {
    
    
    /// <summary>
    /// ComprehensiveSettingsPage
    /// </summary>
    public partial class ComprehensiveSettingsPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 94 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ImportButton;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetButton;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OfficeNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OfficeAddressTextBox;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OfficePhoneTextBox;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OfficeEmailTextBox;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OfficeWebsiteTextBox;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OfficeLicenseTextBox;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OfficeLogoTextBox;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image LogoPreview;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LanguageComboBox;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ThemeComboBox;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PrimaryColorComboBox;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SecondaryColorComboBox;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FontFamilyComboBox;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider FontSizeSlider;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FontSizeLabel;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider OpacitySlider;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OpacityLabel;
        
        #line default
        #line hidden
        
        
        #line 213 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableAnimationsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableSoundsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border PrimaryColorPreview;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border SecondaryColorPreview;
        
        #line default
        #line hidden
        
        
        #line 252 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddMenuItemButton;
        
        #line default
        #line hidden
        
        
        #line 254 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditMenuItemButton;
        
        #line default
        #line hidden
        
        
        #line 256 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteMenuItemButton;
        
        #line default
        #line hidden
        
        
        #line 258 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ToggleVisibilityButton;
        
        #line default
        #line hidden
        
        
        #line 263 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid MenuItemsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 295 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer MenuPreviewScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 296 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel MenuPreviewPanel;
        
        #line default
        #line hidden
        
        
        #line 321 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SmtpServerTextBox;
        
        #line default
        #line hidden
        
        
        #line 324 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SmtpPortTextBox;
        
        #line default
        #line hidden
        
        
        #line 327 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SmtpUsernameTextBox;
        
        #line default
        #line hidden
        
        
        #line 330 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox SmtpPasswordBox;
        
        #line default
        #line hidden
        
        
        #line 333 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableSslCheckBox;
        
        #line default
        #line hidden
        
        
        #line 339 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FromEmailTextBox;
        
        #line default
        #line hidden
        
        
        #line 342 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FromNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 344 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestEmailButton;
        
        #line default
        #line hidden
        
        
        #line 379 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DatabaseTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 382 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ConnectionStringTextBox;
        
        #line default
        #line hidden
        
        
        #line 384 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestConnectionButton;
        
        #line default
        #line hidden
        
        
        #line 393 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoBackupCheckBox;
        
        #line default
        #line hidden
        
        
        #line 396 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BackupIntervalTextBox;
        
        #line default
        #line hidden
        
        
        #line 404 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BackupPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 411 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaxBackupFilesTextBox;
        
        #line default
        #line hidden
        
        
        #line 414 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreateBackupButton;
        
        #line default
        #line hidden
        
        
        #line 418 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RestoreBackupButton;
        
        #line default
        #line hidden
        
        
        #line 437 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 439 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ApplyButton;
        
        #line default
        #line hidden
        
        
        #line 441 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/pages/comprehensivesettingspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ImportButton = ((System.Windows.Controls.Button)(target));
            
            #line 95 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
            this.ImportButton.Click += new System.Windows.RoutedEventHandler(this.ImportButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 97 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ResetButton = ((System.Windows.Controls.Button)(target));
            
            #line 99 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
            this.ResetButton.Click += new System.Windows.RoutedEventHandler(this.ResetButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.OfficeNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.OfficeAddressTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.OfficePhoneTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.OfficeEmailTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.OfficeWebsiteTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.OfficeLicenseTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.OfficeLogoTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            
            #line 154 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BrowseLogoButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.LogoPreview = ((System.Windows.Controls.Image)(target));
            return;
            case 13:
            this.LanguageComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 14:
            this.ThemeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 15:
            this.PrimaryColorComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 16:
            this.SecondaryColorComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 17:
            this.FontFamilyComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 18:
            this.FontSizeSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 19:
            this.FontSizeLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.OpacitySlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 21:
            this.OpacityLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.EnableAnimationsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 23:
            this.EnableSoundsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 24:
            this.PrimaryColorPreview = ((System.Windows.Controls.Border)(target));
            return;
            case 25:
            this.SecondaryColorPreview = ((System.Windows.Controls.Border)(target));
            return;
            case 26:
            this.AddMenuItemButton = ((System.Windows.Controls.Button)(target));
            
            #line 253 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
            this.AddMenuItemButton.Click += new System.Windows.RoutedEventHandler(this.AddMenuItemButton_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.EditMenuItemButton = ((System.Windows.Controls.Button)(target));
            
            #line 255 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
            this.EditMenuItemButton.Click += new System.Windows.RoutedEventHandler(this.EditMenuItemButton_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.DeleteMenuItemButton = ((System.Windows.Controls.Button)(target));
            
            #line 257 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
            this.DeleteMenuItemButton.Click += new System.Windows.RoutedEventHandler(this.DeleteMenuItemButton_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.ToggleVisibilityButton = ((System.Windows.Controls.Button)(target));
            
            #line 259 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
            this.ToggleVisibilityButton.Click += new System.Windows.RoutedEventHandler(this.ToggleVisibilityButton_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            this.MenuItemsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 31:
            this.MenuPreviewScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 32:
            this.MenuPreviewPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 33:
            this.SmtpServerTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 34:
            this.SmtpPortTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 35:
            this.SmtpUsernameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 36:
            this.SmtpPasswordBox = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 37:
            this.EnableSslCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 38:
            this.FromEmailTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 39:
            this.FromNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 40:
            this.TestEmailButton = ((System.Windows.Controls.Button)(target));
            
            #line 347 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
            this.TestEmailButton.Click += new System.Windows.RoutedEventHandler(this.TestEmailButton_Click);
            
            #line default
            #line hidden
            return;
            case 41:
            
            #line 352 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SetGmailSettings_Click);
            
            #line default
            #line hidden
            return;
            case 42:
            
            #line 353 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SetOutlookSettings_Click);
            
            #line default
            #line hidden
            return;
            case 43:
            
            #line 354 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SetYahooSettings_Click);
            
            #line default
            #line hidden
            return;
            case 44:
            this.DatabaseTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 45:
            this.ConnectionStringTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 46:
            this.TestConnectionButton = ((System.Windows.Controls.Button)(target));
            
            #line 387 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
            this.TestConnectionButton.Click += new System.Windows.RoutedEventHandler(this.TestConnectionButton_Click);
            
            #line default
            #line hidden
            return;
            case 47:
            this.AutoBackupCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 48:
            this.BackupIntervalTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 49:
            this.BackupPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 50:
            
            #line 407 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BrowseBackupPathButton_Click);
            
            #line default
            #line hidden
            return;
            case 51:
            this.MaxBackupFilesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 52:
            this.CreateBackupButton = ((System.Windows.Controls.Button)(target));
            
            #line 417 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
            this.CreateBackupButton.Click += new System.Windows.RoutedEventHandler(this.CreateBackupButton_Click);
            
            #line default
            #line hidden
            return;
            case 53:
            this.RestoreBackupButton = ((System.Windows.Controls.Button)(target));
            
            #line 421 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
            this.RestoreBackupButton.Click += new System.Windows.RoutedEventHandler(this.RestoreBackupButton_Click);
            
            #line default
            #line hidden
            return;
            case 54:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 438 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 55:
            this.ApplyButton = ((System.Windows.Controls.Button)(target));
            
            #line 440 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
            this.ApplyButton.Click += new System.Windows.RoutedEventHandler(this.ApplyButton_Click);
            
            #line default
            #line hidden
            return;
            case 56:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 442 "..\..\..\..\..\Views\Pages\ComprehensiveSettingsPage.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

