﻿#pragma checksum "..\..\..\..\..\Views\Windows\SessionsCalendarWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "47C8C41B4ABA3471129E828C56E0D8F8E7140F05"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Windows {
    
    
    /// <summary>
    /// SessionsCalendarWindow
    /// </summary>
    public partial class SessionsCalendarWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 89 "..\..\..\..\..\Views\Windows\SessionsCalendarWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PreviousMonthButton;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\..\Views\Windows\SessionsCalendarWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentMonthLabel;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\..\Views\Windows\SessionsCalendarWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NextMonthButton;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\..\Views\Windows\SessionsCalendarWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TodayButton;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\..\Views\Windows\SessionsCalendarWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddSessionButton;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\..\Views\Windows\SessionsCalendarWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid CalendarGrid;
        
        #line default
        #line hidden
        
        
        #line 213 "..\..\..\..\..\Views\Windows\SessionsCalendarWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectedDateLabel;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\..\Views\Windows\SessionsCalendarWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SessionsCountLabel;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\..\..\Views\Windows\SessionsCalendarWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DaySessionsPanel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/windows/sessionscalendarwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\SessionsCalendarWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PreviousMonthButton = ((System.Windows.Controls.Button)(target));
            
            #line 90 "..\..\..\..\..\Views\Windows\SessionsCalendarWindow.xaml"
            this.PreviousMonthButton.Click += new System.Windows.RoutedEventHandler(this.PreviousMonthButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.CurrentMonthLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.NextMonthButton = ((System.Windows.Controls.Button)(target));
            
            #line 94 "..\..\..\..\..\Views\Windows\SessionsCalendarWindow.xaml"
            this.NextMonthButton.Click += new System.Windows.RoutedEventHandler(this.NextMonthButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.TodayButton = ((System.Windows.Controls.Button)(target));
            
            #line 100 "..\..\..\..\..\Views\Windows\SessionsCalendarWindow.xaml"
            this.TodayButton.Click += new System.Windows.RoutedEventHandler(this.TodayButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.AddSessionButton = ((System.Windows.Controls.Button)(target));
            
            #line 102 "..\..\..\..\..\Views\Windows\SessionsCalendarWindow.xaml"
            this.AddSessionButton.Click += new System.Windows.RoutedEventHandler(this.AddSessionButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.CalendarGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 7:
            this.SelectedDateLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.SessionsCountLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.DaySessionsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

