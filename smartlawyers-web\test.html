<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>اختبار الخادم</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin: 0;
            padding: 20px;
        }
        .container {
            text-align: center;
            max-width: 600px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .success-icon {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 1rem;
            animation: bounce 2s infinite;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            transform: translateY(-2px);
        }
        .status-list {
            text-align: right;
            margin: 1rem 0;
        }
        .status-item {
            margin: 0.5rem 0;
            padding: 0.5rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        
        <h1>🎉 الخادم يعمل بنجاح!</h1>
        
        <p>منصة المحامين الذكية - اختبار الاتصال</p>
        
        <div class="status-list">
            <div class="status-item">
                ✅ خادم الويب يعمل
            </div>
            <div class="status-item">
                ✅ ملفات HTML تعمل
            </div>
            <div class="status-item">
                ⏳ اختبار PHP...
            </div>
        </div>
        
        <div>
            <a href="test-direct.php" class="btn">
                <i class="fas fa-code"></i>
                اختبار PHP
            </a>
            <a href="public/" class="btn">
                <i class="fas fa-home"></i>
                الصفحة الرئيسية
            </a>
            <a href="install.php" class="btn">
                <i class="fas fa-download"></i>
                التثبيت
            </a>
        </div>
        
        <div style="margin-top: 2rem; font-size: 0.9rem; opacity: 0.8;">
            <p>إذا كنت ترى هذه الصفحة، فهذا يعني أن:</p>
            <ul style="text-align: right; display: inline-block;">
                <li>خادم الويب يعمل بشكل صحيح</li>
                <li>الملفات موجودة في المكان الصحيح</li>
                <li>يمكنك الآن اختبار PHP والتطبيق</li>
            </ul>
        </div>
        
        <script>
            // Test if JavaScript works
            document.addEventListener('DOMContentLoaded', function() {
                console.log('JavaScript يعمل بنجاح!');
                
                // Add click effects
                const buttons = document.querySelectorAll('.btn');
                buttons.forEach(btn => {
                    btn.addEventListener('click', function() {
                        this.style.transform = 'scale(0.95)';
                        setTimeout(() => {
                            this.style.transform = '';
                        }, 150);
                    });
                });
                
                // Update status
                setTimeout(() => {
                    const statusItems = document.querySelectorAll('.status-item');
                    if (statusItems[2]) {
                        statusItems[2].innerHTML = '✅ JavaScript يعمل';
                    }
                }, 1000);
            });
        </script>
    </div>
</body>
</html>
