<Page x:Class="AvocatPro.Views.Pages.DashboardPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="لوحة التحكم"
      FlowDirection="RightToLeft">

    <Page.Resources>
        <!-- أنماط البطاقات -->
        <Style x:Key="StatCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="StatNumberStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="32"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
        </Style>

        <Style x:Key="StatLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#666"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,5,0,0"/>
        </Style>
    </Page.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="20">
            <!-- رسالة الترحيب -->
            <Border Background="White" Padding="20" Margin="0,0,0,20">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                </Border.Effect>
                <StackPanel>
                    <TextBlock Text="🌟 مرحباً بك في نظام إدارة مكتب المحاماة"
                              FontSize="18" FontWeight="Bold" Foreground="#1976D2"/>
                    <TextBlock Text="نظرة سريعة على أنشطة مكتبك اليوم"
                              FontSize="14" Foreground="#666" Margin="0,5,0,0"/>
                </StackPanel>
            </Border>

            <!-- الإحصائيات الرئيسية -->
            <TextBlock Text="📊 الإحصائيات الرئيسية" FontSize="16" FontWeight="Bold" 
                      Foreground="#333" Margin="0,0,0,15"/>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- عدد الموكلين -->
                <Border Grid.Column="0" Style="{StaticResource StatCardStyle}">
                    <StackPanel>
                        <Border Width="60" Height="60" Background="#E3F2FD"
                               HorizontalAlignment="Center">
                            <TextBlock Text="👥" FontSize="24" HorizontalAlignment="Center"
                                      VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock Text="127" Style="{StaticResource StatNumberStyle}"
                                  Foreground="#1976D2"/>
                        <TextBlock Text="إجمالي الموكلين" Style="{StaticResource StatLabelStyle}"/>
                    </StackPanel>
                </Border>

                <!-- عدد القضايا -->
                <Border Grid.Column="1" Style="{StaticResource StatCardStyle}">
                    <StackPanel>
                        <Border Width="60" Height="60" Background="#E8F5E8"
                               HorizontalAlignment="Center">
                            <TextBlock Text="📁" FontSize="24" HorizontalAlignment="Center"
                                      VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock Text="89" Style="{StaticResource StatNumberStyle}"
                                  Foreground="#388E3C"/>
                        <TextBlock Text="القضايا النشطة" Style="{StaticResource StatLabelStyle}"/>
                    </StackPanel>
                </Border>

                <!-- الجلسات القادمة -->
                <Border Grid.Column="2" Style="{StaticResource StatCardStyle}">
                    <StackPanel>
                        <Border Width="60" Height="60" Background="#FFF3E0"
                               HorizontalAlignment="Center">
                            <TextBlock Text="⚖️" FontSize="24" HorizontalAlignment="Center"
                                      VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock Text="12" Style="{StaticResource StatNumberStyle}"
                                  Foreground="#F57C00"/>
                        <TextBlock Text="جلسات هذا الأسبوع" Style="{StaticResource StatLabelStyle}"/>
                    </StackPanel>
                </Border>

                <!-- المواعيد اليوم -->
                <Border Grid.Column="3" Style="{StaticResource StatCardStyle}">
                    <StackPanel>
                        <Border Width="60" Height="60" Background="#FCE4EC"
                               HorizontalAlignment="Center">
                            <TextBlock Text="📅" FontSize="24" HorizontalAlignment="Center"
                                      VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock Text="5" Style="{StaticResource StatNumberStyle}"
                                  Foreground="#C2185B"/>
                        <TextBlock Text="مواعيد اليوم" Style="{StaticResource StatLabelStyle}"/>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- الأنشطة الأخيرة والمهام -->
            <Grid Margin="0,30,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- الأنشطة الأخيرة -->
                <Border Grid.Column="0" Style="{StaticResource StatCardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <TextBlock Text="📋" FontSize="16" Margin="0,0,10,0"/>
                            <TextBlock Text="الأنشطة الأخيرة" FontSize="16" FontWeight="Bold"/>
                        </StackPanel>
                        
                        <StackPanel>
                            <Border Background="#F5F5F5" Padding="10" Margin="0,0,0,10">
                                <StackPanel>
                                    <TextBlock Text="تم إضافة موكل جديد: أحمد محمد علي" FontSize="12" FontWeight="Bold"/>
                                    <TextBlock Text="منذ ساعتين" FontSize="10" Foreground="#666"/>
                                </StackPanel>
                            </Border>

                            <Border Background="#F5F5F5" Padding="10" Margin="0,0,0,10">
                                <StackPanel>
                                    <TextBlock Text="تم تحديث قضية رقم 2024/123" FontSize="12" FontWeight="Bold"/>
                                    <TextBlock Text="منذ 4 ساعات" FontSize="10" Foreground="#666"/>
                                </StackPanel>
                            </Border>

                            <Border Background="#F5F5F5" Padding="10" Margin="0,0,0,10">
                                <StackPanel>
                                    <TextBlock Text="جلسة محكمة غداً الساعة 10:00 ص" FontSize="12" FontWeight="Bold"/>
                                    <TextBlock Text="أمس" FontSize="10" Foreground="#666"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- المهام العاجلة -->
                <Border Grid.Column="1" Style="{StaticResource StatCardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <TextBlock Text="⚡" FontSize="16" Margin="0,0,10,0"/>
                            <TextBlock Text="المهام العاجلة" FontSize="16" FontWeight="Bold"/>
                        </StackPanel>
                        
                        <StackPanel>
                            <Border Background="#FFEBEE" Padding="10" Margin="0,0,0,10">
                                <StackPanel>
                                    <TextBlock Text="إعداد مذكرة دفاع - قضية رقم 456" FontSize="12" FontWeight="Bold" Foreground="#D32F2F"/>
                                    <TextBlock Text="مطلوب خلال يومين" FontSize="10" Foreground="#666"/>
                                </StackPanel>
                            </Border>

                            <Border Background="#FFF3E0" Padding="10" Margin="0,0,0,10">
                                <StackPanel>
                                    <TextBlock Text="مراجعة عقد شراكة جديد" FontSize="12" FontWeight="Bold" Foreground="#F57C00"/>
                                    <TextBlock Text="مطلوب خلال أسبوع" FontSize="10" Foreground="#666"/>
                                </StackPanel>
                            </Border>

                            <Border Background="#E8F5E8" Padding="10" Margin="0,0,0,10">
                                <StackPanel>
                                    <TextBlock Text="متابعة دفع أتعاب قضية 789" FontSize="12" FontWeight="Bold" Foreground="#388E3C"/>
                                    <TextBlock Text="مطلوب خلال 3 أيام" FontSize="10" Foreground="#666"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- روابط سريعة -->
            <TextBlock Text="🚀 إجراءات سريعة" FontSize="16" FontWeight="Bold" 
                      Foreground="#333" Margin="0,30,0,15"/>
            
            <StackPanel Orientation="Horizontal">
                <Button Content="➕ إضافة موكل جديد" Background="#1976D2" Foreground="White"
                       Padding="15,10" Margin="0,0,10,0" BorderThickness="0"
                       Click="AddClientButton_Click"/>

                <Button Content="📁 إنشاء ملف جديد" Background="#388E3C" Foreground="White"
                       Padding="15,10" Margin="0,0,10,0" BorderThickness="0"
                       Click="AddCaseButton_Click"/>

                <Button Content="📅 جدولة موعد" Background="#F57C00" Foreground="White"
                       Padding="15,10" Margin="0,0,10,0" BorderThickness="0"
                       Click="AddAppointmentButton_Click"/>

                <Button Content="📊 عرض التقارير" Background="#7B1FA2" Foreground="White"
                       Padding="15,10" BorderThickness="0"
                       Click="ViewReportsButton_Click"/>
            </StackPanel>
        </StackPanel>
    </ScrollViewer>
</Page>
