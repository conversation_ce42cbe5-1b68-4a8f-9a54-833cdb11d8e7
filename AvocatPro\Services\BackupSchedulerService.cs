using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using AvocatPro.Data;
using AvocatPro.Models.Backup;

namespace AvocatPro.Services
{
    /// <summary>
    /// خدمة جدولة النسخ الاحتياطية
    /// </summary>
    public class BackupSchedulerService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<BackupSchedulerService> _logger;
        private readonly System.Timers.Timer _timer;
        private readonly Dictionary<int, System.Timers.Timer> _configurationTimers;
        private bool _isRunning = false;

        public BackupSchedulerService(IServiceProvider serviceProvider, ILogger<BackupSchedulerService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _timer = new System.Timers.Timer(TimeSpan.FromMinutes(1).TotalMilliseconds); // فحص كل دقيقة
            _timer.Elapsed += OnTimerElapsed;
            _configurationTimers = new Dictionary<int, System.Timers.Timer>();
        }

        /// <summary>
        /// بدء الخدمة
        /// </summary>
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("بدء خدمة جدولة النسخ الاحتياطية");
            
            await InitializeScheduledBackupsAsync();
            _timer.Start();

            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken); // فحص دوري كل 5 دقائق
                await RefreshScheduledBackupsAsync();
            }
        }

        /// <summary>
        /// إيقاف الخدمة
        /// </summary>
        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("إيقاف خدمة جدولة النسخ الاحتياطية");
            
            _timer?.Stop();
            
            foreach (var timer in _configurationTimers.Values)
            {
                timer?.Stop();
                timer?.Dispose();
            }
            
            _configurationTimers.Clear();
            
            await base.StopAsync(cancellationToken);
        }

        /// <summary>
        /// تهيئة النسخ الاحتياطية المجدولة
        /// </summary>
        private async Task InitializeScheduledBackupsAsync()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<AvocatProDbContext>();

                var configurations = await context.BackupConfigurations
                    .Where(c => c.IsEnabled && c.Frequency != BackupFrequency.Manual)
                    .ToListAsync();

                foreach (var config in configurations)
                {
                    await ScheduleBackupConfigurationAsync(config);
                }

                _logger.LogInformation($"تم تهيئة {configurations.Count} إعداد نسخ احتياطية مجدولة");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تهيئة النسخ الاحتياطية المجدولة");
            }
        }

        /// <summary>
        /// تحديث النسخ الاحتياطية المجدولة
        /// </summary>
        private async Task RefreshScheduledBackupsAsync()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<AvocatProDbContext>();

                var configurations = await context.BackupConfigurations
                    .Where(c => c.IsEnabled && c.Frequency != BackupFrequency.Manual)
                    .ToListAsync();

                // إزالة الإعدادات المحذوفة أو المعطلة
                var configIds = configurations.Select(c => c.Id).ToHashSet();
                var timersToRemove = _configurationTimers.Keys.Where(id => !configIds.Contains(id)).ToList();
                
                foreach (var id in timersToRemove)
                {
                    _configurationTimers[id].Stop();
                    _configurationTimers[id].Dispose();
                    _configurationTimers.Remove(id);
                }

                // إضافة أو تحديث الإعدادات الجديدة
                foreach (var config in configurations)
                {
                    if (!_configurationTimers.ContainsKey(config.Id))
                    {
                        await ScheduleBackupConfigurationAsync(config);
                    }
                    else
                    {
                        // تحديث الجدولة إذا تغيرت
                        await UpdateBackupScheduleAsync(config);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث النسخ الاحتياطية المجدولة");
            }
        }

        /// <summary>
        /// جدولة إعداد نسخة احتياطية
        /// </summary>
        private async Task ScheduleBackupConfigurationAsync(BackupConfiguration config)
        {
            try
            {
                var nextBackupTime = config.CalculateNextBackupDate();
                if (nextBackupTime == DateTime.MaxValue)
                {
                    return; // لا توجد جدولة
                }

                var interval = CalculateTimerInterval(config);
                if (interval <= 0)
                {
                    return; // فترة غير صحيحة
                }

                var timer = new System.Timers.Timer(interval);
                timer.Elapsed += async (sender, e) => await ExecuteScheduledBackupAsync(config.Id);
                timer.AutoReset = true;
                timer.Start();

                _configurationTimers[config.Id] = timer;

                _logger.LogInformation($"تم جدولة النسخة الاحتياطية '{config.Name}' للتنفيذ في {nextBackupTime:yyyy-MM-dd HH:mm:ss}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في جدولة النسخة الاحتياطية '{config.Name}'");
            }
        }

        /// <summary>
        /// تحديث جدولة النسخة الاحتياطية
        /// </summary>
        private async Task UpdateBackupScheduleAsync(BackupConfiguration config)
        {
            if (_configurationTimers.TryGetValue(config.Id, out var existingTimer))
            {
                existingTimer.Stop();
                existingTimer.Dispose();
                _configurationTimers.Remove(config.Id);
            }

            await ScheduleBackupConfigurationAsync(config);
        }

        /// <summary>
        /// حساب فترة المؤقت
        /// </summary>
        private double CalculateTimerInterval(BackupConfiguration config)
        {
            return config.Frequency switch
            {
                BackupFrequency.Daily => TimeSpan.FromDays(1).TotalMilliseconds,
                BackupFrequency.Weekly => TimeSpan.FromDays(7).TotalMilliseconds,
                BackupFrequency.Monthly => TimeSpan.FromDays(30).TotalMilliseconds, // تقريبي
                _ => 0
            };
        }

        /// <summary>
        /// تنفيذ النسخة الاحتياطية المجدولة
        /// </summary>
        private async Task ExecuteScheduledBackupAsync(int configurationId)
        {
            if (_isRunning)
            {
                _logger.LogWarning($"تم تخطي النسخة الاحتياطية المجدولة {configurationId} - عملية أخرى قيد التنفيذ");
                return;
            }

            _isRunning = true;

            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<AvocatProDbContext>();
                var backupService = scope.ServiceProvider.GetRequiredService<BackupService>();

                var config = await context.BackupConfigurations
                    .FirstOrDefaultAsync(c => c.Id == configurationId);

                if (config == null || !config.IsEnabled)
                {
                    _logger.LogWarning($"إعداد النسخة الاحتياطية {configurationId} غير موجود أو معطل");
                    return;
                }

                // التحقق من الوقت المناسب للتنفيذ
                if (!IsTimeForBackup(config))
                {
                    return;
                }

                _logger.LogInformation($"بدء تنفيذ النسخة الاحتياطية المجدولة '{config.Name}'");

                var result = await backupService.CreateBackupAsync(configurationId);

                if (result.Success)
                {
                    _logger.LogInformation($"تم إنشاء النسخة الاحتياطية '{config.Name}' بنجاح - الحجم: {FormatBytes(result.BackupSize)}");
                }
                else
                {
                    _logger.LogError($"فشل في إنشاء النسخة الاحتياطية '{config.Name}': {result.Message}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في تنفيذ النسخة الاحتياطية المجدولة {configurationId}");
            }
            finally
            {
                _isRunning = false;
            }
        }

        /// <summary>
        /// التحقق من الوقت المناسب للنسخة الاحتياطية
        /// </summary>
        private bool IsTimeForBackup(BackupConfiguration config)
        {
            var now = DateTime.Now;
            var scheduledTime = config.ScheduledTime;

            // التحقق من الوقت اليومي
            if (Math.Abs((now.TimeOfDay - scheduledTime).TotalMinutes) > 1)
            {
                return false; // ليس الوقت المناسب
            }

            return config.Frequency switch
            {
                BackupFrequency.Daily => true,
                BackupFrequency.Weekly => IsWeeklyBackupTime(config, now),
                BackupFrequency.Monthly => IsMonthlyBackupTime(config, now),
                _ => false
            };
        }

        /// <summary>
        /// التحقق من وقت النسخة الأسبوعية
        /// </summary>
        private bool IsWeeklyBackupTime(BackupConfiguration config, DateTime now)
        {
            if (!config.WeeklyDays.HasValue)
            {
                return false;
            }

            var dayFlag = (DaysOfWeek)(1 << (int)now.DayOfWeek);
            return config.WeeklyDays.Value.HasFlag(dayFlag);
        }

        /// <summary>
        /// التحقق من وقت النسخة الشهرية
        /// </summary>
        private bool IsMonthlyBackupTime(BackupConfiguration config, DateTime now)
        {
            if (!config.MonthlyDay.HasValue)
            {
                return false;
            }

            return now.Day == config.MonthlyDay.Value;
        }

        /// <summary>
        /// معالج حدث المؤقت
        /// </summary>
        private async void OnTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            await CheckPendingBackupsAsync();
        }

        /// <summary>
        /// فحص النسخ الاحتياطية المعلقة
        /// </summary>
        private async Task CheckPendingBackupsAsync()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<AvocatProDbContext>();

                var pendingConfigs = await context.BackupConfigurations
                    .Where(c => c.IsEnabled && 
                               c.Frequency != BackupFrequency.Manual &&
                               c.NextBackupDate.HasValue &&
                               c.NextBackupDate.Value <= DateTime.Now)
                    .ToListAsync();

                foreach (var config in pendingConfigs)
                {
                    _ = Task.Run(async () => await ExecuteScheduledBackupAsync(config.Id));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص النسخ الاحتياطية المعلقة");
            }
        }

        /// <summary>
        /// إضافة إعداد نسخة احتياطية جديد للجدولة
        /// </summary>
        public async Task AddBackupConfigurationAsync(int configurationId)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<AvocatProDbContext>();

                var config = await context.BackupConfigurations
                    .FirstOrDefaultAsync(c => c.Id == configurationId);

                if (config != null && config.IsEnabled && config.Frequency != BackupFrequency.Manual)
                {
                    await ScheduleBackupConfigurationAsync(config);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في إضافة إعداد النسخة الاحتياطية {configurationId} للجدولة");
            }
        }

        /// <summary>
        /// إزالة إعداد نسخة احتياطية من الجدولة
        /// </summary>
        public void RemoveBackupConfiguration(int configurationId)
        {
            if (_configurationTimers.TryGetValue(configurationId, out var timer))
            {
                timer.Stop();
                timer.Dispose();
                _configurationTimers.Remove(configurationId);
                
                _logger.LogInformation($"تم إزالة إعداد النسخة الاحتياطية {configurationId} من الجدولة");
            }
        }

        /// <summary>
        /// تنفيذ نسخة احتياطية فورية
        /// </summary>
        public async Task<BackupResult> ExecuteImmediateBackupAsync(int configurationId, int userId)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var backupService = scope.ServiceProvider.GetRequiredService<BackupService>();

                _logger.LogInformation($"بدء تنفيذ النسخة الاحتياطية الفورية {configurationId} بواسطة المستخدم {userId}");

                var result = await backupService.CreateBackupAsync(configurationId, userId);

                if (result.Success)
                {
                    _logger.LogInformation($"تم إنشاء النسخة الاحتياطية الفورية بنجاح - الحجم: {FormatBytes(result.BackupSize)}");
                }
                else
                {
                    _logger.LogError($"فشل في إنشاء النسخة الاحتياطية الفورية: {result.Message}");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في تنفيذ النسخة الاحتياطية الفورية {configurationId}");
                return new BackupResult { Success = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// الحصول على حالة الجدولة
        /// </summary>
        public async Task<List<BackupScheduleStatus>> GetScheduleStatusAsync()
        {
            var statuses = new List<BackupScheduleStatus>();

            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<AvocatProDbContext>();

                var configurations = await context.BackupConfigurations
                    .Where(c => c.IsEnabled)
                    .ToListAsync();

                foreach (var config in configurations)
                {
                    var status = new BackupScheduleStatus
                    {
                        ConfigurationId = config.Id,
                        ConfigurationName = config.Name,
                        Frequency = config.Frequency,
                        ScheduledTime = config.ScheduledTime,
                        NextBackupDate = config.NextBackupDate,
                        LastBackupDate = config.LastBackupDate,
                        LastBackupStatus = config.LastBackupStatus,
                        IsScheduled = _configurationTimers.ContainsKey(config.Id)
                    };

                    statuses.Add(status);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على حالة الجدولة");
            }

            return statuses;
        }

        /// <summary>
        /// تنسيق الحجم بالبايت
        /// </summary>
        private static string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            
            return $"{number:n1} {suffixes[counter]}";
        }

        /// <summary>
        /// تحرير الموارد
        /// </summary>
        public override void Dispose()
        {
            _timer?.Dispose();
            
            foreach (var timer in _configurationTimers.Values)
            {
                timer?.Dispose();
            }
            
            _configurationTimers.Clear();
            
            base.Dispose();
        }
    }

    /// <summary>
    /// حالة جدولة النسخة الاحتياطية
    /// </summary>
    public class BackupScheduleStatus
    {
        public int ConfigurationId { get; set; }
        public string ConfigurationName { get; set; } = string.Empty;
        public BackupFrequency Frequency { get; set; }
        public TimeSpan ScheduledTime { get; set; }
        public DateTime? NextBackupDate { get; set; }
        public DateTime? LastBackupDate { get; set; }
        public BackupStatus? LastBackupStatus { get; set; }
        public bool IsScheduled { get; set; }
    }
}
