﻿#pragma checksum "..\..\..\..\..\Views\Windows\ModernMainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "65E3D44AF977E33CE0CA3FBF9B666C3ADEE8073B"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using AvocatPro.Views.Controls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Windows {
    
    
    /// <summary>
    /// ModernMainWindow
    /// </summary>
    public partial class ModernMainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 107 "..\..\..\..\..\Views\Windows\ModernMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DateTimeDisplay;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\..\..\Views\Windows\ModernMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal AvocatPro.Views.Controls.ModernSidebarControl SidebarControl;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\..\Views\Windows\ModernMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Frame MainContentFrame;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\..\Views\Windows\ModernMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserCountDisplay;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\..\Views\Windows\ModernMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MemoryUsageDisplay;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\..\..\Views\Windows\ModernMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastBackupDisplay;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\..\..\Views\Windows\ModernMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border LoadingOverlay;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\..\..\Views\Windows\ModernMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LoadingText;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\..\..\Views\Windows\ModernMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border NotificationPanel;
        
        #line default
        #line hidden
        
        
        #line 228 "..\..\..\..\..\Views\Windows\ModernMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel NotificationsList;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/windows/modernmainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\ModernMainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 72 "..\..\..\..\..\Views\Windows\ModernMainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.TitleBar_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.DateTimeDisplay = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            
            #line 112 "..\..\..\..\..\Views\Windows\ModernMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.MinimizeWindow_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 115 "..\..\..\..\..\Views\Windows\ModernMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.MaximizeWindow_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 118 "..\..\..\..\..\Views\Windows\ModernMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseWindow_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.SidebarControl = ((AvocatPro.Views.Controls.ModernSidebarControl)(target));
            return;
            case 7:
            this.MainContentFrame = ((System.Windows.Controls.Frame)(target));
            return;
            case 8:
            this.UserCountDisplay = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.MemoryUsageDisplay = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.LastBackupDisplay = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            
            #line 185 "..\..\..\..\..\Views\Windows\ModernMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickHelp_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.LoadingOverlay = ((System.Windows.Controls.Border)(target));
            return;
            case 13:
            this.LoadingText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.NotificationPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 15:
            
            #line 222 "..\..\..\..\..\Views\Windows\ModernMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseNotifications_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.NotificationsList = ((System.Windows.Controls.StackPanel)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

