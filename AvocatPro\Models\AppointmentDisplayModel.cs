using System;

namespace AvocatPro.Models;

/// <summary>
/// نموذج عرض المواعيد في التقويم والجداول
/// </summary>
public class AppointmentDisplayModel
{
    public int Id { get; set; }
    public string Title { get; set; } = "";
    public string? Description { get; set; }
    public DateTime AppointmentDate { get; set; }
    public TimeSpan StartTime { get; set; }
    public TimeSpan EndTime { get; set; }
    public AppointmentType Type { get; set; }
    public AppointmentStatus Status { get; set; }
    public AppointmentPriority Priority { get; set; }
    public int? ClientId { get; set; }
    public string? ClientName { get; set; }
    public int? CaseId { get; set; }
    public string? CaseTitle { get; set; }
    public string? Location { get; set; }
    public string? RequiredAttendees { get; set; }
    public decimal? Cost { get; set; }
    public bool IsPaid { get; set; }
    public bool IsNotified { get; set; }
    public int NotificationMinutes { get; set; }
    public DateTime? NotificationDate { get; set; }
    public string? AppointmentNotes { get; set; }
    public string? Result { get; set; }
    public string? FollowUp { get; set; }
    public string? ContactPhone { get; set; }
    public string? ContactEmail { get; set; }
    public bool IsRecurring { get; set; }
    public string? RecurrencePattern { get; set; }
    public string CreatedByUserName { get; set; } = "";
    public DateTime CreatedAt { get; set; }

    // خصائص العرض
    public string TypeDisplay => Type switch
    {
        AppointmentType.Consultation => "استشارة",
        AppointmentType.Meeting => "اجتماع",
        AppointmentType.PhoneCall => "مكالمة هاتفية",
        AppointmentType.FieldVisit => "زيارة ميدانية",
        AppointmentType.ContractSigning => "توقيع عقد",
        AppointmentType.CaseFollowUp => "متابعة قضية",
        AppointmentType.Personal => "موعد شخصي",
        AppointmentType.Training => "تدريب",
        _ => "غير محدد"
    };

    public string StatusDisplay => Status switch
    {
        AppointmentStatus.Scheduled => "مجدول",
        AppointmentStatus.Confirmed => "مؤكد",
        AppointmentStatus.InProgress => "جاري",
        AppointmentStatus.Completed => "مكتمل",
        AppointmentStatus.Postponed => "مؤجل",
        AppointmentStatus.Cancelled => "ملغي",
        AppointmentStatus.NoShow => "لم يحضر",
        _ => "غير محدد"
    };

    public string PriorityDisplay => Priority switch
    {
        AppointmentPriority.Low => "منخفضة",
        AppointmentPriority.Medium => "متوسطة",
        AppointmentPriority.High => "عالية",
        AppointmentPriority.Urgent => "عاجلة",
        _ => "غير محدد"
    };

    public string AppointmentDateDisplay => AppointmentDate.ToString("dd/MM/yyyy");
    public string StartTimeDisplay => StartTime.ToString(@"hh\:mm");
    public string EndTimeDisplay => EndTime.ToString(@"hh\:mm");
    public string TimeRangeDisplay => $"{StartTimeDisplay} - {EndTimeDisplay}";
    public string FullDateTimeDisplay => $"{AppointmentDateDisplay} ({TimeRangeDisplay})";
    
    public string CostDisplay => Cost?.ToString("N2") + " ريال" ?? "مجاني";
    public string PaymentStatusDisplay => IsPaid ? "مدفوع" : "غير مدفوع";
    public string NotificationStatusDisplay => IsNotified ? "تم التنبيه" : "لم يتم التنبيه";
    public string RecurrenceDisplay => IsRecurring ? "موعد متكرر" : "موعد واحد";
    
    public string ClientDisplay => ClientName ?? "غير محدد";
    public string CaseDisplay => CaseTitle ?? "غير مرتبط بقضية";
    public string LocationDisplay => Location ?? "غير محدد";
    public string RequiredAttendeesDisplay => RequiredAttendees ?? "غير محدد";
    public string AppointmentNotesDisplay => AppointmentNotes ?? "لا توجد ملاحظات";
    public string ResultDisplay => Result ?? "لم يتم تسجيل النتيجة";
    public string FollowUpDisplay => FollowUp ?? "لا توجد متابعة مطلوبة";
    public string ContactPhoneDisplay => ContactPhone ?? "غير محدد";
    public string ContactEmailDisplay => ContactEmail ?? "غير محدد";
    public string CreatedAtDisplay => CreatedAt.ToString("dd/MM/yyyy HH:mm");
    
    public string NotificationTimeDisplay => NotificationMinutes switch
    {
        5 => "5 دقائق قبل الموعد",
        10 => "10 دقائق قبل الموعد",
        15 => "15 دقيقة قبل الموعد",
        30 => "30 دقيقة قبل الموعد",
        60 => "ساعة قبل الموعد",
        120 => "ساعتين قبل الموعد",
        1440 => "يوم قبل الموعد",
        _ => $"{NotificationMinutes} دقيقة قبل الموعد"
    };

    public string NotificationDateDisplay => NotificationDate?.ToString("dd/MM/yyyy HH:mm") ?? "لم يتم التنبيه";

    // خصائص للتقويم
    public string CalendarTitle => $"{TypeDisplay}: {Title}";
    public string CalendarDescription => $"الموكل: {ClientDisplay}\nالمكان: {LocationDisplay}\nالوقت: {TimeRangeDisplay}";
    
    // خصائص الألوان للتقويم والعرض
    public string StatusColor => Status switch
    {
        AppointmentStatus.Scheduled => "#2196F3", // أزرق
        AppointmentStatus.Confirmed => "#4CAF50", // أخضر
        AppointmentStatus.InProgress => "#FF9800", // برتقالي
        AppointmentStatus.Completed => "#8BC34A", // أخضر فاتح
        AppointmentStatus.Postponed => "#FFC107", // أصفر
        AppointmentStatus.Cancelled => "#F44336", // أحمر
        AppointmentStatus.NoShow => "#9E9E9E", // رمادي
        _ => "#607D8B"
    };

    public string TypeColor => Type switch
    {
        AppointmentType.Consultation => "#2196F3",
        AppointmentType.Meeting => "#9C27B0",
        AppointmentType.PhoneCall => "#00BCD4",
        AppointmentType.FieldVisit => "#FF9800",
        AppointmentType.ContractSigning => "#4CAF50",
        AppointmentType.CaseFollowUp => "#795548",
        AppointmentType.Personal => "#E91E63",
        AppointmentType.Training => "#3F51B5",
        _ => "#607D8B"
    };

    public string PriorityColor => Priority switch
    {
        AppointmentPriority.Low => "#4CAF50",
        AppointmentPriority.Medium => "#FF9800",
        AppointmentPriority.High => "#F44336",
        AppointmentPriority.Urgent => "#D32F2F",
        _ => "#607D8B"
    };

    // خصائص للإحصائيات
    public string MonthYear => AppointmentDate.ToString("yyyy-MM");
    public string Year => AppointmentDate.ToString("yyyy");
    public string Month => AppointmentDate.ToString("MM");
    public string DayOfWeek => AppointmentDate.ToString("dddd", new System.Globalization.CultureInfo("ar-SA"));
    public int WeekOfYear => System.Globalization.CultureInfo.CurrentCulture.Calendar.GetWeekOfYear(
        AppointmentDate, System.Globalization.CalendarWeekRule.FirstDay, System.DayOfWeek.Saturday);

    // خصائص للتنبيهات
    public DateTime NotificationDateTime => AppointmentDate.Date.Add(StartTime).AddMinutes(-NotificationMinutes);
    public bool ShouldNotifyNow => !IsNotified && DateTime.Now >= NotificationDateTime && DateTime.Now < AppointmentDate.Date.Add(StartTime);
    public bool IsOverdue => Status == AppointmentStatus.Scheduled && DateTime.Now > AppointmentDate.Date.Add(EndTime);
    public bool IsToday => AppointmentDate.Date == DateTime.Today;
    public bool IsTomorrow => AppointmentDate.Date == DateTime.Today.AddDays(1);
    public bool IsThisWeek => AppointmentDate.Date >= DateTime.Today && AppointmentDate.Date <= DateTime.Today.AddDays(7);

    // خصائص للبحث والتصفية
    public string SearchableText => $"{Title} {Description} {ClientName} {CaseTitle} {Location} {RequiredAttendees} {AppointmentNotes}".ToLower();

    // مدة الموعد
    public TimeSpan Duration => EndTime - StartTime;
    public string DurationDisplay => Duration.TotalMinutes switch
    {
        < 60 => $"{Duration.TotalMinutes:F0} دقيقة",
        >= 60 and < 1440 => $"{Duration.TotalHours:F1} ساعة",
        _ => $"{Duration.TotalDays:F0} يوم"
    };

    // حالة الدفع مع اللون
    public string PaymentStatusColor => IsPaid ? "#4CAF50" : "#F44336";
    
    // أيقونة النوع
    public string TypeIcon => Type switch
    {
        AppointmentType.Consultation => "💼",
        AppointmentType.Meeting => "🤝",
        AppointmentType.PhoneCall => "📞",
        AppointmentType.FieldVisit => "🚗",
        AppointmentType.ContractSigning => "📝",
        AppointmentType.CaseFollowUp => "📋",
        AppointmentType.Personal => "👤",
        AppointmentType.Training => "🎓",
        _ => "📅"
    };

    // أيقونة الحالة
    public string StatusIcon => Status switch
    {
        AppointmentStatus.Scheduled => "📅",
        AppointmentStatus.Confirmed => "✅",
        AppointmentStatus.InProgress => "⏳",
        AppointmentStatus.Completed => "✔️",
        AppointmentStatus.Postponed => "⏰",
        AppointmentStatus.Cancelled => "❌",
        AppointmentStatus.NoShow => "❓",
        _ => "📅"
    };

    // أيقونة الأولوية
    public string PriorityIcon => Priority switch
    {
        AppointmentPriority.Low => "🟢",
        AppointmentPriority.Medium => "🟡",
        AppointmentPriority.High => "🟠",
        AppointmentPriority.Urgent => "🔴",
        _ => "⚪"
    };
}
