<Window x:Class="AvocatPro.Views.Windows.AddUserWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:fa="http://schemas.fontawesome.io/icons/"
        Title="إضافة مستخدم جديد"
        Height="700" Width="900"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="#F8F9FA"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Padding" Value="12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#DEE2E6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Margin" Value="0,5"/>
            <Style.Triggers>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="BorderBrush" Value="#007BFF"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="ModernComboBox" TargetType="ComboBox">
            <Setter Property="Padding" Value="12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#DEE2E6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Margin" Value="0,5"/>
        </Style>

        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#007BFF"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="6"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#0056B3"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SectionHeader" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,20,0,10"/>
        </Style>

        <Style x:Key="FieldLabel" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#495057"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="White" Padding="20" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <fa:ImageAwesome Icon="UserPlus" Foreground="#007BFF" Width="24" Height="24" Margin="0,0,10,0"/>
                    <TextBlock Text="إضافة مستخدم جديد" FontSize="20" FontWeight="Bold" Foreground="#2C3E50" VerticalAlignment="Center"/>
                </StackPanel>

                <Button Grid.Column="1" Content="✕" Width="30" Height="30" 
                       Background="Transparent" BorderThickness="0" 
                       FontSize="16" Foreground="#6C757D" 
                       Click="CloseBtn_Click"/>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <StackPanel MaxWidth="800">
                
                <!-- المعلومات الأساسية -->
                <TextBlock Text="المعلومات الأساسية" Style="{StaticResource SectionHeader}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="اسم المستخدم *" Style="{StaticResource FieldLabel}"/>
                        <TextBox x:Name="UsernameTextBox" Style="{StaticResource ModernTextBox}" 
                                Tag="أدخل اسم المستخدم"/>
                        
                        <TextBlock Text="الاسم الأول *" Style="{StaticResource FieldLabel}"/>
                        <TextBox x:Name="FirstNameTextBox" Style="{StaticResource ModernTextBox}" 
                                Tag="أدخل الاسم الأول"/>
                        
                        <TextBlock Text="رقم الهاتف" Style="{StaticResource FieldLabel}"/>
                        <TextBox x:Name="PhoneTextBox" Style="{StaticResource ModernTextBox}" 
                                Tag="أدخل رقم الهاتف"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="البريد الإلكتروني *" Style="{StaticResource FieldLabel}"/>
                        <TextBox x:Name="EmailTextBox" Style="{StaticResource ModernTextBox}" 
                                Tag="أدخل البريد الإلكتروني"/>
                        
                        <TextBlock Text="اسم العائلة *" Style="{StaticResource FieldLabel}"/>
                        <TextBox x:Name="LastNameTextBox" Style="{StaticResource ModernTextBox}" 
                                Tag="أدخل اسم العائلة"/>
                        
                        <TextBlock Text="القسم" Style="{StaticResource FieldLabel}"/>
                        <ComboBox x:Name="DepartmentComboBox" Style="{StaticResource ModernComboBox}">
                            <ComboBoxItem Content="الشؤون القانونية"/>
                            <ComboBoxItem Content="الشؤون المالية"/>
                            <ComboBoxItem Content="الشؤون الإدارية"/>
                            <ComboBoxItem Content="خدمة العملاء"/>
                            <ComboBoxItem Content="تقنية المعلومات"/>
                        </ComboBox>
                    </StackPanel>
                </Grid>

                <!-- كلمة المرور -->
                <TextBlock Text="كلمة المرور والأمان" Style="{StaticResource SectionHeader}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="كلمة المرور *" Style="{StaticResource FieldLabel}"/>
                        <PasswordBox x:Name="PasswordBox" Padding="12" FontSize="14" 
                                    BorderBrush="#DEE2E6" BorderThickness="1" 
                                    Background="White" Margin="0,5"/>
                        
                        <CheckBox x:Name="RequirePasswordChangeCheckBox" Content="يجب تغيير كلمة المرور عند أول تسجيل دخول" 
                                 FontSize="12" Margin="0,10,0,0"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="تأكيد كلمة المرور *" Style="{StaticResource FieldLabel}"/>
                        <PasswordBox x:Name="ConfirmPasswordBox" Padding="12" FontSize="14" 
                                    BorderBrush="#DEE2E6" BorderThickness="1" 
                                    Background="White" Margin="0,5"/>
                        
                        <CheckBox x:Name="TwoFactorCheckBox" Content="تفعيل المصادقة الثنائية" 
                                 FontSize="12" Margin="0,10,0,0"/>
                    </StackPanel>
                </Grid>

                <!-- الأدوار والصلاحيات -->
                <TextBlock Text="الأدوار والصلاحيات" Style="{StaticResource SectionHeader}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="الأدوار المتاحة" Style="{StaticResource FieldLabel}"/>
                        <Border BorderBrush="#DEE2E6" BorderThickness="1" Background="White" Height="150">
                            <ListBox x:Name="AvailableRolesListBox" SelectionMode="Multiple" 
                                    BorderThickness="0" Background="Transparent">
                                <ListBoxItem Content="مدير النظام"/>
                                <ListBoxItem Content="محامي أول"/>
                                <ListBoxItem Content="محامي"/>
                                <ListBoxItem Content="مستشار قانوني"/>
                                <ListBoxItem Content="سكرتير"/>
                                <ListBoxItem Content="مساعد إداري"/>
                                <ListBoxItem Content="محاسب"/>
                                <ListBoxItem Content="مدير مالي"/>
                                <ListBoxItem Content="متدرب"/>
                            </ListBox>
                        </Border>
                    </StackPanel>

                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                        <Button x:Name="AddRoleBtn" Content="←" Width="30" Height="30" 
                               Background="#28A745" Style="{StaticResource ModernButton}" 
                               Click="AddRole_Click" Margin="0,5"/>
                        <Button x:Name="RemoveRoleBtn" Content="→" Width="30" Height="30" 
                               Background="#DC3545" Style="{StaticResource ModernButton}" 
                               Click="RemoveRole_Click" Margin="0,5"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="الأدوار المحددة" Style="{StaticResource FieldLabel}"/>
                        <Border BorderBrush="#DEE2E6" BorderThickness="1" Background="White" Height="150">
                            <ListBox x:Name="SelectedRolesListBox" SelectionMode="Multiple" 
                                    BorderThickness="0" Background="Transparent"/>
                        </Border>
                    </StackPanel>
                </Grid>

                <!-- الإعدادات الإضافية -->
                <TextBlock Text="الإعدادات الإضافية" Style="{StaticResource SectionHeader}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <CheckBox x:Name="IsActiveCheckBox" Content="المستخدم نشط" 
                                 FontSize="14" IsChecked="True" Margin="0,10,0,5"/>
                        
                        <CheckBox x:Name="EmailNotificationsCheckBox" Content="إشعارات البريد الإلكتروني" 
                                 FontSize="14" IsChecked="True" Margin="0,5"/>
                        
                        <CheckBox x:Name="SystemNotificationsCheckBox" Content="إشعارات النظام" 
                                 FontSize="14" IsChecked="True" Margin="0,5"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="مهلة انتهاء الجلسة (بالدقائق)" Style="{StaticResource FieldLabel}"/>
                        <TextBox x:Name="SessionTimeoutTextBox" Style="{StaticResource ModernTextBox}" 
                                Text="30" Tag="30"/>
                        
                        <TextBlock Text="ملاحظات" Style="{StaticResource FieldLabel}"/>
                        <TextBox x:Name="NotesTextBox" Style="{StaticResource ModernTextBox}" 
                                Height="60" TextWrapping="Wrap" AcceptsReturn="True"
                                Tag="ملاحظات إضافية..."/>
                    </StackPanel>
                </Grid>
            </StackPanel>
        </ScrollViewer>

        <!-- شريط الأزرار -->
        <Border Grid.Row="2" Background="White" Padding="20" BorderBrush="#DEE2E6" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                <Button x:Name="SaveBtn" Style="{StaticResource ModernButton}"
                       Click="SaveBtn_Click">
                    <StackPanel Orientation="Horizontal">
                        <fa:ImageAwesome Icon="Save" Width="14" Height="14" Margin="0,0,5,0"/>
                        <TextBlock Text="حفظ المستخدم"/>
                    </StackPanel>
                </Button>
                
                <Button x:Name="SaveAndAddBtn" Background="#28A745"
                       Style="{StaticResource ModernButton}" Click="SaveAndAddBtn_Click">
                    <StackPanel Orientation="Horizontal">
                        <fa:ImageAwesome Icon="Plus" Width="14" Height="14" Margin="0,0,5,0"/>
                        <TextBlock Text="حفظ وإضافة آخر"/>
                    </StackPanel>
                </Button>

                <Button x:Name="CancelBtn" Background="#6C757D"
                       Style="{StaticResource ModernButton}" Click="CancelBtn_Click">
                    <StackPanel Orientation="Horizontal">
                        <fa:ImageAwesome Icon="Times" Width="14" Height="14" Margin="0,0,5,0"/>
                        <TextBlock Text="إلغاء"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
