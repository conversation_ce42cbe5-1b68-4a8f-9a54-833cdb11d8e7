using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using AvocatPro.Models;
using AvocatPro.Services;
using AvocatPro.Views.Windows;

namespace AvocatPro.Views.Pages
{
    /// <summary>
    /// صفحة إدارة الجلسات الشاملة
    /// </summary>
    public partial class ComprehensiveSessionsPage : Page, INotifyPropertyChanged
    {
        #region Fields

        private readonly SessionsService _sessionsService;
        private readonly MoroccanCourtsService _courtsService;
        private ObservableCollection<AdvancedSessionModel> _allSessions;
        private ObservableCollection<AdvancedSessionModel> _filteredSessions;
        private SessionStatistics _statistics;
        private bool _isSearchPlaceholder = true;

        #endregion

        #region Properties

        public ObservableCollection<AdvancedSessionModel> FilteredSessions
        {
            get => _filteredSessions;
            set { _filteredSessions = value; OnPropertyChanged(); }
        }

        public SessionStatistics Statistics
        {
            get => _statistics;
            set { _statistics = value; OnPropertyChanged(); UpdateStatisticsDisplay(); }
        }

        #endregion

        #region Constructor

        public ComprehensiveSessionsPage()
        {
            InitializeComponent();

            try
            {
                _sessionsService = new SessionsService();
                _courtsService = new MoroccanCourtsService();
                _allSessions = new ObservableCollection<AdvancedSessionModel>();
                _filteredSessions = new ObservableCollection<AdvancedSessionModel>();
                _statistics = new SessionStatistics();

                DataContext = this;

                InitializeData();
                SetupFilters();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة صفحة الجلسات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region Initialization

        private void InitializeData()
        {
            // تحميل بيانات تجريبية
            LoadSampleData();
            
            // تحديث الإحصائيات
            UpdateStatistics();
            
            // تطبيق التصفية الأولية
            ApplyFilters();
        }

        private void LoadSampleData()
        {
            var sampleSessions = new List<AdvancedSessionModel>
            {
                new AdvancedSessionModel
                {
                    Id = 1,
                    SessionNumber = "S2024001",
                    FileNumber = "F2024001",
                    Client = "شركة الأطلس للتجارة",
                    CaseType = "تجاري",
                    Court = "المحكمة التجارية بالدار البيضاء",
                    ProcedureType = "جلسة مرافعة",
                    ProcedureDate = DateTime.Now.AddDays(-5),
                    Decision = "تأجيل للمرافعة",
                    SessionDate = DateTime.Now.AddDays(7),
                    SessionTime = new TimeSpan(9, 30, 0),
                    SessionType = "جلسة مرافعة",
                    AssignedLawyer = "الأستاذ محمد العلوي",
                    Judge = "القاضي أحمد الفاسي",
                    Clerk = "كاتب الضبط محمد",
                    CourtRoom = "القاعة الأولى",
                    Status = "مجدولة",
                    Priority = "مهم",
                    Expenses = 1500,
                    Notes = "ملف مهم يتطلب حضور الموكل",
                    CreatedBy = "المدير"
                },
                new AdvancedSessionModel
                {
                    Id = 2,
                    SessionNumber = "********",
                    FileNumber = "********",
                    Client = "السيد عبد الرحمن الحسني",
                    CaseType = "مدني",
                    Court = "المحكمة الابتدائية بالرباط",
                    ProcedureType = "جلسة تحقيق",
                    ProcedureDate = DateTime.Now.AddDays(-3),
                    Decision = "استكمال التحقيق",
                    SessionDate = DateTime.Now,
                    SessionTime = new TimeSpan(14, 0, 0),
                    SessionType = "جلسة تحقيق",
                    AssignedLawyer = "الأستاذة فاطمة الزهراء",
                    Judge = "القاضية خديجة الإدريسي",
                    Clerk = "كاتبة الضبط فاطمة",
                    CourtRoom = "القاعة الثانية",
                    Status = "جارية",
                    Priority = "عاجل",
                    Expenses = 800,
                    Notes = "قضية عاجلة",
                    CreatedBy = "المساعد"
                },
                new AdvancedSessionModel
                {
                    Id = 3,
                    SessionNumber = "********",
                    FileNumber = "********",
                    Client = "السيدة عائشة المغربي",
                    CaseType = "أسرة",
                    Court = "محكمة الأسرة بفاس",
                    ProcedureType = "جلسة صلح",
                    ProcedureDate = DateTime.Now.AddDays(-10),
                    Decision = "تم الصلح",
                    SessionDate = DateTime.Now.AddDays(-2),
                    SessionTime = new TimeSpan(10, 0, 0),
                    SessionType = "جلسة صلح",
                    AssignedLawyer = "الأستاذ يوسف الحسني",
                    Judge = "القاضي محمد بن علي",
                    Clerk = "كاتب الضبط عبد الرحمن",
                    CourtRoom = "قاعة الصلح",
                    Status = "مكتملة",
                    Priority = "عادي",
                    Expenses = 500,
                    Notes = "تم الوصول لاتفاق",
                    Outcome = "صلح ناجح",
                    CreatedBy = "المدير"
                }
            };

            _allSessions.Clear();
            foreach (var session in sampleSessions)
            {
                _allSessions.Add(session);
            }
        }

        private void SetupFilters()
        {
            try
            {
                // إعداد فلاتر أنواع الجلسات
                if (_sessionsService != null)
                {
                    var sessionTypes = _sessionsService.GetSessionTypes();
                    if (sessionTypes != null && SessionTypeFilterComboBox != null)
                    {
                        foreach (var type in sessionTypes)
                        {
                            SessionTypeFilterComboBox.Items.Add(new ComboBoxItem { Content = type });
                        }
                    }
                }

                // إعداد فلاتر المحاكم
                if (_courtsService != null)
                {
                    var courts = _courtsService.GetMoroccanCourts();
                    if (courts != null && CourtFilterComboBox != null)
                    {
                        foreach (var court in courts)
                        {
                            CourtFilterComboBox.Items.Add(new ComboBoxItem { Content = court });
                        }
                    }
                }

                // إعداد فلاتر المحامين
                if (_sessionsService != null)
                {
                    var lawyers = _sessionsService.GetLawyers();
                    if (lawyers != null && LawyerFilterComboBox != null)
                    {
                        foreach (var lawyer in lawyers)
                        {
                            LawyerFilterComboBox.Items.Add(new ComboBoxItem { Content = lawyer });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ دون إيقاف التطبيق
                System.Diagnostics.Debug.WriteLine($"خطأ في إعداد الفلاتر: {ex.Message}");
            }
        }

        #endregion

        #region Statistics

        private void UpdateStatistics()
        {
            Statistics = _sessionsService.CalculateStatistics(_allSessions.ToList());
        }

        private void UpdateStatisticsDisplay()
        {
            try
            {
                if (Statistics != null)
                {
                    if (TotalSessionsCount != null)
                        TotalSessionsCount.Text = Statistics.TotalSessions.ToString();
                    if (TodaySessionsCount != null)
                        TodaySessionsCount.Text = Statistics.TodaySessions.ToString();
                    if (ScheduledSessionsCount != null)
                        ScheduledSessionsCount.Text = Statistics.ScheduledSessions.ToString();
                    if (CompletedSessionsCount != null)
                        CompletedSessionsCount.Text = Statistics.CompletedSessions.ToString();
                    if (PostponedSessionsCount != null)
                        PostponedSessionsCount.Text = Statistics.PostponedSessions.ToString();
                    if (TotalExpensesAmount != null)
                        TotalExpensesAmount.Text = $"{Statistics.TotalExpenses:N0} درهم";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الإحصائيات: {ex.Message}");
            }
        }

        #endregion

        #region Filtering

        private void ApplyFilters()
        {
            try
            {
                if (_allSessions == null || _filteredSessions == null)
                    return;

                var filtered = _allSessions.AsEnumerable();

            // تطبيق فلتر البحث
            if (!_isSearchPlaceholder && !string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                var searchTerm = SearchTextBox.Text.ToLower();
                filtered = filtered.Where(s =>
                    s.SessionNumber.ToLower().Contains(searchTerm) ||
                    s.FileNumber.ToLower().Contains(searchTerm) ||
                    s.Client.ToLower().Contains(searchTerm) ||
                    s.Court.ToLower().Contains(searchTerm) ||
                    s.SessionType.ToLower().Contains(searchTerm) ||
                    s.AssignedLawyer.ToLower().Contains(searchTerm));
            }

            // تطبيق فلتر الحالة
            if (StatusFilterComboBox.SelectedItem is ComboBoxItem statusItem && 
                statusItem.Content.ToString() != "جميع الحالات")
            {
                filtered = filtered.Where(s => s.Status == statusItem.Content.ToString());
            }

            // تطبيق فلتر نوع الجلسة
            if (SessionTypeFilterComboBox.SelectedItem is ComboBoxItem sessionTypeItem && 
                sessionTypeItem.Content.ToString() != "جميع الأنواع")
            {
                filtered = filtered.Where(s => s.SessionType == sessionTypeItem.Content.ToString());
            }

            // تطبيق فلتر المحكمة
            if (CourtFilterComboBox.SelectedItem is ComboBoxItem courtItem && 
                courtItem.Content.ToString() != "جميع المحاكم")
            {
                filtered = filtered.Where(s => s.Court == courtItem.Content.ToString());
            }

            // تطبيق فلتر المحامي
            if (LawyerFilterComboBox.SelectedItem is ComboBoxItem lawyerItem && 
                lawyerItem.Content.ToString() != "جميع المحامين")
            {
                filtered = filtered.Where(s => s.AssignedLawyer == lawyerItem.Content.ToString());
            }

            // تطبيق فلتر الأولوية
            if (PriorityFilterComboBox.SelectedItem is ComboBoxItem priorityItem && 
                priorityItem.Content.ToString() != "جميع الأولويات")
            {
                filtered = filtered.Where(s => s.Priority == priorityItem.Content.ToString());
            }

            // تطبيق فلتر التاريخ
            if (DateFromPicker.SelectedDate.HasValue)
            {
                filtered = filtered.Where(s => s.SessionDate.Date >= DateFromPicker.SelectedDate.Value.Date);
            }

            if (DateToPicker.SelectedDate.HasValue)
            {
                filtered = filtered.Where(s => s.SessionDate.Date <= DateToPicker.SelectedDate.Value.Date);
            }

            // تطبيق العرض السريع
            if (QuickViewComboBox.SelectedItem is ComboBoxItem quickViewItem)
            {
                switch (quickViewItem.Content.ToString())
                {
                    case "جلسات اليوم":
                        filtered = filtered.Where(s => s.SessionDate.Date == DateTime.Now.Date);
                        break;
                    case "جلسات الأسبوع":
                        var startOfWeek = DateTime.Now.Date.AddDays(-(int)DateTime.Now.DayOfWeek);
                        var endOfWeek = startOfWeek.AddDays(6);
                        filtered = filtered.Where(s => s.SessionDate.Date >= startOfWeek && s.SessionDate.Date <= endOfWeek);
                        break;
                    case "جلسات الشهر":
                        var startOfMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                        var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);
                        filtered = filtered.Where(s => s.SessionDate.Date >= startOfMonth && s.SessionDate.Date <= endOfMonth);
                        break;
                    case "الجلسات القادمة":
                        filtered = filtered.Where(s => s.SessionDate >= DateTime.Now && s.Status == "مجدولة");
                        break;
                    case "الجلسات المتأخرة":
                        filtered = filtered.Where(s => s.SessionDate < DateTime.Now && s.Status == "مجدولة");
                        break;
                }
            }

            // ترتيب النتائج
            filtered = filtered.OrderBy(s => s.SessionDate);

            // تحديث القائمة المفلترة
            FilteredSessions.Clear();
            foreach (var session in filtered)
            {
                FilteredSessions.Add(session);
            }

            if (SessionsDataGrid != null)
                SessionsDataGrid.ItemsSource = FilteredSessions;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق الفلاتر: {ex.Message}");
            }
        }

        #endregion

        #region Event Handlers

        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (_isSearchPlaceholder)
            {
                SearchTextBox.Text = "";
                SearchTextBox.Foreground = System.Windows.Media.Brushes.Black;
                _isSearchPlaceholder = false;
            }
        }

        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                SearchTextBox.Text = "البحث في الجلسات...";
                SearchTextBox.Foreground = System.Windows.Media.Brushes.Gray;
                _isSearchPlaceholder = true;
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (!_isSearchPlaceholder)
            {
                ApplyFilters();
            }
        }

        private void StatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void SessionTypeFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void CourtFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void LawyerFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void PriorityFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void DateFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void QuickView_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadSampleData();
            UpdateStatistics();
            ApplyFilters();
        }

        private void AddSessionButton_Click(object sender, RoutedEventArgs e)
        {
            var addWindow = new AdvancedAddSessionWindow();
            if (addWindow.ShowDialog() == true)
            {
                _allSessions.Add(addWindow.NewSession);
                UpdateStatistics();
                ApplyFilters();
            }
        }

        private void CalendarViewButton_Click(object sender, RoutedEventArgs e)
        {
            var calendarWindow = new SessionsCalendarWindow(_allSessions.ToList());
            calendarWindow.ShowDialog();
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var exportData = _sessionsService.PrepareExportData(FilteredSessions.ToList());
                // هنا يمكن إضافة كود التصدير إلى Excel أو PDF
                MessageBox.Show("تم تصدير البيانات بنجاح!", "تصدير", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // هنا يمكن إضافة كود الطباعة
                MessageBox.Show("تم إرسال القائمة للطباعة!", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SessionsDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (SessionsDataGrid.SelectedItem is AdvancedSessionModel selectedSession)
            {
                var detailsWindow = new SessionDetailsWindow(selectedSession);
                detailsWindow.ShowDialog();
            }
        }

        private void EditSession_Click(object sender, RoutedEventArgs e)
        {
            if (((Button)sender).DataContext is AdvancedSessionModel session)
            {
                var editWindow = new AdvancedAddSessionWindow(session);
                if (editWindow.ShowDialog() == true)
                {
                    // تحديث الجلسة في القائمة
                    var index = _allSessions.IndexOf(session);
                    if (index >= 0)
                    {
                        _allSessions[index] = editWindow.NewSession;
                        UpdateStatistics();
                        ApplyFilters();
                    }
                }
            }
        }

        private void DeleteSession_Click(object sender, RoutedEventArgs e)
        {
            if (((Button)sender).DataContext is AdvancedSessionModel session)
            {
                var result = MessageBox.Show($"هل أنت متأكد من حذف الجلسة رقم {session.SessionNumber}؟",
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    _allSessions.Remove(session);
                    UpdateStatistics();
                    ApplyFilters();
                }
            }
        }

        private void ViewDetails_Click(object sender, RoutedEventArgs e)
        {
            if (((Button)sender).DataContext is AdvancedSessionModel session)
            {
                var detailsWindow = new SessionDetailsWindow(session);
                detailsWindow.ShowDialog();
            }
        }

        private void PrintSession_Click(object sender, RoutedEventArgs e)
        {
            if (((Button)sender).DataContext is AdvancedSessionModel session)
            {
                try
                {
                    // هنا يمكن إضافة كود طباعة الجلسة
                    MessageBox.Show($"تم إرسال الجلسة {session.SessionNumber} للطباعة!", 
                        "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
