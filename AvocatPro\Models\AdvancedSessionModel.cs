using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace AvocatPro.Models
{
    /// <summary>
    /// نموذج الجلسة المتقدم
    /// </summary>
    public class AdvancedSessionModel : INotifyPropertyChanged
    {
        #region Private Fields

        private int _id;
        private string _sessionNumber = string.Empty;
        private string _fileNumber = string.Empty;
        private string _client = string.Empty;
        private string _caseType = string.Empty;
        private string _court = string.Empty;
        private string _procedureType = string.Empty;
        private DateTime _procedureDate = DateTime.Now;
        private string _decision = string.Empty;
        private DateTime _sessionDate = DateTime.Now;
        private TimeSpan _sessionTime = new TimeSpan(9, 0, 0);
        private string _sessionType = string.Empty;
        private string _assignedLawyer = string.Empty;
        private string _judge = string.Empty;
        private string _clerk = string.Empty;
        private string _courtRoom = string.Empty;
        private string _status = "مجدولة";
        private string _notes = string.Empty;
        private string _outcome = string.Empty;
        private DateTime? _nextSessionDate;
        private TimeSpan? _nextSessionTime;
        private string _documents = string.Empty;
        private decimal _expenses = 0;
        private string _priority = "عادي";
        private bool _reminderSent = false;
        private DateTime _createdDate = DateTime.Now;
        private DateTime? _lastUpdated;
        private string _createdBy = string.Empty;

        #endregion

        #region Properties

        public int Id
        {
            get => _id;
            set { _id = value; OnPropertyChanged(); }
        }

        public string SessionNumber
        {
            get => _sessionNumber;
            set { _sessionNumber = value; OnPropertyChanged(); }
        }

        public string FileNumber
        {
            get => _fileNumber;
            set { _fileNumber = value; OnPropertyChanged(); }
        }

        public string Client
        {
            get => _client;
            set { _client = value; OnPropertyChanged(); }
        }

        public string CaseType
        {
            get => _caseType;
            set { _caseType = value; OnPropertyChanged(); }
        }

        public string Court
        {
            get => _court;
            set { _court = value; OnPropertyChanged(); }
        }

        public string ProcedureType
        {
            get => _procedureType;
            set { _procedureType = value; OnPropertyChanged(); }
        }

        public DateTime ProcedureDate
        {
            get => _procedureDate;
            set { _procedureDate = value; OnPropertyChanged(); }
        }

        public string Decision
        {
            get => _decision;
            set { _decision = value; OnPropertyChanged(); }
        }

        public DateTime SessionDate
        {
            get => _sessionDate;
            set { _sessionDate = value; OnPropertyChanged(); OnPropertyChanged(nameof(SessionDateTime)); OnPropertyChanged(nameof(DaysUntilSession)); }
        }

        public TimeSpan SessionTime
        {
            get => _sessionTime;
            set { _sessionTime = value; OnPropertyChanged(); OnPropertyChanged(nameof(SessionDateTime)); }
        }

        public string SessionType
        {
            get => _sessionType;
            set { _sessionType = value; OnPropertyChanged(); }
        }

        public string AssignedLawyer
        {
            get => _assignedLawyer;
            set { _assignedLawyer = value; OnPropertyChanged(); }
        }

        public string Judge
        {
            get => _judge;
            set { _judge = value; OnPropertyChanged(); }
        }

        public string Clerk
        {
            get => _clerk;
            set { _clerk = value; OnPropertyChanged(); }
        }

        public string CourtRoom
        {
            get => _courtRoom;
            set { _courtRoom = value; OnPropertyChanged(); }
        }

        public string Status
        {
            get => _status;
            set { _status = value; OnPropertyChanged(); OnPropertyChanged(nameof(StatusColor)); }
        }

        public string Notes
        {
            get => _notes;
            set { _notes = value; OnPropertyChanged(); }
        }

        public string Outcome
        {
            get => _outcome;
            set { _outcome = value; OnPropertyChanged(); }
        }

        public DateTime? NextSessionDate
        {
            get => _nextSessionDate;
            set { _nextSessionDate = value; OnPropertyChanged(); OnPropertyChanged(nameof(NextSessionText)); }
        }

        public TimeSpan? NextSessionTime
        {
            get => _nextSessionTime;
            set { _nextSessionTime = value; OnPropertyChanged(); OnPropertyChanged(nameof(NextSessionText)); }
        }

        public string Documents
        {
            get => _documents;
            set { _documents = value; OnPropertyChanged(); }
        }

        public decimal Expenses
        {
            get => _expenses;
            set { _expenses = value; OnPropertyChanged(); }
        }

        public string Priority
        {
            get => _priority;
            set { _priority = value; OnPropertyChanged(); OnPropertyChanged(nameof(PriorityColor)); }
        }

        public bool ReminderSent
        {
            get => _reminderSent;
            set { _reminderSent = value; OnPropertyChanged(); }
        }

        public DateTime CreatedDate
        {
            get => _createdDate;
            set { _createdDate = value; OnPropertyChanged(); }
        }

        public DateTime? LastUpdated
        {
            get => _lastUpdated;
            set { _lastUpdated = value; OnPropertyChanged(); }
        }

        public string CreatedBy
        {
            get => _createdBy;
            set { _createdBy = value; OnPropertyChanged(); }
        }

        #endregion

        #region Computed Properties

        public DateTime SessionDateTime => SessionDate.Date + SessionTime;

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    "مجدولة" => "#6366F1",
                    "جارية" => "#F59E0B",
                    "مكتملة" => "#10B981",
                    "مؤجلة" => "#8B5CF6",
                    "ملغية" => "#EF4444",
                    _ => "#6B7280"
                };
            }
        }

        public string PriorityColor
        {
            get
            {
                return Priority switch
                {
                    "عاجل" => "#EF4444",
                    "مهم" => "#F59E0B",
                    "عادي" => "#10B981",
                    _ => "#6B7280"
                };
            }
        }

        public int DaysUntilSession
        {
            get
            {
                var days = (SessionDate.Date - DateTime.Now.Date).Days;
                return days;
            }
        }

        public string DaysUntilSessionText
        {
            get
            {
                var days = DaysUntilSession;
                if (days < 0)
                    return "منتهية";
                else if (days == 0)
                    return "اليوم";
                else if (days == 1)
                    return "غداً";
                else
                    return $"خلال {days} يوم";
            }
        }

        public string NextSessionText
        {
            get
            {
                if (NextSessionDate.HasValue)
                {
                    var timeText = NextSessionTime.HasValue ? $" في {NextSessionTime.Value:hh\\:mm}" : "";
                    return $"{NextSessionDate.Value:dd/MM/yyyy}{timeText}";
                }
                return "غير محدد";
            }
        }

        public string SessionTimeText => SessionTime.ToString(@"hh\:mm");

        public string ExpensesText => Expenses > 0 ? $"{Expenses:N0} درهم" : "لا توجد";

        public string StatusIcon
        {
            get
            {
                return Status switch
                {
                    "مجدولة" => "📅",
                    "جارية" => "⏳",
                    "مكتملة" => "✅",
                    "مؤجلة" => "⏸️",
                    "ملغية" => "❌",
                    _ => "❓"
                };
            }
        }

        public string PriorityIcon
        {
            get
            {
                return Priority switch
                {
                    "عاجل" => "🔴",
                    "مهم" => "🟡",
                    "عادي" => "🟢",
                    _ => "⚪"
                };
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    /// <summary>
    /// إحصائيات الجلسات
    /// </summary>
    public class SessionStatistics
    {
        public int TotalSessions { get; set; }
        public int ScheduledSessions { get; set; }
        public int CompletedSessions { get; set; }
        public int PostponedSessions { get; set; }
        public int CancelledSessions { get; set; }
        public int TodaySessions { get; set; }
        public int ThisWeekSessions { get; set; }
        public int ThisMonthSessions { get; set; }
        public int ThisYearSessions { get; set; }
        public decimal TotalExpenses { get; set; }
        public double AverageSessionsPerMonth { get; set; }
        public string MostActiveCourt { get; set; } = string.Empty;
        public string MostActiveSessionType { get; set; } = string.Empty;
    }
}
