using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AvocatPro.Models;

namespace AvocatPro.Services
{
    public class SimpleAIService
    {
        public async Task<AIResponse> ProcessLegalQueryAsync(string query, User currentUser)
        {
            await Task.Delay(1000); // محاكاة معالجة

            var responses = new[]
            {
                "شكراً لاستفسارك القانوني. هذه نسخة تجريبية من المساعد الذكي.",
                "يمكنني مساعدتك في الاستشارات القانونية العامة. يرجى تحديد نوع القضية.",
                "بناءً على خبرتي القانونية، أنصح بمراجعة الوثائق ذات الصلة.",
                "هذا سؤال مهم يتطلب دراسة دقيقة للحالة القانونية.",
                "يمكنني تحليل هذه المسألة القانونية وتقديم التوجيه المناسب."
            };

            var random = new Random();
            var selectedResponse = responses[random.Next(responses.Length)];

            return new AIResponse
            {
                Query = query,
                Answer = selectedResponse,
                Type = "استشارة قانونية",
                Confidence = 0.75 + (random.NextDouble() * 0.2), // بين 75% و 95%
                Timestamp = DateTime.Now,
                UserId = currentUser.Id,
                Suggestions = new List<string>
                {
                    "هل تريد المزيد من التفاصيل؟",
                    "يمكنني تحليل وثائق إضافية",
                    "هل تحتاج استشارة متخصصة؟"
                }
            };
        }
    }

    public class AIResponse
    {
        public string Query { get; set; } = string.Empty;
        public string Answer { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public double Confidence { get; set; }
        public DateTime Timestamp { get; set; }
        public int UserId { get; set; }
        public List<string> Suggestions { get; set; } = new List<string>();
    }

    public class LiveKPI
    {
        public string Name { get; set; } = string.Empty;
        public double Value { get; set; }
        public string Unit { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
        public double Trend { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    public class InteractiveDashboard
    {
        public List<LiveKPI> LiveKPIs { get; set; } = new List<LiveKPI>();
        public List<InteractiveChart> InteractiveCharts { get; set; } = new List<InteractiveChart>();
    }

    public class InteractiveChart
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public List<ChartDataPoint> Data { get; set; } = new List<ChartDataPoint>();
    }

    public class ChartDataPoint
    {
        public string Label { get; set; } = string.Empty;
        public double Value { get; set; }
        public DateTime? Date { get; set; }
        public string Color { get; set; } = string.Empty;
    }
}
