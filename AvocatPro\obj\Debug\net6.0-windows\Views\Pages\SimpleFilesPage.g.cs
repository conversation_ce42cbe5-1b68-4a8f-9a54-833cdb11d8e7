﻿#pragma checksum "..\..\..\..\..\Views\Pages\SimpleFilesPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "057A40A939FBB77490C2B2F81D22B6D18FC61DEA"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Pages {
    
    
    /// <summary>
    /// SimpleFilesPage
    /// </summary>
    public partial class SimpleFilesPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 78 "..\..\..\..\..\Views\Pages\SimpleFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalFilesCount;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\..\..\Views\Pages\SimpleFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActiveFilesCount;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\..\Views\Pages\SimpleFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InSessionsCount;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\..\Views\Pages\SimpleFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ArchivedFilesCount;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\..\..\Views\Pages\SimpleFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\..\..\Views\Pages\SimpleFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\..\..\Views\Pages\SimpleFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CaseTypeFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 212 "..\..\..\..\..\Views\Pages\SimpleFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\..\..\Views\Pages\SimpleFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintButton;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\..\..\..\Views\Pages\SimpleFilesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid FilesDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/pages/simplefilespage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\SimpleFilesPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 34 "..\..\..\..\..\Views\Pages\SimpleFilesPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddFile_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.TotalFilesCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.ActiveFilesCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.InSessionsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.ArchivedFilesCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 182 "..\..\..\..\..\Views\Pages\SimpleFilesPage.xaml"
            this.SearchTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.SearchTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 183 "..\..\..\..\..\Views\Pages\SimpleFilesPage.xaml"
            this.SearchTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.SearchTextBox_LostFocus);
            
            #line default
            #line hidden
            
            #line 183 "..\..\..\..\..\Views\Pages\SimpleFilesPage.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.StatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 190 "..\..\..\..\..\Views\Pages\SimpleFilesPage.xaml"
            this.StatusFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.StatusFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.CaseTypeFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 201 "..\..\..\..\..\Views\Pages\SimpleFilesPage.xaml"
            this.CaseTypeFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CaseTypeFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 214 "..\..\..\..\..\Views\Pages\SimpleFilesPage.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.Export_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.PrintButton = ((System.Windows.Controls.Button)(target));
            
            #line 219 "..\..\..\..\..\Views\Pages\SimpleFilesPage.xaml"
            this.PrintButton.Click += new System.Windows.RoutedEventHandler(this.Print_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.FilesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

