using System.ComponentModel.DataAnnotations;
using AvocatPro.Models.UserManagement;

namespace AvocatPro.Models;

/// <summary>
/// نموذج المستخدم
/// </summary>
public class User : BaseEntity
{
    [Required]
    [StringLength(50)]
    public string Username { get; set; } = string.Empty;
    
    [Required]
    [StringLength(100)]
    public string FullName { get; set; } = string.Empty;
    
    [Required]
    [EmailAddress]
    [StringLength(100)]
    public string Email { get; set; } = string.Empty;
    
    [Required]
    public string PasswordHash { get; set; } = string.Empty;
    
    [StringLength(20)]
    public string? Phone { get; set; }
    
    public UserRole Role { get; set; } = UserRole.Assistant;
    
    public bool IsActive { get; set; } = true;
    
    public DateTime? LastLoginAt { get; set; }
    
    public int FailedLoginAttempts { get; set; } = 0;
    
    public DateTime? LockedUntil { get; set; }
    
    /// <summary>
    /// سؤال الأمان
    /// </summary>
    [StringLength(200)]
    public string? SecurityQuestion { get; set; }
    
    /// <summary>
    /// إجابة سؤال الأمان (مشفرة)
    /// </summary>
    public string? SecurityAnswerHash { get; set; }
    
    /// <summary>
    /// رمز إعادة تعيين كلمة المرور
    /// </summary>
    public string? ResetPasswordToken { get; set; }
    
    /// <summary>
    /// تاريخ انتهاء رمز إعادة التعيين
    /// </summary>
    public DateTime? ResetPasswordExpiry { get; set; }
    
    /// <summary>
    /// الصورة الشخصية
    /// </summary>
    public string? ProfilePicture { get; set; }
    
    /// <summary>
    /// إعدادات المستخدم (JSON)
    /// </summary>
    public string? UserSettings { get; set; }
    
    // Navigation Properties
    public virtual ICollection<Case> AssignedCases { get; set; } = new List<Case>();
    public virtual ICollection<Session> AssignedSessions { get; set; } = new List<Session>();
    public virtual ICollection<Appointment> CreatedAppointments { get; set; } = new List<Appointment>();

    /// <summary>
    /// الملف الشخصي للمستخدم
    /// </summary>
    public virtual UserProfile? UserProfile { get; set; }

    /// <summary>
    /// إعدادات كلمة المرور
    /// </summary>
    public virtual PasswordSettings? PasswordSettings { get; set; }

    // تم نقل صلاحيات المستخدم إلى نظام إدارة المستخدمين المتقدم
}

/// <summary>
/// أدوار المستخدمين
/// </summary>
public enum UserRole
{
    /// <summary>
    /// مدير النظام
    /// </summary>
    Admin = 1,
    
    /// <summary>
    /// محامي رئيسي
    /// </summary>
    SeniorLawyer = 2,
    
    /// <summary>
    /// محامي
    /// </summary>
    Lawyer = 3,
    
    /// <summary>
    /// مساعد قانوني
    /// </summary>
    LegalAssistant = 4,
    
    /// <summary>
    /// مساعد إداري
    /// </summary>
    Assistant = 5,
    
    /// <summary>
    /// محاسب
    /// </summary>
    Accountant = 6,

    /// <summary>
    /// مستخدم عادي
    /// </summary>
    User = 7,

    /// <summary>
    /// سكرتير
    /// </summary>
    Secretary = 8
}
