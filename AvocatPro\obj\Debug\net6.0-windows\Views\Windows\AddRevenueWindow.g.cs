﻿#pragma checksum "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C0521C4F2E1CC50AA68FBA0B52DA6C1426E63138"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Windows {
    
    
    /// <summary>
    /// AddRevenueWindow
    /// </summary>
    public partial class AddRevenueWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 140 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ReferenceTextBox;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker RevenueDatePicker;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ClientComboBox;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CaseComboBox;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox InvoiceNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PaymentMethodComboBox;
        
        #line default
        #line hidden
        
        
        #line 212 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PaymentStatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 221 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker PaymentDatePicker;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DueDatePicker;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PaymentTermsComboBox;
        
        #line default
        #line hidden
        
        
        #line 238 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CalculateDueDateButton;
        
        #line default
        #line hidden
        
        
        #line 259 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox AttachmentsListBox;
        
        #line default
        #line hidden
        
        
        #line 276 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddAttachmentButton;
        
        #line default
        #line hidden
        
        
        #line 284 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button GenerateInvoiceButton;
        
        #line default
        #line hidden
        
        
        #line 292 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SendInvoiceButton;
        
        #line default
        #line hidden
        
        
        #line 304 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 313 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 320 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveAndNewButton;
        
        #line default
        #line hidden
        
        
        #line 328 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveAndInvoiceButton;
        
        #line default
        #line hidden
        
        
        #line 336 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/windows/addrevenuewindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ReferenceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.AmountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.RevenueDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 5:
            this.TypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            this.ClientComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.CaseComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.InvoiceNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.PaymentMethodComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.PaymentStatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.PaymentDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 12:
            this.DueDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 13:
            this.PaymentTermsComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 14:
            this.CalculateDueDateButton = ((System.Windows.Controls.Button)(target));
            
            #line 239 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
            this.CalculateDueDateButton.Click += new System.Windows.RoutedEventHandler(this.CalculateDueDateButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.AttachmentsListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 17:
            this.AddAttachmentButton = ((System.Windows.Controls.Button)(target));
            
            #line 277 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
            this.AddAttachmentButton.Click += new System.Windows.RoutedEventHandler(this.AddAttachmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.GenerateInvoiceButton = ((System.Windows.Controls.Button)(target));
            
            #line 285 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
            this.GenerateInvoiceButton.Click += new System.Windows.RoutedEventHandler(this.GenerateInvoiceButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.SendInvoiceButton = ((System.Windows.Controls.Button)(target));
            
            #line 293 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
            this.SendInvoiceButton.Click += new System.Windows.RoutedEventHandler(this.SendInvoiceButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 313 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.SaveAndNewButton = ((System.Windows.Controls.Button)(target));
            
            #line 321 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
            this.SaveAndNewButton.Click += new System.Windows.RoutedEventHandler(this.SaveAndNewButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.SaveAndInvoiceButton = ((System.Windows.Controls.Button)(target));
            
            #line 329 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
            this.SaveAndInvoiceButton.Click += new System.Windows.RoutedEventHandler(this.SaveAndInvoiceButton_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 336 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 16:
            
            #line 268 "..\..\..\..\..\Views\Windows\AddRevenueWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RemoveAttachmentButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

