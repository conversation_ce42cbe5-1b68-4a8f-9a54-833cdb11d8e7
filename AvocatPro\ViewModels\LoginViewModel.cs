using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using AvocatPro.Services;
using AvocatPro.Models;
using AvocatPro.Helpers;

namespace AvocatPro.ViewModels;

/// <summary>
/// ViewModel لنافذة تسجيل الدخول
/// </summary>
public class LoginViewModel : INotifyPropertyChanged
{
    private readonly IAuthenticationService _authService;
    private readonly IUserService _userService;
    
    private string _username = string.Empty;
    private string _password = string.Empty;
    private bool _rememberMe = false;
    private bool _isLoading = false;
    private string _errorMessage = string.Empty;
    private bool _hasError = false;

    public LoginViewModel(IAuthenticationService authService, IUserService userService)
    {
        _authService = authService;
        _userService = userService;
        
        // إنشاء الأوامر
        LoginCommand = new RelayCommand(async () => await LoginAsync(), () => CanLogin);
        ForgotPasswordCommand = new RelayCommand(async () => await ForgotPasswordAsync());
        
        // تحميل بيانات تسجيل الدخول المحفوظة
        LoadSavedCredentials();
    }

    #region Properties

    public string Username
    {
        get => _username;
        set
        {
            if (SetProperty(ref _username, value))
            {
                OnPropertyChanged(nameof(CanLogin));
                ClearError();
            }
        }
    }

    public string Password
    {
        get => _password;
        set
        {
            if (SetProperty(ref _password, value))
            {
                OnPropertyChanged(nameof(CanLogin));
                ClearError();
            }
        }
    }

    public bool RememberMe
    {
        get => _rememberMe;
        set => SetProperty(ref _rememberMe, value);
    }

    public bool IsLoading
    {
        get => _isLoading;
        set
        {
            if (SetProperty(ref _isLoading, value))
            {
                OnPropertyChanged(nameof(CanLogin));
            }
        }
    }

    public string ErrorMessage
    {
        get => _errorMessage;
        set => SetProperty(ref _errorMessage, value);
    }

    public bool HasError
    {
        get => _hasError;
        set => SetProperty(ref _hasError, value);
    }

    public bool CanLogin => !IsLoading && !string.IsNullOrWhiteSpace(Username) && !string.IsNullOrWhiteSpace(Password);

    public User? CurrentUser { get; private set; }

    #endregion

    #region Commands

    public ICommand LoginCommand { get; }
    public ICommand ForgotPasswordCommand { get; }

    #endregion

    #region Methods

    private async Task LoginAsync()
    {
        try
        {
            IsLoading = true;
            ClearError();

            // محاولة تسجيل الدخول
            var user = await _authService.LoginAsync(Username, Password);

            if (user != null)
            {
                CurrentUser = user;
                
                // حفظ بيانات تسجيل الدخول إذا كان مطلوباً
                if (RememberMe)
                {
                    SaveCredentials();
                }
                else
                {
                    ClearSavedCredentials();
                }

                // إثارة حدث نجاح تسجيل الدخول
                LoginSuccessful?.Invoke(user);
            }
            else
            {
                ShowError("اسم المستخدم أو كلمة المرور غير صحيحة");
            }
        }
        catch (Exception ex)
        {
            ShowError($"خطأ في تسجيل الدخول: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task ForgotPasswordAsync()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(Username))
            {
                ShowError("يرجى إدخال اسم المستخدم أولاً");
                return;
            }

            IsLoading = true;
            ClearError();

            // البحث عن المستخدم
            var user = await _userService.GetUserByUsernameAsync(Username);
            if (user == null)
            {
                ShowError("اسم المستخدم غير موجود");
                return;
            }

            // طلب إعادة تعيين كلمة المرور
            var resetToken = await _authService.RequestPasswordResetAsync(user.Email);
            if (resetToken != null)
            {
                // إثارة حدث طلب إعادة تعيين كلمة المرور
                PasswordResetRequested?.Invoke(user.Email, resetToken);
            }
            else
            {
                ShowError("فشل في إرسال رمز إعادة تعيين كلمة المرور");
            }
        }
        catch (Exception ex)
        {
            ShowError($"خطأ: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    private void ShowError(string message)
    {
        ErrorMessage = message;
        HasError = true;
    }

    private void ClearError()
    {
        ErrorMessage = string.Empty;
        HasError = false;
    }

    private void LoadSavedCredentials()
    {
        try
        {
            // تحميل بيانات تسجيل الدخول المحفوظة من الإعدادات
            var settings = AvocatPro.Properties.Settings.Default;
            if (settings.RememberLogin)
            {
                Username = settings.SavedUsername ?? string.Empty;
                RememberMe = true;
            }
        }
        catch
        {
            // تجاهل الأخطاء في تحميل الإعدادات
        }
    }

    private void SaveCredentials()
    {
        try
        {
            var settings = AvocatPro.Properties.Settings.Default;
            settings.RememberLogin = true;
            settings.SavedUsername = Username;
            settings.Save();
        }
        catch
        {
            // تجاهل الأخطاء في حفظ الإعدادات
        }
    }

    private void ClearSavedCredentials()
    {
        try
        {
            var settings = AvocatPro.Properties.Settings.Default;
            settings.RememberLogin = false;
            settings.SavedUsername = string.Empty;
            settings.Save();
        }
        catch
        {
            // تجاهل الأخطاء في مسح الإعدادات
        }
    }

    #endregion

    #region Events

    public event Action<User>? LoginSuccessful;
    public event Action<string, string>? PasswordResetRequested;

    #endregion

    #region INotifyPropertyChanged

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value))
            return false;

        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }

    #endregion
}


