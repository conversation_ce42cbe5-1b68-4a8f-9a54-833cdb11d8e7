using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace AvocatPro.Models
{
    /// <summary>
    /// نموذج إعدادات التطبيق الشاملة
    /// </summary>
    public class AppSettings : INotifyPropertyChanged
    {
        #region Office Settings

        private string _officeName = "مكتب المحاماة";
        private string _officeAddress = "";
        private string _officePhone = "";
        private string _officeEmail = "";
        private string _officeLogo = "";
        private string _officeWebsite = "";
        private string _officeLicense = "";

        public string OfficeName
        {
            get => _officeName;
            set { _officeName = value; OnPropertyChanged(); }
        }

        public string OfficeAddress
        {
            get => _officeAddress;
            set { _officeAddress = value; OnPropertyChanged(); }
        }

        public string OfficePhone
        {
            get => _officePhone;
            set { _officePhone = value; OnPropertyChanged(); }
        }

        public string OfficeEmail
        {
            get => _officeEmail;
            set { _officeEmail = value; OnPropertyChanged(); }
        }

        public string OfficeLogo
        {
            get => _officeLogo;
            set { _officeLogo = value; OnPropertyChanged(); }
        }

        public string OfficeWebsite
        {
            get => _officeWebsite;
            set { _officeWebsite = value; OnPropertyChanged(); }
        }

        public string OfficeLicense
        {
            get => _officeLicense;
            set { _officeLicense = value; OnPropertyChanged(); }
        }

        #endregion

        #region UI Settings

        private string _language = "العربية";
        private string _theme = "فاتح";
        private string _primaryColor = "#6366F1";
        private string _secondaryColor = "#10B981";
        private string _fontFamily = "Segoe UI";
        private int _fontSize = 14;
        private bool _enableAnimations = true;
        private bool _enableSounds = true;
        private double _windowOpacity = 1.0;

        public string Language
        {
            get => _language;
            set { _language = value; OnPropertyChanged(); }
        }

        public string Theme
        {
            get => _theme;
            set { _theme = value; OnPropertyChanged(); }
        }

        public string PrimaryColor
        {
            get => _primaryColor;
            set { _primaryColor = value; OnPropertyChanged(); }
        }

        public string SecondaryColor
        {
            get => _secondaryColor;
            set { _secondaryColor = value; OnPropertyChanged(); }
        }

        public string FontFamily
        {
            get => _fontFamily;
            set { _fontFamily = value; OnPropertyChanged(); }
        }

        public int FontSize
        {
            get => _fontSize;
            set { _fontSize = value; OnPropertyChanged(); }
        }

        public bool EnableAnimations
        {
            get => _enableAnimations;
            set { _enableAnimations = value; OnPropertyChanged(); }
        }

        public bool EnableSounds
        {
            get => _enableSounds;
            set { _enableSounds = value; OnPropertyChanged(); }
        }

        public double WindowOpacity
        {
            get => _windowOpacity;
            set { _windowOpacity = value; OnPropertyChanged(); }
        }

        #endregion

        #region Menu Settings

        private Dictionary<string, string> _menuItems = new Dictionary<string, string>
        {
            { "Dashboard", "لوحة التحكم" },
            { "Clients", "العملاء" },
            { "Files", "الملفات" },
            { "Sessions", "الجلسات" },
            { "Appointments", "المواعيد" },
            { "Financial", "المالية" },
            { "Documents", "الوثائق" },
            { "Reports", "التقارير" },
            { "Users", "المستخدمين" },
            { "Settings", "الإعدادات" }
        };

        private Dictionary<string, string> _menuIcons = new Dictionary<string, string>
        {
            { "Dashboard", "🏠" },
            { "Clients", "👥" },
            { "Files", "📁" },
            { "Sessions", "⚖️" },
            { "Appointments", "📅" },
            { "Financial", "💰" },
            { "Documents", "📄" },
            { "Reports", "📊" },
            { "Users", "👤" },
            { "Settings", "⚙️" }
        };

        private List<string> _visibleMenuItems = new List<string>
        {
            "Dashboard", "Clients", "Files", "Sessions", "Appointments", 
            "Financial", "Documents", "Reports", "Users", "Settings"
        };

        public Dictionary<string, string> MenuItems
        {
            get => _menuItems;
            set { _menuItems = value; OnPropertyChanged(); }
        }

        public Dictionary<string, string> MenuIcons
        {
            get => _menuIcons;
            set { _menuIcons = value; OnPropertyChanged(); }
        }

        public List<string> VisibleMenuItems
        {
            get => _visibleMenuItems;
            set { _visibleMenuItems = value; OnPropertyChanged(); }
        }

        #endregion

        #region Email Settings

        private string _smtpServer = "";
        private int _smtpPort = 587;
        private string _smtpUsername = "";
        private string _smtpPassword = "";
        private bool _enableSsl = true;
        private string _fromEmail = "";
        private string _fromName = "";

        public string SmtpServer
        {
            get => _smtpServer;
            set { _smtpServer = value; OnPropertyChanged(); }
        }

        public int SmtpPort
        {
            get => _smtpPort;
            set { _smtpPort = value; OnPropertyChanged(); }
        }

        public string SmtpUsername
        {
            get => _smtpUsername;
            set { _smtpUsername = value; OnPropertyChanged(); }
        }

        public string SmtpPassword
        {
            get => _smtpPassword;
            set { _smtpPassword = value; OnPropertyChanged(); }
        }

        public bool EnableSsl
        {
            get => _enableSsl;
            set { _enableSsl = value; OnPropertyChanged(); }
        }

        public string FromEmail
        {
            get => _fromEmail;
            set { _fromEmail = value; OnPropertyChanged(); }
        }

        public string FromName
        {
            get => _fromName;
            set { _fromName = value; OnPropertyChanged(); }
        }

        #endregion

        #region Database Settings

        private string _connectionString = "";
        private string _databaseType = "SQLite";
        private bool _autoBackup = true;
        private int _backupInterval = 24; // hours
        private string _backupPath = "";
        private int _maxBackupFiles = 10;

        public string ConnectionString
        {
            get => _connectionString;
            set { _connectionString = value; OnPropertyChanged(); }
        }

        public string DatabaseType
        {
            get => _databaseType;
            set { _databaseType = value; OnPropertyChanged(); }
        }

        public bool AutoBackup
        {
            get => _autoBackup;
            set { _autoBackup = value; OnPropertyChanged(); }
        }

        public int BackupInterval
        {
            get => _backupInterval;
            set { _backupInterval = value; OnPropertyChanged(); }
        }

        public string BackupPath
        {
            get => _backupPath;
            set { _backupPath = value; OnPropertyChanged(); }
        }

        public int MaxBackupFiles
        {
            get => _maxBackupFiles;
            set { _maxBackupFiles = value; OnPropertyChanged(); }
        }

        #endregion

        #region Security Settings

        private bool _enableTwoFactor = false;
        private int _sessionTimeout = 30; // minutes
        private bool _enableAuditLog = true;
        private bool _enableEncryption = true;
        private int _passwordMinLength = 8;
        private bool _requireSpecialChars = true;

        public bool EnableTwoFactor
        {
            get => _enableTwoFactor;
            set { _enableTwoFactor = value; OnPropertyChanged(); }
        }

        public int SessionTimeout
        {
            get => _sessionTimeout;
            set { _sessionTimeout = value; OnPropertyChanged(); }
        }

        public bool EnableAuditLog
        {
            get => _enableAuditLog;
            set { _enableAuditLog = value; OnPropertyChanged(); }
        }

        public bool EnableEncryption
        {
            get => _enableEncryption;
            set { _enableEncryption = value; OnPropertyChanged(); }
        }

        public int PasswordMinLength
        {
            get => _passwordMinLength;
            set { _passwordMinLength = value; OnPropertyChanged(); }
        }

        public bool RequireSpecialChars
        {
            get => _requireSpecialChars;
            set { _requireSpecialChars = value; OnPropertyChanged(); }
        }

        #endregion

        #region Notification Settings

        private bool _enableDesktopNotifications = true;
        private bool _enableEmailNotifications = true;
        private bool _enableSmsNotifications = false;
        private int _reminderMinutes = 15;
        private bool _enableSoundAlerts = true;
        private string _notificationSound = "default";

        public bool EnableDesktopNotifications
        {
            get => _enableDesktopNotifications;
            set { _enableDesktopNotifications = value; OnPropertyChanged(); }
        }

        public bool EnableEmailNotifications
        {
            get => _enableEmailNotifications;
            set { _enableEmailNotifications = value; OnPropertyChanged(); }
        }

        public bool EnableSmsNotifications
        {
            get => _enableSmsNotifications;
            set { _enableSmsNotifications = value; OnPropertyChanged(); }
        }

        public int ReminderMinutes
        {
            get => _reminderMinutes;
            set { _reminderMinutes = value; OnPropertyChanged(); }
        }

        public bool EnableSoundAlerts
        {
            get => _enableSoundAlerts;
            set { _enableSoundAlerts = value; OnPropertyChanged(); }
        }

        public string NotificationSound
        {
            get => _notificationSound;
            set { _notificationSound = value; OnPropertyChanged(); }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
