{"format": 1, "restore": {"D:\\smartlawyers\\AvocatPro\\AvocatPro.csproj": {}}, "projects": {"D:\\smartlawyers\\AvocatPro\\AvocatPro.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\smartlawyers\\AvocatPro\\AvocatPro.csproj", "projectName": "AvocatPro", "projectPath": "D:\\smartlawyers\\AvocatPro\\AvocatPro.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\smartlawyers\\AvocatPro\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "EPPlus": {"target": "Package", "version": "[6.2.10, )"}, "FluentValidation": {"target": "Package", "version": "[11.8.0, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.8.0, )"}, "FontAwesome.WPF": {"target": "Package", "version": "[4.7.0.9, )"}, "MaterialDesignColors": {"target": "Package", "version": "[2.1.4, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[6.0.25, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[6.0.25, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[6.0.25, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[6.0.1, )"}, "ModernWpfUI": {"target": "Package", "version": "[0.9.6, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "QuestPDF": {"target": "Package", "version": "[2023.12.6, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "iTextSharp": {"target": "Package", "version": "[********, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}