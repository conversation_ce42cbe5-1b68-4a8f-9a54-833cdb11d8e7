﻿#pragma checksum "..\..\..\..\..\Views\Pages\InteractiveDashboardPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "7ECAA68B445E0DA4360CEEC377DA73809CA009BF"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Pages {
    
    
    /// <summary>
    /// InteractiveDashboardPage
    /// </summary>
    public partial class InteractiveDashboardPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 47 "..\..\..\..\..\Views\Pages\InteractiveDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TimeRangeComboBox;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\..\Views\Pages\InteractiveDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshBtn;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\..\Views\Pages\InteractiveDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportBtn;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\..\Views\Pages\InteractiveDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.UniformGrid LiveKPIGrid;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\..\Views\Pages\InteractiveDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas MainChart;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\..\..\Views\Pages\InteractiveDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas PieChart;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\..\Views\Pages\InteractiveDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PieChartLegend;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\..\Views\Pages\InteractiveDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel CashFlowPanel;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\..\Views\Pages\InteractiveDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ProfitabilityPanel;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\..\..\Views\Pages\InteractiveDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ForecastPanel;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\..\Views\Pages\InteractiveDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ClientStatsPanel;
        
        #line default
        #line hidden
        
        
        #line 231 "..\..\..\..\..\Views\Pages\InteractiveDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel SuccessRatesPanel;
        
        #line default
        #line hidden
        
        
        #line 241 "..\..\..\..\..\Views\Pages\InteractiveDashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border LoadingOverlay;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/pages/interactivedashboardpage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\InteractiveDashboardPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TimeRangeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 48 "..\..\..\..\..\Views\Pages\InteractiveDashboardPage.xaml"
            this.TimeRangeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.TimeRange_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.RefreshBtn = ((System.Windows.Controls.Button)(target));
            
            #line 56 "..\..\..\..\..\Views\Pages\InteractiveDashboardPage.xaml"
            this.RefreshBtn.Click += new System.Windows.RoutedEventHandler(this.Refresh_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ExportBtn = ((System.Windows.Controls.Button)(target));
            
            #line 63 "..\..\..\..\..\Views\Pages\InteractiveDashboardPage.xaml"
            this.ExportBtn.Click += new System.Windows.RoutedEventHandler(this.Export_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.LiveKPIGrid = ((System.Windows.Controls.Primitives.UniformGrid)(target));
            return;
            case 5:
            this.MainChart = ((System.Windows.Controls.Canvas)(target));
            return;
            case 6:
            this.PieChart = ((System.Windows.Controls.Canvas)(target));
            return;
            case 7:
            this.PieChartLegend = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 8:
            this.CashFlowPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 9:
            this.ProfitabilityPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 10:
            this.ForecastPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 11:
            this.ClientStatsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 12:
            this.SuccessRatesPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 13:
            this.LoadingOverlay = ((System.Windows.Controls.Border)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

