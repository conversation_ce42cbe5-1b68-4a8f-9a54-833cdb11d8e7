﻿#pragma checksum "..\..\..\..\..\Views\Controls\ModernSidebarControl.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "9442EDA6EDA1EDA81903003A7A59DC85EC17ABBF"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Controls {
    
    
    /// <summary>
    /// ModernSidebarControl
    /// </summary>
    public partial class ModernSidebarControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/controls/modernsidebarcontrol.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Controls\ModernSidebarControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 84 "..\..\..\..\..\Views\Controls\ModernSidebarControl.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Dashboard_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 91 "..\..\..\..\..\Views\Controls\ModernSidebarControl.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Clients_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 98 "..\..\..\..\..\Views\Controls\ModernSidebarControl.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Files_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 105 "..\..\..\..\..\Views\Controls\ModernSidebarControl.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ComprehensiveFiles_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 112 "..\..\..\..\..\Views\Controls\ModernSidebarControl.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Sessions_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 119 "..\..\..\..\..\Views\Controls\ModernSidebarControl.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Appointments_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 132 "..\..\..\..\..\Views\Controls\ModernSidebarControl.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Finance_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 139 "..\..\..\..\..\Views\Controls\ModernSidebarControl.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Invoices_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 146 "..\..\..\..\..\Views\Controls\ModernSidebarControl.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Reports_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 159 "..\..\..\..\..\Views\Controls\ModernSidebarControl.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Users_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 166 "..\..\..\..\..\Views\Controls\ModernSidebarControl.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Backup_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 173 "..\..\..\..\..\Views\Controls\ModernSidebarControl.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Settings_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 186 "..\..\..\..\..\Views\Controls\ModernSidebarControl.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Help_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 193 "..\..\..\..\..\Views\Controls\ModernSidebarControl.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.About_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 216 "..\..\..\..\..\Views\Controls\ModernSidebarControl.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Logout_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

