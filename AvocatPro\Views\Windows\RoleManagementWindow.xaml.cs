using System;
using System.Windows;
using AvocatPro.Models;

namespace AvocatPro.Views.Windows
{
    public partial class RoleManagementWindow : Window
    {
        private readonly User _currentUser;

        public RoleManagementWindow(User currentUser)
        {
            // محاكاة نافذة إدارة الأدوار
            _currentUser = currentUser;
            
            // إنشاء نافذة بسيطة
            Title = "إدارة الأدوار والصلاحيات";
            Width = 600;
            Height = 400;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;
            
            var button = new System.Windows.Controls.Button
            {
                Content = "إغلاق",
                Width = 100,
                Height = 30,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };
            button.Click += (s, e) => Close();
            
            Content = button;
        }
    }
}
