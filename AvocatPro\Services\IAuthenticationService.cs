using AvocatPro.Models;

namespace AvocatPro.Services;

/// <summary>
/// واجهة خدمة المصادقة والأمان
/// </summary>
public interface IAuthenticationService
{
    /// <summary>
    /// تسجيل دخول المستخدم
    /// </summary>
    /// <param name="username">اسم المستخدم</param>
    /// <param name="password">كلمة المرور</param>
    /// <returns>المستخدم في حالة نجاح تسجيل الدخول، null في حالة الفشل</returns>
    Task<User?> LoginAsync(string username, string password);

    /// <summary>
    /// تسجيل خروج المستخدم
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    Task LogoutAsync(int userId);

    /// <summary>
    /// تغيير كلمة المرور
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <param name="currentPassword">كلمة المرور الحالية</param>
    /// <param name="newPassword">كلمة المرور الجديدة</param>
    /// <returns>true في حالة النجاح</returns>
    Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword);

    /// <summary>
    /// إعادة تعيين كلمة المرور
    /// </summary>
    /// <param name="email">البريد الإلكتروني</param>
    /// <returns>رمز إعادة التعيين</returns>
    Task<string?> RequestPasswordResetAsync(string email);

    /// <summary>
    /// تأكيد إعادة تعيين كلمة المرور
    /// </summary>
    /// <param name="email">البريد الإلكتروني</param>
    /// <param name="resetToken">رمز إعادة التعيين</param>
    /// <param name="newPassword">كلمة المرور الجديدة</param>
    /// <returns>true في حالة النجاح</returns>
    Task<bool> ResetPasswordAsync(string email, string resetToken, string newPassword);

    /// <summary>
    /// التحقق من سؤال الأمان
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <param name="securityAnswer">إجابة سؤال الأمان</param>
    /// <returns>true في حالة الصحة</returns>
    Task<bool> VerifySecurityAnswerAsync(int userId, string securityAnswer);

    /// <summary>
    /// تعيين سؤال الأمان
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <param name="securityQuestion">سؤال الأمان</param>
    /// <param name="securityAnswer">إجابة سؤال الأمان</param>
    Task SetSecurityQuestionAsync(int userId, string securityQuestion, string securityAnswer);

    /// <summary>
    /// التحقق من صلاحية المستخدم
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <param name="requiredRole">الدور المطلوب</param>
    /// <returns>true إذا كان المستخدم يملك الصلاحية</returns>
    Task<bool> HasPermissionAsync(int userId, UserRole requiredRole);

    /// <summary>
    /// قفل حساب المستخدم
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <param name="lockDurationMinutes">مدة القفل بالدقائق</param>
    Task LockUserAccountAsync(int userId, int lockDurationMinutes);

    /// <summary>
    /// إلغاء قفل حساب المستخدم
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    Task UnlockUserAccountAsync(int userId);

    /// <summary>
    /// التحقق من حالة قفل الحساب
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <returns>true إذا كان الحساب مقفلاً</returns>
    Task<bool> IsAccountLockedAsync(int userId);

    /// <summary>
    /// تسجيل محاولة دخول فاشلة
    /// </summary>
    /// <param name="username">اسم المستخدم</param>
    Task RecordFailedLoginAttemptAsync(string username);

    /// <summary>
    /// إعادة تعيين محاولات الدخول الفاشلة
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    Task ResetFailedLoginAttemptsAsync(int userId);

    /// <summary>
    /// التحقق من قوة كلمة المرور
    /// </summary>
    /// <param name="password">كلمة المرور</param>
    /// <returns>نتيجة التحقق</returns>
    PasswordStrengthResult ValidatePasswordStrength(string password);

    /// <summary>
    /// تشفير كلمة المرور
    /// </summary>
    /// <param name="password">كلمة المرور</param>
    /// <returns>كلمة المرور المشفرة</returns>
    string HashPassword(string password);

    /// <summary>
    /// التحقق من كلمة المرور
    /// </summary>
    /// <param name="password">كلمة المرور</param>
    /// <param name="hashedPassword">كلمة المرور المشفرة</param>
    /// <returns>true إذا كانت صحيحة</returns>
    bool VerifyPassword(string password, string hashedPassword);
}

/// <summary>
/// نتيجة التحقق من قوة كلمة المرور
/// </summary>
public class PasswordStrengthResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public PasswordStrength Strength { get; set; }
}

/// <summary>
/// قوة كلمة المرور
/// </summary>
public enum PasswordStrength
{
    /// <summary>
    /// ضعيفة جداً
    /// </summary>
    VeryWeak = 1,
    
    /// <summary>
    /// ضعيفة
    /// </summary>
    Weak = 2,
    
    /// <summary>
    /// متوسطة
    /// </summary>
    Medium = 3,
    
    /// <summary>
    /// قوية
    /// </summary>
    Strong = 4,
    
    /// <summary>
    /// قوية جداً
    /// </summary>
    VeryStrong = 5
}
