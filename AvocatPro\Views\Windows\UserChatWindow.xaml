<Window x:Class="AvocatPro.Views.Windows.UserChatWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:fa="http://schemas.fontawesome.io/icons/"
        Title="محادثة مع المستخدم"
        Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        Background="#F8F9FA"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <Style x:Key="MessageBubble" TargetType="Border">
            <Setter Property="CornerRadius" Value="15"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Margin" Value="5,2"/>
            <Setter Property="MaxWidth" Value="400"/>
        </Style>

        <Style x:Key="SentMessage" TargetType="Border" BasedOn="{StaticResource MessageBubble}">
            <Setter Property="Background" Value="#007BFF"/>
            <Setter Property="HorizontalAlignment" Value="Left"/>
        </Style>

        <Style x:Key="ReceivedMessage" TargetType="Border" BasedOn="{StaticResource MessageBubble}">
            <Setter Property="Background" Value="#E9ECEF"/>
            <Setter Property="HorizontalAlignment" Value="Right"/>
        </Style>

        <Style x:Key="MessageText" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="LineHeight" Value="20"/>
        </Style>

        <Style x:Key="SentMessageText" TargetType="TextBlock" BasedOn="{StaticResource MessageText}">
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style x:Key="ReceivedMessageText" TargetType="TextBlock" BasedOn="{StaticResource MessageText}">
            <Setter Property="Foreground" Value="#2C3E50"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="White" Padding="15" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- صورة المستخدم والمعلومات -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Border Width="40" Height="40" CornerRadius="20" Background="#007BFF" Margin="0,0,10,0">
                        <TextBlock x:Name="UserAvatarText" Text="س" Foreground="White" 
                                  FontSize="16" FontWeight="Bold" 
                                  HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock x:Name="UserNameText" Text="سارة أحمد القانونية" 
                                  FontSize="16" FontWeight="Bold" Foreground="#2C3E50"/>
                        <StackPanel Orientation="Horizontal">
                            <Ellipse x:Name="StatusIndicator" Width="8" Height="8" Fill="Green" Margin="0,0,5,0"/>
                            <TextBlock x:Name="StatusText" Text="متصلة الآن" 
                                      FontSize="12" Foreground="#6C757D"/>
                        </StackPanel>
                    </StackPanel>
                </StackPanel>

                <!-- أزرار الإجراءات -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button x:Name="CallBtn" Width="35" Height="35" Margin="5,0" 
                           Background="#28A745" BorderThickness="0" 
                           ToolTip="مكالمة صوتية" Click="CallBtn_Click">
                        <fa:ImageAwesome Icon="Phone" Foreground="White" Width="16" Height="16"/>
                    </Button>
                    
                    <Button x:Name="VideoCallBtn" Width="35" Height="35" Margin="5,0" 
                           Background="#17A2B8" BorderThickness="0" 
                           ToolTip="مكالمة فيديو" Click="VideoCallBtn_Click">
                        <fa:ImageAwesome Icon="VideoCamera" Foreground="White" Width="16" Height="16"/>
                    </Button>
                    
                    <Button x:Name="UserInfoBtn" Width="35" Height="35" Margin="5,0" 
                           Background="#6C757D" BorderThickness="0" 
                           ToolTip="معلومات المستخدم" Click="UserInfoBtn_Click">
                        <fa:ImageAwesome Icon="InfoCircle" Foreground="White" Width="16" Height="16"/>
                    </Button>
                    
                    <Button x:Name="CloseBtn" Width="35" Height="35" Margin="5,0" 
                           Background="#DC3545" BorderThickness="0" 
                           ToolTip="إغلاق" Click="CloseBtn_Click">
                        <fa:ImageAwesome Icon="Times" Foreground="White" Width="16" Height="16"/>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- منطقة الرسائل -->
        <Border Grid.Row="1" Background="White" Margin="0,1,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- الرسائل -->
                <ScrollViewer x:Name="MessagesScrollViewer" Grid.Column="0" 
                             VerticalScrollBarVisibility="Auto" 
                             Padding="15">
                    <StackPanel x:Name="MessagesPanel"/>
                </ScrollViewer>

                <!-- شريط جانبي للملفات المشتركة -->
                <Border x:Name="FilesSidebar" Grid.Column="1" Width="250" 
                       Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1,0,0,0"
                       Visibility="Collapsed">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <Border Grid.Row="0" Background="White" Padding="15" 
                               BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
                            <StackPanel Orientation="Horizontal">
                                <fa:ImageAwesome Icon="FileO" Foreground="#007BFF" Width="16" Height="16" Margin="0,0,8,0"/>
                                <TextBlock Text="الملفات المشتركة" FontSize="14" FontWeight="Bold" Foreground="#2C3E50"/>
                            </StackPanel>
                        </Border>

                        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="10">
                            <StackPanel x:Name="SharedFilesPanel"/>
                        </ScrollViewer>
                    </Grid>
                </Border>
            </Grid>
        </Border>

        <!-- شريط الكتابة -->
        <Border Grid.Row="2" Background="White" Padding="15" BorderBrush="#DEE2E6" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- معاينة الملف المحدد -->
                <Border x:Name="FilePreviewPanel" Grid.Row="0" Background="#E3F2FD" 
                       CornerRadius="8" Padding="10" Margin="0,0,0,10" Visibility="Collapsed">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <fa:ImageAwesome x:Name="FilePreviewIcon" Grid.Column="0" Icon="FileO" 
                                        Foreground="#1976D2" Width="20" Height="20" Margin="0,0,10,0"/>
                        
                        <StackPanel Grid.Column="1">
                            <TextBlock x:Name="FilePreviewName" Text="اسم الملف.pdf" 
                                      FontSize="14" FontWeight="Bold" Foreground="#1976D2"/>
                            <TextBlock x:Name="FilePreviewSize" Text="2.5 MB" 
                                      FontSize="12" Foreground="#6C757D"/>
                        </StackPanel>

                        <Button x:Name="RemoveFileBtn" Grid.Column="2" Content="✕" 
                               Width="25" Height="25" Background="Transparent" 
                               BorderThickness="0" Foreground="#DC3545" 
                               Click="RemoveFileBtn_Click"/>
                    </Grid>
                </Border>

                <!-- شريط الكتابة والأزرار -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- زر إرفاق ملف -->
                    <Button x:Name="AttachFileBtn" Grid.Column="0" Width="40" Height="40" 
                           Background="#6C757D" BorderThickness="0" Margin="0,0,10,0"
                           ToolTip="إرفاق ملف" Click="AttachFileBtn_Click">
                        <fa:ImageAwesome Icon="Paperclip" Foreground="White" Width="16" Height="16"/>
                    </Button>

                    <!-- مربع النص -->
                    <Border Grid.Column="1" BorderBrush="#DEE2E6" BorderThickness="1" 
                           CornerRadius="20" Background="White">
                        <TextBox x:Name="MessageTextBox" BorderThickness="0" Background="Transparent" 
                                Padding="15,10" FontSize="14" TextWrapping="Wrap" 
                                AcceptsReturn="True" MaxHeight="100"
                                KeyDown="MessageTextBox_KeyDown"
                                Tag="اكتب رسالتك هنا..."/>
                    </Border>

                    <!-- زر الرموز التعبيرية -->
                    <Button x:Name="EmojiBtn" Grid.Column="2" Width="40" Height="40" 
                           Background="#FFC107" BorderThickness="0" Margin="10,0"
                           ToolTip="رموز تعبيرية" Click="EmojiBtn_Click">
                        <TextBlock Text="😊" FontSize="16"/>
                    </Button>

                    <!-- زر الإرسال -->
                    <Button x:Name="SendBtn" Grid.Column="3" Width="40" Height="40" 
                           Background="#007BFF" BorderThickness="0"
                           ToolTip="إرسال (Enter)" Click="SendBtn_Click">
                        <fa:ImageAwesome Icon="Send" Foreground="White" Width="16" Height="16"/>
                    </Button>
                </Grid>
            </Grid>
        </Border>
    </Grid>
</Window>
