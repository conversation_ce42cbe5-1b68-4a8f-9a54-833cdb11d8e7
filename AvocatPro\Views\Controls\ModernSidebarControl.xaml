<UserControl x:Class="AvocatPro.Views.Controls.ModernSidebarControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <!-- أنماط القائمة الجانبية -->
        <Style x:Key="SidebarButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#6B7280"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,15"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" 
                                Background="{TemplateBinding Background}"
                                CornerRadius="12"
                                Margin="10,2"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#F3F4F6"/>
                                <Setter Property="Foreground" Value="#374151"/>
                            </Trigger>
                            <Trigger Property="Tag" Value="Active">
                                <Setter TargetName="border" Property="Background" Value="#EEF2FF"/>
                                <Setter Property="Foreground" Value="#6366F1"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- أنماط العناوين -->
        <Style x:Key="SidebarSectionTitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#9CA3AF"/>
            <Setter Property="Margin" Value="20,20,20,10"/>
        </Style>

        <!-- أنماط الفواصل -->
        <Style x:Key="SidebarSeparatorStyle" TargetType="Border">
            <Setter Property="Height" Value="1"/>
            <Setter Property="Background" Value="#E5E7EB"/>
            <Setter Property="Margin" Value="20,15"/>
        </Style>
    </UserControl.Resources>

    <Border Background="White" CornerRadius="0,25,25,0" Width="280">
        <Border.Effect>
            <DropShadowEffect Color="Black" Opacity="0.05" BlurRadius="20" ShadowDepth="5"/>
        </Border.Effect>
        
        <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
            <StackPanel Margin="0,30,0,20">
                
                <!-- شعار AvocatPro -->
                <StackPanel Orientation="Horizontal" Margin="20,0,20,30" HorizontalAlignment="Right">
                    <Border Background="#6366F1" CornerRadius="12" Width="40" Height="40" Margin="0,0,12,0">
                        <TextBlock Text="⚖️" FontSize="20" Foreground="White" 
                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock Text="AvocatPro" FontSize="20" FontWeight="Bold" Foreground="#1F2937"/>
                        <TextBlock Text="إدارة مكاتب المحاماة" FontSize="10" Foreground="#6B7280"/>
                    </StackPanel>
                </StackPanel>

                <!-- القسم الرئيسي -->
                <TextBlock Text="القائمة الرئيسية" Style="{StaticResource SidebarSectionTitleStyle}"/>
                
                <Button Style="{StaticResource SidebarButtonStyle}" Tag="Active" Click="Dashboard_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🏠" FontSize="18" Margin="0,0,12,0" VerticalAlignment="Center"/>
                        <TextBlock Text="لوحة التحكم" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Style="{StaticResource SidebarButtonStyle}" Click="Clients_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="👥" FontSize="18" Margin="0,0,12,0" VerticalAlignment="Center"/>
                        <TextBlock Text="إدارة الموكلين" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Style="{StaticResource SidebarButtonStyle}" Click="Files_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📁" FontSize="18" Margin="0,0,12,0" VerticalAlignment="Center"/>
                        <TextBlock Text="إدارة الملفات" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Style="{StaticResource SidebarButtonStyle}" Click="ComprehensiveFiles_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🗂️" FontSize="18" Margin="0,0,12,0" VerticalAlignment="Center"/>
                        <TextBlock Text="الملفات الشاملة" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Style="{StaticResource SidebarButtonStyle}" Click="Sessions_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="⚖️" FontSize="18" Margin="0,0,12,0" VerticalAlignment="Center"/>
                        <TextBlock Text="إدارة الجلسات" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Style="{StaticResource SidebarButtonStyle}" Click="Appointments_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📅" FontSize="18" Margin="0,0,12,0" VerticalAlignment="Center"/>
                        <TextBlock Text="إدارة المواعيد" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <!-- فاصل -->
                <Border Style="{StaticResource SidebarSeparatorStyle}"/>

                <!-- قسم المالية -->
                <TextBlock Text="الإدارة المالية" Style="{StaticResource SidebarSectionTitleStyle}"/>
                
                <Button Style="{StaticResource SidebarButtonStyle}" Click="Finance_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="💰" FontSize="18" Margin="0,0,12,0" VerticalAlignment="Center"/>
                        <TextBlock Text="الإيرادات والمصروفات" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Style="{StaticResource SidebarButtonStyle}" Click="Invoices_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🧾" FontSize="18" Margin="0,0,12,0" VerticalAlignment="Center"/>
                        <TextBlock Text="إدارة الفواتير" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Style="{StaticResource SidebarButtonStyle}" Click="Reports_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📊" FontSize="18" Margin="0,0,12,0" VerticalAlignment="Center"/>
                        <TextBlock Text="التقارير المالية" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <!-- فاصل -->
                <Border Style="{StaticResource SidebarSeparatorStyle}"/>

                <!-- قسم الإدارة -->
                <TextBlock Text="الإدارة والإعدادات" Style="{StaticResource SidebarSectionTitleStyle}"/>
                
                <Button Style="{StaticResource SidebarButtonStyle}" Click="Users_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="👤" FontSize="18" Margin="0,0,12,0" VerticalAlignment="Center"/>
                        <TextBlock Text="إدارة المستخدمين" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Style="{StaticResource SidebarButtonStyle}" Click="Backup_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="💾" FontSize="18" Margin="0,0,12,0" VerticalAlignment="Center"/>
                        <TextBlock Text="النسخ الاحتياطي" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Style="{StaticResource SidebarButtonStyle}" Click="Settings_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="⚙️" FontSize="18" Margin="0,0,12,0" VerticalAlignment="Center"/>
                        <TextBlock Text="الإعدادات العامة" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <!-- فاصل -->
                <Border Style="{StaticResource SidebarSeparatorStyle}"/>

                <!-- قسم المساعدة -->
                <TextBlock Text="المساعدة والدعم" Style="{StaticResource SidebarSectionTitleStyle}"/>
                
                <Button Style="{StaticResource SidebarButtonStyle}" Click="Help_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="❓" FontSize="18" Margin="0,0,12,0" VerticalAlignment="Center"/>
                        <TextBlock Text="المساعدة" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Style="{StaticResource SidebarButtonStyle}" Click="About_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="ℹ️" FontSize="18" Margin="0,0,12,0" VerticalAlignment="Center"/>
                        <TextBlock Text="حول البرنامج" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <!-- معلومات المستخدم في الأسفل -->
                <Border Background="#F8FAFC" CornerRadius="15" Margin="15,30,15,0" Padding="15">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <Border Background="#6366F1" CornerRadius="20" Width="35" Height="35" Margin="0,0,10,0">
                                <TextBlock Text="👤" FontSize="16" Foreground="White" 
                                           HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel VerticalAlignment="Center">
                                <TextBlock Text="أحمد محمد" FontSize="14" FontWeight="SemiBold" Foreground="#1F2937"/>
                                <TextBlock Text="مدير النظام" FontSize="11" Foreground="#6B7280"/>
                            </StackPanel>
                        </StackPanel>
                        
                        <Button Background="#EF4444" Foreground="White" BorderThickness="0"
                                Padding="12,8" FontSize="12" Cursor="Hand"
                                Click="Logout_Click">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <TextBlock Text="🚪" FontSize="14" Margin="0,0,8,0"/>
                                <TextBlock Text="تسجيل الخروج"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Border>

                <!-- مساحة إضافية في الأسفل -->
                <Border Height="20"/>
            </StackPanel>
        </ScrollViewer>
    </Border>
</UserControl>
