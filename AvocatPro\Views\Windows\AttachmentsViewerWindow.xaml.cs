using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Imaging;

namespace AvocatPro.Views.Windows;

public partial class AttachmentsViewerWindow : Window
{
    private readonly List<AttachmentInfo> _attachments;
    private AttachmentInfo? _selectedAttachment;

    public AttachmentsViewerWindow(string attachmentsJson, string documentTitle = "")
    {
        InitializeComponent();
        _attachments = ParseAttachments(attachmentsJson);
        
        if (!string.IsNullOrEmpty(documentTitle))
        {
            DocumentInfoTextBlock.Text = $"المرفقات الخاصة بـ: {documentTitle}";
        }
        
        LoadAttachments();
    }

    private List<AttachmentInfo> ParseAttachments(string attachmentsJson)
    {
        var attachments = new List<AttachmentInfo>();
        
        if (string.IsNullOrEmpty(attachmentsJson))
            return attachments;

        try
        {
            var fileNames = System.Text.Json.JsonSerializer.Deserialize<List<string>>(attachmentsJson);
            if (fileNames != null)
            {
                foreach (var fileName in fileNames)
                {
                    attachments.Add(new AttachmentInfo
                    {
                        FileName = fileName,
                        FileType = GetFileType(fileName),
                        FileSize = GetRandomFileSize(), // محاكاة حجم الملف
                        DateAdded = DateTime.Now.AddDays(-new Random().Next(1, 30))
                    });
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحليل المرفقات: {ex.Message}");
        }

        return attachments;
    }

    private string GetFileType(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLower();
        return extension switch
        {
            ".pdf" => "PDF",
            ".doc" or ".docx" => "Word",
            ".xls" or ".xlsx" => "Excel",
            ".jpg" or ".jpeg" => "صورة JPEG",
            ".png" => "صورة PNG",
            ".gif" => "صورة GIF",
            ".txt" => "نص",
            ".zip" or ".rar" => "أرشيف",
            _ => "ملف"
        };
    }

    private string GetRandomFileSize()
    {
        var random = new Random();
        var sizeKb = random.Next(50, 5000);
        
        if (sizeKb < 1024)
            return $"{sizeKb} KB";
        else
            return $"{sizeKb / 1024.0:F1} MB";
    }

    private void LoadAttachments()
    {
        AttachmentsListPanel.Children.Clear();

        if (_attachments.Count == 0)
        {
            var noAttachmentsText = new TextBlock
            {
                Text = "📄 لا توجد مرفقات",
                FontSize = 14,
                Foreground = new SolidColorBrush(Colors.Gray),
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 20, 0, 0)
            };
            AttachmentsListPanel.Children.Add(noAttachmentsText);
            StatusTextBlock.Text = "لا توجد مرفقات";
            return;
        }

        foreach (var attachment in _attachments)
        {
            var card = CreateAttachmentCard(attachment);
            AttachmentsListPanel.Children.Add(card);
        }

        StatusTextBlock.Text = $"تم تحميل {_attachments.Count} مرفق";
    }

    private Border CreateAttachmentCard(AttachmentInfo attachment)
    {
        var card = new Border
        {
            Style = (Style)FindResource("AttachmentCardStyle"),
            Cursor = System.Windows.Input.Cursors.Hand
        };

        var panel = new StackPanel();

        // أيقونة ونوع الملف
        var headerPanel = new StackPanel { Orientation = Orientation.Horizontal };
        
        var icon = new TextBlock
        {
            Text = GetFileIcon(attachment.FileType),
            FontSize = 20,
            Margin = new Thickness(0, 0, 10, 0),
            VerticalAlignment = VerticalAlignment.Center
        };
        
        var typeText = new TextBlock
        {
            Text = attachment.FileType,
            FontSize = 10,
            Foreground = new SolidColorBrush(Colors.Gray),
            VerticalAlignment = VerticalAlignment.Center
        };
        
        headerPanel.Children.Add(icon);
        headerPanel.Children.Add(typeText);
        panel.Children.Add(headerPanel);

        // اسم الملف
        var nameText = new TextBlock
        {
            Text = attachment.FileName,
            FontSize = 12,
            FontWeight = FontWeights.Bold,
            TextWrapping = TextWrapping.Wrap,
            Margin = new Thickness(0, 5, 0, 5)
        };
        panel.Children.Add(nameText);

        // معلومات إضافية
        var infoPanel = new StackPanel();
        
        var sizeText = new TextBlock
        {
            Text = $"الحجم: {attachment.FileSize}",
            FontSize = 10,
            Foreground = new SolidColorBrush(Colors.Gray)
        };
        
        var dateText = new TextBlock
        {
            Text = $"تاريخ الإضافة: {attachment.DateAdded:dd/MM/yyyy}",
            FontSize = 10,
            Foreground = new SolidColorBrush(Colors.Gray)
        };
        
        infoPanel.Children.Add(sizeText);
        infoPanel.Children.Add(dateText);
        panel.Children.Add(infoPanel);

        card.Child = panel;
        card.Tag = attachment;
        card.MouseLeftButtonUp += AttachmentCard_Click;

        return card;
    }

    private string GetFileIcon(string fileType)
    {
        return fileType switch
        {
            "PDF" => "📄",
            "Word" => "📝",
            "Excel" => "📊",
            "صورة JPEG" or "صورة PNG" or "صورة GIF" => "🖼️",
            "نص" => "📃",
            "أرشيف" => "📦",
            _ => "📄"
        };
    }

    private void AttachmentCard_Click(object sender, System.Windows.Input.MouseButtonEventArgs e)
    {
        if (sender is Border card && card.Tag is AttachmentInfo attachment)
        {
            SelectAttachment(attachment);
        }
    }

    private void SelectAttachment(AttachmentInfo attachment)
    {
        _selectedAttachment = attachment;
        
        // تحديث معلومات الملف
        FileNameTextBlock.Text = attachment.FileName;
        FileTypeTextBlock.Text = attachment.FileType;
        FileSizeTextBlock.Text = attachment.FileSize;
        DateAddedTextBlock.Text = attachment.DateAdded.ToString("dd/MM/yyyy HH:mm");

        // تفعيل الأزرار
        OpenFileButton.IsEnabled = true;
        DownloadFileButton.IsEnabled = true;
        PrintFileButton.IsEnabled = true;
        ShareFileButton.IsEnabled = true;
        DeleteFileButton.IsEnabled = true;

        // عرض المعاينة
        ShowPreview(attachment);

        // تحديث تمييز البطاقة المحددة
        UpdateCardSelection();

        StatusTextBlock.Text = $"تم اختيار: {attachment.FileName}";
    }

    private void ShowPreview(AttachmentInfo attachment)
    {
        // إخفاء جميع أنواع المعاينة
        NoPreviewPanel.Visibility = Visibility.Collapsed;
        ImagePreview.Visibility = Visibility.Collapsed;
        TextPreview.Visibility = Visibility.Collapsed;
        PdfPreview.Visibility = Visibility.Collapsed;
        OtherFilePreview.Visibility = Visibility.Collapsed;

        var extension = Path.GetExtension(attachment.FileName).ToLower();

        switch (extension)
        {
            case ".jpg" or ".jpeg" or ".png" or ".gif":
                ShowImagePreview(attachment);
                break;
            case ".txt":
                ShowTextPreview(attachment);
                break;
            case ".pdf":
                PdfPreview.Visibility = Visibility.Visible;
                break;
            default:
                ShowOtherFilePreview(attachment);
                break;
        }
    }

    private void ShowImagePreview(AttachmentInfo attachment)
    {
        try
        {
            // محاكاة عرض الصورة
            ImagePreview.Visibility = Visibility.Visible;
            // في التطبيق الحقيقي، ستقوم بتحميل الصورة من المسار الفعلي
            StatusTextBlock.Text = $"معاينة الصورة: {attachment.FileName}";
        }
        catch (Exception ex)
        {
            ShowOtherFilePreview(attachment);
            StatusTextBlock.Text = $"خطأ في عرض الصورة: {ex.Message}";
        }
    }

    private void ShowTextPreview(AttachmentInfo attachment)
    {
        try
        {
            // محاكاة عرض النص
            TextPreview.Text = $"محتوى نصي تجريبي للملف: {attachment.FileName}\n\n" +
                              "هذا نص تجريبي يوضح كيفية عرض محتوى الملفات النصية.\n" +
                              "في التطبيق الحقيقي، سيتم تحميل المحتوى الفعلي للملف.";
            TextPreview.Visibility = Visibility.Visible;
            StatusTextBlock.Text = $"معاينة النص: {attachment.FileName}";
        }
        catch (Exception ex)
        {
            ShowOtherFilePreview(attachment);
            StatusTextBlock.Text = $"خطأ في عرض النص: {ex.Message}";
        }
    }

    private void ShowOtherFilePreview(AttachmentInfo attachment)
    {
        FileIconTextBlock.Text = GetFileIcon(attachment.FileType);
        FileTypeDisplayTextBlock.Text = attachment.FileType;
        OtherFilePreview.Visibility = Visibility.Visible;
        StatusTextBlock.Text = $"معاينة الملف: {attachment.FileName}";
    }

    private void UpdateCardSelection()
    {
        foreach (Border card in AttachmentsListPanel.Children.OfType<Border>())
        {
            if (card.Tag is AttachmentInfo attachment && attachment == _selectedAttachment)
            {
                card.BorderBrush = new SolidColorBrush(Color.FromRgb(25, 118, 210));
                card.BorderThickness = new Thickness(2);
                card.Background = new SolidColorBrush(Color.FromRgb(227, 242, 253));
            }
            else
            {
                card.BorderBrush = new SolidColorBrush(Color.FromRgb(224, 224, 224));
                card.BorderThickness = new Thickness(1);
                card.Background = Brushes.White;
            }
        }
    }

    // معالجات الأحداث
    private void AddAttachmentButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Title = "اختيار ملف للإرفاق",
                Filter = "جميع الملفات (*.*)|*.*|ملفات PDF (*.pdf)|*.pdf|صور (*.jpg;*.jpeg;*.png)|*.jpg;*.jpeg;*.png|مستندات Word (*.doc;*.docx)|*.doc;*.docx|جداول Excel (*.xls;*.xlsx)|*.xls;*.xlsx",
                Multiselect = true
            };

            if (openFileDialog.ShowDialog() == true)
            {
                foreach (var fileName in openFileDialog.FileNames)
                {
                    var fileInfo = new FileInfo(fileName);
                    var attachment = new AttachmentInfo
                    {
                        FileName = fileInfo.Name,
                        FileType = GetFileType(fileInfo.Name),
                        FileSize = FormatFileSize(fileInfo.Length),
                        DateAdded = DateTime.Now
                    };
                    
                    _attachments.Add(attachment);
                }
                
                LoadAttachments();
                MessageBox.Show($"تم إرفاق {openFileDialog.FileNames.Length} ملف بنجاح!", "إرفاق الملفات", 
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إرفاق الملف: {ex.Message}", "خطأ", 
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private string FormatFileSize(long bytes)
    {
        if (bytes < 1024)
            return $"{bytes} B";
        else if (bytes < 1024 * 1024)
            return $"{bytes / 1024.0:F1} KB";
        else
            return $"{bytes / (1024.0 * 1024.0):F1} MB";
    }

    private void ScanDocumentButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var result = MessageBox.Show("هل تريد بدء عملية المسح الضوئي؟\n\nسيتم فتح تطبيق المسح الضوئي المثبت على النظام.",
                                       "مسح ضوئي", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                var scannedFileName = $"مسح_ضوئي_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";
                var attachment = new AttachmentInfo
                {
                    FileName = scannedFileName,
                    FileType = "PDF",
                    FileSize = GetRandomFileSize(),
                    DateAdded = DateTime.Now
                };

                _attachments.Add(attachment);
                LoadAttachments();

                MessageBox.Show($"تم إنشاء الملف الممسوح ضوئياً: {scannedFileName}", "نجح المسح الضوئي",
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في المسح الضوئي: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void DownloadAllButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (_attachments.Count == 0)
            {
                MessageBox.Show("لا توجد مرفقات للتحميل.", "تحميل المرفقات",
                               MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var result = MessageBox.Show($"هل تريد تحميل جميع المرفقات ({_attachments.Count} ملف)؟",
                                       "تحميل جميع المرفقات", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                MessageBox.Show($"تم تحميل {_attachments.Count} ملف بنجاح!\n\nالمجلد: المرفقات_المالية",
                               "نجح التحميل", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل المرفقات: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void OpenFileButton_Click(object sender, RoutedEventArgs e)
    {
        if (_selectedAttachment == null) return;

        try
        {
            MessageBox.Show($"سيتم فتح الملف: {_selectedAttachment.FileName}\n\nفي التطبيق المناسب على النظام.",
                           "فتح الملف", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void DownloadFileButton_Click(object sender, RoutedEventArgs e)
    {
        if (_selectedAttachment == null) return;

        try
        {
            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                FileName = _selectedAttachment.FileName,
                Filter = "جميع الملفات (*.*)|*.*"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                MessageBox.Show($"تم تحميل الملف بنجاح!\n\nالمسار: {saveFileDialog.FileName}",
                               "نجح التحميل", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل الملف: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void PrintFileButton_Click(object sender, RoutedEventArgs e)
    {
        if (_selectedAttachment == null) return;

        try
        {
            var result = MessageBox.Show($"هل تريد طباعة الملف: {_selectedAttachment.FileName}؟",
                                       "طباعة الملف", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                MessageBox.Show("تم إرسال الملف للطابعة بنجاح!", "نجحت الطباعة",
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة الملف: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ShareFileButton_Click(object sender, RoutedEventArgs e)
    {
        if (_selectedAttachment == null) return;

        try
        {
            var result = MessageBox.Show($"اختر طريقة المشاركة للملف: {_selectedAttachment.FileName}\n\nYes = البريد الإلكتروني\nNo = واتساب\nCancel = إلغاء",
                                       "مشاركة الملف", MessageBoxButton.YesNoCancel, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                MessageBox.Show("تم إرسال الملف عبر البريد الإلكتروني بنجاح!", "نجحت المشاركة",
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else if (result == MessageBoxResult.No)
            {
                MessageBox.Show("تم إرسال الملف عبر واتساب بنجاح!", "نجحت المشاركة",
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في مشاركة الملف: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void DeleteFileButton_Click(object sender, RoutedEventArgs e)
    {
        if (_selectedAttachment == null) return;

        try
        {
            var result = MessageBox.Show($"هل تريد حذف الملف: {_selectedAttachment.FileName}؟\n\nتحذير: لا يمكن التراجع عن هذا الإجراء!",
                                       "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                _attachments.Remove(_selectedAttachment);
                _selectedAttachment = null;

                // إعادة تعيين المعاينة
                NoPreviewPanel.Visibility = Visibility.Visible;
                ImagePreview.Visibility = Visibility.Collapsed;
                TextPreview.Visibility = Visibility.Collapsed;
                PdfPreview.Visibility = Visibility.Collapsed;
                OtherFilePreview.Visibility = Visibility.Collapsed;

                // إعادة تعيين معلومات الملف
                FileNameTextBlock.Text = "لم يتم اختيار ملف";
                FileTypeTextBlock.Text = "-";
                FileSizeTextBlock.Text = "-";
                DateAddedTextBlock.Text = "-";

                // تعطيل الأزرار
                OpenFileButton.IsEnabled = false;
                DownloadFileButton.IsEnabled = false;
                PrintFileButton.IsEnabled = false;
                ShareFileButton.IsEnabled = false;
                DeleteFileButton.IsEnabled = false;

                LoadAttachments();
                MessageBox.Show("تم حذف الملف بنجاح!", "نجح الحذف",
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في حذف الملف: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }
}

// فئة معلومات المرفق
public class AttachmentInfo
{
    public string FileName { get; set; } = "";
    public string FileType { get; set; } = "";
    public string FileSize { get; set; } = "";
    public DateTime DateAdded { get; set; }
}
