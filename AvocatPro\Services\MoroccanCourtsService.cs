using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using System.Text.Json;
using AvocatPro.Models;

namespace AvocatPro.Services
{
    /// <summary>
    /// خدمة التكامل مع المحاكم الإلكترونية المغربية
    /// </summary>
    public class MoroccanCourtsService
    {
        private readonly HttpClient _httpClient;
        private const string BASE_URL = "https://www.mahakim.ma";

        public MoroccanCourtsService()
        {
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
        }

        #region Court Data

        /// <summary>
        /// قائمة المحاكم المغربية
        /// </summary>
        public List<string> GetMoroccanCourts()
        {
            return new List<string>
            {
                // محاكم الاستئناف
                "محكمة الاستئناف بالرباط",
                "محكمة الاستئناف بالدار البيضاء",
                "محكمة الاستئناف بفاس",
                "محكمة الاستئناف بمراكش",
                "محكمة الاستئناف بمكناس",
                "محكمة الاستئناف بوجدة",
                "محكمة الاستئناف بأكادير",
                "محكمة الاستئناف بطنجة",
                "محكمة الاستئناف بتطوان",
                "محكمة الاستئناف بالحسيمة",
                "محكمة الاستئناف بالناظور",
                "محكمة الاستئناف بالعيون",

                // المحاكم الابتدائية - الرباط
                "المحكمة الابتدائية بالرباط",
                "المحكمة الابتدائية بسلا",
                "المحكمة الابتدائية بتمارة",
                "المحكمة الابتدائية بالصخيرات",

                // المحاكم الابتدائية - الدار البيضاء
                "المحكمة الابتدائية بالدار البيضاء",
                "المحكمة الابتدائية بعين السبع",
                "المحكمة الابتدائية بالحي المحمدي",
                "المحكمة الابتدائية بسيدي عثمان",
                "المحكمة الابتدائية بالنواصر",
                "المحكمة الابتدائية بالمحمدية",
                "المحكمة الابتدائية ببرشيد",
                "المحكمة الابتدائية بالجديدة",
                "المحكمة الابتدائية بآسفي",

                // المحاكم الابتدائية - فاس
                "المحكمة الابتدائية بفاس",
                "المحكمة الابتدائية بصفرو",
                "المحكمة الابتدائية بتاونات",
                "المحكمة الابتدائية ببولمان",

                // المحاكم الابتدائية - مراكش
                "المحكمة الابتدائية بمراكش",
                "المحكمة الابتدائية بالحوز",
                "المحكمة الابتدائية بشيشاوة",
                "المحكمة الابتدائية بالصويرة",

                // المحاكم التجارية
                "المحكمة التجارية بالدار البيضاء",
                "المحكمة التجارية بالرباط",
                "المحكمة التجارية بفاس",
                "المحكمة التجارية بمراكش",
                "المحكمة التجارية بطنجة",
                "المحكمة التجارية بأكادير",
                "المحكمة التجارية بوجدة",
                "المحكمة التجارية بمكناس",

                // المحاكم الإدارية
                "المحكمة الإدارية بالرباط",
                "المحكمة الإدارية بالدار البيضاء",
                "المحكمة الإدارية بفاس",
                "المحكمة الإدارية بمراكش",
                "المحكمة الإدارية بأكادير",
                "المحكمة الإدارية بوجدة",
                "المحكمة الإدارية بمكناس",

                // محاكم الأسرة
                "محكمة الأسرة بالرباط",
                "محكمة الأسرة بالدار البيضاء",
                "محكمة الأسرة بفاس",
                "محكمة الأسرة بمراكش",
                "محكمة الأسرة بطنجة",
                "محكمة الأسرة بأكادير"
            };
        }

        /// <summary>
        /// قائمة أنواع القضايا
        /// </summary>
        public List<string> GetCaseTypes()
        {
            return new List<string>
            {
                "مدني",
                "تجاري", 
                "جنائي",
                "عائلي",
                "إداري",
                "عقاري",
                "عمالي",
                "ضريبي",
                "جمركي",
                "بيئي",
                "استهلاكي",
                "ملكية فكرية",
                "تأمين",
                "مصرفي",
                "شركات",
                "إفلاس",
                "تحكيم",
                "تنفيذ"
            };
        }

        /// <summary>
        /// قائمة أنواع الملفات
        /// </summary>
        public List<string> GetFileTypes()
        {
            return new List<string>
            {
                "دعوى",
                "استئناف",
                "نقض",
                "تنفيذ",
                "تحكيم",
                "صلح",
                "خبرة",
                "إنابة قضائية",
                "طلب مساعدة قضائية",
                "شكوى",
                "بلاغ",
                "طعن",
                "مراجعة",
                "إعادة النظر",
                "اعتراض الغير",
                "تصحيح خطأ مادي"
            };
        }

        /// <summary>
        /// قائمة أنواع الإجراءات
        /// </summary>
        public List<string> GetProcedureTypes()
        {
            return new List<string>
            {
                "جلسة مرافعة",
                "جلسة تحقيق",
                "جلسة خبرة",
                "جلسة صلح",
                "جلسة نطق بالحكم",
                "إيداع مذكرة",
                "إيداع مستندات",
                "طلب تأجيل",
                "طلب ضم ملفات",
                "طلب فصل ملفات",
                "طلب إحالة",
                "طلب رد",
                "طلب تنحي",
                "طلب معاينة",
                "طلب انتداب خبير",
                "طلب استجواب",
                "طلب سماع شهود"
            };
        }

        #endregion

        #region Electronic Tracking

        /// <summary>
        /// البحث عن ملف في المحاكم الإلكترونية
        /// </summary>
        public async Task<FileTrackingResult> SearchFileAsync(string fileNumber, string fileCode, int year, string court, bool searchInPrimary = true)
        {
            try
            {
                // محاكاة البحث في النظام الإلكتروني
                await Task.Delay(2000); // محاكاة وقت الاستجابة

                // في التطبيق الحقيقي، ستتم هنا عملية الاتصال بـ API المحاكم
                var result = new FileTrackingResult
                {
                    IsFound = true,
                    FileNumber = fileNumber,
                    FileCode = fileCode,
                    Year = year,
                    Court = court,
                    Status = "نشط",
                    LastUpdate = DateTime.Now,
                    NextSessionDate = DateTime.Now.AddDays(15),
                    Judge = "القاضي محمد العلوي",
                    Clerk = "كاتب الضبط فاطمة الزهراء",
                    LastProcedure = "جلسة مرافعة",
                    Decision = "تأجيل للمداولة"
                };

                return result;
            }
            catch (Exception ex)
            {
                return new FileTrackingResult
                {
                    IsFound = false,
                    ErrorMessage = $"خطأ في البحث: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// مزامنة دورية للملفات
        /// </summary>
        public async Task<List<FileTrackingResult>> SyncFilesAsync(List<AdvancedFileModel> files)
        {
            var results = new List<FileTrackingResult>();

            foreach (var file in files)
            {
                if (file.IsElectronicTracking && !string.IsNullOrEmpty(file.ElectronicFileNumber))
                {
                    var result = await SearchFileAsync(
                        file.ElectronicFileNumber,
                        file.ElectronicFileCode,
                        file.ElectronicYear,
                        file.Court,
                        file.SearchInPrimaryCourts
                    );

                    results.Add(result);

                    // تحديث بيانات الملف
                    if (result.IsFound)
                    {
                        file.LastSyncDate = DateTime.Now;
                        file.SyncStatus = "متزامن";
                        if (result.NextSessionDate.HasValue)
                            file.NextSessionDate = result.NextSessionDate;
                        if (!string.IsNullOrEmpty(result.Decision))
                            file.Decision = result.Decision;
                    }
                    else
                    {
                        file.SyncStatus = "خطأ في المزامنة";
                    }
                }
            }

            return results;
        }

        /// <summary>
        /// الحصول على قائمة الحالات المتاحة
        /// </summary>
        public List<string> GetFileStatuses()
        {
            return new List<string>
            {
                "نشط",
                "في الجلسات",
                "مؤرشف",
                "مغلق",
                "معلق",
                "قيد التنفيذ",
                "مستأنف",
                "منقوض"
            };
        }

        /// <summary>
        /// الحصول على قائمة الأولويات
        /// </summary>
        public List<string> GetPriorities()
        {
            return new List<string>
            {
                "عاجل",
                "مهم",
                "عادي"
            };
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// التحقق من صحة رقم الملف
        /// </summary>
        public bool ValidateFileNumber(string fileNumber, string fileCode, int year)
        {
            if (string.IsNullOrWhiteSpace(fileNumber) || string.IsNullOrWhiteSpace(fileCode))
                return false;

            if (year < 2000 || year > DateTime.Now.Year + 1)
                return false;

            return true;
        }

        /// <summary>
        /// تنسيق رقم الملف الكامل
        /// </summary>
        public string FormatFullFileNumber(string fileNumber, string fileCode, int year)
        {
            return $"{fileNumber}/{fileCode}/{year}";
        }

        #endregion

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    /// <summary>
    /// نتيجة تتبع الملف
    /// </summary>
    public class FileTrackingResult
    {
        public bool IsFound { get; set; }
        public string FileNumber { get; set; } = string.Empty;
        public string FileCode { get; set; } = string.Empty;
        public int Year { get; set; }
        public string Court { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime LastUpdate { get; set; }
        public DateTime? NextSessionDate { get; set; }
        public string Judge { get; set; } = string.Empty;
        public string Clerk { get; set; } = string.Empty;
        public string LastProcedure { get; set; } = string.Empty;
        public string Decision { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
    }
}
