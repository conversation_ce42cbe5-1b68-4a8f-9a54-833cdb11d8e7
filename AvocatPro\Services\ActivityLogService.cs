using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using AvocatPro.Data;
using AvocatPro.Models.UserManagement;

namespace AvocatPro.Services;

/// <summary>
/// خدمة سجل الأنشطة
/// </summary>
public class ActivityLogService
{
    private readonly AvocatProDbContext _context;

    public ActivityLogService(AvocatProDbContext context)
    {
        _context = context;
    }

    /// <summary>
    /// تسجيل نشاط جديد
    /// </summary>
    public async Task LogActivityAsync(
        int userId,
        ActivityType activityType,
        string description,
        string module,
        int? entityId = null,
        string? entityType = null,
        string? oldData = null,
        string? newData = null,
        string? ipAddress = null,
        string? userAgent = null,
        ActivityLevel level = ActivityLevel.Info,
        bool isSuccessful = true,
        string? errorMessage = null,
        string? additionalInfo = null)
    {
        try
        {
            var activity = new UserActivity
            {
                UserId = userId,
                ActivityType = activityType,
                Description = description,
                Module = module,
                EntityId = entityId,
                EntityType = entityType ?? "",
                OldData = oldData,
                NewData = newData,
                IPAddress = ipAddress ?? "",
                UserAgent = userAgent ?? "",
                Level = level,
                IsSuccessful = isSuccessful,
                ErrorMessage = errorMessage ?? "",
                AdditionalInfo = additionalInfo,
                CreatedAt = DateTime.Now
            };

            _context.UserActivities.Add(activity);
            await _context.SaveChangesAsync();

            // تحديث إحصائيات المستخدم
            await UpdateUserStatisticsAsync(userId, activityType, isSuccessful);
        }
        catch (Exception ex)
        {
            // في حالة فشل تسجيل النشاط، نسجل الخطأ في سجل النظام
            System.Diagnostics.Debug.WriteLine($"خطأ في تسجيل النشاط: {ex.Message}");
        }
    }

    /// <summary>
    /// الحصول على أنشطة المستخدم
    /// </summary>
    public async Task<List<UserActivity>> GetUserActivitiesAsync(
        int userId,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        ActivityType? activityType = null,
        string? module = null,
        int pageNumber = 1,
        int pageSize = 50)
    {
        var query = _context.UserActivities
            .Where(ua => ua.UserId == userId);

        if (fromDate.HasValue)
            query = query.Where(ua => ua.CreatedAt >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(ua => ua.CreatedAt <= toDate.Value);

        if (activityType.HasValue)
            query = query.Where(ua => ua.ActivityType == activityType.Value);

        if (!string.IsNullOrEmpty(module))
            query = query.Where(ua => ua.Module == module);

        return await query
            .OrderByDescending(ua => ua.CreatedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    /// <summary>
    /// الحصول على جميع الأنشطة (للمديرين)
    /// </summary>
    public async Task<List<UserActivity>> GetAllActivitiesAsync(
        DateTime? fromDate = null,
        DateTime? toDate = null,
        ActivityType? activityType = null,
        string? module = null,
        ActivityLevel? level = null,
        bool? isSuccessful = null,
        int pageNumber = 1,
        int pageSize = 100)
    {
        var query = _context.UserActivities
            .Include(ua => ua.User)
            .AsQueryable();

        if (fromDate.HasValue)
            query = query.Where(ua => ua.CreatedAt >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(ua => ua.CreatedAt <= toDate.Value);

        if (activityType.HasValue)
            query = query.Where(ua => ua.ActivityType == activityType.Value);

        if (!string.IsNullOrEmpty(module))
            query = query.Where(ua => ua.Module == module);

        if (level.HasValue)
            query = query.Where(ua => ua.Level == level.Value);

        if (isSuccessful.HasValue)
            query = query.Where(ua => ua.IsSuccessful == isSuccessful.Value);

        return await query
            .OrderByDescending(ua => ua.CreatedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    /// <summary>
    /// تسجيل تسجيل الدخول
    /// </summary>
    public async Task<int> LogLoginAsync(
        int userId,
        string ipAddress,
        string userAgent,
        bool isSuccessful,
        string? failureReason = null)
    {
        try
        {
            var loginLog = new LoginLog
            {
                UserId = userId,
                LoginTime = DateTime.Now,
                IPAddress = ipAddress,
                UserAgent = userAgent,
                IsSuccessful = isSuccessful,
                FailureReason = failureReason ?? ""
            };

            _context.LoginLogs.Add(loginLog);
            await _context.SaveChangesAsync();

            // تسجيل النشاط
            await LogActivityAsync(
                userId,
                ActivityType.Login,
                isSuccessful ? "تسجيل دخول ناجح" : $"فشل تسجيل الدخول: {failureReason}",
                "Authentication",
                ipAddress: ipAddress,
                userAgent: userAgent,
                level: isSuccessful ? ActivityLevel.Info : ActivityLevel.Warning,
                isSuccessful: isSuccessful,
                errorMessage: failureReason
            );

            return loginLog.Id;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تسجيل الدخول: {ex.Message}");
            return 0;
        }
    }

    /// <summary>
    /// تسجيل تسجيل الخروج
    /// </summary>
    public async Task LogLogoutAsync(int loginLogId, int userId)
    {
        try
        {
            var loginLog = await _context.LoginLogs.FindAsync(loginLogId);
            if (loginLog != null)
            {
                loginLog.LogoutTime = DateTime.Now;
                await _context.SaveChangesAsync();
            }

            // تسجيل النشاط
            await LogActivityAsync(
                userId,
                ActivityType.Logout,
                "تسجيل خروج",
                "Authentication"
            );
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تسجيل الخروج: {ex.Message}");
        }
    }

    /// <summary>
    /// تحديث إحصائيات المستخدم
    /// </summary>
    private async Task UpdateUserStatisticsAsync(int userId, ActivityType activityType, bool isSuccessful)
    {
        try
        {
            var today = DateTime.Today;
            var stats = await _context.UserStatistics
                .FirstOrDefaultAsync(us => us.UserId == userId && us.StatDate == today);

            if (stats == null)
            {
                stats = new UserStatistics
                {
                    UserId = userId,
                    StatDate = today,
                    CreatedAt = DateTime.Now
                };
                _context.UserStatistics.Add(stats);
            }

            // تحديث الإحصائيات حسب نوع النشاط
            switch (activityType)
            {
                case ActivityType.Login:
                    if (isSuccessful) stats.LoginCount++;
                    break;
                case ActivityType.Create:
                    if (isSuccessful) stats.FilesCreated++;
                    break;
                case ActivityType.Update:
                    if (isSuccessful) stats.FilesUpdated++;
                    break;
                case ActivityType.Delete:
                    if (isSuccessful) stats.FilesDeleted++;
                    break;
                case ActivityType.Print:
                    if (isSuccessful) stats.ReportsPrinted++;
                    break;
                case ActivityType.Export:
                    if (isSuccessful) stats.FilesExported++;
                    break;
            }

            if (isSuccessful)
                stats.ActionsPerformed++;
            else
                stats.ErrorCount++;

            stats.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الإحصائيات: {ex.Message}");
        }
    }

    /// <summary>
    /// الحصول على إحصائيات المستخدم
    /// </summary>
    public async Task<Models.UserManagement.UserStatistics?> GetUserStatisticsAsync(int userId, DateTime? date = null)
    {
        var targetDate = date ?? DateTime.Today;
        return await _context.UserStatistics
            .FirstOrDefaultAsync(us => us.UserId == userId && us.StatDate == targetDate);
    }

    /// <summary>
    /// الحصول على إحصائيات المستخدم لفترة
    /// </summary>
    public async Task<List<Models.UserManagement.UserStatistics>> GetUserStatisticsRangeAsync(
        int userId,
        DateTime fromDate,
        DateTime toDate)
    {
        return await _context.UserStatistics
            .Where(us => us.UserId == userId && us.StatDate >= fromDate && us.StatDate <= toDate)
            .OrderBy(us => us.StatDate)
            .ToListAsync();
    }

    /// <summary>
    /// تنظيف السجلات القديمة
    /// </summary>
    public async Task<int> CleanupOldLogsAsync(int daysToKeep = 90)
    {
        try
        {
            var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
            
            var oldActivities = await _context.UserActivities
                .Where(ua => ua.CreatedAt < cutoffDate)
                .ToListAsync();

            var oldLoginLogs = await _context.LoginLogs
                .Where(ll => ll.LoginTime < cutoffDate)
                .ToListAsync();

            _context.UserActivities.RemoveRange(oldActivities);
            _context.LoginLogs.RemoveRange(oldLoginLogs);

            await _context.SaveChangesAsync();

            return oldActivities.Count + oldLoginLogs.Count;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تنظيف السجلات: {ex.Message}");
            return 0;
        }
    }
}
