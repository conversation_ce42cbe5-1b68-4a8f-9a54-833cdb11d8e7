<Window x:Class="AvocatPro.Views.Windows.FinancialReportWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="التقارير المالية المتقدمة" 
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        ResizeMode="CanResize">

    <Window.Resources>
        <!-- أنماط النافذة -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1976D2"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="ReportCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="StatCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#1976D2"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="5"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1565C0"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="FilterComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Margin" Value="5,0"/>
        </Style>

        <Style x:Key="TabHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
        </Style>
    </Window.Resources>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- رأس النافذة -->
        <Border Grid.Row="0" Background="#1976D2" Padding="20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📊" FontSize="24" Foreground="White" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="التقارير المالية المتقدمة" FontSize="20" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="تقارير شاملة ومفصلة للأداء المالي مع رسوم بيانية وإحصائيات متقدمة" FontSize="12" Foreground="#E3F2FD"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- شريط التحكم -->
        <Border Grid.Row="1" Background="White" Padding="20" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- مرشحات الفترة -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="الفترة:" FontSize="14" FontWeight="Bold" 
                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                    
                    <ComboBox Name="PeriodComboBox" Style="{StaticResource FilterComboBoxStyle}"
                             SelectionChanged="PeriodComboBox_SelectionChanged">
                        <ComboBoxItem Content="هذا الشهر" Tag="ThisMonth" IsSelected="True"/>
                        <ComboBoxItem Content="الشهر الماضي" Tag="LastMonth"/>
                        <ComboBoxItem Content="آخر 3 أشهر" Tag="Last3Months"/>
                        <ComboBoxItem Content="آخر 6 أشهر" Tag="Last6Months"/>
                        <ComboBoxItem Content="هذا العام" Tag="ThisYear"/>
                        <ComboBoxItem Content="العام الماضي" Tag="LastYear"/>
                        <ComboBoxItem Content="فترة مخصصة" Tag="Custom"/>
                    </ComboBox>

                    <DatePicker Name="StartDatePicker" Style="{StaticResource FilterComboBoxStyle}" 
                               Visibility="Collapsed" SelectedDateChanged="DatePicker_SelectedDateChanged"/>
                    
                    <TextBlock Text="إلى" FontSize="12" VerticalAlignment="Center" 
                              Margin="10,0" Visibility="Collapsed" Name="ToLabel"/>
                    
                    <DatePicker Name="EndDatePicker" Style="{StaticResource FilterComboBoxStyle}" 
                               Visibility="Collapsed" SelectedDateChanged="DatePicker_SelectedDateChanged"/>
                </StackPanel>

                <!-- نوع التقرير -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                    <TextBlock Text="نوع التقرير:" FontSize="14" FontWeight="Bold" 
                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                    
                    <ComboBox Name="ReportTypeComboBox" Style="{StaticResource FilterComboBoxStyle}"
                             SelectionChanged="ReportTypeComboBox_SelectionChanged">
                        <ComboBoxItem Content="📊 تقرير شامل" Tag="Comprehensive" IsSelected="True"/>
                        <ComboBoxItem Content="💰 تقرير الإيرادات" Tag="Revenues"/>
                        <ComboBoxItem Content="💸 تقرير المصاريف" Tag="Expenses"/>
                        <ComboBoxItem Content="📈 تقرير الربحية" Tag="Profitability"/>
                        <ComboBoxItem Content="⏰ تقرير المستحقات" Tag="Receivables"/>
                        <ComboBoxItem Content="🔄 تقرير التدفق النقدي" Tag="CashFlow"/>
                    </ComboBox>
                </StackPanel>

                <!-- أزرار الإجراءات -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Name="GenerateReportButton" Style="{StaticResource PrimaryButtonStyle}" 
                           Click="GenerateReportButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📊" FontSize="14" Margin="0,0,5,0"/>
                            <TextBlock Text="إنشاء التقرير"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Name="ExportPdfButton" Style="{StaticResource PrimaryButtonStyle}" 
                           Background="#F44336" Click="ExportPdfButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📄" FontSize="14" Margin="0,0,5,0"/>
                            <TextBlock Text="تصدير PDF"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Name="ExportExcelButton" Style="{StaticResource PrimaryButtonStyle}" 
                           Background="#4CAF50" Click="ExportExcelButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📊" FontSize="14" Margin="0,0,5,0"/>
                            <TextBlock Text="تصدير Excel"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Name="PrintReportButton" Style="{StaticResource PrimaryButtonStyle}" 
                           Background="#FF9800" Click="PrintReportButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🖨️" FontSize="14" Margin="0,0,5,0"/>
                            <TextBlock Text="طباعة"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <TabControl Grid.Row="2" Margin="20" Background="White">
            <!-- تبويب الملخص التنفيذي -->
            <TabItem>
                <TabItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📋" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="الملخص التنفيذي" Style="{StaticResource TabHeaderStyle}"/>
                    </StackPanel>
                </TabItem.Header>
                
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
                    <StackPanel>
                        <!-- بطاقات الإحصائيات الرئيسية -->
                        <TextBlock Text="📊 الأداء المالي العام" Style="{StaticResource HeaderTextStyle}"/>
                        
                        <Grid Margin="0,0,0,30">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Border Grid.Column="0" Style="{StaticResource StatCardStyle}">
                                <StackPanel>
                                    <TextBlock Text="💰" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                    <TextBlock Text="إجمالي الإيرادات" FontSize="12" Foreground="#666" HorizontalAlignment="Center"/>
                                    <TextBlock Name="SummaryTotalRevenuesTextBlock" Text="0 ريال" FontSize="18" FontWeight="Bold" 
                                              Foreground="#4CAF50" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <Border Grid.Column="1" Style="{StaticResource StatCardStyle}">
                                <StackPanel>
                                    <TextBlock Text="💸" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                    <TextBlock Text="إجمالي المصاريف" FontSize="12" Foreground="#666" HorizontalAlignment="Center"/>
                                    <TextBlock Name="SummaryTotalExpensesTextBlock" Text="0 ريال" FontSize="18" FontWeight="Bold" 
                                              Foreground="#F44336" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <Border Grid.Column="2" Style="{StaticResource StatCardStyle}">
                                <StackPanel>
                                    <TextBlock Text="📈" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                    <TextBlock Text="صافي الربح" FontSize="12" Foreground="#666" HorizontalAlignment="Center"/>
                                    <TextBlock Name="SummaryNetProfitTextBlock" Text="0 ريال" FontSize="18" FontWeight="Bold" 
                                              Foreground="#2196F3" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <Border Grid.Column="3" Style="{StaticResource StatCardStyle}">
                                <StackPanel>
                                    <TextBlock Text="📊" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                    <TextBlock Text="هامش الربح" FontSize="12" Foreground="#666" HorizontalAlignment="Center"/>
                                    <TextBlock Name="SummaryProfitMarginTextBlock" Text="0%" FontSize="18" FontWeight="Bold" 
                                              Foreground="#9C27B0" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>
                        </Grid>

                        <!-- الرسوم البيانية -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- رسم بياني للاتجاه الشهري -->
                            <Border Grid.Column="0" Style="{StaticResource ReportCardStyle}">
                                <StackPanel>
                                    <TextBlock Text="📈 الاتجاه الشهري للإيرادات والمصاريف" Style="{StaticResource HeaderTextStyle}"/>
                                    <ScrollViewer Name="MonthlyTrendChart" Height="300" VerticalScrollBarVisibility="Auto">
                                        <StackPanel Name="MonthlyTrendChartPanel"/>
                                    </ScrollViewer>
                                </StackPanel>
                            </Border>

                            <!-- رسم بياني للتوزيع -->
                            <Border Grid.Column="1" Style="{StaticResource ReportCardStyle}">
                                <StackPanel>
                                    <TextBlock Text="🥧 توزيع المصاريف حسب الفئة" Style="{StaticResource HeaderTextStyle}"/>
                                    <ScrollViewer Name="CategoryDistributionChart" Height="300" VerticalScrollBarVisibility="Auto">
                                        <StackPanel Name="CategoryDistributionChartPanel"/>
                                    </ScrollViewer>
                                </StackPanel>
                            </Border>
                        </Grid>

                        <!-- تحليل الأداء -->
                        <Border Style="{StaticResource ReportCardStyle}" Margin="0,20,0,0">
                            <StackPanel>
                                <TextBlock Text="🔍 تحليل الأداء المالي" Style="{StaticResource HeaderTextStyle}"/>
                                <StackPanel Name="PerformanceAnalysisPanel">
                                    <!-- سيتم ملؤها ديناميكياً -->
                                </StackPanel>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- تبويب التقرير التفصيلي -->
            <TabItem>
                <TabItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📄" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="التقرير التفصيلي" Style="{StaticResource TabHeaderStyle}"/>
                    </StackPanel>
                </TabItem.Header>
                
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
                    <StackPanel Name="DetailedReportPanel">
                        <!-- سيتم ملؤها ديناميكياً حسب نوع التقرير المختار -->
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- تبويب المقارنات -->
            <TabItem>
                <TabItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="⚖️" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="المقارنات" Style="{StaticResource TabHeaderStyle}"/>
                    </StackPanel>
                </TabItem.Header>
                
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
                    <StackPanel>
                        <TextBlock Text="📊 مقارنة الأداء" Style="{StaticResource HeaderTextStyle}"/>
                        
                        <!-- مقارنة شهرية -->
                        <Border Style="{StaticResource ReportCardStyle}">
                            <StackPanel>
                                <TextBlock Text="📅 المقارنة الشهرية" FontSize="16" FontWeight="Bold" 
                                          Foreground="#1976D2" Margin="0,0,0,15"/>
                                <StackPanel Name="MonthlyComparisonPanel">
                                    <!-- سيتم ملؤها ديناميكياً -->
                                </StackPanel>
                            </StackPanel>
                        </Border>

                        <!-- مقارنة سنوية -->
                        <Border Style="{StaticResource ReportCardStyle}">
                            <StackPanel>
                                <TextBlock Text="📆 المقارنة السنوية" FontSize="16" FontWeight="Bold" 
                                          Foreground="#1976D2" Margin="0,0,0,15"/>
                                <StackPanel Name="YearlyComparisonPanel">
                                    <!-- سيتم ملؤها ديناميكياً -->
                                </StackPanel>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- تبويب التوقعات -->
            <TabItem>
                <TabItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🔮" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="التوقعات والتنبؤات" Style="{StaticResource TabHeaderStyle}"/>
                    </StackPanel>
                </TabItem.Header>
                
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
                    <StackPanel>
                        <TextBlock Text="🔮 التوقعات المالية" Style="{StaticResource HeaderTextStyle}"/>
                        
                        <Border Style="{StaticResource ReportCardStyle}">
                            <StackPanel>
                                <TextBlock Text="📈 توقعات الأشهر القادمة" FontSize="16" FontWeight="Bold" 
                                          Foreground="#1976D2" Margin="0,0,0,15"/>
                                <StackPanel Name="ForecastPanel">
                                    <!-- سيتم ملؤها ديناميكياً -->
                                </StackPanel>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>

        <!-- تذييل النافذة -->
        <Border Grid.Row="3" Background="White" Padding="15" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Name="ReportStatusTextBlock" Text="جاهز لإنشاء التقرير" FontSize="12" 
                              Foreground="#666" VerticalAlignment="Center"/>
                </StackPanel>

                <Button Name="CloseButton" Grid.Column="1" Style="{StaticResource PrimaryButtonStyle}" 
                       Background="#F44336" Click="CloseButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="❌" FontSize="16" Margin="0,0,8,0"/>
                        <TextBlock Text="إغلاق"/>
                    </StackPanel>
                </Button>
            </Grid>
        </Border>
    </Grid>
</Window>
