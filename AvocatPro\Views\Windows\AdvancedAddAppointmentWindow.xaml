<Window x:Class="AvocatPro.Views.Windows.AdvancedAddAppointmentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:fa="http://schemas.fontawesome.io/icons/"
        Title="إضافة موعد جديد" 
        Height="700" Width="900"
        WindowStartupLocation="CenterScreen"
        Background="#F8FAFC"
        ResizeMode="CanResize">

    <Window.Resources>
        <!-- أنماط مخصصة -->
        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#D1D5DB"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}" 
                                CornerRadius="8">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                          Margin="{TemplateBinding Padding}"
                                          VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#D1D5DB"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#6366F1"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="8" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#5B5BD6"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#4F46E5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="TabItemStyle" TargetType="TabItem">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#6B7280"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TabItem">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="8,8,0,0" 
                                Padding="{TemplateBinding Padding}"
                                Margin="2,0">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="White"/>
                                <Setter Property="Foreground" Value="#1F2937"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F3F4F6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Background="White" Padding="30,20" Margin="20,20,20,10">
            <Border.Effect>
                <DropShadowEffect Color="#E5E7EB" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.3"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <fa:ImageAwesome Grid.Column="0" Icon="CalendarPlus" Foreground="#6366F1" Width="32" Height="32" Margin="0,0,15,0"/>
                
                <StackPanel Grid.Column="1">
                    <TextBlock x:Name="WindowTitle" Text="إضافة موعد جديد" FontSize="24" FontWeight="Bold" Foreground="#1F2937"/>
                    <TextBlock Text="املأ جميع المعلومات المطلوبة لإنشاء موعد جديد" FontSize="14" Foreground="#6B7280" Margin="0,5,0,0"/>
                </StackPanel>

                <TextBlock Grid.Column="2" x:Name="AppointmentNumberText" Text="رقم الموعد: سيتم توليده تلقائياً" 
                           FontSize="12" Foreground="#6B7280" VerticalAlignment="Center"/>
            </Grid>
        </Border>

        <!-- التبويبات -->
        <TabControl Grid.Row="1" Background="Transparent" BorderThickness="0" Margin="20,10,20,10">
            <TabControl.Style>
                <Style TargetType="TabControl">
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="TabControl">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>
                                    
                                    <TabPanel Grid.Row="0" IsItemsHost="True" Background="#F3F4F6" 
                                              Margin="0,0,0,0" Panel.ZIndex="1"/>
                                    
                                    <Border Grid.Row="1" Background="White" CornerRadius="0,12,12,12" 
                                            Padding="30" BorderThickness="0">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#E5E7EB" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.3"/>
                                        </Border.Effect>
                                        <ContentPresenter ContentSource="SelectedContent"/>
                                    </Border>
                                </Grid>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </TabControl.Style>

            <!-- التبويب الأول: المعلومات الأساسية -->
            <TabItem Header="📋 المعلومات الأساسية" Style="{StaticResource TabItemStyle}">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- عنوان الموعد -->
                        <StackPanel Grid.Row="0" Grid.ColumnSpan="2" Margin="0,0,0,20">
                            <TextBlock Text="عنوان الموعد *" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                            <TextBox x:Name="TitleTextBox" Style="{StaticResource ModernTextBoxStyle}" 
                                     Text="استشارة قانونية"/>
                        </StackPanel>

                        <!-- نوع الموعد -->
                        <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,20">
                            <TextBlock Text="نوع الموعد *" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                            <ComboBox x:Name="AppointmentTypeComboBox" Style="{StaticResource ModernComboBoxStyle}"/>
                        </StackPanel>

                        <!-- الفئة -->
                        <StackPanel Grid.Row="1" Grid.Column="1" Margin="10,0,0,20">
                            <TextBlock Text="الفئة" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                            <ComboBox x:Name="CategoryComboBox" Style="{StaticResource ModernComboBoxStyle}"/>
                        </StackPanel>

                        <!-- التاريخ والوقت -->
                        <StackPanel Grid.Row="2" Grid.Column="0" Margin="0,0,10,20">
                            <TextBlock Text="تاريخ الموعد *" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                            <DatePicker x:Name="AppointmentDatePicker" FontSize="14" Padding="12,8"/>
                        </StackPanel>

                        <StackPanel Grid.Row="2" Grid.Column="1" Margin="10,0,0,20">
                            <TextBlock Text="وقت الموعد *" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <ComboBox x:Name="HourComboBox" Grid.Column="0" Style="{StaticResource ModernComboBoxStyle}" Margin="0,0,5,0"/>
                                <TextBlock Grid.Column="1" Text=":" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="16" FontWeight="Bold"/>
                                <ComboBox x:Name="MinuteComboBox" Grid.Column="2" Style="{StaticResource ModernComboBoxStyle}" Margin="5,0,0,0"/>
                            </Grid>
                        </StackPanel>

                        <!-- المدة والأولوية -->
                        <StackPanel Grid.Row="3" Grid.Column="0" Margin="0,0,10,20">
                            <TextBlock Text="مدة الموعد (بالدقائق)" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                            <TextBox x:Name="DurationTextBox" Style="{StaticResource ModernTextBoxStyle}" Text="60"/>
                        </StackPanel>

                        <StackPanel Grid.Row="3" Grid.Column="1" Margin="10,0,0,20">
                            <TextBlock Text="الأولوية *" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                            <ComboBox x:Name="PriorityComboBox" Style="{StaticResource ModernComboBoxStyle}"/>
                        </StackPanel>

                        <!-- الوصف -->
                        <StackPanel Grid.Row="4" Grid.ColumnSpan="2" Margin="0,0,0,20">
                            <TextBlock Text="وصف الموعد" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                            <TextBox x:Name="DescriptionTextBox" Style="{StaticResource ModernTextBoxStyle}" 
                                     Height="80" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
                        </StackPanel>
                    </Grid>
                </ScrollViewer>
            </TabItem>

            <!-- التبويب الثاني: معلومات الموكل -->
            <TabItem Header="👤 معلومات الموكل" Style="{StaticResource TabItemStyle}">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- اسم الموكل -->
                        <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,10,20">
                            <TextBlock Text="اسم الموكل *" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                            <TextBox x:Name="ClientNameTextBox" Style="{StaticResource ModernTextBoxStyle}"/>
                        </StackPanel>

                        <!-- رقم الهاتف -->
                        <StackPanel Grid.Row="0" Grid.Column="1" Margin="10,0,0,20">
                            <TextBlock Text="رقم الهاتف" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                            <TextBox x:Name="ClientPhoneTextBox" Style="{StaticResource ModernTextBoxStyle}"/>
                        </StackPanel>

                        <!-- البريد الإلكتروني -->
                        <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,20">
                            <TextBlock Text="البريد الإلكتروني" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                            <TextBox x:Name="ClientEmailTextBox" Style="{StaticResource ModernTextBoxStyle}"/>
                        </StackPanel>

                        <!-- المحامي المكلف -->
                        <StackPanel Grid.Row="1" Grid.Column="1" Margin="10,0,0,20">
                            <TextBlock Text="المحامي المكلف *" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                            <ComboBox x:Name="AssignedLawyerComboBox" Style="{StaticResource ModernComboBoxStyle}"/>
                        </StackPanel>

                        <!-- المكان -->
                        <StackPanel Grid.Row="2" Grid.ColumnSpan="2" Margin="0,0,0,20">
                            <TextBlock Text="مكان الموعد" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                            <TextBox x:Name="LocationTextBox" Style="{StaticResource ModernTextBoxStyle}" 
                                     Text="المكتب الرئيسي"/>
                        </StackPanel>

                        <!-- رقم الملف المرتبط -->
                        <StackPanel Grid.Row="3" Grid.Column="0" Margin="0,0,10,20">
                            <TextBlock Text="رقم الملف المرتبط" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                            <TextBox x:Name="RelatedFileNumberTextBox" Style="{StaticResource ModernTextBoxStyle}"/>
                        </StackPanel>

                        <!-- الرسوم -->
                        <StackPanel Grid.Row="3" Grid.Column="1" Margin="10,0,0,20">
                            <TextBlock Text="الرسوم (درهم)" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                            <TextBox x:Name="FeesTextBox" Style="{StaticResource ModernTextBoxStyle}" Text="0"/>
                        </StackPanel>
                    </Grid>
                </ScrollViewer>
            </TabItem>

            <!-- التبويب الثالث: التذكيرات والإشعارات -->
            <TabItem Header="🔔 التذكيرات والإشعارات" Style="{StaticResource TabItemStyle}">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- تفعيل التذكير -->
                        <CheckBox x:Name="ReminderEnabledCheckBox" Content="تفعيل التذكير" 
                                  FontSize="16" FontWeight="SemiBold" Foreground="#374151" 
                                  IsChecked="True" Margin="0,0,0,20"/>

                        <!-- وقت التذكير -->
                        <StackPanel Margin="0,0,0,20">
                            <TextBlock Text="وقت التذكير قبل الموعد" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                            <ComboBox x:Name="ReminderTimeComboBox" Style="{StaticResource ModernComboBoxStyle}"/>
                        </StackPanel>

                        <!-- أنواع التذكيرات -->
                        <TextBlock Text="أنواع التذكيرات" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,15"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <CheckBox x:Name="DesktopReminderCheckBox" Content="إشعار سطح المكتب" 
                                      FontSize="14" Foreground="#374151" IsChecked="True" Margin="0,0,30,0"/>
                            <CheckBox x:Name="EmailReminderCheckBox" Content="تذكير عبر البريد الإلكتروني" 
                                      FontSize="14" Foreground="#374151" Margin="0,0,30,0"/>
                            <CheckBox x:Name="SmsReminderCheckBox" Content="تذكير عبر الرسائل النصية" 
                                      FontSize="14" Foreground="#374151"/>
                        </StackPanel>

                        <!-- التكرار -->
                        <CheckBox x:Name="IsRecurringCheckBox" Content="موعد متكرر" 
                                  FontSize="16" FontWeight="SemiBold" Foreground="#374151" 
                                  Margin="0,20,0,15" Checked="IsRecurringCheckBox_Checked" Unchecked="IsRecurringCheckBox_Unchecked"/>

                        <StackPanel x:Name="RecurrencePanel" Visibility="Collapsed">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- نمط التكرار -->
                                <StackPanel Grid.Column="0" Margin="0,0,10,20">
                                    <TextBlock Text="نمط التكرار" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                                    <ComboBox x:Name="RecurrencePatternComboBox" Style="{StaticResource ModernComboBoxStyle}"/>
                                </StackPanel>

                                <!-- تاريخ انتهاء التكرار -->
                                <StackPanel Grid.Column="1" Margin="10,0,0,20">
                                    <TextBlock Text="تاريخ انتهاء التكرار" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                                    <DatePicker x:Name="RecurrenceEndDatePicker" FontSize="14" Padding="12,8"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>

                        <!-- الملاحظات -->
                        <StackPanel Margin="0,20,0,0">
                            <TextBlock Text="ملاحظات إضافية" FontWeight="SemiBold" Foreground="#374151" Margin="0,0,0,8"/>
                            <TextBox x:Name="NotesTextBox" Style="{StaticResource ModernTextBoxStyle}" 
                                     Height="100" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>

        <!-- أزرار الإجراءات -->
        <Border Grid.Row="2" Background="White" Padding="30,20" Margin="20,10,20,20">
            <Border.Effect>
                <DropShadowEffect Color="#E5E7EB" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.3"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- معلومات إضافية -->
                <StackPanel Grid.Column="0" VerticalAlignment="Center">
                    <TextBlock Text="💡 تلميح: يمكنك إنشاء مواعيد متكررة لتوفير الوقت" 
                               FontSize="12" Foreground="#6B7280"/>
                    <TextBlock Text="📧 سيتم إرسال التذكيرات حسب الإعدادات المحددة" 
                               FontSize="12" Foreground="#6B7280" Margin="0,2,0,0"/>
                </StackPanel>

                <!-- الأزرار -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="SaveButton" Style="{StaticResource ModernButtonStyle}" Click="SaveButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <fa:ImageAwesome Icon="Save" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="حفظ الموعد"/>
                        </StackPanel>
                    </Button>
                    <Button x:Name="SaveAndAddAnotherButton" Style="{StaticResource ModernButtonStyle}" 
                            Background="#10B981" Click="SaveAndAddAnotherButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <fa:ImageAwesome Icon="Plus" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="حفظ وإضافة آخر"/>
                        </StackPanel>
                    </Button>
                    <Button x:Name="CancelButton" Style="{StaticResource ModernButtonStyle}" 
                            Background="#6B7280" Click="CancelButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <fa:ImageAwesome Icon="Times" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="إلغاء"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
