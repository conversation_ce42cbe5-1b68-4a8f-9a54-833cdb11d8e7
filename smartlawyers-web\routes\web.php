<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\CaseController;
use App\Http\Controllers\AppointmentController;
use App\Http\Controllers\DocumentController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\CourtSessionController;
use App\Http\Controllers\ContractController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\IntegrationController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\WhatsAppController;
use App\Http\Controllers\CalendarController;
use App\Http\Controllers\MoroccanCourtsController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\Auth\AuthenticatedSessionController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

// Public routes
Route::get('/', function () {
    return view('welcome');
});

// Installation route
Route::get('/install', function () {
    if (file_exists(base_path('.env')) && config('app.key')) {
        return redirect()->route('login');
    }
    return view('install');
})->name('install');

// Authentication routes
require __DIR__.'/auth.php';

// Protected routes
Route::middleware(['auth', 'verified'])->group(function () {
    
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/stats', [DashboardController::class, 'getQuickStats'])->name('dashboard.stats');
    Route::get('/dashboard/calendar-events', [DashboardController::class, 'getCalendarEvents'])->name('dashboard.calendar-events');
    Route::get('/dashboard/moroccan-holidays', [DashboardController::class, 'getMoroccanHolidays'])->name('dashboard.moroccan-holidays');

    // Profile Management
    Route::prefix('profile')->name('profile.')->group(function () {
        Route::get('/', [ProfileController::class, 'edit'])->name('edit');
        Route::patch('/', [ProfileController::class, 'update'])->name('update');
        Route::delete('/', [ProfileController::class, 'destroy'])->name('destroy');
    });

    // Client Management
    Route::resource('clients', ClientController::class);
    Route::prefix('clients')->name('clients.')->group(function () {
        Route::get('{client}/cases', [ClientController::class, 'cases'])->name('cases');
        Route::get('{client}/appointments', [ClientController::class, 'appointments'])->name('appointments');
        Route::get('{client}/documents', [ClientController::class, 'documents'])->name('documents');
        Route::get('{client}/invoices', [ClientController::class, 'invoices'])->name('invoices');
        Route::post('{client}/send-whatsapp', [ClientController::class, 'sendWhatsApp'])->name('send-whatsapp');
        Route::get('search', [ClientController::class, 'search'])->name('search');
        Route::post('import', [ClientController::class, 'import'])->name('import');
        Route::get('export', [ClientController::class, 'export'])->name('export');
    });

    // Case Management
    Route::resource('cases', CaseController::class);
    Route::prefix('cases')->name('cases.')->group(function () {
        Route::get('{case}/timeline', [CaseController::class, 'timeline'])->name('timeline');
        Route::post('{case}/update-status', [CaseController::class, 'updateStatus'])->name('update-status');
        Route::get('{case}/documents', [CaseController::class, 'documents'])->name('documents');
        Route::get('{case}/sessions', [CaseController::class, 'sessions'])->name('sessions');
        Route::get('search', [CaseController::class, 'search'])->name('search');
        Route::post('import', [CaseController::class, 'import'])->name('import');
        Route::get('export', [CaseController::class, 'export'])->name('export');
    });

    // Appointment Management
    Route::resource('appointments', AppointmentController::class);
    Route::prefix('appointments')->name('appointments.')->group(function () {
        Route::post('{appointment}/confirm', [AppointmentController::class, 'confirm'])->name('confirm');
        Route::post('{appointment}/cancel', [AppointmentController::class, 'cancel'])->name('cancel');
        Route::post('{appointment}/reschedule', [AppointmentController::class, 'reschedule'])->name('reschedule');
        Route::get('calendar', [AppointmentController::class, 'calendar'])->name('calendar');
        Route::get('calendar-events', [AppointmentController::class, 'calendarEvents'])->name('calendar-events');
    });

    // Document Management
    Route::resource('documents', DocumentController::class);
    Route::prefix('documents')->name('documents.')->group(function () {
        Route::get('{document}/download', [DocumentController::class, 'download'])->name('download');
        Route::get('{document}/preview', [DocumentController::class, 'preview'])->name('preview');
        Route::post('{document}/share', [DocumentController::class, 'share'])->name('share');
        Route::get('search', [DocumentController::class, 'search'])->name('search');
        Route::post('upload-multiple', [DocumentController::class, 'uploadMultiple'])->name('upload-multiple');
    });

    // Invoice Management
    Route::resource('invoices', InvoiceController::class);
    Route::prefix('invoices')->name('invoices.')->group(function () {
        Route::get('{invoice}/pdf', [InvoiceController::class, 'generatePdf'])->name('pdf');
        Route::post('{invoice}/send', [InvoiceController::class, 'send'])->name('send');
        Route::post('{invoice}/mark-paid', [InvoiceController::class, 'markPaid'])->name('mark-paid');
        Route::get('overdue', [InvoiceController::class, 'overdue'])->name('overdue');
    });

    // Court Sessions
    Route::resource('court-sessions', CourtSessionController::class);
    Route::prefix('court-sessions')->name('court-sessions.')->group(function () {
        Route::get('calendar', [CourtSessionController::class, 'calendar'])->name('calendar');
        Route::post('{session}/add-note', [CourtSessionController::class, 'addNote'])->name('add-note');
        Route::post('{session}/upload-document', [CourtSessionController::class, 'uploadDocument'])->name('upload-document');
    });

    // Moroccan Contracts
    Route::resource('contracts', ContractController::class);
    Route::prefix('contracts')->name('contracts.')->group(function () {
        Route::get('templates', [ContractController::class, 'templates'])->name('templates');
        Route::post('generate', [ContractController::class, 'generate'])->name('generate');
        Route::get('{contract}/pdf', [ContractController::class, 'generatePdf'])->name('pdf');
        Route::post('{contract}/send', [ContractController::class, 'send'])->name('send');
    });

    // Reports
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/', [ReportController::class, 'index'])->name('index');
        Route::get('clients', [ReportController::class, 'clients'])->name('clients');
        Route::get('cases', [ReportController::class, 'cases'])->name('cases');
        Route::get('financial', [ReportController::class, 'financial'])->name('financial');
        Route::get('performance', [ReportController::class, 'performance'])->name('performance');
        Route::post('generate', [ReportController::class, 'generate'])->name('generate');
        Route::get('export/{type}', [ReportController::class, 'export'])->name('export');
    });

    // Integrations
    Route::prefix('integrations')->name('integrations.')->group(function () {
        Route::get('/', [IntegrationController::class, 'index'])->name('index');
        
        // Google Calendar
        Route::get('google-calendar/connect', [IntegrationController::class, 'connectGoogleCalendar'])->name('google-calendar.connect');
        Route::get('google-calendar/callback', [IntegrationController::class, 'googleCalendarCallback'])->name('google-calendar.callback');
        Route::post('google-calendar/sync', [IntegrationController::class, 'syncGoogleCalendar'])->name('google-calendar.sync');
        
        // Microsoft Outlook
        Route::get('outlook/connect', [IntegrationController::class, 'connectOutlook'])->name('outlook.connect');
        Route::get('outlook/callback', [IntegrationController::class, 'outlookCallback'])->name('outlook.callback');
        Route::post('outlook/sync', [IntegrationController::class, 'syncOutlook'])->name('outlook.sync');
        
        // WhatsApp Business
        Route::post('whatsapp/test', [IntegrationController::class, 'testWhatsApp'])->name('whatsapp.test');
        Route::get('whatsapp/status', [IntegrationController::class, 'whatsappStatus'])->name('whatsapp.status');
        
        // Moroccan Courts
        Route::post('courts/test', [IntegrationController::class, 'testCourts'])->name('courts.test');
        Route::get('courts/sync', [IntegrationController::class, 'syncCourts'])->name('courts.sync');
    });

    // Calendar
    Route::prefix('calendar')->name('calendar.')->group(function () {
        Route::get('/', [CalendarController::class, 'index'])->name('index');
        Route::get('events', [CalendarController::class, 'events'])->name('events');
        Route::post('events', [CalendarController::class, 'store'])->name('events.store');
        Route::put('events/{event}', [CalendarController::class, 'update'])->name('events.update');
        Route::delete('events/{event}', [CalendarController::class, 'destroy'])->name('events.destroy');
    });

    // Moroccan Courts Integration
    Route::prefix('moroccan-courts')->name('moroccan-courts.')->group(function () {
        Route::get('/', [MoroccanCourtsController::class, 'index'])->name('index');
        Route::get('search', [MoroccanCourtsController::class, 'search'])->name('search');
        Route::post('search-case', [MoroccanCourtsController::class, 'searchCase'])->name('search-case');
        Route::get('fees-calculator', [MoroccanCourtsController::class, 'feesCalculator'])->name('fees-calculator');
        Route::post('calculate-fees', [MoroccanCourtsController::class, 'calculateFees'])->name('calculate-fees');
        Route::get('courts', [MoroccanCourtsController::class, 'courts'])->name('courts');
        Route::post('submit-filing', [MoroccanCourtsController::class, 'submitFiling'])->name('submit-filing');
    });

    // Settings
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', [SettingsController::class, 'index'])->name('index');
        Route::post('/', [SettingsController::class, 'update'])->name('update');
        Route::get('general', [SettingsController::class, 'general'])->name('general');
        Route::get('notifications', [SettingsController::class, 'notifications'])->name('notifications');
        Route::get('integrations', [SettingsController::class, 'integrations'])->name('integrations');
        Route::get('backup', [SettingsController::class, 'backup'])->name('backup');
        Route::post('backup/create', [SettingsController::class, 'createBackup'])->name('backup.create');
        Route::get('backup/download/{file}', [SettingsController::class, 'downloadBackup'])->name('backup.download');
    });
});

// API Routes for mobile app and external integrations
Route::prefix('api')->middleware(['auth:sanctum'])->group(function () {
    Route::get('dashboard/stats', [DashboardController::class, 'getQuickStats']);
    Route::apiResource('clients', ClientController::class);
    Route::apiResource('cases', CaseController::class);
    Route::apiResource('appointments', AppointmentController::class);
    Route::apiResource('documents', DocumentController::class);
});

// Webhook Routes (no authentication required)
Route::prefix('webhooks')->group(function () {
    // WhatsApp webhook
    Route::get('whatsapp', [WhatsAppController::class, 'verifyWebhook']);
    Route::post('whatsapp', [WhatsAppController::class, 'handleWebhook']);
    
    // Payment webhooks
    Route::post('stripe', [InvoiceController::class, 'stripeWebhook']);
    
    // Calendar webhooks
    Route::post('google-calendar', [CalendarController::class, 'googleWebhook']);
    Route::post('outlook', [CalendarController::class, 'outlookWebhook']);
});

// Public API endpoints (with rate limiting)
Route::prefix('public-api')->middleware(['throttle:60,1'])->group(function () {
    Route::get('courts', [MoroccanCourtsController::class, 'publicCourts']);
    Route::post('fees-calculator', [MoroccanCourtsController::class, 'publicFeesCalculator']);
    Route::get('holidays', [DashboardController::class, 'getMoroccanHolidays']);
});

// Admin routes (for super admin users)
Route::prefix('admin')->middleware(['auth', 'role:admin'])->group(function () {
    Route::get('users', [SettingsController::class, 'users'])->name('admin.users');
    Route::get('system-logs', [SettingsController::class, 'systemLogs'])->name('admin.logs');
    Route::get('performance', [SettingsController::class, 'performance'])->name('admin.performance');
    Route::post('maintenance-mode', [SettingsController::class, 'maintenanceMode'])->name('admin.maintenance');
});

// Fallback route
Route::fallback(function () {
    return response()->view('errors.404', [], 404);
});
