﻿#pragma checksum "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C653B7A4F60815524F451094C695A63FE44E6475"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Windows {
    
    
    /// <summary>
    /// ComprehensiveAddFileWindow
    /// </summary>
    public partial class ComprehensiveAddFileWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 32 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WindowTitle;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FileNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CourtReferenceTextBox;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OfficeReferenceTextBox;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FileTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CourtComboBox;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CaseTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OpponentTextBox;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SubjectTextBox;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ElectronicTrackingCheckBox;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ElectronicCodePanel;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ElectronicFileCodeTextBox;
        
        #line default
        #line hidden
        
        
        #line 205 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel FileYearPanel;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FileYearTextBox;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AppealCourtPanel;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AppealCourtComboBox;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel SearchPrimaryPanel;
        
        #line default
        #line hidden
        
        
        #line 230 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox SearchInPrimaryCourtsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 246 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ClientTextBox;
        
        #line default
        #line hidden
        
        
        #line 255 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AssignedLawyerComboBox;
        
        #line default
        #line hidden
        
        
        #line 273 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ProcedureTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 290 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DecisionTextBox;
        
        #line default
        #line hidden
        
        
        #line 301 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker NextSessionDatePicker;
        
        #line default
        #line hidden
        
        
        #line 314 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 328 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PriorityComboBox;
        
        #line default
        #line hidden
        
        
        #line 341 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox EstimatedValueTextBox;
        
        #line default
        #line hidden
        
        
        #line 351 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 359 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsArchivedCheckBox;
        
        #line default
        #line hidden
        
        
        #line 380 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ValidateButton;
        
        #line default
        #line hidden
        
        
        #line 407 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        
        #line 432 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/windows/comprehensiveaddfilewindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.WindowTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 39 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.Close_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.FileNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.CourtReferenceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.OfficeReferenceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.FileTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.CourtComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.CaseTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.OpponentTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.SubjectTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.ElectronicTrackingCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 191 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
            this.ElectronicTrackingCheckBox.Checked += new System.Windows.RoutedEventHandler(this.ElectronicTracking_Checked);
            
            #line default
            #line hidden
            
            #line 191 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
            this.ElectronicTrackingCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.ElectronicTracking_Unchecked);
            
            #line default
            #line hidden
            return;
            case 12:
            this.ElectronicCodePanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 13:
            this.ElectronicFileCodeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.FileYearPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 15:
            this.FileYearTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.AppealCourtPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 17:
            this.AppealCourtComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 18:
            this.SearchPrimaryPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 19:
            this.SearchInPrimaryCourtsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 20:
            this.ClientTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.AssignedLawyerComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 22:
            this.ProcedureTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 23:
            this.DecisionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 24:
            this.NextSessionDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 25:
            this.StatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 26:
            this.PriorityComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 27:
            this.EstimatedValueTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 28:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 29:
            this.IsArchivedCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 30:
            this.ValidateButton = ((System.Windows.Controls.Button)(target));
            
            #line 383 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
            this.ValidateButton.Click += new System.Windows.RoutedEventHandler(this.Validate_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 410 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.Cancel_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 435 "..\..\..\..\..\Views\Windows\ComprehensiveAddFileWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.Save_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

