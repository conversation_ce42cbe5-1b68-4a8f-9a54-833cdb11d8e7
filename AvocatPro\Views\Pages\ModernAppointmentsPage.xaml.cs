using System.Windows;
using System.Windows.Controls;

namespace AvocatPro.Views.Pages
{
    /// <summary>
    /// صفحة إدارة المواعيد الحديثة
    /// </summary>
    public partial class ModernAppointmentsPage : Page
    {
        public ModernAppointmentsPage()
        {
            InitializeComponent();
        }

        /// <summary>
        /// إضافة موعد جديد
        /// </summary>
        private void AddAppointment_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم إضافة هذه الميزة قريباً", "قيد التطوير", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// رسالة قريباً
        /// </summary>
        private void ComingSoon_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("نعمل بجد لإنجاز هذا القسم. ترقبوا التحديثات القادمة!", "قريباً جداً", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
