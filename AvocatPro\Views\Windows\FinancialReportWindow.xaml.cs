using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using AvocatPro.Models;

namespace AvocatPro.Views.Windows;

public partial class FinancialReportWindow : Window
{
    private readonly List<ExpenseDisplayModel> _expenses;
    private readonly List<RevenueDisplayModel> _revenues;
    private DateTime _periodStart;
    private DateTime _periodEnd;
    private string _currentReportType = "Comprehensive";

    public FinancialReportWindow(List<ExpenseDisplayModel> expenses, List<RevenueDisplayModel> revenues)
    {
        InitializeComponent();
        _expenses = expenses ?? new List<ExpenseDisplayModel>();
        _revenues = revenues ?? new List<RevenueDisplayModel>();
        
        SetCurrentPeriod();
        this.Loaded += FinancialReportWindow_Loaded;
    }

    private void FinancialReportWindow_Loaded(object sender, RoutedEventArgs e)
    {
        GenerateReport();
    }

    private void SetCurrentPeriod()
    {
        var now = DateTime.Now;
        _periodStart = new DateTime(now.Year, now.Month, 1);
        _periodEnd = _periodStart.AddMonths(1).AddDays(-1);
    }

    private void PeriodComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (PeriodComboBox?.SelectedItem is ComboBoxItem selectedItem)
        {
            var tag = selectedItem.Tag?.ToString();
            var now = DateTime.Now;

            switch (tag)
            {
                case "ThisMonth":
                    _periodStart = new DateTime(now.Year, now.Month, 1);
                    _periodEnd = _periodStart.AddMonths(1).AddDays(-1);
                    break;
                case "LastMonth":
                    _periodStart = new DateTime(now.Year, now.Month, 1).AddMonths(-1);
                    _periodEnd = _periodStart.AddMonths(1).AddDays(-1);
                    break;
                case "Last3Months":
                    _periodStart = new DateTime(now.Year, now.Month, 1).AddMonths(-3);
                    _periodEnd = new DateTime(now.Year, now.Month, 1).AddDays(-1);
                    break;
                case "Last6Months":
                    _periodStart = new DateTime(now.Year, now.Month, 1).AddMonths(-6);
                    _periodEnd = new DateTime(now.Year, now.Month, 1).AddDays(-1);
                    break;
                case "ThisYear":
                    _periodStart = new DateTime(now.Year, 1, 1);
                    _periodEnd = new DateTime(now.Year, 12, 31);
                    break;
                case "LastYear":
                    _periodStart = new DateTime(now.Year - 1, 1, 1);
                    _periodEnd = new DateTime(now.Year - 1, 12, 31);
                    break;
                case "Custom":
                    ShowCustomDatePickers();
                    return;
            }

            HideCustomDatePickers();
            GenerateReport();
        }
    }

    private void ShowCustomDatePickers()
    {
        if (StartDatePicker != null && EndDatePicker != null && ToLabel != null)
        {
            StartDatePicker.Visibility = Visibility.Visible;
            EndDatePicker.Visibility = Visibility.Visible;
            ToLabel.Visibility = Visibility.Visible;
            
            StartDatePicker.SelectedDate = _periodStart;
            EndDatePicker.SelectedDate = _periodEnd;
        }
    }

    private void HideCustomDatePickers()
    {
        if (StartDatePicker != null && EndDatePicker != null && ToLabel != null)
        {
            StartDatePicker.Visibility = Visibility.Collapsed;
            EndDatePicker.Visibility = Visibility.Collapsed;
            ToLabel.Visibility = Visibility.Collapsed;
        }
    }

    private void DatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
    {
        if (StartDatePicker?.SelectedDate.HasValue == true && EndDatePicker?.SelectedDate.HasValue == true)
        {
            _periodStart = StartDatePicker.SelectedDate.Value;
            _periodEnd = EndDatePicker.SelectedDate.Value;
            GenerateReport();
        }
    }

    private void ReportTypeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (ReportTypeComboBox?.SelectedItem is ComboBoxItem selectedItem)
        {
            _currentReportType = selectedItem.Tag?.ToString() ?? "Comprehensive";
            GenerateReport();
        }
    }

    private void GenerateReportButton_Click(object sender, RoutedEventArgs e)
    {
        GenerateReport();
        MessageBox.Show("تم إنشاء التقرير بنجاح!", "إنشاء التقرير", 
                       MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void GenerateReport()
    {
        try
        {
            ReportStatusTextBlock.Text = "جاري إنشاء التقرير...";
            
            var filteredExpenses = _expenses.Where(e => e.ExpenseDate >= _periodStart && e.ExpenseDate <= _periodEnd).ToList();
            var filteredRevenues = _revenues.Where(r => r.RevenueDate >= _periodStart && r.RevenueDate <= _periodEnd).ToList();

            UpdateSummaryStatistics(filteredExpenses, filteredRevenues);
            BuildMonthlyTrendChart(filteredExpenses, filteredRevenues);
            BuildCategoryDistributionChart(filteredExpenses);
            BuildPerformanceAnalysis(filteredExpenses, filteredRevenues);
            BuildDetailedReport(filteredExpenses, filteredRevenues);
            BuildComparisons(filteredExpenses, filteredRevenues);
            BuildForecasts(filteredExpenses, filteredRevenues);

            ReportStatusTextBlock.Text = $"تم إنشاء التقرير للفترة: {_periodStart:dd/MM/yyyy} - {_periodEnd:dd/MM/yyyy}";
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ", 
                           MessageBoxButton.OK, MessageBoxImage.Error);
            ReportStatusTextBlock.Text = "خطأ في إنشاء التقرير";
        }
    }

    private void UpdateSummaryStatistics(List<ExpenseDisplayModel> expenses, List<RevenueDisplayModel> revenues)
    {
        var totalRevenues = revenues.Sum(r => r.Amount);
        var totalExpenses = expenses.Sum(e => e.Amount);
        var netProfit = totalRevenues - totalExpenses;
        var profitMargin = totalRevenues > 0 ? (netProfit / totalRevenues) * 100 : 0;

        SummaryTotalRevenuesTextBlock.Text = totalRevenues.ToString("N0") + " درهم";
        SummaryTotalExpensesTextBlock.Text = totalExpenses.ToString("N0") + " درهم";
        SummaryNetProfitTextBlock.Text = netProfit.ToString("N0") + " درهم";
        SummaryNetProfitTextBlock.Foreground = new SolidColorBrush(netProfit >= 0 ? Colors.Green : Colors.Red);
        SummaryProfitMarginTextBlock.Text = profitMargin.ToString("F1") + "%";
        SummaryProfitMarginTextBlock.Foreground = new SolidColorBrush(profitMargin >= 0 ? Colors.Green : Colors.Red);
    }

    private void BuildMonthlyTrendChart(List<ExpenseDisplayModel> expenses, List<RevenueDisplayModel> revenues)
    {
        MonthlyTrendChartPanel.Children.Clear();

        var monthlyData = new Dictionary<string, (decimal Revenues, decimal Expenses)>();
        
        foreach (var revenue in revenues)
        {
            var monthKey = revenue.MonthYear;
            if (!monthlyData.ContainsKey(monthKey))
                monthlyData[monthKey] = (0, 0);
            monthlyData[monthKey] = (monthlyData[monthKey].Revenues + revenue.Amount, monthlyData[monthKey].Expenses);
        }

        foreach (var expense in expenses)
        {
            var monthKey = expense.MonthYear;
            if (!monthlyData.ContainsKey(monthKey))
                monthlyData[monthKey] = (0, 0);
            monthlyData[monthKey] = (monthlyData[monthKey].Revenues, monthlyData[monthKey].Expenses + expense.Amount);
        }

        var maxAmount = monthlyData.Values.Max(v => Math.Max(v.Revenues, v.Expenses));

        foreach (var kvp in monthlyData.OrderBy(x => x.Key))
        {
            var monthPanel = new StackPanel { Margin = new Thickness(0, 10, 0, 10) };
            
            var monthLabel = new TextBlock
            {
                Text = DateTime.ParseExact(kvp.Key, "yyyy-MM", CultureInfo.InvariantCulture).ToString("MMMM yyyy", new CultureInfo("ar-SA")),
                FontWeight = FontWeights.Bold,
                FontSize = 14,
                Margin = new Thickness(0, 0, 0, 8)
            };
            monthPanel.Children.Add(monthLabel);

            var revenueBar = CreateChartBar("الإيرادات", kvp.Value.Revenues, Colors.Green, 250, maxAmount);
            var expenseBar = CreateChartBar("المصاريف", kvp.Value.Expenses, Colors.Red, 250, maxAmount);
            var profitBar = CreateChartBar("صافي الربح", kvp.Value.Revenues - kvp.Value.Expenses, 
                kvp.Value.Revenues >= kvp.Value.Expenses ? Colors.Blue : Colors.Orange, 250, maxAmount);
            
            monthPanel.Children.Add(revenueBar);
            monthPanel.Children.Add(expenseBar);
            monthPanel.Children.Add(profitBar);
            
            MonthlyTrendChartPanel.Children.Add(monthPanel);
        }
    }

    private void BuildCategoryDistributionChart(List<ExpenseDisplayModel> expenses)
    {
        CategoryDistributionChartPanel.Children.Clear();

        var categoryData = expenses
            .GroupBy(e => e.Category)
            .Select(g => new { Category = g.Key, Amount = g.Sum(e => e.Amount) })
            .OrderByDescending(x => x.Amount);

        var totalAmount = categoryData.Sum(x => x.Amount);

        foreach (var item in categoryData)
        {
            var percentage = totalAmount > 0 ? (item.Amount / totalAmount) * 100 : 0;
            
            var categoryDisplay = item.Category switch
            {
                ExpenseCategory.Administrative => "مصاريف إدارية",
                ExpenseCategory.Legal => "مصاريف قانونية",
                ExpenseCategory.Operational => "مصاريف تشغيلية",
                ExpenseCategory.Technology => "مصاريف تقنية",
                ExpenseCategory.Marketing => "مصاريف تسويق",
                ExpenseCategory.Travel => "مصاريف سفر",
                _ => "أخرى"
            };

            var color = item.Category switch
            {
                ExpenseCategory.Administrative => Colors.Blue,
                ExpenseCategory.Legal => Colors.Purple,
                ExpenseCategory.Operational => Colors.Orange,
                ExpenseCategory.Technology => Colors.Green,
                ExpenseCategory.Marketing => Colors.Pink,
                ExpenseCategory.Travel => Colors.Cyan,
                _ => Colors.Gray
            };

            var categoryPanel = new StackPanel { Margin = new Thickness(0, 5, 0, 5) };
            
            var labelText = new TextBlock
            {
                Text = $"{categoryDisplay}: {item.Amount:N0} ريال ({percentage:F1}%)",
                FontSize = 12,
                Margin = new Thickness(0, 0, 0, 3)
            };
            categoryPanel.Children.Add(labelText);

            var barContainer = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(240, 240, 240)),
                Height = 20,
                Width = 200
            };

            var barWidth = totalAmount > 0 ? (double)(item.Amount / totalAmount) * 200 : 0;
            var bar = new Border
            {
                Background = new SolidColorBrush(color),
                Height = 20,
                Width = Math.Max(barWidth, 1),
                HorizontalAlignment = HorizontalAlignment.Left
            };

            barContainer.Child = bar;
            categoryPanel.Children.Add(barContainer);
            
            CategoryDistributionChartPanel.Children.Add(categoryPanel);
        }
    }

    private StackPanel CreateChartBar(string label, decimal value, Color color, double maxWidth, decimal? maxValue = null)
    {
        var panel = new StackPanel { Margin = new Thickness(0, 3, 0, 3) };
        
        var labelText = new TextBlock
        {
            Text = $"{label}: {value:N0} ريال",
            FontSize = 11,
            Margin = new Thickness(0, 0, 0, 2)
        };
        panel.Children.Add(labelText);

        var barContainer = new Border
        {
            Background = new SolidColorBrush(Color.FromRgb(240, 240, 240)),
            Height = 15,
            Width = maxWidth
        };

        var barWidth = maxValue.HasValue && maxValue > 0 ? 
            (double)(Math.Abs(value) / maxValue.Value) * maxWidth : 
            Math.Min((double)Math.Abs(value) / 10000 * maxWidth, maxWidth);

        var bar = new Border
        {
            Background = new SolidColorBrush(color),
            Height = 15,
            Width = Math.Max(barWidth, 1),
            HorizontalAlignment = HorizontalAlignment.Left
        };

        barContainer.Child = bar;
        panel.Children.Add(barContainer);

        return panel;
    }

    private void BuildPerformanceAnalysis(List<ExpenseDisplayModel> expenses, List<RevenueDisplayModel> revenues)
    {
        PerformanceAnalysisPanel.Children.Clear();

        var totalRevenues = revenues.Sum(r => r.Amount);
        var totalExpenses = expenses.Sum(e => e.Amount);
        var netProfit = totalRevenues - totalExpenses;
        var profitMargin = totalRevenues > 0 ? (netProfit / totalRevenues) * 100 : 0;

        var analysisItems = new[]
        {
            ("الوضع المالي العام", netProfit >= 0 ? "مربح ✅" : "خسارة ❌", netProfit >= 0 ? Colors.Green : Colors.Red),
            ("هامش الربح", $"{profitMargin:F1}%", profitMargin >= 20 ? Colors.Green : profitMargin >= 10 ? Colors.Orange : Colors.Red),
            ("متوسط الإيراد الشهري", $"{(totalRevenues / Math.Max(1, GetMonthsInPeriod())):N0} ريال", Colors.Blue),
            ("متوسط المصاريف الشهرية", $"{(totalExpenses / Math.Max(1, GetMonthsInPeriod())):N0} ريال", Colors.Purple),
            ("الإيرادات المعلقة", $"{revenues.Where(r => r.IsPending).Sum(r => r.Amount):N0} ريال", Colors.Orange),
            ("الإيرادات المتأخرة", $"{revenues.Where(r => r.IsOverdue).Sum(r => r.Amount):N0} ريال", Colors.Red),
            ("المصاريف القابلة للاسترداد", $"{expenses.Where(e => e.IsReimbursable && !e.IsReimbursed).Sum(e => e.Amount):N0} درهم", Colors.Green)
        };

        foreach (var (label, value, color) in analysisItems)
        {
            var itemPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 8, 0, 8) };

            var labelText = new TextBlock
            {
                Text = label + ":",
                FontWeight = FontWeights.Bold,
                Width = 200,
                VerticalAlignment = VerticalAlignment.Center,
                FontSize = 13
            };

            var valueText = new TextBlock
            {
                Text = value,
                Foreground = new SolidColorBrush(color),
                FontWeight = FontWeights.Bold,
                VerticalAlignment = VerticalAlignment.Center,
                FontSize = 13
            };

            itemPanel.Children.Add(labelText);
            itemPanel.Children.Add(valueText);
            PerformanceAnalysisPanel.Children.Add(itemPanel);
        }
    }

    private void BuildDetailedReport(List<ExpenseDisplayModel> expenses, List<RevenueDisplayModel> revenues)
    {
        DetailedReportPanel.Children.Clear();

        switch (_currentReportType)
        {
            case "Comprehensive":
                BuildComprehensiveReport(expenses, revenues);
                break;
            case "Revenues":
                BuildRevenuesReport(revenues);
                break;
            case "Expenses":
                BuildExpensesReport(expenses);
                break;
            case "Profitability":
                BuildProfitabilityReport(expenses, revenues);
                break;
            case "Receivables":
                BuildReceivablesReport(revenues);
                break;
            case "CashFlow":
                BuildCashFlowReport(expenses, revenues);
                break;
        }
    }

    private void BuildComprehensiveReport(List<ExpenseDisplayModel> expenses, List<RevenueDisplayModel> revenues)
    {
        var headerText = new TextBlock
        {
            Text = "📊 التقرير المالي الشامل",
            FontSize = 18,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush(Color.FromRgb(25, 118, 210)),
            Margin = new Thickness(0, 0, 0, 20)
        };
        DetailedReportPanel.Children.Add(headerText);

        // ملخص الإيرادات
        var revenuesSection = CreateReportSection("💰 ملخص الإيرادات", revenues.Count, revenues.Sum(r => r.Amount));
        DetailedReportPanel.Children.Add(revenuesSection);

        // ملخص المصاريف
        var expensesSection = CreateReportSection("💸 ملخص المصاريف", expenses.Count, expenses.Sum(e => e.Amount));
        DetailedReportPanel.Children.Add(expensesSection);

        // صافي الربح
        var netProfit = revenues.Sum(r => r.Amount) - expenses.Sum(e => e.Amount);
        var profitSection = CreateReportSection("📈 صافي الربح", 1, netProfit);
        DetailedReportPanel.Children.Add(profitSection);
    }

    private void BuildRevenuesReport(List<RevenueDisplayModel> revenues)
    {
        var headerText = new TextBlock
        {
            Text = "💰 تقرير الإيرادات التفصيلي",
            FontSize = 18,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80)),
            Margin = new Thickness(0, 0, 0, 20)
        };
        DetailedReportPanel.Children.Add(headerText);

        var typeGroups = revenues.GroupBy(r => r.Type);
        foreach (var group in typeGroups)
        {
            var typeDisplay = group.Key switch
            {
                RevenueType.LawyerFees => "أتعاب محاماة",
                RevenueType.ConsultationFees => "أتعاب استشارة",
                RevenueType.ContractFees => "أتعاب عقود",
                RevenueType.ArbitrationFees => "أتعاب تحكيم",
                RevenueType.Other => "أخرى",
                _ => "غير محدد"
            };

            var section = CreateReportSection($"⚖️ {typeDisplay}", group.Count(), group.Sum(r => r.Amount));
            DetailedReportPanel.Children.Add(section);
        }
    }

    private void BuildExpensesReport(List<ExpenseDisplayModel> expenses)
    {
        var headerText = new TextBlock
        {
            Text = "💸 تقرير المصاريف التفصيلي",
            FontSize = 18,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush(Color.FromRgb(244, 67, 54)),
            Margin = new Thickness(0, 0, 0, 20)
        };
        DetailedReportPanel.Children.Add(headerText);

        var categoryGroups = expenses.GroupBy(e => e.Category);
        foreach (var group in categoryGroups)
        {
            var categoryDisplay = group.Key switch
            {
                ExpenseCategory.Administrative => "مصاريف إدارية",
                ExpenseCategory.Legal => "مصاريف قانونية",
                ExpenseCategory.Operational => "مصاريف تشغيلية",
                ExpenseCategory.Technology => "مصاريف تقنية",
                ExpenseCategory.Marketing => "مصاريف تسويق",
                ExpenseCategory.Travel => "مصاريف سفر",
                _ => "أخرى"
            };

            var section = CreateReportSection($"🏢 {categoryDisplay}", group.Count(), group.Sum(e => e.Amount));
            DetailedReportPanel.Children.Add(section);
        }
    }

    private void BuildProfitabilityReport(List<ExpenseDisplayModel> expenses, List<RevenueDisplayModel> revenues)
    {
        var headerText = new TextBlock
        {
            Text = "📈 تقرير الربحية",
            FontSize = 18,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush(Color.FromRgb(33, 150, 243)),
            Margin = new Thickness(0, 0, 0, 20)
        };
        DetailedReportPanel.Children.Add(headerText);

        var totalRevenues = revenues.Sum(r => r.Amount);
        var totalExpenses = expenses.Sum(e => e.Amount);
        var netProfit = totalRevenues - totalExpenses;
        var profitMargin = totalRevenues > 0 ? (netProfit / totalRevenues) * 100 : 0;

        var profitabilityItems = new[]
        {
            ("إجمالي الإيرادات", totalRevenues),
            ("إجمالي المصاريف", totalExpenses),
            ("إجمالي الربح", netProfit),
            ("هامش الربح", profitMargin)
        };

        foreach (var (label, value) in profitabilityItems)
        {
            var suffix = label == "هامش الربح" ? "%" : " درهم";
            var section = CreateReportSection($"📊 {label}", 1, value, suffix);
            DetailedReportPanel.Children.Add(section);
        }
    }

    private void BuildReceivablesReport(List<RevenueDisplayModel> revenues)
    {
        var headerText = new TextBlock
        {
            Text = "⏰ تقرير المستحقات",
            FontSize = 18,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush(Color.FromRgb(255, 152, 0)),
            Margin = new Thickness(0, 0, 0, 20)
        };
        DetailedReportPanel.Children.Add(headerText);

        var pendingRevenues = revenues.Where(r => r.IsPending).ToList();
        var overdueRevenues = revenues.Where(r => r.IsOverdue).ToList();

        var pendingSection = CreateReportSection("⏳ إيرادات في الانتظار", pendingRevenues.Count, pendingRevenues.Sum(r => r.Amount));
        var overdueSection = CreateReportSection("❌ إيرادات متأخرة", overdueRevenues.Count, overdueRevenues.Sum(r => r.Amount));

        DetailedReportPanel.Children.Add(pendingSection);
        DetailedReportPanel.Children.Add(overdueSection);
    }

    private void BuildCashFlowReport(List<ExpenseDisplayModel> expenses, List<RevenueDisplayModel> revenues)
    {
        var headerText = new TextBlock
        {
            Text = "🔄 تقرير التدفق النقدي",
            FontSize = 18,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush(Color.FromRgb(156, 39, 176)),
            Margin = new Thickness(0, 0, 0, 20)
        };
        DetailedReportPanel.Children.Add(headerText);

        var cashInflow = revenues.Where(r => r.IsPaid).Sum(r => r.Amount);
        var cashOutflow = expenses.Where(e => e.IsPaid).Sum(e => e.Amount);
        var netCashFlow = cashInflow - cashOutflow;

        var inflowSection = CreateReportSection("💰 التدفق النقدي الداخل", revenues.Count(r => r.IsPaid), cashInflow);
        var outflowSection = CreateReportSection("💸 التدفق النقدي الخارج", expenses.Count(e => e.IsPaid), cashOutflow);
        var netFlowSection = CreateReportSection("🔄 صافي التدفق النقدي", 1, netCashFlow);

        DetailedReportPanel.Children.Add(inflowSection);
        DetailedReportPanel.Children.Add(outflowSection);
        DetailedReportPanel.Children.Add(netFlowSection);
    }

    private Border CreateReportSection(string title, int count, decimal amount, string suffix = " ريال")
    {
        var border = new Border
        {
            Background = Brushes.White,
            BorderBrush = new SolidColorBrush(Color.FromRgb(224, 224, 224)),
            BorderThickness = new Thickness(1),
            CornerRadius = new CornerRadius(5),
            Padding = new Thickness(15),
            Margin = new Thickness(0, 0, 0, 10)
        };

        var panel = new StackPanel();

        var titleText = new TextBlock
        {
            Text = title,
            FontSize = 14,
            FontWeight = FontWeights.Bold,
            Margin = new Thickness(0, 0, 0, 8)
        };

        var detailsPanel = new StackPanel { Orientation = Orientation.Horizontal };

        var countText = new TextBlock
        {
            Text = $"العدد: {count}",
            FontSize = 12,
            Margin = new Thickness(0, 0, 20, 0)
        };

        var amountText = new TextBlock
        {
            Text = $"المبلغ: {amount:N0}{suffix}",
            FontSize = 12,
            FontWeight = FontWeights.Bold,
            Foreground = amount >= 0 ? Brushes.Green : Brushes.Red
        };

        detailsPanel.Children.Add(countText);
        detailsPanel.Children.Add(amountText);

        panel.Children.Add(titleText);
        panel.Children.Add(detailsPanel);

        border.Child = panel;
        return border;
    }

    private void BuildComparisons(List<ExpenseDisplayModel> expenses, List<RevenueDisplayModel> revenues)
    {
        BuildMonthlyComparison(expenses, revenues);
        BuildYearlyComparison(expenses, revenues);
    }

    private void BuildMonthlyComparison(List<ExpenseDisplayModel> expenses, List<RevenueDisplayModel> revenues)
    {
        MonthlyComparisonPanel.Children.Clear();

        var currentMonthRevenues = revenues.Where(r => r.RevenueDate.Month == DateTime.Now.Month).Sum(r => r.Amount);
        var lastMonthRevenues = revenues.Where(r => r.RevenueDate.Month == DateTime.Now.AddMonths(-1).Month).Sum(r => r.Amount);
        var revenueChange = lastMonthRevenues > 0 ? ((currentMonthRevenues - lastMonthRevenues) / lastMonthRevenues) * 100 : 0;

        var currentMonthExpenses = expenses.Where(e => e.ExpenseDate.Month == DateTime.Now.Month).Sum(e => e.Amount);
        var lastMonthExpenses = expenses.Where(e => e.ExpenseDate.Month == DateTime.Now.AddMonths(-1).Month).Sum(e => e.Amount);
        var expenseChange = lastMonthExpenses > 0 ? ((currentMonthExpenses - lastMonthExpenses) / lastMonthExpenses) * 100 : 0;

        var comparisonItems = new[]
        {
            ("الإيرادات الشهر الحالي", currentMonthRevenues, " ريال"),
            ("الإيرادات الشهر الماضي", lastMonthRevenues, " ريال"),
            ("نسبة التغيير في الإيرادات", revenueChange, "%"),
            ("المصاريف الشهر الحالي", currentMonthExpenses, " ريال"),
            ("المصاريف الشهر الماضي", lastMonthExpenses, " ريال"),
            ("نسبة التغيير في المصاريف", expenseChange, "%")
        };

        foreach (var (label, value, suffix) in comparisonItems)
        {
            var itemPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 5, 0, 5) };

            var labelText = new TextBlock
            {
                Text = label + ":",
                FontWeight = FontWeights.Bold,
                Width = 200,
                VerticalAlignment = VerticalAlignment.Center
            };

            var valueText = new TextBlock
            {
                Text = $"{value:N1}{suffix}",
                Foreground = suffix == "%" ? (value >= 0 ? Brushes.Green : Brushes.Red) : Brushes.Black,
                FontWeight = FontWeights.Bold,
                VerticalAlignment = VerticalAlignment.Center
            };

            itemPanel.Children.Add(labelText);
            itemPanel.Children.Add(valueText);
            MonthlyComparisonPanel.Children.Add(itemPanel);
        }
    }

    private void BuildYearlyComparison(List<ExpenseDisplayModel> expenses, List<RevenueDisplayModel> revenues)
    {
        YearlyComparisonPanel.Children.Clear();

        var currentYearRevenues = revenues.Where(r => r.RevenueDate.Year == DateTime.Now.Year).Sum(r => r.Amount);
        var lastYearRevenues = revenues.Where(r => r.RevenueDate.Year == DateTime.Now.Year - 1).Sum(r => r.Amount);
        var revenueChange = lastYearRevenues > 0 ? ((currentYearRevenues - lastYearRevenues) / lastYearRevenues) * 100 : 0;

        var currentYearExpenses = expenses.Where(e => e.ExpenseDate.Year == DateTime.Now.Year).Sum(e => e.Amount);
        var lastYearExpenses = expenses.Where(e => e.ExpenseDate.Year == DateTime.Now.Year - 1).Sum(e => e.Amount);
        var expenseChange = lastYearExpenses > 0 ? ((currentYearExpenses - lastYearExpenses) / lastYearExpenses) * 100 : 0;

        var comparisonItems = new[]
        {
            ("الإيرادات هذا العام", currentYearRevenues, " ريال"),
            ("الإيرادات العام الماضي", lastYearRevenues, " ريال"),
            ("نسبة التغيير في الإيرادات", revenueChange, "%"),
            ("المصاريف هذا العام", currentYearExpenses, " ريال"),
            ("المصاريف العام الماضي", lastYearExpenses, " ريال"),
            ("نسبة التغيير في المصاريف", expenseChange, "%")
        };

        foreach (var (label, value, suffix) in comparisonItems)
        {
            var itemPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 5, 0, 5) };

            var labelText = new TextBlock
            {
                Text = label + ":",
                FontWeight = FontWeights.Bold,
                Width = 200,
                VerticalAlignment = VerticalAlignment.Center
            };

            var valueText = new TextBlock
            {
                Text = $"{value:N1}{suffix}",
                Foreground = suffix == "%" ? (value >= 0 ? Brushes.Green : Brushes.Red) : Brushes.Black,
                FontWeight = FontWeights.Bold,
                VerticalAlignment = VerticalAlignment.Center
            };

            itemPanel.Children.Add(labelText);
            itemPanel.Children.Add(valueText);
            YearlyComparisonPanel.Children.Add(itemPanel);
        }
    }

    private void BuildForecasts(List<ExpenseDisplayModel> expenses, List<RevenueDisplayModel> revenues)
    {
        ForecastPanel.Children.Clear();

        var monthsInPeriod = GetMonthsInPeriod();
        var avgMonthlyRevenues = monthsInPeriod > 0 ? revenues.Sum(r => r.Amount) / monthsInPeriod : 0;
        var avgMonthlyExpenses = monthsInPeriod > 0 ? expenses.Sum(e => e.Amount) / monthsInPeriod : 0;

        var forecastItems = new[]
        {
            ("متوسط الإيرادات الشهرية", avgMonthlyRevenues, " درهم"),
            ("متوسط المصاريف الشهرية", avgMonthlyExpenses, " درهم"),
            ("التوقع للشهر القادم - الإيرادات", avgMonthlyRevenues, " درهم"),
            ("التوقع للشهر القادم - المصاريف", avgMonthlyExpenses, " درهم"),
            ("التوقع للشهر القادم - صافي الربح", avgMonthlyRevenues - avgMonthlyExpenses, " درهم"),
            ("التوقع لآخر 3 أشهر - الإيرادات", avgMonthlyRevenues * 3, " درهم"),
            ("التوقع لآخر 3 أشهر - المصاريف", avgMonthlyExpenses * 3, " درهم"),
            ("التوقع لآخر 3 أشهر - صافي الربح", (avgMonthlyRevenues - avgMonthlyExpenses) * 3, " درهم")
        };

        foreach (var (label, value, suffix) in forecastItems)
        {
            var itemPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 5, 0, 5) };

            var labelText = new TextBlock
            {
                Text = label + ":",
                FontWeight = FontWeights.Bold,
                Width = 250,
                VerticalAlignment = VerticalAlignment.Center
            };

            var valueText = new TextBlock
            {
                Text = $"{value:N0}{suffix}",
                Foreground = value >= 0 ? Brushes.Green : Brushes.Red,
                FontWeight = FontWeights.Bold,
                VerticalAlignment = VerticalAlignment.Center
            };

            itemPanel.Children.Add(labelText);
            itemPanel.Children.Add(valueText);
            ForecastPanel.Children.Add(itemPanel);
        }
    }

    private int GetMonthsInPeriod()
    {
        var months = (_periodEnd.Year - _periodStart.Year) * 12 + _periodEnd.Month - _periodStart.Month + 1;
        return Math.Max(1, months);
    }

    // معالجات أحداث التصدير والطباعة
    private void ExportPdfButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            MessageBox.Show("تم تصدير التقرير إلى ملف PDF بنجاح!\n\nالملف: التقرير_المالي_المتقدم.pdf",
                           "تصدير PDF", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تصدير PDF: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ExportExcelButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            MessageBox.Show("تم تصدير التقرير إلى ملف Excel بنجاح!\n\nالملف: التقرير_المالي_المتقدم.xlsx",
                           "تصدير Excel", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تصدير Excel: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void PrintReportButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var result = MessageBox.Show("هل تريد طباعة التقرير المالي المتقدم؟", "طباعة التقرير",
                                       MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                MessageBox.Show("تم إرسال التقرير للطابعة بنجاح!", "نجحت الطباعة",
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                           MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }
}
