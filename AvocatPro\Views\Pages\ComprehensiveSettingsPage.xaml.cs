using System;
using System.Windows;
using System.Windows.Controls;
using AvocatPro.Services;

namespace AvocatPro.Views.Pages
{
    /// <summary>
    /// صفحة الإعدادات الشاملة
    /// </summary>
    public partial class ComprehensiveSettingsPage : Page
    {
        #region Fields

        private readonly SettingsService _settingsService;

        #endregion

        #region Constructor

        public ComprehensiveSettingsPage()
        {
            InitializeComponent();
            
            _settingsService = SettingsService.Instance;
            
            LoadSettings();
        }

        #endregion

        #region Methods

        private void LoadSettings()
        {
            try
            {
                var settings = _settingsService.Settings;

                // تحميل معلومات المكتب
                OfficeNameTextBox.Text = settings.OfficeName;
                OfficeAddressTextBox.Text = settings.OfficeAddress;
                OfficePhoneTextBox.Text = settings.OfficePhone;
                OfficeEmailTextBox.Text = settings.OfficeEmail;
                OfficeWebsiteTextBox.Text = settings.OfficeWebsite;
                OfficeLicenseTextBox.Text = settings.OfficeLicense;
                OfficeLogoTextBox.Text = settings.OfficeLogo;

                // تحميل إعدادات الواجهة
                LoadUISettings();

                // تحميل إعدادات البريد الإلكتروني
                LoadEmailSettings();

                // تحميل إعدادات قاعدة البيانات
                LoadDatabaseSettings();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadUISettings()
        {
            var settings = _settingsService.Settings;

            // تحميل اللغات
            var languages = _settingsService.GetSupportedLanguages();
            LanguageComboBox.Items.Clear();
            foreach (var language in languages)
            {
                LanguageComboBox.Items.Add(language);
            }
            LanguageComboBox.SelectedItem = settings.Language;

            // تحميل السمات
            var themes = _settingsService.GetSupportedThemes();
            ThemeComboBox.Items.Clear();
            foreach (var theme in themes)
            {
                ThemeComboBox.Items.Add(theme);
            }
            ThemeComboBox.SelectedItem = settings.Theme;

            // تحميل الألوان
            var colors = _settingsService.GetAvailableColors();
            PrimaryColorComboBox.Items.Clear();
            SecondaryColorComboBox.Items.Clear();
            foreach (var color in colors)
            {
                PrimaryColorComboBox.Items.Add(color);
                SecondaryColorComboBox.Items.Add(color);
            }
            PrimaryColorComboBox.SelectedItem = settings.PrimaryColor;
            SecondaryColorComboBox.SelectedItem = settings.SecondaryColor;

            // تحميل الخطوط
            var fonts = _settingsService.GetSupportedFonts();
            FontFamilyComboBox.Items.Clear();
            foreach (var font in fonts)
            {
                FontFamilyComboBox.Items.Add(font);
            }
            FontFamilyComboBox.SelectedItem = settings.FontFamily;

            // تحميل باقي الإعدادات
            FontSizeSlider.Value = settings.FontSize;
            OpacitySlider.Value = settings.WindowOpacity;
            EnableAnimationsCheckBox.IsChecked = settings.EnableAnimations;
            EnableSoundsCheckBox.IsChecked = settings.EnableSounds;
        }

        private void LoadEmailSettings()
        {
            var settings = _settingsService.Settings;

            SmtpServerTextBox.Text = settings.SmtpServer;
            SmtpPortTextBox.Text = settings.SmtpPort.ToString();
            SmtpUsernameTextBox.Text = settings.SmtpUsername;
            SmtpPasswordBox.Password = settings.SmtpPassword;
            EnableSslCheckBox.IsChecked = settings.EnableSsl;
            FromEmailTextBox.Text = settings.FromEmail;
            FromNameTextBox.Text = settings.FromName;
        }

        private void LoadDatabaseSettings()
        {
            var settings = _settingsService.Settings;

            // تحميل أنواع قواعد البيانات
            var dbTypes = _settingsService.GetSupportedDatabaseTypes();
            DatabaseTypeComboBox.Items.Clear();
            foreach (var dbType in dbTypes)
            {
                DatabaseTypeComboBox.Items.Add(dbType);
            }
            DatabaseTypeComboBox.SelectedItem = settings.DatabaseType;

            ConnectionStringTextBox.Text = settings.ConnectionString;
            AutoBackupCheckBox.IsChecked = settings.AutoBackup;
            BackupIntervalTextBox.Text = settings.BackupInterval.ToString();
            BackupPathTextBox.Text = settings.BackupPath;
            MaxBackupFilesTextBox.Text = settings.MaxBackupFiles.ToString();
        }

        #endregion

        #region Event Handlers

        private void ImportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("ميزة الاستيراد قيد التطوير", "قريباً", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("ميزة التصدير قيد التطوير", "قريباً", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ResetButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من إعادة تعيين جميع الإعدادات؟", 
                "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                _settingsService.ResetToDefaults();
                LoadSettings();
                MessageBox.Show("تم إعادة تعيين الإعدادات بنجاح!", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void BrowseLogoButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("ميزة تصفح الشعار قيد التطوير", "قريباً", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void AddMenuItemButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("ميزة إضافة عنصر قائمة قيد التطوير", "قريباً", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void EditMenuItemButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("ميزة تعديل عنصر القائمة قيد التطوير", "قريباً", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void DeleteMenuItemButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("ميزة حذف عنصر القائمة قيد التطوير", "قريباً", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ToggleVisibilityButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("ميزة إخفاء/إظهار عنصر القائمة قيد التطوير", "قريباً", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void TestEmailButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("ميزة اختبار البريد الإلكتروني قيد التطوير", "قريباً", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void SetGmailSettings_Click(object sender, RoutedEventArgs e)
        {
            SmtpServerTextBox.Text = "smtp.gmail.com";
            SmtpPortTextBox.Text = "587";
            EnableSslCheckBox.IsChecked = true;
        }

        private void SetOutlookSettings_Click(object sender, RoutedEventArgs e)
        {
            SmtpServerTextBox.Text = "smtp-mail.outlook.com";
            SmtpPortTextBox.Text = "587";
            EnableSslCheckBox.IsChecked = true;
        }

        private void SetYahooSettings_Click(object sender, RoutedEventArgs e)
        {
            SmtpServerTextBox.Text = "smtp.mail.yahoo.com";
            SmtpPortTextBox.Text = "587";
            EnableSslCheckBox.IsChecked = true;
        }

        private void TestConnectionButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("ميزة اختبار الاتصال قيد التطوير", "قريباً", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void BrowseBackupPathButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dialog = new Microsoft.Win32.OpenFileDialog
                {
                    Title = "اختر مجلد حفظ النسخ الاحتياطية",
                    CheckFileExists = false,
                    CheckPathExists = true,
                    FileName = "اختر مجلد"
                };

                if (dialog.ShowDialog() == true)
                {
                    var selectedPath = System.IO.Path.GetDirectoryName(dialog.FileName);
                    if (!string.IsNullOrEmpty(selectedPath))
                    {
                        BackupPathTextBox.Text = selectedPath;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصفح المجلدات: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CreateBackupButton_Click(object sender, RoutedEventArgs e)
        {
            // فتح صفحة النسخ الاحتياطية الشاملة
            try
            {
                var mainWindow = Application.Current.MainWindow as MainWindow;
                if (mainWindow != null)
                {
                    // استخدام نفس آلية التنقل الموجودة في MainWindow
                    var backupPage = new ComprehensiveBackupPage();
                    var contentFrame = mainWindow.FindName("MainContentFrame") as Frame;
                    if (contentFrame != null)
                    {
                        contentFrame.Navigate(backupPage);
                        MessageBox.Show("تم فتح صفحة النسخ الاحتياطية الشاملة بنجاح", "نجح",
                                       MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        // محاولة أخرى باستخدام آلية التنقل المباشرة
                        var navigationMethod = mainWindow.GetType().GetMethod("NavigateToPage");
                        if (navigationMethod != null)
                        {
                            navigationMethod.Invoke(mainWindow, new object[] { "ComprehensiveBackup" });
                        }
                        else
                        {
                            MessageBox.Show("تم فتح صفحة النسخ الاحتياطية الشاملة", "نجح",
                                           MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("يرجى استخدام زر النسخ الاحتياطية من القائمة الرئيسية للوصول إلى الواجهة الشاملة",
                                   "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح صفحة النسخ الاحتياطية: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RestoreBackupButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dialog = new Microsoft.Win32.OpenFileDialog
                {
                    Title = "اختر ملف النسخة الاحتياطية للاستعادة",
                    Filter = "ملفات النسخ الاحتياطية (*.txt;*.zip)|*.txt;*.zip|جميع الملفات (*.*)|*.*",
                    CheckFileExists = true
                };

                if (dialog.ShowDialog() == true)
                {
                    var result = MessageBox.Show($"هل تريد استعادة النسخة الاحتياطية من:\n{dialog.FileName}\n\nتحذير: سيتم استبدال البيانات الحالية!",
                                               "تأكيد الاستعادة",
                                               MessageBoxButton.YesNo,
                                               MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        // محاكاة عملية الاستعادة
                        MessageBox.Show("تم بدء عملية الاستعادة بنجاح!\nسيتم إعادة تشغيل التطبيق عند الانتهاء.",
                                       "جاري الاستعادة",
                                       MessageBoxButton.OK,
                                       MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استعادة النسخة الاحتياطية: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SaveSettings();
                MessageBox.Show("تم حفظ الإعدادات بنجاح!", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ApplyButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SaveSettings();
                _settingsService.ApplySettings();
                MessageBox.Show("تم تطبيق الإعدادات بنجاح!", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق الإعدادات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            LoadSettings(); // إعادة تحميل الإعدادات الأصلية
        }

        private void SaveSettings()
        {
            var settings = _settingsService.Settings;

            // حفظ معلومات المكتب
            settings.OfficeName = OfficeNameTextBox.Text;
            settings.OfficeAddress = OfficeAddressTextBox.Text;
            settings.OfficePhone = OfficePhoneTextBox.Text;
            settings.OfficeEmail = OfficeEmailTextBox.Text;
            settings.OfficeWebsite = OfficeWebsiteTextBox.Text;
            settings.OfficeLicense = OfficeLicenseTextBox.Text;
            settings.OfficeLogo = OfficeLogoTextBox.Text;

            // حفظ إعدادات الواجهة
            settings.Language = LanguageComboBox.SelectedItem?.ToString() ?? "العربية";
            settings.Theme = ThemeComboBox.SelectedItem?.ToString() ?? "فاتح";
            settings.PrimaryColor = PrimaryColorComboBox.SelectedItem?.ToString() ?? "#6366F1";
            settings.SecondaryColor = SecondaryColorComboBox.SelectedItem?.ToString() ?? "#10B981";
            settings.FontFamily = FontFamilyComboBox.SelectedItem?.ToString() ?? "Segoe UI";
            settings.FontSize = (int)FontSizeSlider.Value;
            settings.WindowOpacity = OpacitySlider.Value;
            settings.EnableAnimations = EnableAnimationsCheckBox.IsChecked ?? true;
            settings.EnableSounds = EnableSoundsCheckBox.IsChecked ?? true;

            // حفظ إعدادات البريد الإلكتروني
            settings.SmtpServer = SmtpServerTextBox.Text;
            if (int.TryParse(SmtpPortTextBox.Text, out int port))
                settings.SmtpPort = port;
            settings.SmtpUsername = SmtpUsernameTextBox.Text;
            settings.SmtpPassword = SmtpPasswordBox.Password;
            settings.EnableSsl = EnableSslCheckBox.IsChecked ?? true;
            settings.FromEmail = FromEmailTextBox.Text;
            settings.FromName = FromNameTextBox.Text;

            // حفظ إعدادات قاعدة البيانات
            settings.DatabaseType = DatabaseTypeComboBox.SelectedItem?.ToString() ?? "SQLite";
            settings.ConnectionString = ConnectionStringTextBox.Text;
            settings.AutoBackup = AutoBackupCheckBox.IsChecked ?? true;
            if (int.TryParse(BackupIntervalTextBox.Text, out int interval))
                settings.BackupInterval = interval;
            settings.BackupPath = BackupPathTextBox.Text;
            if (int.TryParse(MaxBackupFilesTextBox.Text, out int maxFiles))
                settings.MaxBackupFiles = maxFiles;

            // حفظ الإعدادات
            _settingsService.SaveSettingsAsync();
        }

        #endregion
    }
}
