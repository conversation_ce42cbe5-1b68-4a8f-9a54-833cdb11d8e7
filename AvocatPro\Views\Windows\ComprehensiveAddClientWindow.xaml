<Window x:Class="AvocatPro.Views.Windows.ComprehensiveAddClientWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة موكل جديد - شامل" Height="700" Width="900"
        WindowStartupLocation="CenterOwner" ResizeMode="CanResize"
        FlowDirection="RightToLeft" Background="#F5F7FA">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="White" CornerRadius="16" Padding="24" Margin="0,0,0,24">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="20" ShadowDepth="0" Opacity="0.4"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <Border Grid.Column="0" Background="#6366F1" CornerRadius="12" Padding="12" Margin="0,0,16,0">
                    <TextBlock Text="👤" FontSize="24" Foreground="White"/>
                </Border>
                
                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock Text="إضافة موكل جديد" FontSize="24" FontWeight="Bold" Foreground="#1F2937"/>
                    <TextBlock Text="إدخال جميع البيانات الأساسية والتفصيلية للموكل الجديد" FontSize="14" Foreground="#6B7280"/>
                </StackPanel>

                <Button Grid.Column="2" Content="✕" Background="#EF4444" Foreground="White" 
                        BorderThickness="0" Width="32" Height="32" FontSize="14" FontWeight="Bold"
                        Cursor="Hand" Click="Close_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" CornerRadius="16">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#DC2626"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
            <StackPanel>
                <!-- البيانات الأساسية -->
                <Border Background="White" CornerRadius="16" Padding="24" Margin="0,0,0,24">
                    <Border.Effect>
                        <DropShadowEffect Color="#E2E8F0" BlurRadius="15" ShadowDepth="0" Opacity="0.3"/>
                    </Border.Effect>
                    <StackPanel>
                        <TextBlock Text="📋 البيانات الأساسية" FontSize="18" FontWeight="Bold" 
                                   Foreground="#1F2937" Margin="0,0,0,20"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- اسم الموكل -->
                            <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,0,16">
                                <TextBlock Text="اسم الموكل الكامل *" FontSize="14" FontWeight="SemiBold" 
                                           Foreground="#374151" Margin="0,0,0,8"/>
                                <TextBox x:Name="ClientNameTextBox" Background="#F9FAFB" BorderBrush="#D1D5DB" 
                                         BorderThickness="1" Padding="12" FontSize="14">
                                    <TextBox.Style>
                                        <Style TargetType="TextBox">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="TextBox">
                                                        <Border Background="{TemplateBinding Background}"
                                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                                CornerRadius="8">
                                                            <ScrollViewer x:Name="PART_ContentHost" 
                                                                          Margin="{TemplateBinding Padding}"
                                                                          VerticalAlignment="Center"/>
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                            <Trigger Property="IsFocused" Value="True">
                                                                <Setter Property="BorderBrush" Value="#6366F1"/>
                                                                <Setter Property="Background" Value="White"/>
                                                            </Trigger>
                                                        </ControlTemplate.Triggers>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </TextBox.Style>
                                </TextBox>
                            </StackPanel>

                            <!-- نوع الموكل -->
                            <StackPanel Grid.Row="0" Grid.Column="2" Margin="0,0,0,16">
                                <TextBlock Text="نوع الموكل *" FontSize="14" FontWeight="SemiBold" 
                                           Foreground="#374151" Margin="0,0,0,8"/>
                                <ComboBox x:Name="ClientTypeComboBox" Background="#F9FAFB" BorderBrush="#D1D5DB" 
                                          BorderThickness="1" Padding="12" FontSize="14">
                                    <ComboBoxItem Content="فرد" IsSelected="True"/>
                                    <ComboBoxItem Content="شركة"/>
                                    <ComboBoxItem Content="مؤسسة"/>
                                    <ComboBoxItem Content="جمعية"/>
                                </ComboBox>
                            </StackPanel>

                            <!-- رقم الهاتف -->
                            <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,0,16">
                                <TextBlock Text="رقم الهاتف *" FontSize="14" FontWeight="SemiBold" 
                                           Foreground="#374151" Margin="0,0,0,8"/>
                                <TextBox x:Name="PhoneTextBox" Background="#F9FAFB" BorderBrush="#D1D5DB" 
                                         BorderThickness="1" Padding="12" FontSize="14" 
                                         Text="+966" Style="{DynamicResource TextBoxStyle}"/>
                            </StackPanel>

                            <!-- البريد الإلكتروني -->
                            <StackPanel Grid.Row="1" Grid.Column="2" Margin="0,0,0,16">
                                <TextBlock Text="البريد الإلكتروني" FontSize="14" FontWeight="SemiBold" 
                                           Foreground="#374151" Margin="0,0,0,8"/>
                                <TextBox x:Name="EmailTextBox" Background="#F9FAFB" BorderBrush="#D1D5DB" 
                                         BorderThickness="1" Padding="12" FontSize="14" 
                                         Style="{DynamicResource TextBoxStyle}"/>
                            </StackPanel>

                            <!-- مرجع المكتب -->
                            <StackPanel Grid.Row="2" Grid.Column="0" Margin="0,0,0,16">
                                <TextBlock Text="مرجع المكتب" FontSize="14" FontWeight="SemiBold" 
                                           Foreground="#374151" Margin="0,0,0,8"/>
                                <TextBox x:Name="OfficeRefTextBox" Background="#F9FAFB" BorderBrush="#D1D5DB" 
                                         BorderThickness="1" Padding="12" FontSize="14" 
                                         IsReadOnly="True" Style="{DynamicResource TextBoxStyle}"/>
                            </StackPanel>

                            <!-- مرجع الملف -->
                            <StackPanel Grid.Row="2" Grid.Column="2" Margin="0,0,0,16">
                                <TextBlock Text="مرجع الملف" FontSize="14" FontWeight="SemiBold" 
                                           Foreground="#374151" Margin="0,0,0,8"/>
                                <TextBox x:Name="FileRefTextBox" Background="#F9FAFB" BorderBrush="#D1D5DB" 
                                         BorderThickness="1" Padding="12" FontSize="14" 
                                         Style="{DynamicResource TextBoxStyle}"/>
                            </StackPanel>

                            <!-- الحالة -->
                            <StackPanel Grid.Row="3" Grid.Column="0" Margin="0,0,0,16">
                                <TextBlock Text="حالة الموكل" FontSize="14" FontWeight="SemiBold" 
                                           Foreground="#374151" Margin="0,0,0,8"/>
                                <ComboBox x:Name="StatusComboBox" Background="#F9FAFB" BorderBrush="#D1D5DB" 
                                          BorderThickness="1" Padding="12" FontSize="14">
                                    <ComboBoxItem Content="نشط" IsSelected="True"/>
                                    <ComboBoxItem Content="غير نشط"/>
                                    <ComboBoxItem Content="معلق"/>
                                    <ComboBoxItem Content="مؤرشف"/>
                                </ComboBox>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- العنوان التفصيلي -->
                <Border Background="White" CornerRadius="16" Padding="24" Margin="0,0,0,24">
                    <Border.Effect>
                        <DropShadowEffect Color="#E2E8F0" BlurRadius="15" ShadowDepth="0" Opacity="0.3"/>
                    </Border.Effect>
                    <StackPanel>
                        <TextBlock Text="📍 العنوان التفصيلي" FontSize="18" FontWeight="Bold" 
                                   Foreground="#1F2937" Margin="0,0,0,20"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- المدينة -->
                            <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,0,16">
                                <TextBlock Text="المدينة" FontSize="14" FontWeight="SemiBold" 
                                           Foreground="#374151" Margin="0,0,0,8"/>
                                <ComboBox x:Name="CityComboBox" Background="#F9FAFB" BorderBrush="#D1D5DB" 
                                          BorderThickness="1" Padding="12" FontSize="14">
                                    <ComboBoxItem Content="الرياض"/>
                                    <ComboBoxItem Content="جدة"/>
                                    <ComboBoxItem Content="الدمام"/>
                                    <ComboBoxItem Content="مكة المكرمة"/>
                                    <ComboBoxItem Content="المدينة المنورة"/>
                                    <ComboBoxItem Content="الطائف"/>
                                    <ComboBoxItem Content="تبوك"/>
                                    <ComboBoxItem Content="أبها"/>
                                    <ComboBoxItem Content="الخبر"/>
                                    <ComboBoxItem Content="بريدة"/>
                                    <ComboBoxItem Content="خميس مشيط"/>
                                    <ComboBoxItem Content="حائل"/>
                                    <ComboBoxItem Content="نجران"/>
                                    <ComboBoxItem Content="الجبيل"/>
                                    <ComboBoxItem Content="ينبع"/>
                                </ComboBox>
                            </StackPanel>

                            <!-- الحي -->
                            <StackPanel Grid.Row="0" Grid.Column="2" Margin="0,0,0,16">
                                <TextBlock Text="الحي" FontSize="14" FontWeight="SemiBold" 
                                           Foreground="#374151" Margin="0,0,0,8"/>
                                <TextBox x:Name="DistrictTextBox" Background="#F9FAFB" BorderBrush="#D1D5DB" 
                                         BorderThickness="1" Padding="12" FontSize="14" 
                                         Style="{DynamicResource TextBoxStyle}"/>
                            </StackPanel>

                            <!-- الشارع -->
                            <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,0,16">
                                <TextBlock Text="الشارع" FontSize="14" FontWeight="SemiBold" 
                                           Foreground="#374151" Margin="0,0,0,8"/>
                                <TextBox x:Name="StreetTextBox" Background="#F9FAFB" BorderBrush="#D1D5DB" 
                                         BorderThickness="1" Padding="12" FontSize="14" 
                                         Style="{DynamicResource TextBoxStyle}"/>
                            </StackPanel>

                            <!-- رقم المبنى -->
                            <StackPanel Grid.Row="1" Grid.Column="2" Margin="0,0,0,16">
                                <TextBlock Text="رقم المبنى" FontSize="14" FontWeight="SemiBold" 
                                           Foreground="#374151" Margin="0,0,0,8"/>
                                <TextBox x:Name="BuildingNumberTextBox" Background="#F9FAFB" BorderBrush="#D1D5DB" 
                                         BorderThickness="1" Padding="12" FontSize="14" 
                                         Style="{DynamicResource TextBoxStyle}"/>
                            </StackPanel>

                            <!-- العنوان الكامل -->
                            <StackPanel Grid.Row="2" Grid.ColumnSpan="3" Margin="0,0,0,16">
                                <TextBlock Text="العنوان الكامل" FontSize="14" FontWeight="SemiBold" 
                                           Foreground="#374151" Margin="0,0,0,8"/>
                                <TextBox x:Name="FullAddressTextBox" Background="#F9FAFB" BorderBrush="#D1D5DB" 
                                         BorderThickness="1" Padding="12" FontSize="14" Height="80"
                                         TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"
                                         Style="{DynamicResource TextBoxStyle}"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- الملاحظات والمعلومات الإضافية -->
                <Border Background="White" CornerRadius="16" Padding="24" Margin="0,0,0,24">
                    <Border.Effect>
                        <DropShadowEffect Color="#E2E8F0" BlurRadius="15" ShadowDepth="0" Opacity="0.3"/>
                    </Border.Effect>
                    <StackPanel>
                        <TextBlock Text="📝 الملاحظات والمعلومات الإضافية" FontSize="18" FontWeight="Bold" 
                                   Foreground="#1F2937" Margin="0,0,0,20"/>
                        
                        <TextBox x:Name="NotesTextBox" Background="#F9FAFB" BorderBrush="#D1D5DB" 
                                 BorderThickness="1" Padding="12" FontSize="14" Height="120"
                                 TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"
                                 Style="{DynamicResource TextBoxStyle}"
                                 Text="أدخل أي ملاحظات أو معلومات إضافية حول الموكل..."/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- أزرار الإجراءات -->
        <Border Grid.Row="2" Background="White" CornerRadius="16" Padding="24">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="15" ShadowDepth="0" Opacity="0.3"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- معلومات الحفظ -->
                <StackPanel Grid.Column="0" VerticalAlignment="Center">
                    <TextBlock Text="💾 سيتم حفظ البيانات تلقائياً" FontSize="12" Foreground="#6B7280"/>
                    <TextBlock Text="تأكد من صحة جميع البيانات قبل الحفظ" FontSize="11" Foreground="#9CA3AF"/>
                </StackPanel>

                <!-- زر الإلغاء -->
                <Button Grid.Column="1" Content="إلغاء" Background="#F3F4F6" Foreground="#374151" 
                        BorderThickness="0" Padding="24,12" FontSize="14" FontWeight="SemiBold" 
                        Cursor="Hand" Margin="0,0,12,0" Click="Cancel_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" CornerRadius="12" 
                                                Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#E5E7EB"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>

                <!-- زر الحفظ والمتابعة -->
                <Button Grid.Column="2" Content="حفظ ومتابعة" Background="#F59E0B" Foreground="White" 
                        BorderThickness="0" Padding="24,12" FontSize="14" FontWeight="SemiBold" 
                        Cursor="Hand" Margin="0,0,12,0" Click="SaveAndContinue_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" CornerRadius="12" 
                                                Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#D97706"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>

                <!-- زر الحفظ والإنهاء -->
                <Button Grid.Column="3" Content="حفظ وإنهاء" Background="#10B981" Foreground="White" 
                        BorderThickness="0" Padding="24,12" FontSize="14" FontWeight="SemiBold" 
                        Cursor="Hand" Click="SaveAndFinish_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" CornerRadius="12" 
                                                Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#059669"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
            </Grid>
        </Border>
    </Grid>

    <Window.Resources>
        <Style x:Key="TextBoxStyle" TargetType="TextBox">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="8">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                          Margin="{TemplateBinding Padding}"
                                          VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#6366F1"/>
                                <Setter Property="Background" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
</Window>
