using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace AvocatPro.Views.Pages
{
    /// <summary>
    /// صفحة إدارة الموكلين الحديثة
    /// </summary>
    public partial class ModernClientsPage : Page
    {
        private ObservableCollection<ClientViewModel> _allClients = new ObservableCollection<ClientViewModel>();
        private ObservableCollection<ClientViewModel> _filteredClients = new ObservableCollection<ClientViewModel>();

        public ModernClientsPage()
        {
            try
            {
                InitializeComponent();
                InitializeData();
                LoadClients();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة صفحة الموكلين: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تهيئة البيانات الوهمية
        /// </summary>
        private void InitializeData()
        {
            _allClients = new ObservableCollection<ClientViewModel>();
            _filteredClients = new ObservableCollection<ClientViewModel>();

            // إضافة بيانات وهمية للموكلين
            var sampleClients = new List<ClientViewModel>
            {
                new ClientViewModel
                {
                    Id = 1,
                    Name = "أحمد محمد الحسن",
                    Type = "فرد",
                    TypeColor = "#3B82F6",
                    Phone = "+212 661 234 567",
                    Email = "<EMAIL>",
                    FilesCount = 3,
                    Address = "الرباط، المملكة المغربية"
                },
                new ClientViewModel
                {
                    Id = 2,
                    Name = "شركة التجارة المغربية",
                    Type = "شركة",
                    TypeColor = "#10B981",
                    Phone = "+212 537 123 456",
                    Email = "<EMAIL>",
                    FilesCount = 7,
                    Address = "الدار البيضاء، المملكة المغربية"
                },
                new ClientViewModel
                {
                    Id = 3,
                    Name = "فاطمة الزهراء",
                    Type = "فرد",
                    TypeColor = "#3B82F6",
                    Phone = "+212 662 345 678",
                    Email = "<EMAIL>",
                    FilesCount = 2,
                    Address = "فاس، المملكة المغربية"
                },
                new ClientViewModel
                {
                    Id = 4,
                    Name = "مؤسسة البناء الحديث",
                    Type = "شركة",
                    TypeColor = "#10B981",
                    Phone = "+212 528 987 654",
                    Email = "<EMAIL>",
                    FilesCount = 12,
                    Address = "مراكش، المملكة المغربية"
                },
                new ClientViewModel
                {
                    Id = 5,
                    Name = "يوسف بن علي",
                    Type = "فرد",
                    TypeColor = "#3B82F6",
                    Phone = "+212 663 456 789",
                    Email = "<EMAIL>",
                    FilesCount = 1,
                    Address = "طنجة، المملكة المغربية"
                },
                new ClientViewModel
                {
                    Id = 6,
                    Name = "شركة التكنولوجيا المتقدمة",
                    Type = "شركة",
                    TypeColor = "#10B981",
                    Phone = "+212 539 111 222",
                    Email = "<EMAIL>",
                    FilesCount = 5,
                    Address = "الرباط، المملكة المغربية"
                },
                new ClientViewModel
                {
                    Id = 7,
                    Name = "خديجة المرابط",
                    Type = "فرد",
                    TypeColor = "#3B82F6",
                    Phone = "+212 664 567 890",
                    Email = "<EMAIL>",
                    FilesCount = 4,
                    Address = "أكادير، المملكة المغربية"
                },
                new ClientViewModel
                {
                    Id = 8,
                    Name = "مجموعة الاستثمار الذهبي",
                    Type = "شركة",
                    TypeColor = "#10B981",
                    Phone = "+212 522 333 444",
                    Email = "<EMAIL>",
                    FilesCount = 9,
                    Address = "الدار البيضاء، المملكة المغربية"
                }
            };

            foreach (var client in sampleClients)
            {
                _allClients.Add(client);
                _filteredClients.Add(client);
            }
        }

        /// <summary>
        /// تحميل قائمة الموكلين
        /// </summary>
        private void LoadClients()
        {
            try
            {
                if (ClientsItemsControl != null)
                {
                    ClientsItemsControl.ItemsSource = _filteredClients;
                }
                UpdateClientsCount();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الموكلين: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث عدد الموكلين
        /// </summary>
        private void UpdateClientsCount()
        {
            try
            {
                if (ClientsCountLabel != null && _filteredClients != null)
                {
                    ClientsCountLabel.Text = $"إجمالي الموكلين: {_filteredClients.Count}";
                }
            }
            catch (Exception ex)
            {
                // تجاهل الخطأ في التحديث
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث عدد الموكلين: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج البحث في النص
        /// </summary>
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterClients();
        }

        /// <summary>
        /// معالج تصفية حسب النوع
        /// </summary>
        private void TypeFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            FilterClients();
        }

        /// <summary>
        /// تصفية الموكلين
        /// </summary>
        private void FilterClients()
        {
            var searchText = SearchTextBox?.Text?.ToLower() ?? "";
            var selectedType = (TypeFilterComboBox?.SelectedItem as ComboBoxItem)?.Content?.ToString();

            _filteredClients.Clear();

            var filtered = _allClients.Where(client =>
            {
                // تصفية النص
                var matchesSearch = string.IsNullOrEmpty(searchText) ||
                                   client.Name.ToLower().Contains(searchText) ||
                                   client.Email.ToLower().Contains(searchText) ||
                                   client.Phone.Contains(searchText);

                // تصفية النوع
                var matchesType = selectedType == "جميع الأنواع" ||
                                 selectedType == client.Type;

                return matchesSearch && matchesType;
            });

            foreach (var client in filtered)
            {
                _filteredClients.Add(client);
            }

            UpdateClientsCount();
        }

        /// <summary>
        /// إضافة موكل جديد
        /// </summary>
        private void AddClient_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة إضافة موكل جديد", "إضافة موكل", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// عرض تفاصيل الموكل
        /// </summary>
        private void ViewClient_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var clientId = (int)button.Tag;
            MessageBox.Show($"عرض تفاصيل الموكل رقم: {clientId}", "تفاصيل الموكل", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// تعديل الموكل
        /// </summary>
        private void EditClient_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var clientId = (int)button.Tag;
            MessageBox.Show($"تعديل الموكل رقم: {clientId}", "تعديل الموكل", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// حذف الموكل
        /// </summary>
        private void DeleteClient_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var clientId = (int)button.Tag;
            
            var result = MessageBox.Show($"هل أنت متأكد من حذف الموكل رقم {clientId}؟", 
                                        "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);
            
            if (result == MessageBoxResult.Yes)
            {
                var clientToRemove = _allClients.FirstOrDefault(c => c.Id == clientId);
                if (clientToRemove != null)
                {
                    _allClients.Remove(clientToRemove);
                    FilterClients();
                    MessageBox.Show("تم حذف الموكل بنجاح", "حذف الموكل", 
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        /// <summary>
        /// تصدير البيانات
        /// </summary>
        private void Export_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تصدير قائمة الموكلين إلى ملف Excel", "تصدير البيانات", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// الصفحة السابقة
        /// </summary>
        private void PreviousPage_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("الانتقال إلى الصفحة السابقة", "التنقل", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// الصفحة التالية
        /// </summary>
        private void NextPage_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("الانتقال إلى الصفحة التالية", "التنقل", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    /// <summary>
    /// نموذج عرض الموكل
    /// </summary>
    public class ClientViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Type { get; set; } = "";
        public string TypeColor { get; set; } = "";
        public string Phone { get; set; } = "";
        public string Email { get; set; } = "";
        public int FilesCount { get; set; }
        public string Address { get; set; } = "";
    }
}
