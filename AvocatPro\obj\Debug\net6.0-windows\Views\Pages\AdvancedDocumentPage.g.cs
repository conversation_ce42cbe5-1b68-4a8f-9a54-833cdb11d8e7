﻿#pragma checksum "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "52BCB7FE55A2FC3F9A92C346D5662728C246CC57"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Pages {
    
    
    /// <summary>
    /// AdvancedDocumentPage
    /// </summary>
    public partial class AdvancedDocumentPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 45 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button UploadDocumentBtn;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ScanDocumentBtn;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreateFromTemplateBtn;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CategoryFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox SignedDocumentsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EncryptedDocumentsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox SharedDocumentsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel QuickStatsPanel;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SortComboBox;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ViewModeComboBox;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshDocumentsBtn;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DocumentsPanel;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border LoadingOverlay;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/pages/advanceddocumentpage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.UploadDocumentBtn = ((System.Windows.Controls.Button)(target));
            
            #line 46 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
            this.UploadDocumentBtn.Click += new System.Windows.RoutedEventHandler(this.UploadDocument_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ScanDocumentBtn = ((System.Windows.Controls.Button)(target));
            
            #line 53 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
            this.ScanDocumentBtn.Click += new System.Windows.RoutedEventHandler(this.ScanDocument_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.CreateFromTemplateBtn = ((System.Windows.Controls.Button)(target));
            
            #line 60 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
            this.CreateFromTemplateBtn.Click += new System.Windows.RoutedEventHandler(this.CreateFromTemplate_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 88 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.CategoryFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 96 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
            this.CategoryFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CategoryFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.SignedDocumentsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 112 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
            this.SignedDocumentsCheckBox.Checked += new System.Windows.RoutedEventHandler(this.DocumentFilter_Changed);
            
            #line default
            #line hidden
            
            #line 112 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
            this.SignedDocumentsCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.DocumentFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 7:
            this.EncryptedDocumentsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 114 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
            this.EncryptedDocumentsCheckBox.Checked += new System.Windows.RoutedEventHandler(this.DocumentFilter_Changed);
            
            #line default
            #line hidden
            
            #line 114 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
            this.EncryptedDocumentsCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.DocumentFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 8:
            this.SharedDocumentsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 116 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
            this.SharedDocumentsCheckBox.Checked += new System.Windows.RoutedEventHandler(this.DocumentFilter_Changed);
            
            #line default
            #line hidden
            
            #line 116 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
            this.SharedDocumentsCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.DocumentFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 9:
            this.QuickStatsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 10:
            this.SortComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 146 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
            this.SortComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.Sort_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ViewModeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 154 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
            this.ViewModeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ViewMode_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 12:
            this.RefreshDocumentsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 161 "..\..\..\..\..\Views\Pages\AdvancedDocumentPage.xaml"
            this.RefreshDocumentsBtn.Click += new System.Windows.RoutedEventHandler(this.RefreshDocuments_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.DocumentsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 14:
            this.LoadingOverlay = ((System.Windows.Controls.Border)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

