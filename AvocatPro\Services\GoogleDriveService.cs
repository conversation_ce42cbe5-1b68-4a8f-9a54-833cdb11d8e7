using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using AvocatPro.Data;
using AvocatPro.Models.Backup;
using System.Net.Http;
using System.Text.Json;
using System.Text;

namespace AvocatPro.Services
{
    /// <summary>
    /// خدمة Google Drive للنسخ الاحتياطية
    /// </summary>
    public class GoogleDriveService
    {
        private readonly AvocatProDbContext _context;
        private readonly HttpClient _httpClient;
        private const string DriveApiBaseUrl = "https://www.googleapis.com/drive/v3";
        private const string UploadApiBaseUrl = "https://www.googleapis.com/upload/drive/v3";

        public GoogleDriveService(AvocatProDbContext context, HttpClient httpClient)
        {
            _context = context;
            _httpClient = httpClient;
        }

        /// <summary>
        /// رفع نسخة احتياطية إلى Google Drive
        /// </summary>
        public async Task<GoogleDriveUploadResult> UploadBackupAsync(string filePath, string? folderId = null)
        {
            try
            {
                var settings = await GetGoogleDriveSettingsAsync();
                if (settings == null || !settings.IsEnabled)
                {
                    return new GoogleDriveUploadResult { Success = false, Message = "Google Drive غير مفعل" };
                }

                // التحقق من صحة رمز الوصول
                if (!settings.IsAccessTokenValid())
                {
                    var refreshResult = await RefreshAccessTokenAsync(settings);
                    if (!refreshResult.Success)
                    {
                        return new GoogleDriveUploadResult { Success = false, Message = "فشل في تحديث رمز الوصول" };
                    }
                }

                // التحقق من المساحة المتاحة
                var fileInfo = new FileInfo(filePath);
                if (!settings.HasSufficientSpace(fileInfo.Length))
                {
                    return new GoogleDriveUploadResult { Success = false, Message = "مساحة Google Drive غير كافية" };
                }

                // إنشاء مجلد التاريخ إذا لزم الأمر
                var targetFolderId = folderId ?? settings.RootFolderId;
                if (settings.CreateDateFolders)
                {
                    targetFolderId = await CreateDateFolderAsync(settings, targetFolderId);
                }

                // رفع الملف
                var uploadResult = fileInfo.Length > settings.MaxFileSize
                    ? await UploadLargeFileAsync(settings, filePath, targetFolderId)
                    : await UploadSmallFileAsync(settings, filePath, targetFolderId);

                if (uploadResult.Success)
                {
                    // تحديث الإحصائيات
                    var duration = (int)(DateTime.Now - DateTime.Now.AddSeconds(-10)).TotalSeconds; // تقدير
                    settings.UpdateUploadStats(true, fileInfo.Length, duration);
                    await _context.SaveChangesAsync();
                }

                return uploadResult;
            }
            catch (Exception ex)
            {
                return new GoogleDriveUploadResult { Success = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// رفع ملف صغير
        /// </summary>
        private async Task<GoogleDriveUploadResult> UploadSmallFileAsync(GoogleDriveSettings settings, string filePath, string? folderId)
        {
            var fileName = Path.GetFileName(filePath);
            var fileContent = await File.ReadAllBytesAsync(filePath);

            var metadata = new
            {
                name = fileName,
                parents = folderId != null ? new[] { folderId } : null
            };

            var metadataJson = JsonSerializer.Serialize(metadata);
            var boundary = "----FormBoundary" + DateTime.Now.Ticks.ToString("x");

            using var content = new MultipartFormDataContent(boundary);
            content.Add(new StringContent(metadataJson, Encoding.UTF8, "application/json"), "metadata");
            content.Add(new ByteArrayContent(fileContent), "media", fileName);

            _httpClient.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", settings.AccessToken);

            var response = await _httpClient.PostAsync($"{UploadApiBaseUrl}/files?uploadType=multipart", content);
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<GoogleDriveFileResponse>(responseContent);
                
                return new GoogleDriveUploadResult
                {
                    Success = true,
                    FileId = result?.Id,
                    FileUrl = $"https://drive.google.com/file/d/{result?.Id}/view",
                    Message = "تم رفع الملف بنجاح"
                };
            }
            else
            {
                var error = await response.Content.ReadAsStringAsync();
                return new GoogleDriveUploadResult { Success = false, Message = $"فشل الرفع: {error}" };
            }
        }

        /// <summary>
        /// رفع ملف كبير بالتقسيم
        /// </summary>
        private async Task<GoogleDriveUploadResult> UploadLargeFileAsync(GoogleDriveSettings settings, string filePath, string? folderId)
        {
            // تنفيذ الرفع المتقطع للملفات الكبيرة
            // هذا يتطلب تنفيذ معقد للرفع المتقطع
            // للبساطة، سنستخدم الرفع العادي مع زيادة المهلة الزمنية
            
            _httpClient.Timeout = TimeSpan.FromMinutes(30); // زيادة المهلة للملفات الكبيرة
            return await UploadSmallFileAsync(settings, filePath, folderId);
        }

        /// <summary>
        /// إنشاء مجلد التاريخ
        /// </summary>
        private async Task<string> CreateDateFolderAsync(GoogleDriveSettings settings, string? parentFolderId)
        {
            var folderName = DateTime.Now.ToString(settings.DateFolderFormat);
            
            // البحث عن المجلد الموجود
            var existingFolder = await FindFolderAsync(settings, folderName, parentFolderId);
            if (existingFolder != null)
            {
                return existingFolder.Id!;
            }

            // إنشاء مجلد جديد
            var metadata = new
            {
                name = folderName,
                mimeType = "application/vnd.google-apps.folder",
                parents = parentFolderId != null ? new[] { parentFolderId } : null
            };

            var metadataJson = JsonSerializer.Serialize(metadata);
            var content = new StringContent(metadataJson, Encoding.UTF8, "application/json");

            _httpClient.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", settings.AccessToken);

            var response = await _httpClient.PostAsync($"{DriveApiBaseUrl}/files", content);
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<GoogleDriveFileResponse>(responseContent);
                return result?.Id ?? parentFolderId ?? "";
            }

            return parentFolderId ?? "";
        }

        /// <summary>
        /// البحث عن مجلد
        /// </summary>
        private async Task<GoogleDriveFileResponse?> FindFolderAsync(GoogleDriveSettings settings, string folderName, string? parentFolderId)
        {
            var query = $"name='{folderName}' and mimeType='application/vnd.google-apps.folder'";
            if (parentFolderId != null)
            {
                query += $" and '{parentFolderId}' in parents";
            }

            _httpClient.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", settings.AccessToken);

            var response = await _httpClient.GetAsync($"{DriveApiBaseUrl}/files?q={Uri.EscapeDataString(query)}");
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<GoogleDriveFilesResponse>(responseContent);
                return result?.Files?.FirstOrDefault();
            }

            return null;
        }

        /// <summary>
        /// حذف ملف من Google Drive
        /// </summary>
        public async Task<bool> DeleteFileAsync(string fileId)
        {
            try
            {
                var settings = await GetGoogleDriveSettingsAsync();
                if (settings == null || !settings.IsEnabled)
                {
                    return false;
                }

                if (!settings.IsAccessTokenValid())
                {
                    var refreshResult = await RefreshAccessTokenAsync(settings);
                    if (!refreshResult.Success)
                    {
                        return false;
                    }
                }

                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", settings.AccessToken);

                var response = await _httpClient.DeleteAsync($"{DriveApiBaseUrl}/files/{fileId}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// تحديث رمز الوصول
        /// </summary>
        private async Task<GoogleDriveAuthResult> RefreshAccessTokenAsync(GoogleDriveSettings settings)
        {
            try
            {
                var requestData = new
                {
                    client_id = settings.ClientId,
                    client_secret = DecryptString(settings.ClientSecret),
                    refresh_token = DecryptString(settings.RefreshToken!),
                    grant_type = "refresh_token"
                };

                var json = JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("https://oauth2.googleapis.com/token", content);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<GoogleDriveTokenResponse>(responseContent);
                    
                    if (result != null && !string.IsNullOrEmpty(result.AccessToken))
                    {
                        settings.AccessToken = EncryptString(result.AccessToken);
                        settings.AccessTokenExpiry = DateTime.Now.AddSeconds(result.ExpiresIn - 300); // 5 دقائق هامش أمان
                        settings.UpdatedAt = DateTime.Now;
                        
                        await _context.SaveChangesAsync();
                        
                        return new GoogleDriveAuthResult { Success = true };
                    }
                }

                return new GoogleDriveAuthResult { Success = false, Message = "فشل في تحديث رمز الوصول" };
            }
            catch (Exception ex)
            {
                return new GoogleDriveAuthResult { Success = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// الحصول على إعدادات Google Drive
        /// </summary>
        private async Task<GoogleDriveSettings?> GetGoogleDriveSettingsAsync()
        {
            return await _context.GoogleDriveSettings.FirstOrDefaultAsync();
        }

        /// <summary>
        /// تشفير النص
        /// </summary>
        private string EncryptString(string text)
        {
            // تنفيذ بسيط للتشفير - يجب استخدام تشفير أقوى في الإنتاج
            var bytes = Encoding.UTF8.GetBytes(text);
            return Convert.ToBase64String(bytes);
        }

        /// <summary>
        /// فك تشفير النص
        /// </summary>
        private string DecryptString(string encryptedText)
        {
            // فك التشفير البسيط - يجب استخدام فك تشفير أقوى في الإنتاج
            var bytes = Convert.FromBase64String(encryptedText);
            return Encoding.UTF8.GetString(bytes);
        }
    }

    /// <summary>
    /// نتيجة رفع Google Drive
    /// </summary>
    public class GoogleDriveUploadResult
    {
        public bool Success { get; set; }
        public string? FileId { get; set; }
        public string? FileUrl { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// نتيجة مصادقة Google Drive
    /// </summary>
    public class GoogleDriveAuthResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// استجابة ملف Google Drive
    /// </summary>
    public class GoogleDriveFileResponse
    {
        public string? Id { get; set; }
        public string? Name { get; set; }
        public string? MimeType { get; set; }
    }

    /// <summary>
    /// استجابة ملفات Google Drive
    /// </summary>
    public class GoogleDriveFilesResponse
    {
        public List<GoogleDriveFileResponse>? Files { get; set; }
    }

    /// <summary>
    /// استجابة رمز Google Drive
    /// </summary>
    public class GoogleDriveTokenResponse
    {
        public string? AccessToken { get; set; }
        public int ExpiresIn { get; set; }
        public string? RefreshToken { get; set; }
    }
}
