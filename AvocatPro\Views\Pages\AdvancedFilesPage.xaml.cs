using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using AvocatPro.Models;
using AvocatPro.Services;
using AvocatPro.Views.Windows;

namespace AvocatPro.Views.Pages
{
    /// <summary>
    /// صفحة إدارة الملفات المتقدمة
    /// </summary>
    public partial class AdvancedFilesPage : Page, INotifyPropertyChanged
    {
        #region Private Fields

        private ObservableCollection<AdvancedFileModel> _allFiles;
        private ObservableCollection<AdvancedFileModel> _filteredFiles;
        private readonly MoroccanCourtsService _courtsService;
        private bool _isSearchFocused = false;

        #endregion

        #region Properties

        public ObservableCollection<AdvancedFileModel> AllFiles
        {
            get => _allFiles;
            set { _allFiles = value; OnPropertyChanged(nameof(AllFiles)); }
        }

        public ObservableCollection<AdvancedFileModel> FilteredFiles
        {
            get => _filteredFiles;
            set { _filteredFiles = value; OnPropertyChanged(nameof(FilteredFiles)); }
        }

        #endregion

        #region Constructor

        public AdvancedFilesPage()
        {
            try
            {
                InitializeComponent();
                DataContext = this;
                
                _courtsService = new MoroccanCourtsService();
                
                // تهيئة المجموعات
                AllFiles = new ObservableCollection<AdvancedFileModel>();
                FilteredFiles = new ObservableCollection<AdvancedFileModel>();
                
                // تهيئة العناصر
                InitializeControls();
                
                // تحميل البيانات التجريبية
                LoadSampleData();
                
                // ربط البيانات بالجدول
                FilesDataGrid.ItemsSource = FilteredFiles;
                
                // تحديث الإحصائيات
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة صفحة الملفات: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region Initialization

        /// <summary>
        /// تهيئة العناصر
        /// </summary>
        private void InitializeControls()
        {
            try
            {
                // تحميل أنواع القضايا
                var caseTypes = _courtsService.GetCaseTypes();
                caseTypes.Insert(0, "جميع أنواع القضايا");
                CaseTypeFilterComboBox.ItemsSource = caseTypes;
                CaseTypeFilterComboBox.SelectedIndex = 0;

                // تحميل المحاكم
                var courts = _courtsService.GetMoroccanCourts();
                courts.Insert(0, "جميع المحاكم");
                CourtFilterComboBox.ItemsSource = courts;
                CourtFilterComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة العناصر: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل البيانات التجريبية
        /// </summary>
        private void LoadSampleData()
        {
            var sampleFiles = new[]
            {
                new AdvancedFileModel
                {
                    Id = 1,
                    FileNumber = "2024/001",
                    OfficeReference = "REF-001",
                    CourtReference = "COURT-001",
                    FileType = "دعوى",
                    Court = "المحكمة الابتدائية بالرباط",
                    CaseType = "مدني",
                    Opponent = "شركة البناء المغربية",
                    Subject = "نزاع عقاري حول ملكية أرض",
                    Client = "أحمد محمد علي",
                    Lawyer = "الأستاذ محمد العلوي",
                    FileValue = 500000,
                    Priority = "مهم",
                    Status = "نشط",
                    Category = "عقاري",
                    ProcedureType = "جلسة مرافعة",
                    Decision = "تأجيل للمداولة",
                    NextSessionDate = DateTime.Now.AddDays(15),
                    CreatedDate = DateTime.Now.AddDays(-30),
                    IsElectronicTracking = true,
                    ElectronicFileNumber = "2024001",
                    ElectronicFileCode = "MD",
                    ElectronicYear = 2024,
                    AppealCourt = "محكمة الاستئناف بالرباط",
                    SearchInPrimaryCourts = true,
                    LastSyncDate = DateTime.Now.AddHours(-2),
                    SyncStatus = "متزامن"
                },
                new AdvancedFileModel
                {
                    Id = 2,
                    FileNumber = "2024/002",
                    OfficeReference = "REF-002",
                    FileType = "استئناف",
                    Court = "المحكمة التجارية بالدار البيضاء",
                    CaseType = "تجاري",
                    Opponent = "البنك الشعبي",
                    Subject = "نزاع تجاري حول عقد توريد",
                    Client = "فاطمة الزهراء",
                    Lawyer = "الأستاذة خديجة الإدريسي",
                    FileValue = 1200000,
                    Priority = "عاجل",
                    Status = "في الجلسات",
                    Category = "تجاري",
                    ProcedureType = "جلسة تحقيق",
                    Decision = "طلب خبرة",
                    NextSessionDate = DateTime.Now.AddDays(7),
                    CreatedDate = DateTime.Now.AddDays(-25),
                    IsElectronicTracking = true,
                    ElectronicFileNumber = "2024002",
                    ElectronicFileCode = "TJ",
                    ElectronicYear = 2024,
                    LastSyncDate = DateTime.Now.AddHours(-1),
                    SyncStatus = "متزامن"
                },
                new AdvancedFileModel
                {
                    Id = 3,
                    FileNumber = "2024/003",
                    OfficeReference = "REF-003",
                    FileType = "دعوى",
                    Court = "محكمة الأسرة بفاس",
                    CaseType = "عائلي",
                    Opponent = "محمد الحسن",
                    Subject = "قضية طلاق وحضانة",
                    Client = "عائشة بنت محمد",
                    Lawyer = "الأستاذة فاطمة الزهراء",
                    FileValue = 0,
                    Priority = "عادي",
                    Status = "مؤرشف",
                    Category = "أحوال شخصية",
                    ProcedureType = "جلسة صلح",
                    Decision = "حكم نهائي",
                    CreatedDate = DateTime.Now.AddDays(-60),
                    IsElectronicTracking = false,
                    SyncStatus = "غير متزامن"
                },
                new AdvancedFileModel
                {
                    Id = 4,
                    FileNumber = "2024/004",
                    OfficeReference = "REF-004",
                    FileType = "شكوى",
                    Court = "المحكمة الجنائية بمراكش",
                    CaseType = "جنائي",
                    Opponent = "مجهول",
                    Subject = "قضية احتيال مالي",
                    Client = "يوسف الإدريسي",
                    Lawyer = "الأستاذ عبد الرحمن الفاسي",
                    FileValue = 800000,
                    Priority = "عاجل",
                    Status = "نشط",
                    Category = "جنائي",
                    ProcedureType = "جلسة تحقيق",
                    Decision = "قيد التحقيق",
                    NextSessionDate = DateTime.Now.AddDays(3),
                    CreatedDate = DateTime.Now.AddDays(-15),
                    IsElectronicTracking = true,
                    ElectronicFileNumber = "2024004",
                    ElectronicFileCode = "JN",
                    ElectronicYear = 2024,
                    LastSyncDate = DateTime.Now.AddMinutes(-30),
                    SyncStatus = "متزامن"
                },
                new AdvancedFileModel
                {
                    Id = 5,
                    FileNumber = "2024/005",
                    OfficeReference = "REF-005",
                    FileType = "طعن",
                    Court = "المحكمة الإدارية بالرباط",
                    CaseType = "إداري",
                    Opponent = "وزارة الداخلية",
                    Subject = "طعن في قرار إداري",
                    Client = "جمعية حقوق الإنسان",
                    Lawyer = "الأستاذ يوسف الحسني",
                    FileValue = 0,
                    Priority = "مهم",
                    Status = "في الجلسات",
                    Category = "إداري",
                    ProcedureType = "جلسة مرافعة",
                    Decision = "قيد النظر",
                    NextSessionDate = DateTime.Now.AddDays(20),
                    CreatedDate = DateTime.Now.AddDays(-10),
                    IsElectronicTracking = false,
                    SyncStatus = "غير متزامن"
                }
            };

            foreach (var file in sampleFiles)
            {
                AllFiles.Add(file);
                FilteredFiles.Add(file);
            }
        }

        #endregion

        #region Statistics

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        private void UpdateStatistics()
        {
            if (AllFiles == null) return;

            TotalFilesCount.Text = AllFiles.Count.ToString();
            ActiveFilesCount.Text = AllFiles.Count(f => f.Status == "نشط").ToString();
            InSessionsCount.Text = AllFiles.Count(f => f.Status == "في الجلسات").ToString();
            ArchivedFilesCount.Text = AllFiles.Count(f => f.Status == "مؤرشف").ToString();
            SyncedFilesCount.Text = AllFiles.Count(f => f.IsElectronicTracking && f.SyncStatus == "متزامن").ToString();
        }

        #endregion

        #region Search and Filter

        /// <summary>
        /// تطبيق المرشحات
        /// </summary>
        private void ApplyFilters()
        {
            if (AllFiles == null || FilteredFiles == null) return;

            var filtered = AllFiles.AsEnumerable();

            // تصفية البحث النصي
            if (_isSearchFocused && !string.IsNullOrWhiteSpace(SearchTextBox.Text) && 
                SearchTextBox.Text != "البحث في الملفات...")
            {
                var searchText = SearchTextBox.Text.ToLower();
                filtered = filtered.Where(f => 
                    f.FileNumber.ToLower().Contains(searchText) ||
                    f.Client.ToLower().Contains(searchText) ||
                    f.CaseType.ToLower().Contains(searchText) ||
                    f.Court.ToLower().Contains(searchText) ||
                    f.Subject.ToLower().Contains(searchText) ||
                    f.Opponent.ToLower().Contains(searchText));
            }

            // تصفية الحالة
            if (StatusFilterComboBox.SelectedItem is ComboBoxItem statusItem && 
                statusItem.Content.ToString() != "جميع الحالات")
            {
                filtered = filtered.Where(f => f.Status == statusItem.Content.ToString());
            }

            // تصفية نوع القضية
            if (CaseTypeFilterComboBox.SelectedItem is string caseType && 
                caseType != "جميع أنواع القضايا")
            {
                filtered = filtered.Where(f => f.CaseType == caseType);
            }

            // تصفية المحكمة
            if (CourtFilterComboBox.SelectedItem is string court && 
                court != "جميع المحاكم")
            {
                filtered = filtered.Where(f => f.Court == court);
            }

            // تصفية الأولوية
            if (PriorityFilterComboBox.SelectedItem is ComboBoxItem priorityItem && 
                priorityItem.Content.ToString() != "جميع الأولويات")
            {
                filtered = filtered.Where(f => f.Priority == priorityItem.Content.ToString());
            }

            // تصفية التتبع الإلكتروني
            if (SyncFilterComboBox.SelectedItem is ComboBoxItem syncItem)
            {
                var syncFilter = syncItem.Content.ToString();
                if (syncFilter == "متزامنة إلكترونياً")
                    filtered = filtered.Where(f => f.IsElectronicTracking && f.SyncStatus == "متزامن");
                else if (syncFilter == "غير متزامنة")
                    filtered = filtered.Where(f => !f.IsElectronicTracking || f.SyncStatus != "متزامن");
            }

            // تصفية التاريخ
            if (DateFromPicker.SelectedDate.HasValue)
            {
                filtered = filtered.Where(f => f.CreatedDate >= DateFromPicker.SelectedDate.Value);
            }

            if (DateToPicker.SelectedDate.HasValue)
            {
                filtered = filtered.Where(f => f.CreatedDate <= DateToPicker.SelectedDate.Value.AddDays(1));
            }

            FilteredFiles.Clear();
            foreach (var file in filtered)
            {
                FilteredFiles.Add(file);
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// إضافة ملف جديد
        /// </summary>
        private void AddFileButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addWindow = new AdvancedAddFileWindow();
                if (addWindow.ShowDialog() == true)
                {
                    var newFile = addWindow.NewFile;
                    newFile.Id = AllFiles.Count > 0 ? AllFiles.Max(f => f.Id) + 1 : 1;
                    
                    AllFiles.Add(newFile);
                    ApplyFilters();
                    UpdateStatistics();
                    
                    MessageBox.Show("تم إضافة الملف بنجاح!", "نجح الحفظ", 
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الملف: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// مزامنة جميع الملفات
        /// </summary>
        private async void SyncAllButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SyncAllButton.IsEnabled = false;
                SyncAllButton.Content = "🔄 جاري المزامنة...";

                var electronicFiles = AllFiles.Where(f => f.IsElectronicTracking).ToList();
                if (electronicFiles.Count == 0)
                {
                    MessageBox.Show("لا توجد ملفات مفعلة للتتبع الإلكتروني", "تنبيه", 
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                var results = await _courtsService.SyncFilesAsync(electronicFiles);
                var successCount = results.Count(r => r.IsFound);

                UpdateStatistics();
                ApplyFilters();

                MessageBox.Show($"تمت مزامنة {successCount} من أصل {results.Count} ملف", "نتيجة المزامنة", 
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في المزامنة: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                SyncAllButton.IsEnabled = true;
                SyncAllButton.Content = "🔄 مزامنة جميع الملفات";
            }
        }

        /// <summary>
        /// البحث في النص
        /// </summary>
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (!_isSearchFocused) return;
            ApplyFilters();
        }

        /// <summary>
        /// التركيز على مربع البحث
        /// </summary>
        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            _isSearchFocused = true;
            if (SearchTextBox.Text == "البحث في الملفات...")
            {
                SearchTextBox.Text = "";
                SearchTextBox.Foreground = System.Windows.Media.Brushes.Black;
            }
        }

        /// <summary>
        /// فقدان التركيز من مربع البحث
        /// </summary>
        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                _isSearchFocused = false;
                SearchTextBox.Text = "البحث في الملفات...";
                SearchTextBox.Foreground = System.Windows.Media.Brushes.Gray;
            }
        }

        /// <summary>
        /// تصفية حسب الحالة
        /// </summary>
        private void StatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        /// <summary>
        /// تصفية حسب نوع القضية
        /// </summary>
        private void CaseTypeFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        /// <summary>
        /// تصفية حسب المحكمة
        /// </summary>
        private void CourtFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        /// <summary>
        /// تصفية حسب الأولوية
        /// </summary>
        private void PriorityFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        /// <summary>
        /// تصفية حسب التتبع الإلكتروني
        /// </summary>
        private void SyncFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        /// <summary>
        /// تصفية حسب التاريخ
        /// </summary>
        private void DateFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        /// <summary>
        /// النقر المزدوج على الملف
        /// </summary>
        private void FilesDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (FilesDataGrid.SelectedItem is AdvancedFileModel selectedFile)
            {
                EditFile_Click(sender, e);
            }
        }

        /// <summary>
        /// تعديل الملف
        /// </summary>
        private void EditFile_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var file = button?.DataContext as AdvancedFileModel;
                if (file == null) return;

                var editWindow = new AdvancedAddFileWindow(file);
                if (editWindow.ShowDialog() == true)
                {
                    var updatedFile = editWindow.NewFile;
                    var index = AllFiles.IndexOf(file);
                    if (index >= 0)
                    {
                        AllFiles[index] = updatedFile;
                        ApplyFilters();
                        UpdateStatistics();

                        MessageBox.Show("تم تحديث الملف بنجاح!", "نجح التحديث",
                                       MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل الملف: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حذف الملف
        /// </summary>
        private void DeleteFile_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var file = button?.DataContext as AdvancedFileModel;
                if (file == null) return;

                var result = MessageBox.Show($"هل أنت متأكد من حذف الملف رقم {file.FileNumber}؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                                           "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    AllFiles.Remove(file);
                    ApplyFilters();
                    UpdateStatistics();

                    MessageBox.Show("تم حذف الملف بنجاح!", "نجح الحذف",
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الملف: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// طباعة الملف
        /// </summary>
        private void PrintFile_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var file = button?.DataContext as AdvancedFileModel;
                if (file == null) return;

                MessageBox.Show($"سيتم طباعة الملف رقم {file.FileNumber}", "طباعة الملف",
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة الملف: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// مزامنة الملف
        /// </summary>
        private async void SyncFile_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var file = button?.DataContext as AdvancedFileModel;
                if (file == null) return;

                if (!file.IsElectronicTracking)
                {
                    MessageBox.Show("هذا الملف غير مفعل للتتبع الإلكتروني", "تنبيه",
                                   MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                button.IsEnabled = false;
                button.Content = "🔄";

                var result = await _courtsService.SearchFileAsync(
                    file.ElectronicFileNumber,
                    file.ElectronicFileCode,
                    file.ElectronicYear,
                    file.Court,
                    file.SearchInPrimaryCourts
                );

                if (result.IsFound)
                {
                    file.LastSyncDate = DateTime.Now;
                    file.SyncStatus = "متزامن";
                    if (result.NextSessionDate.HasValue)
                        file.NextSessionDate = result.NextSessionDate;
                    if (!string.IsNullOrEmpty(result.Decision))
                        file.Decision = result.Decision;

                    UpdateStatistics();
                    ApplyFilters();

                    MessageBox.Show("تمت مزامنة الملف بنجاح!", "نجحت المزامنة",
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    file.SyncStatus = "خطأ في المزامنة";
                    MessageBox.Show($"فشلت مزامنة الملف: {result.ErrorMessage}", "فشلت المزامنة",
                                   MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في مزامنة الملف: {ex.Message}", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                var button = sender as Button;
                if (button != null)
                {
                    button.IsEnabled = true;
                    button.Content = "🔄";
                }
            }
        }

        /// <summary>
        /// تصدير البيانات
        /// </summary>
        private void Export_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم إضافة وظيفة التصدير قريباً", "قيد التطوير",
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// طباعة القائمة
        /// </summary>
        private void Print_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم إضافة وظيفة الطباعة قريباً", "قيد التطوير",
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
