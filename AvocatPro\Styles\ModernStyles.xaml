<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- الألوان الحديثة -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="#1E3A8A"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="#3B82F6"/>
    <SolidColorBrush x:Key="AccentBrush" Color="#F59E0B"/>
    <SolidColorBrush x:Key="SuccessBrush" Color="#10B981"/>
    <SolidColorBrush x:Key="DangerBrush" Color="#EF4444"/>
    <SolidColorBrush x:Key="WarningBrush" Color="#F59E0B"/>
    <SolidColorBrush x:Key="InfoBrush" Color="#06B6D4"/>
    
    <!-- ألوان الخلفية -->
    <SolidColorBrush x:Key="BackgroundBrush" Color="#F8FAFC"/>
    <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="CardBrush" Color="#FFFFFF"/>
    
    <!-- ألوان النص -->
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="#1F2937"/>
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="#6B7280"/>
    <SolidColorBrush x:Key="TextMutedBrush" Color="#9CA3AF"/>
    
    <!-- ألوان الحدود -->
    <SolidColorBrush x:Key="BorderBrush" Color="#E5E7EB"/>
    <SolidColorBrush x:Key="BorderLightBrush" Color="#F3F4F6"/>
    
    <!-- تدرجات لونية -->
    <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#1E3A8A" Offset="0"/>
        <GradientStop Color="#3B82F6" Offset="1"/>
    </LinearGradientBrush>
    
    <LinearGradientBrush x:Key="AccentGradientBrush" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#F59E0B" Offset="0"/>
        <GradientStop Color="#FBBF24" Offset="1"/>
    </LinearGradientBrush>
    
    <LinearGradientBrush x:Key="SuccessGradientBrush" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#10B981" Offset="0"/>
        <GradientStop Color="#34D399" Offset="1"/>
    </LinearGradientBrush>

    <!-- ظلال -->
    <DropShadowEffect x:Key="CardShadow" Color="#000000" Opacity="0.1" BlurRadius="10" ShadowDepth="2" Direction="270"/>
    <DropShadowEffect x:Key="ButtonShadow" Color="#000000" Opacity="0.15" BlurRadius="8" ShadowDepth="2" Direction="270"/>
    <DropShadowEffect x:Key="HeaderShadow" Color="#000000" Opacity="0.08" BlurRadius="12" ShadowDepth="3" Direction="270"/>

    <!-- أنماط الأزرار الحديثة -->
    <Style x:Key="ModernButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="16,10"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Effect" Value="{StaticResource ButtonShadow}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border" 
                            Background="{TemplateBinding Background}"
                            CornerRadius="8"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource PrimaryLightBrush}"/>
                            <Setter Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="#000000" Opacity="0.2" BlurRadius="12" ShadowDepth="3" Direction="270"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource PrimaryBrush}"/>
                            <Setter TargetName="border" Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- زر النجاح -->
    <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
        <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#059669"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- زر الخطر -->
    <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
        <Setter Property="Background" Value="{StaticResource DangerBrush}"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#DC2626"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- زر التحذير -->
    <Style x:Key="WarningButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
        <Setter Property="Background" Value="{StaticResource WarningBrush}"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#D97706"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- زر المعلومات -->
    <Style x:Key="InfoButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
        <Setter Property="Background" Value="{StaticResource InfoBrush}"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#0891B2"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- زر ثانوي -->
    <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
                <Setter Property="Foreground" Value="White"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- أنماط البطاقات -->
    <Style x:Key="ModernCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource CardBrush}"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="Padding" Value="20"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
        <Setter Property="Effect" Value="{StaticResource CardShadow}"/>
    </Style>

    <!-- أنماط النصوص -->
    <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="24"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="Margin" Value="0,0,0,8"/>
    </Style>

    <Style x:Key="SubHeaderTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="Margin" Value="0,0,0,6"/>
    </Style>

    <Style x:Key="BodyTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
        <Setter Property="LineHeight" Value="20"/>
    </Style>

    <Style x:Key="CaptionTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Foreground" Value="{StaticResource TextMutedBrush}"/>
    </Style>

    <!-- أنماط حقول الإدخال -->
    <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
        <Setter Property="Padding" Value="15,12"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="Background" Value="White"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="HorizontalContentAlignment" Value="Right"/>
        <Setter Property="MinHeight" Value="40"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="10">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.05" BlurRadius="8" ShadowDepth="2"/>
                        </Border.Effect>
                        <ScrollViewer x:Name="PART_ContentHost"
                                      Focusable="false"
                                      HorizontalScrollBarVisibility="Hidden"
                                      VerticalScrollBarVisibility="Hidden"
                                      Margin="{TemplateBinding Padding}"
                                      VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                            <Setter TargetName="border" Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="#3B82F6" Opacity="0.2" BlurRadius="12" ShadowDepth="0"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                            <Setter TargetName="border" Property="Background" Value="#F8FAFC"/>
                            <Setter TargetName="border" Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="#1D4ED8" Opacity="0.3" BlurRadius="15" ShadowDepth="0"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="#F9FAFB"/>
                            <Setter TargetName="border" Property="BorderBrush" Value="#D1D5DB"/>
                            <Setter Property="Foreground" Value="#9CA3AF"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- نمط TextBox للبحث -->
    <Style x:Key="SearchTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource ModernTextBoxStyle}">
        <Setter Property="Background" Value="#F8FAFC"/>
        <Setter Property="BorderBrush" Value="#E2E8F0"/>
        <Setter Property="Padding" Value="40,12,15,12"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="25">
                        <Grid>
                            <TextBlock Text="🔍" FontSize="16" Foreground="#64748B"
                                       VerticalAlignment="Center" HorizontalAlignment="Right"
                                       Margin="15,0"/>
                            <ScrollViewer x:Name="PART_ContentHost"
                                          Focusable="false"
                                          HorizontalScrollBarVisibility="Hidden"
                                          VerticalScrollBarVisibility="Hidden"
                                          Margin="{TemplateBinding Padding}"
                                          VerticalAlignment="Center"/>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                            <Setter TargetName="border" Property="Background" Value="White"/>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                            <Setter TargetName="border" Property="Background" Value="White"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- أنماط القوائم المنسدلة -->
    <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
        <Setter Property="Padding" Value="15,12"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="Background" Value="White"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="MinHeight" Value="40"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ComboBox">
                    <Grid>
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="10">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" Opacity="0.05" BlurRadius="8" ShadowDepth="2"/>
                            </Border.Effect>
                            <Grid>
                                <ContentPresenter x:Name="contentPresenter"
                                                  Margin="{TemplateBinding Padding}"
                                                  VerticalAlignment="Center"
                                                  HorizontalAlignment="Right"/>
                                <Path x:Name="arrow"
                                      Data="M 0 0 L 4 4 L 8 0 Z"
                                      Fill="{StaticResource TextSecondaryBrush}"
                                      HorizontalAlignment="Left"
                                      Margin="12,0,0,0"
                                      VerticalAlignment="Center"/>
                            </Grid>
                        </Border>
                        <Popup x:Name="PART_Popup"
                               Placement="Bottom"
                               IsOpen="{TemplateBinding IsDropDownOpen}">
                            <Border Background="White"
                                    BorderBrush="{StaticResource BorderBrush}"
                                    BorderThickness="2"
                                    CornerRadius="10"
                                    Effect="{StaticResource CardShadow}">
                                <ScrollViewer MaxHeight="200">
                                    <ItemsPresenter/>
                                </ScrollViewer>
                            </Border>
                        </Popup>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                            <Setter TargetName="border" Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="#3B82F6" Opacity="0.2" BlurRadius="12" ShadowDepth="0"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                            <Setter TargetName="border" Property="Background" Value="#F8FAFC"/>
                            <Setter TargetName="border" Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="#1D4ED8" Opacity="0.3" BlurRadius="15" ShadowDepth="0"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="#F9FAFB"/>
                            <Setter TargetName="border" Property="BorderBrush" Value="#D1D5DB"/>
                            <Setter Property="Foreground" Value="#9CA3AF"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- أنماط مربعات الاختيار -->
    <Style x:Key="ModernCheckBoxStyle" TargetType="CheckBox">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="CheckBox">
                    <StackPanel Orientation="Horizontal">
                        <Border x:Name="checkBorder"
                                Width="20" Height="20"
                                Background="{StaticResource SurfaceBrush}"
                                BorderBrush="{StaticResource BorderBrush}"
                                BorderThickness="2"
                                CornerRadius="4"
                                Margin="0,0,8,0">
                            <Path x:Name="checkMark"
                                  Data="M 2 6 L 6 10 L 14 2"
                                  Stroke="{StaticResource PrimaryBrush}"
                                  StrokeThickness="2"
                                  Visibility="Collapsed"/>
                        </Border>
                        <ContentPresenter VerticalAlignment="Center"/>
                    </StackPanel>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="checkMark" Property="Visibility" Value="Visible"/>
                            <Setter TargetName="checkBorder" Property="Background" Value="{StaticResource PrimaryBrush}"/>
                            <Setter TargetName="checkBorder" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                            <Setter TargetName="checkMark" Property="Stroke" Value="White"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="checkBorder" Property="BorderBrush" Value="{StaticResource PrimaryLightBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- أنماط الجداول -->
    <Style x:Key="ModernDataGridStyle" TargetType="DataGrid">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="RowBackground" Value="White"/>
        <Setter Property="AlternatingRowBackground" Value="#F8FAFC"/>
        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
        <Setter Property="HorizontalGridLinesBrush" Value="#E5E7EB"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
        <Setter Property="CanUserResizeRows" Value="False"/>
        <Setter Property="CanUserAddRows" Value="False"/>
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="RowHeight" Value="55"/>
        <Setter Property="FlowDirection" Value="RightToLeft"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" Opacity="0.08" BlurRadius="12" ShadowDepth="3"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- أنماط رؤوس الجداول -->
    <Style x:Key="ModernDataGridColumnHeaderStyle" TargetType="DataGridColumnHeader">
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <GradientStop Color="#1E3A8A" Offset="0"/>
                    <GradientStop Color="#3B82F6" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="FontSize" Value="15"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="Padding" Value="20,15"/>
        <Setter Property="BorderBrush" Value="#1E3A8A"/>
        <Setter Property="BorderThickness" Value="0,0,1,0"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="Height" Value="50"/>
    </Style>

    <!-- أنماط خلايا الجداول -->
    <Style x:Key="ModernDataGridCellStyle" TargetType="DataGridCell">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="20,12"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="Foreground" Value="#374151"/>
        <Style.Triggers>
            <Trigger Property="IsSelected" Value="True">
                <Setter Property="Background" Value="#E0F2FE"/>
                <Setter Property="Foreground" Value="#1E3A8A"/>
            </Trigger>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#F0F8FF"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- أنماط أزرار الإجراءات في الجداول -->
    <Style x:Key="ActionButtonStyle" TargetType="Button">
        <Setter Property="Width" Value="35"/>
        <Setter Property="Height" Value="35"/>
        <Setter Property="Margin" Value="2"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            CornerRadius="8">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="4" ShadowDepth="1"/>
                        </Border.Effect>
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="1.1" ScaleY="1.1"/>
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="border" Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="Black" Opacity="0.2" BlurRadius="8" ShadowDepth="2"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="0.95" ScaleY="0.95"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- أزرار الإجراءات الملونة -->
    <Style x:Key="EditButtonStyle" TargetType="Button" BasedOn="{StaticResource ActionButtonStyle}">
        <Setter Property="Background" Value="#10B981"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <Style x:Key="DeleteButtonStyle" TargetType="Button" BasedOn="{StaticResource ActionButtonStyle}">
        <Setter Property="Background" Value="#EF4444"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <Style x:Key="ViewButtonStyle" TargetType="Button" BasedOn="{StaticResource ActionButtonStyle}">
        <Setter Property="Background" Value="#3B82F6"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <Style x:Key="PrintButtonStyle" TargetType="Button" BasedOn="{StaticResource ActionButtonStyle}">
        <Setter Property="Background" Value="#8B5CF6"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <!-- أنماط شريط التقدم -->
    <Style x:Key="ModernProgressBarStyle" TargetType="ProgressBar">
        <Setter Property="Height" Value="8"/>
        <Setter Property="Background" Value="{StaticResource BorderLightBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryGradientBrush}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ProgressBar">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="4">
                        <Rectangle x:Name="PART_Track"
                                   Fill="{TemplateBinding Foreground}"
                                   RadiusX="4" RadiusY="4"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- أنماط التبويبات -->
    <Style x:Key="ModernTabControlStyle" TargetType="TabControl">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="0"/>
    </Style>

    <Style x:Key="ModernTabItemStyle" TargetType="TabItem">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="20,12"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TabItem">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            Padding="{TemplateBinding Padding}"
                            CornerRadius="8,8,0,0"
                            Margin="0,0,4,0">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource SurfaceBrush}"/>
                            <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource BorderLightBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- أنماط القوائم -->
    <Style x:Key="ModernListBoxStyle" TargetType="ListBox">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Disabled"/>
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto"/>
    </Style>

    <Style x:Key="ModernListBoxItemStyle" TargetType="ListBoxItem">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="16,12"/>
        <Setter Property="Margin" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ListBoxItem">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource PrimaryLightBrush}"/>
                            <Setter Property="Foreground" Value="White"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource BorderLightBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- أنماط أشرطة التمرير -->
    <Style x:Key="ModernScrollBarStyle" TargetType="ScrollBar">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Width" Value="12"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ScrollBar">
                    <Grid>
                        <Border Background="{TemplateBinding Background}" CornerRadius="6"/>
                        <Track x:Name="PART_Track" IsDirectionReversed="True">
                            <Track.Thumb>
                                <Thumb Background="{StaticResource BorderBrush}"
                                       Width="8"
                                       Margin="2"/>
                            </Track.Thumb>
                        </Track>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- أنماط النوافذ المنبثقة -->
    <Style x:Key="ModernToolTipStyle" TargetType="ToolTip">
        <Setter Property="Background" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ToolTip">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="6"
                            Padding="{TemplateBinding Padding}"
                            Effect="{StaticResource CardShadow}">
                        <ContentPresenter/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
