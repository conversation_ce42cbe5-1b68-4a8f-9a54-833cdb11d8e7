<Window x:Class="AvocatPro.Views.Windows.AdvancedAddFileWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        Title="إضافة/تعديل ملف" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize"
        Background="#F8FAFC" FlowDirection="RightToLeft">

    <Window.Resources>
        <Style TargetType="TextBox">
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#D1D5DB"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
            <Style.Triggers>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="BorderBrush" Value="#6366F1"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style TargetType="ComboBox">
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#D1D5DB"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
        </Style>

        <Style TargetType="DatePicker">
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#D1D5DB"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
        </Style>

        <Style TargetType="CheckBox">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <Style TargetType="Label">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#374151"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Background="White" CornerRadius="12" Padding="24" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock x:Name="WindowTitle" Text="إضافة ملف جديد" FontSize="24" FontWeight="Bold" Foreground="#1F2937"/>
                    <TextBlock Text="نظام إدارة الملفات القانونية المتقدم" FontSize="14" Foreground="#6B7280"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="SyncButton" Content="🔄 مزامنة" Background="#10B981" Foreground="White" 
                            BorderThickness="0" Padding="16,8" FontSize="14" FontWeight="SemiBold"
                            Cursor="Hand" Click="SyncButton_Click" Margin="0,0,10,0">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" 
                                                    CornerRadius="8" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#059669"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <TabControl Background="Transparent" BorderThickness="0">
                
                <!-- تبويب المعلومات الأساسية -->
                <TabItem Header="📋 المعلومات الأساسية" FontSize="14" FontWeight="SemiBold">
                    <Border Background="White" CornerRadius="12" Padding="24" Margin="0,10">
                        <Border.Effect>
                            <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                        </Border.Effect>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- العمود الأول -->
                            <StackPanel Grid.Column="0">
                                <Label Content="رقم الملف *"/>
                                <TextBox x:Name="FileNumberTextBox" Margin="0,0,0,15"/>
                                
                                <Label Content="مرجع المكتب *"/>
                                <TextBox x:Name="OfficeReferenceTextBox" Margin="0,0,0,15"/>
                                
                                <Label Content="مرجع المحكمة"/>
                                <TextBox x:Name="CourtReferenceTextBox" Margin="0,0,0,15"/>
                                
                                <Label Content="نوع الملف *"/>
                                <ComboBox x:Name="FileTypeComboBox" Margin="0,0,0,15"/>
                                
                                <Label Content="المحكمة المختصة *"/>
                                <ComboBox x:Name="CourtComboBox" IsEditable="True" Margin="0,0,0,15"/>
                                
                                <Label Content="نوع القضية *"/>
                                <ComboBox x:Name="CaseTypeComboBox" Margin="0,0,0,15"/>
                                
                                <Label Content="الخصم"/>
                                <TextBox x:Name="OpponentTextBox" Margin="0,0,0,15"/>
                            </StackPanel>
                            
                            <!-- العمود الثاني -->
                            <StackPanel Grid.Column="2">
                                <Label Content="الموضوع *"/>
                                <TextBox x:Name="SubjectTextBox" Height="60" TextWrapping="Wrap" 
                                         AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="0,0,0,15"/>
                                
                                <Label Content="الموكّل *"/>
                                <ComboBox x:Name="ClientComboBox" IsEditable="True" Margin="0,0,0,15"/>
                                
                                <Label Content="المحامي المكلف"/>
                                <ComboBox x:Name="LawyerComboBox" IsEditable="True" Margin="0,0,0,15"/>
                                
                                <Label Content="قيمة الملف (درهم)"/>
                                <TextBox x:Name="FileValueTextBox" Margin="0,0,0,15"/>
                                
                                <Label Content="الأولوية"/>
                                <ComboBox x:Name="PriorityComboBox" Margin="0,0,0,15"/>
                                
                                <Label Content="الحالة"/>
                                <ComboBox x:Name="StatusComboBox" Margin="0,0,0,15"/>
                                
                                <Label Content="الفئة"/>
                                <TextBox x:Name="CategoryTextBox" Margin="0,0,0,15"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                </TabItem>
                
                <!-- تبويب الإجراءات والجلسات -->
                <TabItem Header="⚖️ الإجراءات والجلسات" FontSize="14" FontWeight="SemiBold">
                    <Border Background="White" CornerRadius="12" Padding="24" Margin="0,10">
                        <Border.Effect>
                            <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                        </Border.Effect>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- العمود الأول -->
                            <StackPanel Grid.Column="0">
                                <Label Content="نوع الإجراء"/>
                                <ComboBox x:Name="ProcedureTypeComboBox" Margin="0,0,0,15"/>
                                
                                <Label Content="القرار"/>
                                <TextBox x:Name="DecisionTextBox" Height="80" TextWrapping="Wrap" 
                                         AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="0,0,0,15"/>
                                
                                <Label Content="تاريخ الجلسة المقبلة"/>
                                <DatePicker x:Name="NextSessionDatePicker" Margin="0,0,0,15"/>
                            </StackPanel>
                            
                            <!-- العمود الثاني -->
                            <StackPanel Grid.Column="2">
                                <Label Content="ملاحظات"/>
                                <TextBox x:Name="NotesTextBox" Height="150" TextWrapping="Wrap" 
                                         AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="0,0,0,15"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                </TabItem>
                
                <!-- تبويب التتبع الإلكتروني -->
                <TabItem Header="🌐 التتبع الإلكتروني" FontSize="14" FontWeight="SemiBold">
                    <Border Background="White" CornerRadius="12" Padding="24" Margin="0,10">
                        <Border.Effect>
                            <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                        </Border.Effect>
                        
                        <StackPanel>
                            <CheckBox x:Name="ElectronicTrackingCheckBox" Content="تفعيل التتبع الإلكتروني" 
                                      FontSize="16" FontWeight="SemiBold" Margin="0,0,0,20"
                                      Checked="ElectronicTrackingCheckBox_Checked" 
                                      Unchecked="ElectronicTrackingCheckBox_Unchecked"/>
                            
                            <Grid x:Name="ElectronicTrackingPanel" IsEnabled="False">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="20"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <!-- العمود الأول -->
                                <StackPanel Grid.Column="0">
                                    <Label Content="الرقم الكامل للملف *"/>
                                    <TextBox x:Name="ElectronicFileNumberTextBox" Margin="0,0,0,15"/>
                                    
                                    <Label Content="رمز الملف *"/>
                                    <TextBox x:Name="ElectronicFileCodeTextBox" Margin="0,0,0,15"/>
                                    
                                    <Label Content="السنة *"/>
                                    <TextBox x:Name="ElectronicYearTextBox" Margin="0,0,0,15"/>
                                    
                                    <CheckBox x:Name="SearchInPrimaryCourtsCheckBox" Content="البحث في المحاكم الابتدائية" 
                                              IsChecked="True" Margin="0,0,0,15"/>
                                </StackPanel>
                                
                                <!-- العمود الثاني -->
                                <StackPanel Grid.Column="2">
                                    <Label Content="محكمة الاستئناف"/>
                                    <ComboBox x:Name="AppealCourtComboBox" Margin="0,0,0,15"/>
                                    
                                    <Button x:Name="SearchElectronicButton" Content="🔍 البحث في المحاكم الإلكترونية" 
                                            Background="#6366F1" Foreground="White" BorderThickness="0" 
                                            Padding="16,12" FontSize="14" FontWeight="SemiBold"
                                            Cursor="Hand" Click="SearchElectronicButton_Click" Margin="0,0,0,15">
                                        <Button.Style>
                                            <Style TargetType="Button">
                                                <Setter Property="Template">
                                                    <Setter.Value>
                                                        <ControlTemplate TargetType="Button">
                                                            <Border Background="{TemplateBinding Background}" 
                                                                    CornerRadius="8" Padding="{TemplateBinding Padding}">
                                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                            </Border>
                                                            <ControlTemplate.Triggers>
                                                                <Trigger Property="IsMouseOver" Value="True">
                                                                    <Setter Property="Background" Value="#5B5BD6"/>
                                                                </Trigger>
                                                            </ControlTemplate.Triggers>
                                                        </ControlTemplate>
                                                    </Setter.Value>
                                                </Setter>
                                            </Style>
                                        </Button.Style>
                                    </Button>
                                    
                                    <TextBlock x:Name="SyncStatusTextBlock" Text="حالة المزامنة: غير متزامن" 
                                               FontSize="12" Foreground="#6B7280" Margin="0,10,0,0"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>
                </TabItem>
            </TabControl>
        </ScrollViewer>

        <!-- أزرار الإجراءات -->
        <Border Grid.Row="2" Background="White" CornerRadius="12" Padding="20" Margin="0,20,0,0">
            <Border.Effect>
                <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
            </Border.Effect>
            
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="SaveButton" Content="💾 حفظ" Background="#10B981" Foreground="White" 
                        BorderThickness="0" Padding="24,12" FontSize="16" FontWeight="SemiBold"
                        Cursor="Hand" Click="SaveButton_Click" Margin="0,0,15,0">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" 
                                                CornerRadius="8" Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#059669"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
                
                <Button x:Name="CancelButton" Content="❌ إلغاء" Background="#6B7280" Foreground="White" 
                        BorderThickness="0" Padding="24,12" FontSize="16" FontWeight="SemiBold"
                        Cursor="Hand" Click="CancelButton_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" 
                                                CornerRadius="8" Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#4B5563"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
