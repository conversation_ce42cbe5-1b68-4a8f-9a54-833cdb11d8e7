using AvocatPro.Models;

namespace AvocatPro.Services;

/// <summary>
/// واجهة خدمة إدارة المستخدمين
/// </summary>
public interface IUserService
{
    /// <summary>
    /// الحصول على جميع المستخدمين
    /// </summary>
    /// <returns>قائمة المستخدمين</returns>
    Task<List<User>> GetAllUsersAsync();

    /// <summary>
    /// الحصول على المستخدمين النشطين
    /// </summary>
    /// <returns>قائمة المستخدمين النشطين</returns>
    Task<List<User>> GetActiveUsersAsync();

    /// <summary>
    /// الحصول على مستخدم بالمعرف
    /// </summary>
    /// <param name="id">معرف المستخدم</param>
    /// <returns>المستخدم</returns>
    Task<User?> GetUserByIdAsync(int id);

    /// <summary>
    /// الحصول على مستخدم باسم المستخدم
    /// </summary>
    /// <param name="username">اسم المستخدم</param>
    /// <returns>المستخدم</returns>
    Task<User?> GetUserByUsernameAsync(string username);

    /// <summary>
    /// الحصول على مستخدم بالبريد الإلكتروني
    /// </summary>
    /// <param name="email">البريد الإلكتروني</param>
    /// <returns>المستخدم</returns>
    Task<User?> GetUserByEmailAsync(string email);

    /// <summary>
    /// إنشاء مستخدم جديد
    /// </summary>
    /// <param name="user">بيانات المستخدم</param>
    /// <param name="password">كلمة المرور</param>
    /// <returns>المستخدم المنشأ</returns>
    Task<User> CreateUserAsync(User user, string password);

    /// <summary>
    /// تحديث بيانات المستخدم
    /// </summary>
    /// <param name="user">بيانات المستخدم المحدثة</param>
    /// <returns>المستخدم المحدث</returns>
    Task<User> UpdateUserAsync(User user);

    /// <summary>
    /// حذف مستخدم (حذف منطقي)
    /// </summary>
    /// <param name="id">معرف المستخدم</param>
    /// <param name="deletedBy">المستخدم الذي قام بالحذف</param>
    /// <returns>true في حالة النجاح</returns>
    Task<bool> DeleteUserAsync(int id, string deletedBy);

    /// <summary>
    /// تفعيل/إلغاء تفعيل مستخدم
    /// </summary>
    /// <param name="id">معرف المستخدم</param>
    /// <param name="isActive">حالة التفعيل</param>
    /// <returns>true في حالة النجاح</returns>
    Task<bool> SetUserActiveStatusAsync(int id, bool isActive);

    /// <summary>
    /// تغيير دور المستخدم
    /// </summary>
    /// <param name="id">معرف المستخدم</param>
    /// <param name="newRole">الدور الجديد</param>
    /// <returns>true في حالة النجاح</returns>
    Task<bool> ChangeUserRoleAsync(int id, UserRole newRole);

    /// <summary>
    /// البحث في المستخدمين
    /// </summary>
    /// <param name="searchTerm">مصطلح البحث</param>
    /// <returns>قائمة المستخدمين المطابقين</returns>
    Task<List<User>> SearchUsersAsync(string searchTerm);

    /// <summary>
    /// الحصول على المستخدمين حسب الدور
    /// </summary>
    /// <param name="role">الدور</param>
    /// <returns>قائمة المستخدمين</returns>
    Task<List<User>> GetUsersByRoleAsync(UserRole role);

    /// <summary>
    /// التحقق من توفر اسم المستخدم
    /// </summary>
    /// <param name="username">اسم المستخدم</param>
    /// <param name="excludeUserId">معرف المستخدم المستثنى (للتحديث)</param>
    /// <returns>true إذا كان متاحاً</returns>
    Task<bool> IsUsernameAvailableAsync(string username, int? excludeUserId = null);

    /// <summary>
    /// التحقق من توفر البريد الإلكتروني
    /// </summary>
    /// <param name="email">البريد الإلكتروني</param>
    /// <param name="excludeUserId">معرف المستخدم المستثنى (للتحديث)</param>
    /// <returns>true إذا كان متاحاً</returns>
    Task<bool> IsEmailAvailableAsync(string email, int? excludeUserId = null);

    /// <summary>
    /// تحديث الصورة الشخصية
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <param name="profilePicturePath">مسار الصورة</param>
    /// <returns>true في حالة النجاح</returns>
    Task<bool> UpdateProfilePictureAsync(int userId, string profilePicturePath);

    /// <summary>
    /// تحديث إعدادات المستخدم
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <param name="settings">الإعدادات (JSON)</param>
    /// <returns>true في حالة النجاح</returns>
    Task<bool> UpdateUserSettingsAsync(int userId, string settings);

    /// <summary>
    /// الحصول على إحصائيات المستخدمين
    /// </summary>
    /// <returns>إحصائيات المستخدمين</returns>
    Task<UserStatistics> GetUserStatisticsAsync();

    /// <summary>
    /// إنشاء المستخدم الافتراضي إذا لم يكن موجوداً
    /// </summary>
    /// <returns>true إذا تم الإنشاء</returns>
    Task<bool> CreateDefaultUserIfNotExistsAsync();
}

/// <summary>
/// إحصائيات المستخدمين
/// </summary>
public class UserStatistics
{
    public int TotalUsers { get; set; }
    public int ActiveUsers { get; set; }
    public int InactiveUsers { get; set; }
    public int LockedUsers { get; set; }
    public Dictionary<UserRole, int> UsersByRole { get; set; } = new();
    public int UsersLoggedInToday { get; set; }
    public int UsersLoggedInThisWeek { get; set; }
    public int UsersLoggedInThisMonth { get; set; }
}
