using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;
using AvocatPro.Models;
using AvocatPro.Services;

namespace AvocatPro.Views.Windows
{
    /// <summary>
    /// نافذة تفاصيل الملف
    /// </summary>
    public partial class FileDetailsWindow : Window
    {
        #region Properties

        private ComprehensiveFileModel _file;
        private MoroccanCourtsIntegrationService _courtsService;

        #endregion

        #region Constructor

        public FileDetailsWindow(ComprehensiveFileModel file)
        {
            InitializeComponent();
            _file = file;
            _courtsService = new MoroccanCourtsIntegrationService();
            LoadFileDetails();
        }

        #endregion

        #region Methods

        /// <summary>
        /// تحميل تفاصيل الملف
        /// </summary>
        private void LoadFileDetails()
        {
            try
            {
                // العنوان
                FileNumberTitle.Text = $"تفاصيل الملف: {_file.FileNumber}";
                FileSubject.Text = _file.Subject;

                // المعلومات الأساسية
                FileNumberText.Text = _file.FileNumber;
                CourtReferenceText.Text = string.IsNullOrEmpty(_file.CourtReference) ? "غير محدد" : _file.CourtReference;
                OfficeReferenceText.Text = _file.OfficeReference;
                FileTypeText.Text = _file.FileType;
                CourtText.Text = _file.Court;
                CaseTypeText.Text = _file.CaseType;
                StatusText.Text = _file.Status;
                PriorityText.Text = _file.Priority;

                // تحديث ألوان الحالة والأولوية
                UpdateStatusColors();

                // معلومات الأطراف
                ClientText.Text = _file.Client;
                OpponentText.Text = _file.Opponent;
                AssignedLawyerText.Text = string.IsNullOrEmpty(_file.AssignedLawyer) ? "غير محدد" : _file.AssignedLawyer;
                SubjectText.Text = _file.Subject;

                // معلومات الإجراءات
                ProcedureTypeText.Text = string.IsNullOrEmpty(_file.ProcedureType) ? "غير محدد" : _file.ProcedureType;
                DecisionText.Text = string.IsNullOrEmpty(_file.Decision) ? "لا يوجد قرار بعد" : _file.Decision;

                // التواريخ
                CreatedDateText.Text = _file.CreatedDate.ToString("dd/MM/yyyy");
                LastUpdateText.Text = _file.LastUpdateDate.ToString("dd/MM/yyyy HH:mm");
                
                if (_file.NextSessionDate != default)
                {
                    NextSessionText.Text = _file.NextSessionDate.ToString("dd/MM/yyyy");
                    var daysUntil = (_file.NextSessionDate - DateTime.Now).Days;
                    if (daysUntil < 0)
                    {
                        NextSessionIcon.Text = "⏰";
                        NextSessionText.Text += " (فات الموعد)";
                    }
                    else if (daysUntil == 0)
                    {
                        NextSessionIcon.Text = "🔥";
                        NextSessionText.Text += " (اليوم)";
                    }
                    else if (daysUntil <= 7)
                    {
                        NextSessionIcon.Text = "⚠️";
                        NextSessionText.Text += $" (خلال {daysUntil} أيام)";
                    }
                }
                else
                {
                    NextSessionIcon.Text = "📅";
                    NextSessionText.Text = "لا توجد جلسة مقررة";
                }

                if (_file.LastSessionDate != default)
                {
                    LastSessionText.Text = _file.LastSessionDate.ToString("dd/MM/yyyy");
                }
                else
                {
                    LastSessionText.Text = "لا توجد جلسات سابقة";
                }

                // معلومات إضافية
                EstimatedValueText.Text = _file.EstimatedValue > 0 ? 
                    $"{_file.EstimatedValue:N0} درهم" : "غير محدد";
                SessionsCountText.Text = _file.SessionsCount.ToString();
                NotesText.Text = string.IsNullOrEmpty(_file.Notes) ? "لا توجد ملاحظات" : _file.Notes;

                // التتبع الإلكتروني
                UpdateElectronicTrackingInfo();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تفاصيل الملف: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث ألوان الحالة والأولوية
        /// </summary>
        private void UpdateStatusColors()
        {
            // ألوان الحالة
            var statusColor = _file.Status switch
            {
                "نشط" => "#10B981",
                "في الجلسات" => "#F59E0B",
                "مؤرشف" => "#6B7280",
                "مغلق" => "#EF4444",
                "معلق" => "#8B5CF6",
                _ => "#6B7280"
            };
            StatusBorder.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(statusColor));

            // ألوان الأولوية
            var priorityColor = _file.Priority switch
            {
                "عالية" => "#EF4444",
                "متوسطة" => "#F59E0B",
                "منخفضة" => "#10B981",
                _ => "#6B7280"
            };
            PriorityBorder.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(priorityColor));

            // ألوان نوع الملف ونوع القضية
            FileTypeBorder.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#6366F1"));
            CaseTypeBorder.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#10B981"));
        }

        /// <summary>
        /// تحديث معلومات التتبع الإلكتروني
        /// </summary>
        private void UpdateElectronicTrackingInfo()
        {
            if (_file.IsElectronicTracking)
            {
                TrackingStatusIcon.Text = "🟢";
                TrackingStatusText.Text = "مفعل";
                
                ElectronicCodePanel.Visibility = Visibility.Visible;
                ElectronicCodeText.Text = _file.ElectronicFileCode;
                
                FileYearPanel.Visibility = Visibility.Visible;
                FileYearText.Text = _file.FileYear.ToString();
                
                if (!string.IsNullOrEmpty(_file.AppealCourt))
                {
                    AppealCourtPanel.Visibility = Visibility.Visible;
                    AppealCourtText.Text = _file.AppealCourt;
                }

                TrackButton.IsEnabled = true;
            }
            else
            {
                TrackingStatusIcon.Text = "🔴";
                TrackingStatusText.Text = "غير مفعل";
                
                ElectronicCodePanel.Visibility = Visibility.Collapsed;
                FileYearPanel.Visibility = Visibility.Collapsed;
                AppealCourtPanel.Visibility = Visibility.Collapsed;

                TrackButton.IsEnabled = false;
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// تعديل الملف
        /// </summary>
        private void Edit_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var editWindow = new ComprehensiveAddFileWindow(_file);
                if (editWindow.ShowDialog() == true)
                {
                    // تحديث الملف الحالي بالبيانات الجديدة
                    _file = editWindow.NewFile;
                    
                    // إعادة تحميل التفاصيل
                    LoadFileDetails();
                    
                    MessageBox.Show("تم تحديث الملف بنجاح!", "نجح", 
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل الملف: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تتبع الملف في المحاكم
        /// </summary>
        private async void Track_Click(object sender, RoutedEventArgs e)
        {
            if (!_file.IsElectronicTracking)
            {
                MessageBox.Show("التتبع الإلكتروني غير مفعل لهذا الملف", "تنبيه", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                TrackButton.Content = "⏳ جاري التتبع...";
                TrackButton.IsEnabled = false;

                var result = await _courtsService.TrackFileAsync(
                    _file.FileNumber, 
                    _file.ElectronicFileCode, 
                    _file.FileYear, 
                    _file.AppealCourt, 
                    _file.SearchInPrimaryCourts);

                if (result.Success)
                {
                    // تحديث بيانات الملف من نتيجة التتبع
                    if (result.FileInfo != null)
                    {
                        _file.Status = result.FileInfo.Status;
                        _file.LastProcedure = result.FileInfo.LastProcedure;
                        _file.NextSessionDate = result.FileInfo.NextSessionDate;
                        _file.LastSessionDate = result.FileInfo.LastSessionDate;
                        _file.LastUpdateDate = DateTime.Now;
                        
                        // إعادة تحميل التفاصيل
                        LoadFileDetails();
                    }

                    MessageBox.Show("تم تحديث بيانات الملف من المحاكم الإلكترونية بنجاح!", "نجح التتبع", 
                                   MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show($"فشل في التتبع: {result.ErrorMessage}", "خطأ في التتبع", 
                                   MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التتبع: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                TrackButton.Content = "🔄 تتبع في المحاكم";
                TrackButton.IsEnabled = true;
            }
        }

        /// <summary>
        /// طباعة تفاصيل الملف
        /// </summary>
        private void Print_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تنفيذ الطباعة
                MessageBox.Show("سيتم تطوير ميزة الطباعة قريباً", "قيد التطوير", 
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void Close_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        #endregion
    }
}
