# دليل التثبيت المفصل - منصة المحامين الذكية

## 🚀 التثبيت السهل والسريع

### الطريقة الأولى: المعالج التلقائي (الأسهل والأسرع)

#### الخطوة 1: تحميل الملفات
1. قم بتحميل ملفات المشروع
2. فك الضغط في مجلد الخادم (public_html أو www)
3. تأكد من أن جميع الملفات موجودة

#### الخطوة 2: تشغيل المعالج
1. افتح المتصفح واذهب إلى: `http://your-domain.com/install.php`
2. ستظهر لك واجهة التثبيت الأنيقة
3. اتبع الخطوات التالية:

**الخطوة الأولى: فحص المتطلبات**
- سيتم فحص جميع متطلبات النظام تلقائياً
- تأكد من أن جميع المتطلبات متوفرة (✅)
- إذا كان هناك متطلب مفقود (❌)، قم بتثبيته أولاً

**الخطوة الثانية: إعداد قاعدة البيانات**
- أدخل معلومات قاعدة البيانات:
  - خادم قاعدة البيانات: `localhost` أو `127.0.0.1`
  - المنفذ: `3306` (افتراضي)
  - اسم قاعدة البيانات: `smartlawyers_web`
  - اسم المستخدم: `root` (أو اسم المستخدم الخاص بك)
  - كلمة المرور: (اتركها فارغة إذا لم تكن محددة)

**الخطوة الثالثة: إعدادات التطبيق**
- اسم التطبيق: `منصة المحامين الذكية`
- رابط الموقع: `http://your-domain.com`
- المنطقة الزمنية: `Africa/Casablanca`
- اللغة: `العربية (ar)`

**الخطوة الرابعة: تثبيت التبعيات**
- سيتم تثبيت جميع التبعيات تلقائياً
- انتظر حتى اكتمال العملية

**الخطوة الخامسة: إنشاء الجداول**
- سيتم إنشاء جميع جداول قاعدة البيانات
- إضافة البيانات الأولية

**الخطوة السادسة: إنشاء حساب المدير**
- الاسم الكامل: `مدير النظام`
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: (اختر كلمة مرور قوية)

**الخطوة السابعة: إعداد التكاملات (اختياري)**
- Google Calendar API Key
- WhatsApp Business Token
- مفاتيح API للمحاكم المغربية

**الخطوة الثامنة: اكتمال التثبيت**
- تهانينا! تم التثبيت بنجاح 🎉
- يمكنك الآن الدخول إلى المنصة

---

### الطريقة الثانية: التثبيت اليدوي

#### متطلبات النظام
```
✅ PHP >= 8.1
✅ MySQL >= 8.0 أو PostgreSQL >= 13
✅ Composer
✅ Node.js >= 16.0
✅ NPM >= 8.0
✅ Git (اختياري)
```

#### الإضافات المطلوبة لـ PHP
```
✅ OpenSSL Extension
✅ PDO Extension
✅ Mbstring Extension
✅ Tokenizer Extension
✅ XML Extension
✅ Ctype Extension
✅ JSON Extension
✅ BCMath Extension
✅ Fileinfo Extension
✅ GD Extension
✅ cURL Extension
✅ Zip Extension
```

#### خطوات التثبيت اليدوي

**1. تحميل المشروع**
```bash
# باستخدام Git
git clone https://github.com/smartlawyers/web-platform.git smartlawyers-web
cd smartlawyers-web

# أو تحميل الملف المضغوط وفك الضغط
```

**2. تثبيت تبعيات PHP**
```bash
composer install --no-dev --optimize-autoloader
```

**3. تثبيت تبعيات JavaScript**
```bash
npm install --production
```

**4. إعداد ملف البيئة**
```bash
cp .env.example .env
```

**5. تحرير ملف .env**
```env
APP_NAME="منصة المحامين الذكية"
APP_URL=http://your-domain.com
APP_TIMEZONE=Africa/Casablanca

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=smartlawyers_web
DB_USERNAME=root
DB_PASSWORD=your_password
```

**6. إنشاء مفتاح التطبيق**
```bash
php artisan key:generate
```

**7. إنشاء قاعدة البيانات**
```sql
CREATE DATABASE smartlawyers_web CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

**8. تشغيل الترحيلات**
```bash
php artisan migrate --seed
```

**9. بناء الأصول**
```bash
npm run build
```

**10. إعداد الصلاحيات**
```bash
chmod -R 755 storage
chmod -R 755 bootstrap/cache
```

**11. إنشاء حساب المدير**
```bash
php artisan make:admin "مدير النظام" "<EMAIL>" "password123"
```

**12. تشغيل الخادم**
```bash
# للتطوير
php artisan serve

# للإنتاج - استخدم Apache أو Nginx
```

---

## ⚙️ إعدادات الخادم

### Apache Configuration
```apache
<VirtualHost *:80>
    ServerName smartlawyers.local
    DocumentRoot /path/to/smartlawyers-web/public
    
    <Directory /path/to/smartlawyers-web/public>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/smartlawyers_error.log
    CustomLog ${APACHE_LOG_DIR}/smartlawyers_access.log combined
</VirtualHost>
```

### Nginx Configuration
```nginx
server {
    listen 80;
    server_name smartlawyers.local;
    root /path/to/smartlawyers-web/public;
    
    index index.php index.html;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    location ~ /\.ht {
        deny all;
    }
}
```

---

## 🔧 إعدادات التكاملات

### Google Calendar
1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com)
2. أنشئ مشروع جديد أو اختر مشروع موجود
3. فعّل Google Calendar API
4. أنشئ OAuth 2.0 credentials
5. أضف المفاتيح في ملف .env:
```env
GOOGLE_CLIENT_ID=your_client_id
GOOGLE_CLIENT_SECRET=your_client_secret
GOOGLE_REDIRECT_URI=http://your-domain.com/auth/google/callback
```

### WhatsApp Business
1. اذهب إلى [Facebook Developers](https://developers.facebook.com)
2. أنشئ تطبيق جديد
3. أضف WhatsApp Business API
4. احصل على Phone Number ID و Access Token
5. أضف المفاتيح في ملف .env:
```env
WHATSAPP_TOKEN=your_access_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_verify_token
```

### المحاكم المغربية
```env
MAHAKIM_API_KEY=your_api_key
MAHAKIM_API_URL=https://api.mahakim.ma
MAHAKIM_CERTIFICATE_PATH=/path/to/certificate.pem
MAHAKIM_PRIVATE_KEY_PATH=/path/to/private_key.pem
```

---

## 🚨 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**خطأ: "Class not found"**
```bash
composer dump-autoload
```

**خطأ: "Permission denied"**
```bash
sudo chown -R www-data:www-data storage bootstrap/cache
sudo chmod -R 755 storage bootstrap/cache
```

**خطأ: "Database connection failed"**
- تأكد من صحة معلومات قاعدة البيانات في .env
- تأكد من تشغيل خادم MySQL
- تأكد من وجود قاعدة البيانات

**خطأ: "Mix manifest not found"**
```bash
npm run build
```

**خطأ: "Storage link not found"**
```bash
php artisan storage:link
```

---

## 📞 الدعم والمساعدة

- **الوثائق**: [docs.smartlawyers.ma](https://docs.smartlawyers.ma)
- **الدعم الفني**: <EMAIL>
- **المجتمع**: [community.smartlawyers.ma](https://community.smartlawyers.ma)
- **الهاتف**: +212 5XX-XXXXXX

---

## 🎉 تهانينا!

تم تثبيت منصة المحامين الذكية بنجاح! 

**الخطوات التالية:**
1. قم بتسجيل الدخول باستخدام حساب المدير
2. أضف المستخدمين الأوائل
3. قم بإعداد التكاملات المطلوبة
4. ابدأ في إضافة العملاء والقضايا
5. استمتع بالمنصة! 🚀
