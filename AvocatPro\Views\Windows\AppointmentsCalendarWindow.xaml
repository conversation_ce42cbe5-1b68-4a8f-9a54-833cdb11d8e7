<Window x:Class="AvocatPro.Views.Windows.AppointmentsCalendarWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:fa="http://schemas.fontawesome.io/icons/"
        Title="تقويم المواعيد" 
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        Background="#F8FAFC"
        Icon="/Resources/calendar.ico">

    <Window.Resources>
        <!-- أنماط مخصصة -->
        <Style x:Key="CalendarHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1F2937"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <Style x:Key="CalendarDayStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E5E7EB"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Margin" Value="1"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F3F4F6"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="TodayDayStyle" TargetType="Border" BasedOn="{StaticResource CalendarDayStyle}">
            <Setter Property="Background" Value="#EEF2FF"/>
            <Setter Property="BorderBrush" Value="#6366F1"/>
            <Setter Property="BorderThickness" Value="2"/>
        </Style>

        <Style x:Key="AppointmentCardStyle" TargetType="Border">
            <Setter Property="Background" Value="#6366F1"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Padding" Value="4,2"/>
            <Setter Property="Margin" Value="1"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>

        <Style x:Key="NavigationButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#6366F1"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="8" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#5B5BD6"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#4F46E5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان والتنقل -->
        <Border Grid.Row="0" Background="White" Padding="20" Margin="10,10,10,5">
            <Border.Effect>
                <DropShadowEffect Color="#E5E7EB" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.3"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- أزرار التنقل -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Button x:Name="PreviousMonthButton" Style="{StaticResource NavigationButtonStyle}" Click="PreviousMonthButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <fa:ImageAwesome Icon="ChevronLeft" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="الشهر السابق"/>
                        </StackPanel>
                    </Button>
                    <Button x:Name="TodayButton" Style="{StaticResource NavigationButtonStyle}" Background="#10B981" Click="TodayButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <fa:ImageAwesome Icon="Calendar" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="اليوم"/>
                        </StackPanel>
                    </Button>
                    <Button x:Name="NextMonthButton" Style="{StaticResource NavigationButtonStyle}" Click="NextMonthButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="الشهر التالي"/>
                            <fa:ImageAwesome Icon="ChevronRight" Width="16" Height="16" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>
                </StackPanel>

                <!-- عنوان الشهر والسنة -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                    <TextBlock x:Name="MonthYearTitle" Text="يناير 2024" FontSize="28" FontWeight="Bold" 
                               Foreground="#1F2937" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="MonthStatsText" Text="15 موعد هذا الشهر" FontSize="14" 
                               Foreground="#6B7280" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                </StackPanel>

                <!-- أزرار الإجراءات -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button x:Name="AddAppointmentButton" Style="{StaticResource NavigationButtonStyle}" Background="#F59E0B" Click="AddAppointmentButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <fa:ImageAwesome Icon="Plus" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="إضافة موعد"/>
                        </StackPanel>
                    </Button>
                    <Button x:Name="ViewModeButton" Style="{StaticResource NavigationButtonStyle}" Background="#8B5CF6" Click="ViewModeButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <fa:ImageAwesome Icon="List" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="عرض القائمة"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- أيام الأسبوع -->
        <Border Grid.Row="1" Background="White" Margin="10,5,10,5">
            <Border.Effect>
                <DropShadowEffect Color="#E5E7EB" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.3"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <Border Grid.Column="0" Background="#F3F4F6" Padding="10">
                    <TextBlock Text="الأحد" Style="{StaticResource CalendarHeaderStyle}"/>
                </Border>
                <Border Grid.Column="1" Background="#F3F4F6" Padding="10">
                    <TextBlock Text="الإثنين" Style="{StaticResource CalendarHeaderStyle}"/>
                </Border>
                <Border Grid.Column="2" Background="#F3F4F6" Padding="10">
                    <TextBlock Text="الثلاثاء" Style="{StaticResource CalendarHeaderStyle}"/>
                </Border>
                <Border Grid.Column="3" Background="#F3F4F6" Padding="10">
                    <TextBlock Text="الأربعاء" Style="{StaticResource CalendarHeaderStyle}"/>
                </Border>
                <Border Grid.Column="4" Background="#F3F4F6" Padding="10">
                    <TextBlock Text="الخميس" Style="{StaticResource CalendarHeaderStyle}"/>
                </Border>
                <Border Grid.Column="5" Background="#F3F4F6" Padding="10">
                    <TextBlock Text="الجمعة" Style="{StaticResource CalendarHeaderStyle}"/>
                </Border>
                <Border Grid.Column="6" Background="#F3F4F6" Padding="10">
                    <TextBlock Text="السبت" Style="{StaticResource CalendarHeaderStyle}"/>
                </Border>
            </Grid>
        </Border>

        <!-- التقويم -->
        <Border Grid.Row="2" Background="White" Margin="10,5,10,5">
            <Border.Effect>
                <DropShadowEffect Color="#E5E7EB" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.3"/>
            </Border.Effect>
            
            <Grid x:Name="CalendarGrid" Margin="5">
                <!-- سيتم إنشاء الشبكة ديناميكياً -->
            </Grid>
        </Border>

        <!-- تفاصيل اليوم المحدد -->
        <Border Grid.Row="3" Background="White" Padding="20" Margin="10,5,10,10">
            <Border.Effect>
                <DropShadowEffect Color="#E5E7EB" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.3"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- معلومات اليوم المحدد -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <fa:ImageAwesome Icon="Calendar" Foreground="#6366F1" Width="24" Height="24" Margin="0,0,10,0"/>
                    <TextBlock x:Name="SelectedDateText" Text="اختر يوماً لعرض المواعيد" 
                               FontSize="16" FontWeight="SemiBold" Foreground="#1F2937" VerticalAlignment="Center"/>
                </StackPanel>

                <!-- قائمة مواعيد اليوم -->
                <ScrollViewer Grid.Column="1" HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Disabled">
                    <StackPanel x:Name="DayAppointmentsPanel" Orientation="Horizontal" Margin="20,0,0,0">
                        <!-- سيتم إضافة المواعيد هنا ديناميكياً -->
                    </StackPanel>
                </ScrollViewer>
            </Grid>
        </Border>
    </Grid>
</Window>
