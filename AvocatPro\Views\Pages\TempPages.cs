using System.Windows;
using System.Windows.Controls;
using AvocatPro.Models;

namespace AvocatPro.Views.Pages;

// الصفحات المؤقتة للتطوير
// تم نقل CasesPage إلى ملف منفصل

public partial class SessionsPage : Page
{
    public SessionsPage(User currentUser)
    {
        InitializeComponent();
    }

    private void InitializeComponent()
    {
        var textBlock = new TextBlock
        {
            Text = "صفحة إدارة الجلسات قيد التطوير",
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center,
            FontSize = 16
        };
        Content = textBlock;
    }
}

public partial class AppointmentsPage : Page
{
    public AppointmentsPage(User currentUser)
    {
        InitializeComponent();
    }

    private void InitializeComponent()
    {
        var textBlock = new TextBlock
        {
            Text = "صفحة إدارة المواعيد قيد التطوير",
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center,
            FontSize = 16
        };
        Content = textBlock;
    }
}

public partial class FinancePage : Page
{
    public FinancePage(User currentUser)
    {
        InitializeComponent();
    }

    private void InitializeComponent()
    {
        var textBlock = new TextBlock
        {
            Text = "صفحة الإدارة المالية قيد التطوير",
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center,
            FontSize = 16
        };
        Content = textBlock;
    }
}



public partial class UsersPage : Page
{
    public UsersPage(User currentUser)
    {
        InitializeComponent();
    }

    private void InitializeComponent()
    {
        var textBlock = new TextBlock
        {
            Text = "صفحة إدارة المستخدمين قيد التطوير",
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center,
            FontSize = 16
        };
        Content = textBlock;
    }
}

public partial class SettingsPage : Page
{
    public SettingsPage(User currentUser)
    {
        InitializeComponent();
    }

    private void InitializeComponent()
    {
        var textBlock = new TextBlock
        {
            Text = "صفحة الإعدادات قيد التطوير",
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center,
            FontSize = 16
        };
        Content = textBlock;
    }
}
