<Page x:Class="AvocatPro.Views.Pages.ClientsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="إدارة الموكلين"
      FlowDirection="RightToLeft">

    <Page.Resources>
        <!-- أنماط الأزرار -->
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="FontSize" Value="11"/>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#1976D2"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="FontWeight" Value="Bold"/>
        </Style>
    </Page.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط الأدوات العلوي -->
        <Border Grid.Row="0" Background="White" Padding="20" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Button Style="{StaticResource PrimaryButtonStyle}" Click="AddClientButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="➕" Margin="0,0,5,0"/>
                            <TextBlock Text="إضافة موكل جديد"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Background="#4CAF50" Foreground="White" Style="{StaticResource PrimaryButtonStyle}" 
                           Margin="10,0,0,0" Click="RefreshButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔄" Margin="0,0,5,0"/>
                            <TextBlock Text="تحديث"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Background="#FF9800" Foreground="White" Style="{StaticResource PrimaryButtonStyle}"
                           Margin="10,0,0,0" Click="ExportButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📤" Margin="0,0,5,0"/>
                            <TextBlock Text="تصدير"/>
                        </StackPanel>
                    </Button>

                    <Button Background="#9C27B0" Foreground="White" Style="{StaticResource PrimaryButtonStyle}"
                           Margin="10,0,0,0" Click="PrintButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🖨️" Margin="0,0,5,0"/>
                            <TextBlock Text="طباعة"/>
                        </StackPanel>
                    </Button>

                    <Button Background="#607D8B" Foreground="White" Style="{StaticResource PrimaryButtonStyle}"
                           Margin="10,0,0,0" Click="ImportButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📥" Margin="0,0,5,0"/>
                            <TextBlock Text="استيراد"/>
                        </StackPanel>
                    </Button>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBox Name="SearchTextBox" Width="250"
                             Style="{StaticResource SearchTextBoxStyle}"
                             Text="البحث في الموكلين..." Foreground="#999"
                             GotFocus="SearchTextBox_GotFocus" LostFocus="SearchTextBox_LostFocus"
                             TextChanged="SearchTextBox_TextChanged"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- شريط التصفية -->
        <Border Grid.Row="1" Background="White" Padding="15" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
            </Border.Effect>
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="🔽 تصفية حسب:" VerticalAlignment="Center" Margin="0,0,15,0" FontWeight="Bold"/>
                
                <TextBlock Text="النوع:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <ComboBox Name="ClientTypeComboBox" Width="140" Margin="0,0,15,0"
                          Style="{StaticResource ModernComboBoxStyle}"
                          SelectionChanged="ClientTypeComboBox_SelectionChanged">
                    <ComboBoxItem Content="الكل" Tag="All" IsSelected="True"/>
                    <ComboBoxItem Content="فرد" Tag="Individual"/>
                    <ComboBoxItem Content="شركة" Tag="Company"/>
                    <ComboBoxItem Content="مؤسسة حكومية" Tag="Government"/>
                    <ComboBoxItem Content="جمعية" Tag="Association"/>
                </ComboBox>

                <TextBlock Text="الحالة:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <ComboBox Name="ClientStatusComboBox" Width="140" Margin="0,0,15,0"
                          Style="{StaticResource ModernComboBoxStyle}"
                          SelectionChanged="ClientStatusComboBox_SelectionChanged">
                    <ComboBoxItem Content="الكل" Tag="All" IsSelected="True"/>
                    <ComboBoxItem Content="نشط" Tag="Active"/>
                    <ComboBoxItem Content="غير نشط" Tag="Inactive"/>
                    <ComboBoxItem Content="محظور" Tag="Blocked"/>
                </ComboBox>
                
                <Button Content="🗑️ مسح التصفية" Background="#F44336" Foreground="White"
                       Padding="10,5" BorderThickness="0"
                       Click="ClearFiltersButton_Click"/>
            </StackPanel>
        </Border>

        <!-- جدول البيانات -->
        <Border Grid.Row="2" Background="White">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
            </Border.Effect>
            <DataGrid Name="ClientsDataGrid"
                      Style="{StaticResource ModernDataGridStyle}"
                      ColumnHeaderStyle="{StaticResource ModernDataGridColumnHeaderStyle}"
                      CellStyle="{StaticResource ModernDataGridCellStyle}">

                <DataGrid.Columns>
                    <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="60" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="Foreground" Value="#1976D2"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <DataGridTextColumn Header="مرجع المكتب" Binding="{Binding OfficeReferenceDisplay}" Width="100" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Padding" Value="5"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="Foreground" Value="#4CAF50"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <DataGridTextColumn Header="الاسم الكامل" Binding="{Binding FullName}" Width="180" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Padding" Value="8,5"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <DataGridTextColumn Header="النوع" Binding="{Binding TypeDisplay}" Width="80" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Padding" Value="5"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Phone}" Width="120" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Padding" Value="5"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding Email}" Width="160" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Padding" Value="5"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <DataGridTextColumn Header="المدينة" Binding="{Binding CityDisplay}" Width="80" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Padding" Value="5"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <DataGridTextColumn Header="الحالة" Binding="{Binding StatusDisplay}" Width="80" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Padding" Value="5"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <DataGridTextColumn Header="تاريخ الإضافة" Binding="{Binding CreatedAtDisplay}" Width="100" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Padding" Value="5"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontSize" Value="11"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <DataGridTemplateColumn Header="الإجراءات" Width="250">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button Content="👁️" Style="{StaticResource ViewButtonStyle}"
                                           Click="ViewClientButton_Click" Tag="{Binding Id}" ToolTip="عرض تفاصيل الموكل"/>

                                    <Button Content="✏️" Style="{StaticResource EditButtonStyle}"
                                           Click="EditClientButton_Click" Tag="{Binding Id}" ToolTip="تعديل بيانات الموكل"/>

                                    <Button Content="📁" Style="{StaticResource PrintButtonStyle}"
                                           Click="ViewClientCasesButton_Click" Tag="{Binding Id}" ToolTip="عرض ملفات الموكل"/>

                                    <Button Content="📅" Style="{StaticResource ViewButtonStyle}"
                                           Click="ViewClientAppointmentsButton_Click" Tag="{Binding Id}" ToolTip="عرض مواعيد الموكل"/>

                                    <Button Content="🗑️" Style="{StaticResource DeleteButtonStyle}"
                                           Click="DeleteClientButton_Click" Tag="{Binding Id}" ToolTip="حذف الموكل"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>
    </Grid>
</Page>
