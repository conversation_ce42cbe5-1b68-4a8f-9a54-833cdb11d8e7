using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace AvocatPro.Views.Pages
{
    /// <summary>
    /// صفحة إدارة الموكلين - اختبار
    /// </summary>
    public partial class SimpleClientsPageTest : Page
    {
        public SimpleClientsPageTest()
        {
            InitializeComponent();
            LoadTestData();
        }

        /// <summary>
        /// تحميل البيانات التجريبية
        /// </summary>
        private void LoadTestData()
        {
            try
            {
                var clients = new ObservableCollection<TestClientModel>
                {
                    new TestClientModel
                    {
                        Name = "أحمد محمد علي",
                        ClientType = "فرد",
                        Phone = "+966501234567",
                        Email = "<EMAIL>",
                        Status = "نشط"
                    },
                    new TestClientModel
                    {
                        Name = "شركة النور للتجارة",
                        ClientType = "شركة",
                        Phone = "+966502345678",
                        Email = "<EMAIL>",
                        Status = "نشط"
                    },
                    new TestClientModel
                    {
                        Name = "فاطمة عبدالله",
                        ClientType = "فرد",
                        Phone = "+966503456789",
                        Email = "<EMAIL>",
                        Status = "نشط"
                    },
                    new TestClientModel
                    {
                        Name = "مؤسسة البناء الحديث",
                        ClientType = "شركة",
                        Phone = "+966504567890",
                        Email = "<EMAIL>",
                        Status = "غير نشط"
                    }
                };

                ClientsDataGrid.ItemsSource = clients;
                StatusText.Text = $"تم تحميل {clients.Count} موكل بنجاح";
            }
            catch (Exception ex)
            {
                StatusText.Text = $"خطأ في تحميل البيانات: {ex.Message}";
            }
        }

        /// <summary>
        /// إضافة موكل جديد
        /// </summary>
        private void AddClient_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم إضافة ميزة إضافة الموكلين قريباً", "قيد التطوير", 
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    /// <summary>
    /// نموذج بيانات الموكل للاختبار
    /// </summary>
    public class TestClientModel
    {
        public string Name { get; set; } = "";
        public string ClientType { get; set; } = "";
        public string Phone { get; set; } = "";
        public string Email { get; set; } = "";
        public string Status { get; set; } = "";
    }
}
