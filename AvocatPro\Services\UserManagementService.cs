using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Security.Cryptography;
using System.Text;
using Microsoft.EntityFrameworkCore;
using AvocatPro.Data;
using AvocatPro.Models;
using AvocatPro.Models.UserManagement;
using System.Text.Json;

namespace AvocatPro.Services;

/// <summary>
/// خدمة إدارة المستخدمين المتقدمة
/// </summary>
public class UserManagementService
{
    private readonly AvocatProDbContext _context;
    private readonly ActivityLogService _activityLogService;

    public UserManagementService(AvocatProDbContext context, ActivityLogService activityLogService)
    {
        _context = context;
        _activityLogService = activityLogService;
    }

    #region إدارة المستخدمين الأساسية

    /// <summary>
    /// الحصول على جميع المستخدمين مع تفاصيلهم
    /// </summary>
    public async Task<List<User>> GetAllUsersWithDetailsAsync()
    {
        return await _context.Users
            .Include(u => u.UserProfile)
            .Include(u => u.PasswordSettings)
            .Include(u => u.UserPermissions)
                .ThenInclude(up => up.Permission)
            .Include(u => u.UserPermissionGroups)
                .ThenInclude(upg => upg.PermissionGroup)
            .OrderBy(u => u.UserProfile!.FirstName)
            .ToListAsync();
    }

    /// <summary>
    /// الحصول على مستخدم بالمعرف مع جميع التفاصيل
    /// </summary>
    public async Task<User?> GetUserWithDetailsAsync(int userId)
    {
        return await _context.Users
            .Include(u => u.UserProfile)
            .Include(u => u.PasswordSettings)
            .Include(u => u.UserPermissions)
                .ThenInclude(up => up.Permission)
            .Include(u => u.UserPermissionGroups)
                .ThenInclude(upg => upg.PermissionGroup)
            .FirstOrDefaultAsync(u => u.Id == userId);
    }

    /// <summary>
    /// إنشاء مستخدم جديد
    /// </summary>
    public async Task<(bool Success, string Message, User? User)> CreateUserAsync(
        string username, 
        string password, 
        string firstName, 
        string lastName, 
        string email, 
        string phone = "",
        UserRole role = UserRole.User,
        int createdBy = 0)
    {
        try
        {
            // التحقق من عدم وجود اسم المستخدم
            if (await _context.Users.AnyAsync(u => u.Username == username))
            {
                return (false, "اسم المستخدم موجود بالفعل", null);
            }

            // التحقق من عدم وجود البريد الإلكتروني
            if (await _context.UserProfiles.AnyAsync(up => up.Email == email))
            {
                return (false, "البريد الإلكتروني مستخدم بالفعل", null);
            }

            // إنشاء المستخدم
            var user = new User
            {
                Username = username,
                PasswordHash = HashPassword(password),
                Role = role,
                IsActive = true,
                CreatedAt = DateTime.Now
            };

            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            // إنشاء الملف الشخصي
            var userProfile = new UserProfile
            {
                UserId = user.Id,
                FirstName = firstName,
                LastName = lastName,
                Email = email,
                Phone = phone,
                Status = UserStatus.Active,
                CreatedAt = DateTime.Now
            };

            _context.UserProfiles.Add(userProfile);

            // إنشاء إعدادات كلمة المرور
            var passwordSettings = new PasswordSettings
            {
                UserId = user.Id,
                MustChangePassword = true,
                LastPasswordChange = DateTime.Now,
                PasswordExpiryDays = 90,
                CreatedAt = DateTime.Now
            };

            _context.PasswordSettings.Add(passwordSettings);
            await _context.SaveChangesAsync();

            // تسجيل النشاط
            await _activityLogService.LogActivityAsync(
                createdBy,
                Models.UserManagement.ActivityType.Create,
                "إنشاء مستخدم جديد",
                "UserManagement",
                user.Id,
                "User",
                newData: JsonSerializer.Serialize(new { Username = username, FirstName = firstName, LastName = lastName, Email = email })
            );

            return (true, "تم إنشاء المستخدم بنجاح", user);
        }
        catch (Exception ex)
        {
            return (false, $"خطأ في إنشاء المستخدم: {ex.Message}", null);
        }
    }

    /// <summary>
    /// تحديث بيانات المستخدم
    /// </summary>
    public async Task<(bool Success, string Message)> UpdateUserAsync(
        int userId, 
        string firstName, 
        string lastName, 
        string email, 
        string phone, 
        UserRole role,
        UserStatus status,
        int updatedBy)
    {
        try
        {
            var user = await GetUserWithDetailsAsync(userId);
            if (user == null)
            {
                return (false, "المستخدم غير موجود");
            }

            // حفظ البيانات القديمة للسجل
            var oldData = JsonSerializer.Serialize(new 
            { 
                FirstName = user.UserProfile?.FirstName,
                LastName = user.UserProfile?.LastName,
                Email = user.UserProfile?.Email,
                Phone = user.UserProfile?.Phone,
                Role = user.Role.ToString(),
                Status = user.UserProfile?.Status.ToString()
            });

            // تحديث البيانات
            user.Role = role;
            user.UpdatedAt = DateTime.Now;

            if (user.UserProfile != null)
            {
                user.UserProfile.FirstName = firstName;
                user.UserProfile.LastName = lastName;
                user.UserProfile.Email = email;
                user.UserProfile.Phone = phone;
                user.UserProfile.Status = status;
                user.UserProfile.UpdatedAt = DateTime.Now;
            }

            await _context.SaveChangesAsync();

            // تسجيل النشاط
            var newData = JsonSerializer.Serialize(new 
            { 
                FirstName = firstName,
                LastName = lastName,
                Email = email,
                Phone = phone,
                Role = role.ToString(),
                Status = status.ToString()
            });

            await _activityLogService.LogActivityAsync(
                updatedBy,
                Models.UserManagement.ActivityType.Update,
                "تحديث بيانات المستخدم",
                "UserManagement",
                userId,
                "User",
                oldData: oldData,
                newData: newData
            );

            return (true, "تم تحديث بيانات المستخدم بنجاح");
        }
        catch (Exception ex)
        {
            return (false, $"خطأ في تحديث المستخدم: {ex.Message}");
        }
    }

    /// <summary>
    /// حذف مستخدم
    /// </summary>
    public async Task<(bool Success, string Message)> DeleteUserAsync(int userId, int deletedBy)
    {
        try
        {
            var user = await GetUserWithDetailsAsync(userId);
            if (user == null)
            {
                return (false, "المستخدم غير موجود");
            }

            // حفظ البيانات للسجل
            var userData = JsonSerializer.Serialize(new 
            { 
                Username = user.Username,
                FirstName = user.UserProfile?.FirstName,
                LastName = user.UserProfile?.LastName,
                Email = user.UserProfile?.Email
            });

            // حذف البيانات المرتبطة
            if (user.UserProfile != null)
                _context.UserProfiles.Remove(user.UserProfile);
            
            if (user.PasswordSettings != null)
                _context.PasswordSettings.Remove(user.PasswordSettings);

            // حذف الصلاحيات
            _context.UserPermissions.RemoveRange(user.UserPermissions);
            _context.UserPermissionGroups.RemoveRange(user.UserPermissionGroups);

            // حذف المستخدم
            _context.Users.Remove(user);
            await _context.SaveChangesAsync();

            // تسجيل النشاط
            await _activityLogService.LogActivityAsync(
                deletedBy, 
                Models.UserManagement.ActivityType.Delete,
                "حذف مستخدم", 
                "UserManagement", 
                userId, 
                "User",
                oldData: userData
            );

            return (true, "تم حذف المستخدم بنجاح");
        }
        catch (Exception ex)
        {
            return (false, $"خطأ في حذف المستخدم: {ex.Message}");
        }
    }

    #endregion

    #region إدارة كلمات المرور

    /// <summary>
    /// تغيير كلمة مرور المستخدم
    /// </summary>
    public async Task<(bool Success, string Message)> ChangePasswordAsync(
        int userId, 
        string currentPassword, 
        string newPassword, 
        int changedBy)
    {
        try
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
            {
                return (false, "المستخدم غير موجود");
            }

            // التحقق من كلمة المرور الحالية
            if (!VerifyPassword(currentPassword, user.PasswordHash))
            {
                await _activityLogService.LogActivityAsync(
                    userId, 
                    Models.UserManagement.ActivityType.PasswordChange,
                    "محاولة تغيير كلمة مرور بكلمة مرور خاطئة", 
                    "UserManagement",
                    level: Models.UserManagement.ActivityLevel.Warning,
                    isSuccessful: false
                );
                return (false, "كلمة المرور الحالية غير صحيحة");
            }

            // تحديث كلمة المرور
            user.PasswordHash = HashPassword(newPassword);
            user.UpdatedAt = DateTime.Now;

            // تحديث إعدادات كلمة المرور
            var passwordSettings = await _context.PasswordSettings.FirstOrDefaultAsync(ps => ps.UserId == userId);
            if (passwordSettings != null)
            {
                passwordSettings.LastPasswordChange = DateTime.Now;
                passwordSettings.MustChangePassword = false;
                passwordSettings.FailedLoginAttempts = 0;
                passwordSettings.LockedUntil = null;
                passwordSettings.UpdatedAt = DateTime.Now;
            }

            await _context.SaveChangesAsync();

            // تسجيل النشاط
            await _activityLogService.LogActivityAsync(
                changedBy, 
                Models.UserManagement.ActivityType.PasswordChange,
                "تغيير كلمة المرور", 
                "UserManagement", 
                userId, 
                "User"
            );

            return (true, "تم تغيير كلمة المرور بنجاح");
        }
        catch (Exception ex)
        {
            return (false, $"خطأ في تغيير كلمة المرور: {ex.Message}");
        }
    }

    /// <summary>
    /// إعادة تعيين كلمة مرور المستخدم
    /// </summary>
    public async Task<(bool Success, string Message, string? NewPassword)> ResetPasswordAsync(
        int userId, 
        int resetBy)
    {
        try
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
            {
                return (false, "المستخدم غير موجود", null);
            }

            // توليد كلمة مرور جديدة
            var newPassword = GenerateRandomPassword();
            user.PasswordHash = HashPassword(newPassword);
            user.UpdatedAt = DateTime.Now;

            // تحديث إعدادات كلمة المرور
            var passwordSettings = await _context.PasswordSettings.FirstOrDefaultAsync(ps => ps.UserId == userId);
            if (passwordSettings != null)
            {
                passwordSettings.LastPasswordChange = DateTime.Now;
                passwordSettings.MustChangePassword = true;
                passwordSettings.FailedLoginAttempts = 0;
                passwordSettings.LockedUntil = null;
                passwordSettings.UpdatedAt = DateTime.Now;
            }

            await _context.SaveChangesAsync();

            // تسجيل النشاط
            await _activityLogService.LogActivityAsync(
                resetBy, 
                Models.UserManagement.ActivityType.PasswordReset,
                "إعادة تعيين كلمة المرور", 
                "UserManagement", 
                userId, 
                "User"
            );

            return (true, "تم إعادة تعيين كلمة المرور بنجاح", newPassword);
        }
        catch (Exception ex)
        {
            return (false, $"خطأ في إعادة تعيين كلمة المرور: {ex.Message}", null);
        }
    }

    /// <summary>
    /// قفل حساب المستخدم
    /// </summary>
    public async Task<(bool Success, string Message)> LockUserAccountAsync(
        int userId, 
        int lockDurationMinutes, 
        int lockedBy)
    {
        try
        {
            var passwordSettings = await _context.PasswordSettings.FirstOrDefaultAsync(ps => ps.UserId == userId);
            if (passwordSettings == null)
            {
                return (false, "إعدادات كلمة المرور غير موجودة");
            }

            passwordSettings.LockedUntil = DateTime.Now.AddMinutes(lockDurationMinutes);
            passwordSettings.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();

            // تسجيل النشاط
            await _activityLogService.LogActivityAsync(
                lockedBy, 
                Models.UserManagement.ActivityType.AccountLock,
                $"قفل الحساب لمدة {lockDurationMinutes} دقيقة", 
                "UserManagement", 
                userId, 
                "User",
                level: Models.UserManagement.ActivityLevel.Warning
            );

            return (true, "تم قفل الحساب بنجاح");
        }
        catch (Exception ex)
        {
            return (false, $"خطأ في قفل الحساب: {ex.Message}");
        }
    }

    /// <summary>
    /// إلغاء قفل حساب المستخدم
    /// </summary>
    public async Task<(bool Success, string Message)> UnlockUserAccountAsync(int userId, int unlockedBy)
    {
        try
        {
            var passwordSettings = await _context.PasswordSettings.FirstOrDefaultAsync(ps => ps.UserId == userId);
            if (passwordSettings == null)
            {
                return (false, "إعدادات كلمة المرور غير موجودة");
            }

            passwordSettings.LockedUntil = null;
            passwordSettings.FailedLoginAttempts = 0;
            passwordSettings.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();

            // تسجيل النشاط
            await _activityLogService.LogActivityAsync(
                unlockedBy, 
                Models.UserManagement.ActivityType.AccountUnlock,
                "إلغاء قفل الحساب", 
                "UserManagement", 
                userId, 
                "User"
            );

            return (true, "تم إلغاء قفل الحساب بنجاح");
        }
        catch (Exception ex)
        {
            return (false, $"خطأ في إلغاء قفل الحساب: {ex.Message}");
        }
    }

    #endregion

    #region الطرق المساعدة

    /// <summary>
    /// تشفير كلمة المرور
    /// </summary>
    private string HashPassword(string password)
    {
        using var sha256 = SHA256.Create();
        var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "AvocatPro_Salt"));
        return Convert.ToBase64String(hashedBytes);
    }

    /// <summary>
    /// التحقق من كلمة المرور
    /// </summary>
    private bool VerifyPassword(string password, string hash)
    {
        return HashPassword(password) == hash;
    }

    /// <summary>
    /// توليد كلمة مرور عشوائية
    /// </summary>
    private string GenerateRandomPassword(int length = 12)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
        var random = new Random();
        return new string(Enumerable.Repeat(chars, length)
            .Select(s => s[random.Next(s.Length)]).ToArray());
    }

    #endregion

    #region إدارة الصلاحيات

    /// <summary>
    /// منح صلاحية للمستخدم
    /// </summary>
    public async Task<(bool Success, string Message)> GrantPermissionAsync(
        int userId,
        int permissionId,
        int grantedBy,
        DateTime? expiresAt = null)
    {
        try
        {
            // التحقق من وجود الصلاحية مسبقاً
            var existingPermission = await _context.UserPermissions
                .FirstOrDefaultAsync(up => up.UserId == userId && up.PermissionId == permissionId);

            if (existingPermission != null)
            {
                if (existingPermission.IsGranted)
                {
                    return (false, "الصلاحية ممنوحة بالفعل للمستخدم");
                }
                else
                {
                    // تفعيل الصلاحية المعطلة
                    existingPermission.IsGranted = true;
                    existingPermission.GrantedAt = DateTime.Now;
                    existingPermission.GrantedBy = grantedBy;
                    existingPermission.ExpiresAt = expiresAt;
                }
            }
            else
            {
                // إنشاء صلاحية جديدة
                var userPermission = new UserPermission
                {
                    UserId = userId,
                    PermissionId = permissionId,
                    IsGranted = true,
                    GrantedAt = DateTime.Now,
                    GrantedBy = grantedBy,
                    ExpiresAt = expiresAt
                };

                _context.UserPermissions.Add(userPermission);
            }

            await _context.SaveChangesAsync();

            // الحصول على تفاصيل الصلاحية للسجل
            var permission = await _context.Permissions.FindAsync(permissionId);

            // تسجيل النشاط
            await _activityLogService.LogActivityAsync(
                grantedBy,
                Models.UserManagement.ActivityType.PermissionGrant,
                $"منح صلاحية: {permission?.Name}",
                "UserManagement",
                userId,
                "UserPermission",
                newData: JsonSerializer.Serialize(new { PermissionId = permissionId, ExpiresAt = expiresAt })
            );

            return (true, "تم منح الصلاحية بنجاح");
        }
        catch (Exception ex)
        {
            return (false, $"خطأ في منح الصلاحية: {ex.Message}");
        }
    }

    /// <summary>
    /// إلغاء صلاحية من المستخدم
    /// </summary>
    public async Task<(bool Success, string Message)> RevokePermissionAsync(
        int userId,
        int permissionId,
        int revokedBy)
    {
        try
        {
            var userPermission = await _context.UserPermissions
                .FirstOrDefaultAsync(up => up.UserId == userId && up.PermissionId == permissionId);

            if (userPermission == null)
            {
                return (false, "الصلاحية غير موجودة للمستخدم");
            }

            userPermission.IsGranted = false;
            await _context.SaveChangesAsync();

            // الحصول على تفاصيل الصلاحية للسجل
            var permission = await _context.Permissions.FindAsync(permissionId);

            // تسجيل النشاط
            await _activityLogService.LogActivityAsync(
                revokedBy,
                Models.UserManagement.ActivityType.PermissionRevoke,
                $"إلغاء صلاحية: {permission?.Name}",
                "UserManagement",
                userId,
                "UserPermission",
                oldData: JsonSerializer.Serialize(new { PermissionId = permissionId, IsGranted = true })
            );

            return (true, "تم إلغاء الصلاحية بنجاح");
        }
        catch (Exception ex)
        {
            return (false, $"خطأ في إلغاء الصلاحية: {ex.Message}");
        }
    }

    /// <summary>
    /// التحقق من صلاحية المستخدم
    /// </summary>
    public async Task<bool> HasPermissionAsync(int userId, string permissionCode)
    {
        try
        {
            var hasPermission = await _context.UserPermissions
                .Include(up => up.Permission)
                .AnyAsync(up => up.UserId == userId
                    && up.Permission.Code == permissionCode
                    && up.IsGranted
                    && up.Permission.IsActive
                    && (up.ExpiresAt == null || up.ExpiresAt > DateTime.Now));

            return hasPermission;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// الحصول على صلاحيات المستخدم
    /// </summary>
    public async Task<List<Models.UserManagement.Permission>> GetUserPermissionsAsync(int userId)
    {
        return await _context.UserPermissions
            .Where(up => up.UserId == userId && up.IsGranted && (up.ExpiresAt == null || up.ExpiresAt > DateTime.Now))
            .Include(up => up.Permission)
            .Select(up => up.Permission)
            .Where(p => p.IsActive)
            .OrderBy(p => p.Module)
            .ThenBy(p => p.Name)
            .ToListAsync();
    }

    /// <summary>
    /// إضافة مستخدم لمجموعة صلاحيات
    /// </summary>
    public async Task<(bool Success, string Message)> AddUserToPermissionGroupAsync(
        int userId,
        int permissionGroupId,
        int assignedBy)
    {
        try
        {
            // التحقق من وجود المستخدم في المجموعة مسبقاً
            var existingAssignment = await _context.UserPermissionGroups
                .FirstOrDefaultAsync(upg => upg.UserId == userId && upg.PermissionGroupId == permissionGroupId);

            if (existingAssignment != null)
            {
                return (false, "المستخدم موجود بالفعل في هذه المجموعة");
            }

            // إضافة المستخدم للمجموعة
            var userPermissionGroup = new UserPermissionGroup
            {
                UserId = userId,
                PermissionGroupId = permissionGroupId,
                AssignedAt = DateTime.Now,
                AssignedBy = assignedBy
            };

            _context.UserPermissionGroups.Add(userPermissionGroup);
            await _context.SaveChangesAsync();

            // الحصول على تفاصيل المجموعة للسجل
            var group = await _context.PermissionGroups.FindAsync(permissionGroupId);

            // تسجيل النشاط
            await _activityLogService.LogActivityAsync(
                assignedBy,
                Models.UserManagement.ActivityType.PermissionGrant,
                $"إضافة لمجموعة صلاحيات: {group?.Name}",
                "UserManagement",
                userId,
                "UserPermissionGroup",
                newData: JsonSerializer.Serialize(new { PermissionGroupId = permissionGroupId })
            );

            return (true, "تم إضافة المستخدم لمجموعة الصلاحيات بنجاح");
        }
        catch (Exception ex)
        {
            return (false, $"خطأ في إضافة المستخدم لمجموعة الصلاحيات: {ex.Message}");
        }
    }

    /// <summary>
    /// إزالة مستخدم من مجموعة صلاحيات
    /// </summary>
    public async Task<(bool Success, string Message)> RemoveUserFromPermissionGroupAsync(
        int userId,
        int permissionGroupId,
        int removedBy)
    {
        try
        {
            var userPermissionGroup = await _context.UserPermissionGroups
                .FirstOrDefaultAsync(upg => upg.UserId == userId && upg.PermissionGroupId == permissionGroupId);

            if (userPermissionGroup == null)
            {
                return (false, "المستخدم غير موجود في هذه المجموعة");
            }

            _context.UserPermissionGroups.Remove(userPermissionGroup);
            await _context.SaveChangesAsync();

            // الحصول على تفاصيل المجموعة للسجل
            var group = await _context.PermissionGroups.FindAsync(permissionGroupId);

            // تسجيل النشاط
            await _activityLogService.LogActivityAsync(
                removedBy,
                Models.UserManagement.ActivityType.PermissionRevoke,
                $"إزالة من مجموعة صلاحيات: {group?.Name}",
                "UserManagement",
                userId,
                "UserPermissionGroup",
                oldData: JsonSerializer.Serialize(new { PermissionGroupId = permissionGroupId })
            );

            return (true, "تم إزالة المستخدم من مجموعة الصلاحيات بنجاح");
        }
        catch (Exception ex)
        {
            return (false, $"خطأ في إزالة المستخدم من مجموعة الصلاحيات: {ex.Message}");
        }
    }

    #endregion
}
