using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AvocatPro.Models.UserManagement;

/// <summary>
/// سجل أنشطة المستخدم
/// </summary>
public class UserActivity
{
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// معرف المستخدم
    /// </summary>
    [Required]
    public int UserId { get; set; }

    /// <summary>
    /// نوع النشاط
    /// </summary>
    [Required]
    public ActivityType ActivityType { get; set; }

    /// <summary>
    /// وصف النشاط
    /// </summary>
    [Required]
    [MaxLength(500)]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// القسم/الوحدة
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string Module { get; set; } = string.Empty;

    /// <summary>
    /// معرف الكائن المتأثر
    /// </summary>
    public int? EntityId { get; set; }

    /// <summary>
    /// نوع الكائن المتأثر
    /// </summary>
    [MaxLength(50)]
    public string EntityType { get; set; } = string.Empty;

    /// <summary>
    /// البيانات القديمة (JSON)
    /// </summary>
    [Column(TypeName = "ntext")]
    public string? OldData { get; set; }

    /// <summary>
    /// البيانات الجديدة (JSON)
    /// </summary>
    [Column(TypeName = "ntext")]
    public string? NewData { get; set; }

    /// <summary>
    /// عنوان IP
    /// </summary>
    [MaxLength(45)]
    public string IPAddress { get; set; } = string.Empty;

    /// <summary>
    /// معلومات المتصفح
    /// </summary>
    [MaxLength(500)]
    public string UserAgent { get; set; } = string.Empty;

    /// <summary>
    /// مستوى الأهمية
    /// </summary>
    public ActivityLevel Level { get; set; } = ActivityLevel.Info;

    /// <summary>
    /// هل النشاط ناجح
    /// </summary>
    public bool IsSuccessful { get; set; } = true;

    /// <summary>
    /// رسالة الخطأ في حالة الفشل
    /// </summary>
    [MaxLength(1000)]
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// تاريخ ووقت النشاط
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// معلومات إضافية (JSON)
    /// </summary>
    [Column(TypeName = "ntext")]
    public string? AdditionalInfo { get; set; }

    // Navigation Properties
    [ForeignKey("UserId")]
    public virtual User User { get; set; } = null!;
}

/// <summary>
/// أنواع الأنشطة
/// </summary>
public enum ActivityType
{
    /// <summary>
    /// تسجيل دخول
    /// </summary>
    Login = 1,

    /// <summary>
    /// تسجيل خروج
    /// </summary>
    Logout = 2,

    /// <summary>
    /// إنشاء
    /// </summary>
    Create = 3,

    /// <summary>
    /// تحديث
    /// </summary>
    Update = 4,

    /// <summary>
    /// حذف
    /// </summary>
    Delete = 5,

    /// <summary>
    /// عرض
    /// </summary>
    View = 6,

    /// <summary>
    /// طباعة
    /// </summary>
    Print = 7,

    /// <summary>
    /// تصدير
    /// </summary>
    Export = 8,

    /// <summary>
    /// استيراد
    /// </summary>
    Import = 9,

    /// <summary>
    /// تغيير كلمة المرور
    /// </summary>
    PasswordChange = 10,

    /// <summary>
    /// إعادة تعيين كلمة المرور
    /// </summary>
    PasswordReset = 11,

    /// <summary>
    /// منح صلاحية
    /// </summary>
    PermissionGrant = 12,

    /// <summary>
    /// إلغاء صلاحية
    /// </summary>
    PermissionRevoke = 13,

    /// <summary>
    /// تفعيل حساب
    /// </summary>
    AccountActivation = 14,

    /// <summary>
    /// إلغاء تفعيل حساب
    /// </summary>
    AccountDeactivation = 15,

    /// <summary>
    /// قفل حساب
    /// </summary>
    AccountLock = 16,

    /// <summary>
    /// إلغاء قفل حساب
    /// </summary>
    AccountUnlock = 17,

    /// <summary>
    /// تحديث الملف الشخصي
    /// </summary>
    ProfileUpdate = 18,

    /// <summary>
    /// تحميل ملف
    /// </summary>
    FileUpload = 19,

    /// <summary>
    /// تنزيل ملف
    /// </summary>
    FileDownload = 20,

    /// <summary>
    /// نسخ احتياطي
    /// </summary>
    Backup = 21,

    /// <summary>
    /// استعادة
    /// </summary>
    Restore = 22,

    /// <summary>
    /// تكوين النظام
    /// </summary>
    SystemConfiguration = 23,

    /// <summary>
    /// خطأ في النظام
    /// </summary>
    SystemError = 24,

    /// <summary>
    /// تحذير أمني
    /// </summary>
    SecurityWarning = 25
}

/// <summary>
/// مستويات أهمية النشاط
/// </summary>
public enum ActivityLevel
{
    /// <summary>
    /// معلومات
    /// </summary>
    Info = 1,

    /// <summary>
    /// تحذير
    /// </summary>
    Warning = 2,

    /// <summary>
    /// خطأ
    /// </summary>
    Error = 3,

    /// <summary>
    /// حرج
    /// </summary>
    Critical = 4,

    /// <summary>
    /// تصحيح
    /// </summary>
    Debug = 5
}

/// <summary>
/// إحصائيات المستخدم
/// </summary>
public class UserStatistics
{
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// معرف المستخدم
    /// </summary>
    [Required]
    public int UserId { get; set; }

    /// <summary>
    /// تاريخ الإحصائية
    /// </summary>
    public DateTime StatDate { get; set; } = DateTime.Today;

    /// <summary>
    /// عدد مرات تسجيل الدخول
    /// </summary>
    public int LoginCount { get; set; }

    /// <summary>
    /// إجمالي وقت الجلسات بالدقائق
    /// </summary>
    public int TotalSessionMinutes { get; set; }

    /// <summary>
    /// عدد العمليات المنجزة
    /// </summary>
    public int ActionsPerformed { get; set; }

    /// <summary>
    /// عدد الملفات المنشأة
    /// </summary>
    public int FilesCreated { get; set; }

    /// <summary>
    /// عدد الملفات المحدثة
    /// </summary>
    public int FilesUpdated { get; set; }

    /// <summary>
    /// عدد الملفات المحذوفة
    /// </summary>
    public int FilesDeleted { get; set; }

    /// <summary>
    /// عدد التقارير المطبوعة
    /// </summary>
    public int ReportsPrinted { get; set; }

    /// <summary>
    /// عدد الملفات المصدرة
    /// </summary>
    public int FilesExported { get; set; }

    /// <summary>
    /// عدد الأخطاء
    /// </summary>
    public int ErrorCount { get; set; }

    /// <summary>
    /// تاريخ الإنشاء
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// تاريخ آخر تحديث
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    // Navigation Properties
    [ForeignKey("UserId")]
    public virtual User User { get; set; } = null!;
}

/// <summary>
/// تقييم أداء المستخدم
/// </summary>
public class UserPerformanceRating
{
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// معرف المستخدم
    /// </summary>
    [Required]
    public int UserId { get; set; }

    /// <summary>
    /// معرف المقيم
    /// </summary>
    [Required]
    public int RatedBy { get; set; }

    /// <summary>
    /// فترة التقييم (شهر/سنة)
    /// </summary>
    [Required]
    [MaxLength(7)] // YYYY-MM
    public string Period { get; set; } = string.Empty;

    /// <summary>
    /// تقييم الإنتاجية (1-10)
    /// </summary>
    [Range(1, 10)]
    public int ProductivityRating { get; set; }

    /// <summary>
    /// تقييم الجودة (1-10)
    /// </summary>
    [Range(1, 10)]
    public int QualityRating { get; set; }

    /// <summary>
    /// تقييم الالتزام بالوقت (1-10)
    /// </summary>
    [Range(1, 10)]
    public int PunctualityRating { get; set; }

    /// <summary>
    /// تقييم التعاون (1-10)
    /// </summary>
    [Range(1, 10)]
    public int CollaborationRating { get; set; }

    /// <summary>
    /// التقييم الإجمالي
    /// </summary>
    [NotMapped]
    public double OverallRating => (ProductivityRating + QualityRating + PunctualityRating + CollaborationRating) / 4.0;

    /// <summary>
    /// ملاحظات التقييم
    /// </summary>
    [MaxLength(1000)]
    public string Comments { get; set; } = string.Empty;

    /// <summary>
    /// تاريخ التقييم
    /// </summary>
    public DateTime RatedAt { get; set; } = DateTime.Now;

    // Navigation Properties
    [ForeignKey("UserId")]
    public virtual User User { get; set; } = null!;

    [ForeignKey("RatedBy")]
    public virtual User RatedByUser { get; set; } = null!;
}
