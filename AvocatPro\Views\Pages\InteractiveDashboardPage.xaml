<Page x:Class="AvocatPro.Views.Pages.InteractiveDashboardPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="لوحة التحكم التفاعلية" FlowDirection="RightToLeft"
      Background="#F8FAFC">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان والتحكم -->
        <Border Grid.Row="0" Background="White" CornerRadius="15" Padding="20" Margin="20,20,20,10">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- العنوان -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border CornerRadius="50" Width="60" Height="60" Margin="0,0,20,0">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#3B82F6" Offset="0"/>
                                <GradientStop Color="#1E3A8A" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <Border.Effect>
                            <DropShadowEffect Color="#3B82F6" Opacity="0.4" BlurRadius="15" ShadowDepth="0"/>
                        </Border.Effect>
                        <TextBlock Text="📊" FontSize="30" Foreground="White" 
                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <StackPanel>
                        <TextBlock Text="لوحة التحكم التفاعلية" FontSize="24" FontWeight="Bold" Foreground="#1F2937"/>
                        <TextBlock Text="تحليلات حية ومؤشرات أداء متقدمة مع رسوم بيانية تفاعلية" FontSize="14" Foreground="#6B7280"/>
                    </StackPanel>
                </StackPanel>

                <!-- أزرار التحكم -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <ComboBox x:Name="TimeRangeComboBox" Width="150" Padding="10,8" 
                              Margin="0,0,10,0" SelectionChanged="TimeRange_SelectionChanged">
                        <ComboBoxItem Content="آخر 7 أيام"/>
                        <ComboBoxItem Content="آخر 30 يوم" IsSelected="True"/>
                        <ComboBoxItem Content="آخر 3 أشهر"/>
                        <ComboBoxItem Content="آخر سنة"/>
                        <ComboBoxItem Content="مخصص"/>
                    </ComboBox>
                    <Button x:Name="RefreshBtn" Background="#3B82F6" Foreground="White" Padding="12,8" 
                            Margin="0,0,10,0" Click="Refresh_Click" BorderThickness="0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔄" FontSize="14" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث"/>
                        </StackPanel>
                    </Button>
                    <Button x:Name="ExportBtn" Background="#10B981" Foreground="White" Padding="12,8" 
                            Click="Export_Click" BorderThickness="0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📥" FontSize="14" Margin="0,0,8,0"/>
                            <TextBlock Text="تصدير"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="20,0,20,20">
            <StackPanel>
                <!-- مؤشرات الأداء المباشرة -->
                <Border Background="White" CornerRadius="15" Padding="20" Margin="0,0,0,20">
                    <Border.Effect>
                        <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                    </Border.Effect>
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                            <TextBlock Text="⚡" FontSize="24" Margin="0,0,10,0"/>
                            <TextBlock Text="مؤشرات الأداء المباشرة" FontSize="18" FontWeight="SemiBold" Foreground="#1F2937"/>
                            <Border Background="#10B981" CornerRadius="10" Padding="8,4" Margin="15,0,0,0">
                                <StackPanel Orientation="Horizontal">
                                    <Ellipse Width="8" Height="8" Fill="White" Margin="0,0,5,0"/>
                                    <TextBlock Text="مباشر" FontSize="11" Foreground="White" FontWeight="SemiBold"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                        <UniformGrid x:Name="LiveKPIGrid" Columns="4" Margin="0,10,0,0">
                            <!-- سيتم إضافة مؤشرات الأداء ديناميكياً -->
                        </UniformGrid>
                    </StackPanel>
                </Border>

                <!-- الرسوم البيانية -->
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- الرسم البياني الرئيسي -->
                    <Border Grid.Column="0" Background="White" CornerRadius="15" Padding="20">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                        </Border.Effect>
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                <TextBlock Text="📈" FontSize="24" Margin="0,0,10,0"/>
                                <TextBlock Text="اتجاه الإيرادات التفاعلي" FontSize="18" FontWeight="SemiBold" Foreground="#1F2937"/>
                            </StackPanel>
                            <Canvas x:Name="MainChart" Height="300" Background="Transparent"/>
                        </StackPanel>
                    </Border>

                    <!-- الرسم البياني الثانوي -->
                    <Border Grid.Column="2" Background="White" CornerRadius="15" Padding="20">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                        </Border.Effect>
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                <TextBlock Text="🥧" FontSize="24" Margin="0,0,10,0"/>
                                <TextBlock Text="توزيع القضايا" FontSize="18" FontWeight="SemiBold" Foreground="#1F2937"/>
                            </StackPanel>
                            <Canvas x:Name="PieChart" Height="300" Background="Transparent"/>
                            <StackPanel x:Name="PieChartLegend" Margin="0,15,0,0">
                                <!-- سيتم إضافة وسائل الإيضاح ديناميكياً -->
                            </StackPanel>
                        </StackPanel>
                    </Border>
                </Grid>

                <!-- التحليلات المالية -->
                <Border Background="White" CornerRadius="15" Padding="20" Margin="0,0,0,20">
                    <Border.Effect>
                        <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                    </Border.Effect>
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                            <TextBlock Text="💰" FontSize="24" Margin="0,0,10,0"/>
                            <TextBlock Text="التحليلات المالية المتقدمة" FontSize="18" FontWeight="SemiBold" Foreground="#1F2937"/>
                        </StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- تحليل التدفق النقدي -->
                            <Border Grid.Column="0" Background="#F3F4F6" CornerRadius="12" Padding="20" Margin="0,0,10,0">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                        <TextBlock Text="💸" FontSize="20" Margin="0,0,10,0"/>
                                        <TextBlock Text="التدفق النقدي" FontWeight="SemiBold" FontSize="16"/>
                                    </StackPanel>
                                    <StackPanel x:Name="CashFlowPanel">
                                        <!-- سيتم إضافة بيانات التدفق النقدي ديناميكياً -->
                                    </StackPanel>
                                </StackPanel>
                            </Border>

                            <!-- تحليل الربحية -->
                            <Border Grid.Column="1" Background="#F3F4F6" CornerRadius="12" Padding="20" Margin="5,0">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                        <TextBlock Text="📊" FontSize="20" Margin="0,0,10,0"/>
                                        <TextBlock Text="تحليل الربحية" FontWeight="SemiBold" FontSize="16"/>
                                    </StackPanel>
                                    <StackPanel x:Name="ProfitabilityPanel">
                                        <!-- سيتم إضافة بيانات الربحية ديناميكياً -->
                                    </StackPanel>
                                </StackPanel>
                            </Border>

                            <!-- التوقعات المستقبلية -->
                            <Border Grid.Column="2" Background="#F3F4F6" CornerRadius="12" Padding="20" Margin="10,0,0,0">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                        <TextBlock Text="🔮" FontSize="20" Margin="0,0,10,0"/>
                                        <TextBlock Text="التوقعات" FontWeight="SemiBold" FontSize="16"/>
                                    </StackPanel>
                                    <StackPanel x:Name="ForecastPanel">
                                        <!-- سيتم إضافة التوقعات ديناميكياً -->
                                    </StackPanel>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- إحصائيات العملاء ومعدلات النجاح -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- إحصائيات العملاء -->
                    <Border Grid.Column="0" Background="White" CornerRadius="15" Padding="20">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                        </Border.Effect>
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                <TextBlock Text="👥" FontSize="24" Margin="0,0,10,0"/>
                                <TextBlock Text="إحصائيات العملاء" FontSize="18" FontWeight="SemiBold" Foreground="#1F2937"/>
                            </StackPanel>
                            <StackPanel x:Name="ClientStatsPanel">
                                <!-- سيتم إضافة إحصائيات العملاء ديناميكياً -->
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- معدلات النجاح -->
                    <Border Grid.Column="2" Background="White" CornerRadius="15" Padding="20">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                        </Border.Effect>
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                <TextBlock Text="🏆" FontSize="24" Margin="0,0,10,0"/>
                                <TextBlock Text="معدلات النجاح" FontSize="18" FontWeight="SemiBold" Foreground="#1F2937"/>
                            </StackPanel>
                            <StackPanel x:Name="SuccessRatesPanel">
                                <!-- سيتم إضافة معدلات النجاح ديناميكياً -->
                            </StackPanel>
                        </StackPanel>
                    </Border>
                </Grid>
            </StackPanel>
        </ScrollViewer>

        <!-- مؤشر التحميل -->
        <Border x:Name="LoadingOverlay" Grid.RowSpan="2" Background="Black" Opacity="0.5" Visibility="Collapsed">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="300" Height="6" 
                             Foreground="#3B82F6" Background="Transparent"/>
                <TextBlock Text="جاري تحميل البيانات التفاعلية..." Foreground="White" 
                           HorizontalAlignment="Center" Margin="0,20,0,0" FontSize="18"/>
            </StackPanel>
        </Border>
    </Grid>
</Page>
