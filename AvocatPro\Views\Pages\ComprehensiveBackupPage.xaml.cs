using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;

namespace AvocatPro.Views.Pages
{
    public partial class ComprehensiveBackupPage : Page
    {
        public ComprehensiveBackupPage()
        {
            InitializeComponent();
            InitializeBackupSettings();
        }

        private void InitializeBackupSettings()
        {
            var defaultPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "AvocatPro Backups");
            LocalPathTextBox.Text = defaultPath;

            if (!Directory.Exists(defaultPath))
            {
                try
                {
                    Directory.CreateDirectory(defaultPath);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إنشاء مجلد النسخ الاحتياطية: {ex.Message}", "خطأ", 
                                   MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
        }

        private void QuickBackupButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show("هل تريد إنشاء نسخة احتياطية سريعة؟", 
                                           "نسخة احتياطية سريعة", 
                                           MessageBoxButton.YesNo, 
                                           MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    CreateQuickBackup();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LocalBackupButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(LocalPathTextBox.Text))
                {
                    MessageBox.Show("يرجى تحديد مجلد الحفظ أولاً.", "تنبيه", 
                                   MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                CreateLocalBackup();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloudBackupButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("ميزة النسخ السحابي ستكون متاحة قريباً!", 
                           "قيد التطوير", 
                           MessageBoxButton.OK, 
                           MessageBoxImage.Information);
        }

        private void BrowseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dialog = new Microsoft.Win32.OpenFileDialog
                {
                    Title = "اختر مجلد حفظ النسخ الاحتياطية",
                    CheckFileExists = false,
                    CheckPathExists = true,
                    FileName = "اختر مجلد"
                };

                if (dialog.ShowDialog() == true)
                {
                    var selectedPath = System.IO.Path.GetDirectoryName(dialog.FileName);
                    if (!string.IsNullOrEmpty(selectedPath))
                    {
                        LocalPathTextBox.Text = selectedPath;
                    }
                }
            }
            catch
            {
                MessageBox.Show("خطأ في تصفح المجلدات", "خطأ",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("إعدادات النسخ الاحتياطية المتقدمة ستكون متاحة قريباً!", 
                           "قيد التطوير", 
                           MessageBoxButton.OK, 
                           MessageBoxImage.Information);
        }

        private void CreateQuickBackup()
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var backupFileName = $"AvocatPro_QuickBackup_{timestamp}.txt";
            var backupPath = Path.Combine(LocalPathTextBox.Text, backupFileName);

            CreateBackup(backupPath, "نسخة احتياطية سريعة");
        }

        private void CreateLocalBackup()
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var backupFileName = $"AvocatPro_Backup_{timestamp}.txt";
            var backupPath = Path.Combine(LocalPathTextBox.Text, backupFileName);

            CreateBackup(backupPath, "نسخة احتياطية محلية");
        }

        private void CreateBackup(string backupPath, string backupType)
        {
            try
            {
                var directory = Path.GetDirectoryName(backupPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var backupContent = $"نسخة احتياطية لبرنامج AvocatPro\nتاريخ الإنشاء: {DateTime.Now}\nالنوع: {backupType}";
                File.WriteAllText(backupPath, backupContent);

                MessageBox.Show($"تم إنشاء {backupType} بنجاح!\nالمسار: {backupPath}", 
                               "نجح", 
                               MessageBoxButton.OK, 
                               MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
