using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using AvocatPro.Data;
using AvocatPro.Models.UserManagement;

namespace AvocatPro.Services;

/// <summary>
/// خدمة إدارة الصلاحيات
/// </summary>
public class PermissionService
{
    private readonly AvocatProDbContext _context;
    private readonly ActivityLogService _activityLogService;

    public PermissionService(AvocatProDbContext context, ActivityLogService activityLogService)
    {
        _context = context;
        _activityLogService = activityLogService;
    }

    #region إدارة الصلاحيات الأساسية

    /// <summary>
    /// الحصول على جميع الصلاحيات
    /// </summary>
    public async Task<List<Permission>> GetAllPermissionsAsync()
    {
        return await _context.Permissions
            .Where(p => p.IsActive)
            .OrderBy(p => p.Module)
            .ThenBy(p => p.Type)
            .ThenBy(p => p.Name)
            .ToListAsync();
    }

    /// <summary>
    /// الحصول على الصلاحيات حسب الوحدة
    /// </summary>
    public async Task<List<Permission>> GetPermissionsByModuleAsync(string module)
    {
        return await _context.Permissions
            .Where(p => p.Module == module && p.IsActive)
            .OrderBy(p => p.Type)
            .ThenBy(p => p.Name)
            .ToListAsync();
    }

    /// <summary>
    /// إنشاء صلاحية جديدة
    /// </summary>
    public async Task<(bool Success, string Message)> CreatePermissionAsync(
        string name,
        string code,
        string description,
        string module,
        PermissionType type,
        PermissionLevel level,
        int createdBy)
    {
        try
        {
            // التحقق من عدم وجود رمز الصلاحية
            if (await _context.Permissions.AnyAsync(p => p.Code == code))
            {
                return (false, "رمز الصلاحية موجود بالفعل");
            }

            var permission = new Permission
            {
                Name = name,
                Code = code,
                Description = description,
                Module = module,
                Type = type,
                Level = level,
                IsActive = true,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            _context.Permissions.Add(permission);
            await _context.SaveChangesAsync();

            // تسجيل النشاط
            await _activityLogService.LogActivityAsync(
                createdBy,
                ActivityType.Create,
                $"إنشاء صلاحية جديدة: {name}",
                "PermissionManagement",
                permission.Id,
                "Permission"
            );

            return (true, "تم إنشاء الصلاحية بنجاح");
        }
        catch (Exception ex)
        {
            return (false, $"خطأ في إنشاء الصلاحية: {ex.Message}");
        }
    }

    /// <summary>
    /// تحديث صلاحية
    /// </summary>
    public async Task<(bool Success, string Message)> UpdatePermissionAsync(
        int permissionId,
        string name,
        string description,
        PermissionLevel level,
        int updatedBy)
    {
        try
        {
            var permission = await _context.Permissions.FindAsync(permissionId);
            if (permission == null)
            {
                return (false, "الصلاحية غير موجودة");
            }

            permission.Name = name;
            permission.Description = description;
            permission.Level = level;
            permission.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();

            // تسجيل النشاط
            await _activityLogService.LogActivityAsync(
                updatedBy,
                ActivityType.Update,
                $"تحديث صلاحية: {name}",
                "PermissionManagement",
                permissionId,
                "Permission"
            );

            return (true, "تم تحديث الصلاحية بنجاح");
        }
        catch (Exception ex)
        {
            return (false, $"خطأ في تحديث الصلاحية: {ex.Message}");
        }
    }

    /// <summary>
    /// حذف صلاحية
    /// </summary>
    public async Task<(bool Success, string Message)> DeletePermissionAsync(int permissionId, int deletedBy)
    {
        try
        {
            var permission = await _context.Permissions.FindAsync(permissionId);
            if (permission == null)
            {
                return (false, "الصلاحية غير موجودة");
            }

            // التحقق من عدم استخدام الصلاحية
            var isUsed = await _context.UserPermissions.AnyAsync(up => up.PermissionId == permissionId);
            if (isUsed)
            {
                // إلغاء تفعيل بدلاً من الحذف
                permission.IsActive = false;
                permission.UpdatedAt = DateTime.Now;
            }
            else
            {
                _context.Permissions.Remove(permission);
            }

            await _context.SaveChangesAsync();

            // تسجيل النشاط
            await _activityLogService.LogActivityAsync(
                deletedBy,
                ActivityType.Delete,
                $"حذف صلاحية: {permission.Name}",
                "PermissionManagement",
                permissionId,
                "Permission"
            );

            return (true, isUsed ? "تم إلغاء تفعيل الصلاحية" : "تم حذف الصلاحية بنجاح");
        }
        catch (Exception ex)
        {
            return (false, $"خطأ في حذف الصلاحية: {ex.Message}");
        }
    }

    #endregion

    #region إدارة مجموعات الصلاحيات

    /// <summary>
    /// الحصول على جميع مجموعات الصلاحيات
    /// </summary>
    public async Task<List<PermissionGroup>> GetAllPermissionGroupsAsync()
    {
        return await _context.PermissionGroups
            .Include(pg => pg.Permissions)
            .Where(pg => pg.IsActive)
            .OrderBy(pg => pg.DisplayOrder)
            .ThenBy(pg => pg.Name)
            .ToListAsync();
    }

    /// <summary>
    /// إنشاء مجموعة صلاحيات جديدة
    /// </summary>
    public async Task<(bool Success, string Message)> CreatePermissionGroupAsync(
        string name,
        string description,
        string color,
        string icon,
        int displayOrder,
        List<int> permissionIds,
        int createdBy)
    {
        try
        {
            var group = new PermissionGroup
            {
                Name = name,
                Description = description,
                Color = color,
                Icon = icon,
                DisplayOrder = displayOrder,
                IsActive = true,
                CreatedAt = DateTime.Now
            };

            _context.PermissionGroups.Add(group);
            await _context.SaveChangesAsync();

            // إضافة الصلاحيات للمجموعة
            if (permissionIds.Any())
            {
                var permissions = await _context.Permissions
                    .Where(p => permissionIds.Contains(p.Id))
                    .ToListAsync();

                group.Permissions = permissions;
                await _context.SaveChangesAsync();
            }

            // تسجيل النشاط
            await _activityLogService.LogActivityAsync(
                createdBy,
                ActivityType.Create,
                $"إنشاء مجموعة صلاحيات: {name}",
                "PermissionManagement",
                group.Id,
                "PermissionGroup"
            );

            return (true, "تم إنشاء مجموعة الصلاحيات بنجاح");
        }
        catch (Exception ex)
        {
            return (false, $"خطأ في إنشاء مجموعة الصلاحيات: {ex.Message}");
        }
    }

    /// <summary>
    /// تحديث مجموعة صلاحيات
    /// </summary>
    public async Task<(bool Success, string Message)> UpdatePermissionGroupAsync(
        int groupId,
        string name,
        string description,
        string color,
        string icon,
        int displayOrder,
        List<int> permissionIds,
        int updatedBy)
    {
        try
        {
            var group = await _context.PermissionGroups
                .Include(pg => pg.Permissions)
                .FirstOrDefaultAsync(pg => pg.Id == groupId);

            if (group == null)
            {
                return (false, "مجموعة الصلاحيات غير موجودة");
            }

            group.Name = name;
            group.Description = description;
            group.Color = color;
            group.Icon = icon;
            group.DisplayOrder = displayOrder;

            // تحديث الصلاحيات
            group.Permissions.Clear();
            if (permissionIds.Any())
            {
                var permissions = await _context.Permissions
                    .Where(p => permissionIds.Contains(p.Id))
                    .ToListAsync();

                foreach (var permission in permissions)
                {
                    group.Permissions.Add(permission);
                }
            }

            await _context.SaveChangesAsync();

            // تسجيل النشاط
            await _activityLogService.LogActivityAsync(
                updatedBy,
                ActivityType.Update,
                $"تحديث مجموعة صلاحيات: {name}",
                "PermissionManagement",
                groupId,
                "PermissionGroup"
            );

            return (true, "تم تحديث مجموعة الصلاحيات بنجاح");
        }
        catch (Exception ex)
        {
            return (false, $"خطأ في تحديث مجموعة الصلاحيات: {ex.Message}");
        }
    }

    #endregion

    #region تهيئة الصلاحيات الافتراضية

    /// <summary>
    /// تهيئة الصلاحيات الافتراضية للنظام
    /// </summary>
    public async Task InitializeDefaultPermissionsAsync()
    {
        try
        {
            // التحقق من وجود صلاحيات مسبقاً
            if (await _context.Permissions.AnyAsync())
                return;

            var permissions = GetDefaultPermissions();
            _context.Permissions.AddRange(permissions);
            await _context.SaveChangesAsync();

            // إنشاء مجموعات الصلاحيات الافتراضية
            await CreateDefaultPermissionGroupsAsync();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة الصلاحيات: {ex.Message}");
        }
    }

    /// <summary>
    /// الحصول على الصلاحيات الافتراضية
    /// </summary>
    private List<Permission> GetDefaultPermissions()
    {
        var permissions = new List<Permission>();
        var modules = new[]
        {
            ("ClientManagement", "إدارة العملاء"),
            ("CaseManagement", "إدارة القضايا"),
            ("SessionManagement", "إدارة الجلسات"),
            ("AppointmentManagement", "إدارة المواعيد"),
            ("DocumentManagement", "إدارة الوثائق"),
            ("ReportManagement", "إدارة التقارير"),
            ("UserManagement", "إدارة المستخدمين"),
            ("SystemSettings", "إعدادات النظام"),
            ("CheckPrinting", "طباعة الشيكات والأظرفة")
        };

        var permissionTypes = new[]
        {
            (PermissionType.View, "عرض", PermissionLevel.Basic),
            (PermissionType.Create, "إضافة", PermissionLevel.Intermediate),
            (PermissionType.Update, "تعديل", PermissionLevel.Intermediate),
            (PermissionType.Delete, "حذف", PermissionLevel.Advanced),
            (PermissionType.Print, "طباعة", PermissionLevel.Basic),
            (PermissionType.Export, "تصدير", PermissionLevel.Intermediate),
            (PermissionType.Manage, "إدارة", PermissionLevel.Administrative)
        };

        foreach (var (moduleCode, moduleName) in modules)
        {
            foreach (var (type, typeName, level) in permissionTypes)
            {
                permissions.Add(new Permission
                {
                    Name = $"{typeName} {moduleName}",
                    Code = $"{moduleCode}.{type}",
                    Description = $"صلاحية {typeName} في {moduleName}",
                    Module = moduleCode,
                    Type = type,
                    Level = level,
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                });
            }
        }

        return permissions;
    }

    /// <summary>
    /// إنشاء مجموعات الصلاحيات الافتراضية
    /// </summary>
    private async Task CreateDefaultPermissionGroupsAsync()
    {
        var groups = new[]
        {
            new PermissionGroup
            {
                Name = "مدير النظام",
                Description = "صلاحيات كاملة لجميع أجزاء النظام",
                Color = "#FF0000",
                Icon = "👑",
                DisplayOrder = 1,
                IsActive = true,
                CreatedAt = DateTime.Now
            },
            new PermissionGroup
            {
                Name = "محامي رئيسي",
                Description = "صلاحيات إدارية للمحامين الرئيسيين",
                Color = "#0066CC",
                Icon = "⚖️",
                DisplayOrder = 2,
                IsActive = true,
                CreatedAt = DateTime.Now
            },
            new PermissionGroup
            {
                Name = "محامي",
                Description = "صلاحيات أساسية للمحامين",
                Color = "#009900",
                Icon = "👨‍💼",
                DisplayOrder = 3,
                IsActive = true,
                CreatedAt = DateTime.Now
            },
            new PermissionGroup
            {
                Name = "سكرتير",
                Description = "صلاحيات محدودة للسكرتارية",
                Color = "#FF9900",
                Icon = "📋",
                DisplayOrder = 4,
                IsActive = true,
                CreatedAt = DateTime.Now
            },
            new PermissionGroup
            {
                Name = "مستخدم عادي",
                Description = "صلاحيات عرض فقط",
                Color = "#666666",
                Icon = "👤",
                DisplayOrder = 5,
                IsActive = true,
                CreatedAt = DateTime.Now
            }
        };

        _context.PermissionGroups.AddRange(groups);
        await _context.SaveChangesAsync();
    }

    #endregion
}
