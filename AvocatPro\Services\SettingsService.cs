using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using AvocatPro.Models;

namespace AvocatPro.Services
{
    /// <summary>
    /// خدمة إدارة الإعدادات
    /// </summary>
    public class SettingsService
    {
        private const string SETTINGS_FILE = "settings.json";
        private static SettingsService? _instance;
        private AppSettings _settings;

        public static SettingsService Instance => _instance ??= new SettingsService();

        public AppSettings Settings => _settings;

        private SettingsService()
        {
            _settings = new AppSettings();
            LoadSettings();
        }

        /// <summary>
        /// تحميل الإعدادات من الملف
        /// </summary>
        public void LoadSettings()
        {
            try
            {
                if (File.Exists(SETTINGS_FILE))
                {
                    var json = File.ReadAllText(SETTINGS_FILE);
                    var loadedSettings = JsonSerializer.Deserialize<AppSettings>(json);
                    if (loadedSettings != null)
                    {
                        _settings = loadedSettings;
                    }
                }
            }
            catch (Exception ex)
            {
                // في حالة فشل التحميل، استخدم الإعدادات الافتراضية
                Console.WriteLine($"خطأ في تحميل الإعدادات: {ex.Message}");
                _settings = new AppSettings();
            }
        }

        /// <summary>
        /// حفظ الإعدادات إلى الملف
        /// </summary>
        public async Task SaveSettingsAsync()
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                var json = JsonSerializer.Serialize(_settings, options);
                await File.WriteAllTextAsync(SETTINGS_FILE, json);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ الإعدادات: {ex.Message}");
            }
        }

        /// <summary>
        /// إعادة تعيين الإعدادات إلى القيم الافتراضية
        /// </summary>
        public void ResetToDefaults()
        {
            _settings = new AppSettings();
        }

        /// <summary>
        /// تصدير الإعدادات
        /// </summary>
        public async Task ExportSettingsAsync(string filePath)
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                var json = JsonSerializer.Serialize(_settings, options);
                await File.WriteAllTextAsync(filePath, json);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تصدير الإعدادات: {ex.Message}");
            }
        }

        /// <summary>
        /// استيراد الإعدادات
        /// </summary>
        public async Task ImportSettingsAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    throw new FileNotFoundException("ملف الإعدادات غير موجود");

                var json = await File.ReadAllTextAsync(filePath);
                var importedSettings = JsonSerializer.Deserialize<AppSettings>(json);
                
                if (importedSettings != null)
                {
                    _settings = importedSettings;
                    await SaveSettingsAsync();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استيراد الإعدادات: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية من الإعدادات
        /// </summary>
        public async Task CreateBackupAsync()
        {
            try
            {
                var backupFileName = $"settings_backup_{DateTime.Now:yyyyMMdd_HHmmss}.json";
                await ExportSettingsAsync(backupFileName);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من صحة إعدادات البريد الإلكتروني
        /// </summary>
        public bool ValidateEmailSettings()
        {
            return !string.IsNullOrWhiteSpace(_settings.SmtpServer) &&
                   _settings.SmtpPort > 0 &&
                   !string.IsNullOrWhiteSpace(_settings.SmtpUsername) &&
                   !string.IsNullOrWhiteSpace(_settings.FromEmail);
        }

        /// <summary>
        /// التحقق من صحة إعدادات قاعدة البيانات
        /// </summary>
        public bool ValidateDatabaseSettings()
        {
            return !string.IsNullOrWhiteSpace(_settings.ConnectionString) &&
                   !string.IsNullOrWhiteSpace(_settings.DatabaseType);
        }

        /// <summary>
        /// الحصول على قائمة اللغات المدعومة
        /// </summary>
        public string[] GetSupportedLanguages()
        {
            return new[] { "العربية", "English", "Français" };
        }

        /// <summary>
        /// الحصول على قائمة السمات المدعومة
        /// </summary>
        public string[] GetSupportedThemes()
        {
            return new[] { "فاتح", "داكن", "تلقائي" };
        }

        /// <summary>
        /// الحصول على قائمة الألوان المتاحة
        /// </summary>
        public string[] GetAvailableColors()
        {
            return new[]
            {
                "#6366F1", // Indigo
                "#10B981", // Emerald
                "#F59E0B", // Amber
                "#EF4444", // Red
                "#8B5CF6", // Violet
                "#06B6D4", // Cyan
                "#84CC16", // Lime
                "#F97316", // Orange
                "#EC4899", // Pink
                "#6B7280"  // Gray
            };
        }

        /// <summary>
        /// الحصول على قائمة الخطوط المدعومة
        /// </summary>
        public string[] GetSupportedFonts()
        {
            return new[]
            {
                "Segoe UI",
                "Arial",
                "Tahoma",
                "Calibri",
                "Times New Roman",
                "Courier New",
                "Verdana",
                "Georgia"
            };
        }

        /// <summary>
        /// الحصول على قائمة أنواع قواعد البيانات المدعومة
        /// </summary>
        public string[] GetSupportedDatabaseTypes()
        {
            return new[] { "SQLite", "SQL Server", "MySQL", "PostgreSQL" };
        }

        /// <summary>
        /// تطبيق الإعدادات على التطبيق
        /// </summary>
        public void ApplySettings()
        {
            try
            {
                // تطبيق إعدادات الواجهة
                ApplyUISettings();

                // تطبيق إعدادات اللغة
                ApplyLanguageSettings();

                // تطبيق إعدادات السمة
                ApplyThemeSettings();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تطبيق الإعدادات: {ex.Message}");
            }
        }

        private void ApplyUISettings()
        {
            // تطبيق إعدادات الخط وحجم الخط
            // سيتم تنفيذها في الواجهة الرئيسية
        }

        private void ApplyLanguageSettings()
        {
            // تطبيق إعدادات اللغة
            // سيتم تنفيذها مع نظام الترجمة
        }

        private void ApplyThemeSettings()
        {
            // تطبيق إعدادات السمة (فاتح/داكن)
            // سيتم تنفيذها في الواجهة الرئيسية
        }

        /// <summary>
        /// إضافة عنصر قائمة جديد
        /// </summary>
        public void AddMenuItem(string key, string name, string icon)
        {
            if (!_settings.MenuItems.ContainsKey(key))
            {
                _settings.MenuItems[key] = name;
                _settings.MenuIcons[key] = icon;
                if (!_settings.VisibleMenuItems.Contains(key))
                {
                    _settings.VisibleMenuItems.Add(key);
                }
            }
        }

        /// <summary>
        /// حذف عنصر قائمة
        /// </summary>
        public void RemoveMenuItem(string key)
        {
            _settings.MenuItems.Remove(key);
            _settings.MenuIcons.Remove(key);
            _settings.VisibleMenuItems.Remove(key);
        }

        /// <summary>
        /// تحديث عنصر قائمة
        /// </summary>
        public void UpdateMenuItem(string key, string newName, string newIcon)
        {
            if (_settings.MenuItems.ContainsKey(key))
            {
                _settings.MenuItems[key] = newName;
                _settings.MenuIcons[key] = newIcon;
            }
        }

        /// <summary>
        /// إخفاء/إظهار عنصر قائمة
        /// </summary>
        public void ToggleMenuItemVisibility(string key)
        {
            if (_settings.VisibleMenuItems.Contains(key))
            {
                _settings.VisibleMenuItems.Remove(key);
            }
            else
            {
                _settings.VisibleMenuItems.Add(key);
            }
        }
    }
}
