﻿#pragma checksum "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "DB1A8807A51D9C3077E262385E456442F838D2D8"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Pages {
    
    
    /// <summary>
    /// AppointmentsPageNew
    /// </summary>
    public partial class AppointmentsPageNew : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 123 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NotificationsButton;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border NotificationBadge;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NotificationCount;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddAppointmentButton;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAppointmentsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TodayAppointmentsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WeekAppointmentsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ConfirmedAppointmentsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PendingNotificationsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalRevenueTextBlock;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrevMonthButton;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentMonthTextBlock;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NextMonthButton;
        
        #line default
        #line hidden
        
        
        #line 234 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AppointmentTypeFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 247 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AppointmentStatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 259 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PriorityFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 271 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SendNotificationsButton;
        
        #line default
        #line hidden
        
        
        #line 279 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 287 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewModeButton;
        
        #line default
        #line hidden
        
        
        #line 290 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ViewModeIcon;
        
        #line default
        #line hidden
        
        
        #line 291 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ViewModeText;
        
        #line default
        #line hidden
        
        
        #line 305 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border CalendarView;
        
        #line default
        #line hidden
        
        
        #line 334 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.UniformGrid CalendarGrid;
        
        #line default
        #line hidden
        
        
        #line 339 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border TableView;
        
        #line default
        #line hidden
        
        
        #line 353 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid AppointmentsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 425 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/pages/appointmentspagenew.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.NotificationsButton = ((System.Windows.Controls.Button)(target));
            
            #line 124 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
            this.NotificationsButton.Click += new System.Windows.RoutedEventHandler(this.NotificationsButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.NotificationBadge = ((System.Windows.Controls.Border)(target));
            return;
            case 3:
            this.NotificationCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.AddAppointmentButton = ((System.Windows.Controls.Button)(target));
            
            #line 134 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
            this.AddAppointmentButton.Click += new System.Windows.RoutedEventHandler(this.AddAppointmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 142 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.TotalAppointmentsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.TodayAppointmentsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.WeekAppointmentsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.ConfirmedAppointmentsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.PendingNotificationsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.TotalRevenueTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.PrevMonthButton = ((System.Windows.Controls.Button)(target));
            
            #line 218 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
            this.PrevMonthButton.Click += new System.Windows.RoutedEventHandler(this.PrevMonthButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.CurrentMonthTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.NextMonthButton = ((System.Windows.Controls.Button)(target));
            
            #line 227 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
            this.NextMonthButton.Click += new System.Windows.RoutedEventHandler(this.NextMonthButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.AppointmentTypeFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 235 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
            this.AppointmentTypeFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 16:
            this.AppointmentStatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 248 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
            this.AppointmentStatusFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 17:
            this.PriorityFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 260 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
            this.PriorityFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 18:
            this.SendNotificationsButton = ((System.Windows.Controls.Button)(target));
            
            #line 272 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
            this.SendNotificationsButton.Click += new System.Windows.RoutedEventHandler(this.SendNotificationsButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 280 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.ViewModeButton = ((System.Windows.Controls.Button)(target));
            
            #line 288 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
            this.ViewModeButton.Click += new System.Windows.RoutedEventHandler(this.ViewModeButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.ViewModeIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.ViewModeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.CalendarView = ((System.Windows.Controls.Border)(target));
            return;
            case 24:
            this.CalendarGrid = ((System.Windows.Controls.Primitives.UniformGrid)(target));
            return;
            case 25:
            this.TableView = ((System.Windows.Controls.Border)(target));
            return;
            case 26:
            this.AppointmentsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 33:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 27:
            
            #line 382 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewAppointmentButton_Click);
            
            #line default
            #line hidden
            break;
            case 28:
            
            #line 388 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditAppointmentButton_Click);
            
            #line default
            #line hidden
            break;
            case 29:
            
            #line 394 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ConfirmAppointmentButton_Click);
            
            #line default
            #line hidden
            break;
            case 30:
            
            #line 400 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NotifyAppointmentButton_Click);
            
            #line default
            #line hidden
            break;
            case 31:
            
            #line 406 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PostponeAppointmentButton_Click);
            
            #line default
            #line hidden
            break;
            case 32:
            
            #line 412 "..\..\..\..\..\Views\Pages\AppointmentsPageNew.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteAppointmentButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

