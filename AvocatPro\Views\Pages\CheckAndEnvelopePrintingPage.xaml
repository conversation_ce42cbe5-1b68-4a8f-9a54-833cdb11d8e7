<Page x:Class="AvocatPro.Views.Pages.CheckAndEnvelopePrintingPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="طباعة الشيكات والأظرفة"
      FlowDirection="RightToLeft">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان الرئيسي -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,30">
            <TextBlock Text="🖨️" FontSize="32" VerticalAlignment="Center" Margin="0,0,15,0"/>
            <StackPanel>
                <TextBlock Text="طباعة الشيكات والأظرفة" FontSize="24" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBlock Text="نظام متقدم لطباعة الشيكات والأظرفة بتصاميم احترافية" 
                           FontSize="14" Foreground="Gray"/>
            </StackPanel>
        </StackPanel>

        <!-- المحتوى الرئيسي -->
        <TabControl Grid.Row="1">
            <!-- تبويب طباعة الشيكات -->
            <TabItem Header="💳 طباعة الشيكات">
                <Border Background="White" CornerRadius="10" Padding="20" Margin="10">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="10"/>
                    </Border.Effect>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- نموذج بيانات الشيك -->
                        <StackPanel Grid.Column="0" Margin="0,0,20,0">
                            <TextBlock Text="بيانات الشيك" FontSize="18" FontWeight="SemiBold" Margin="0,0,0,15"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- رقم الشيك -->
                                <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="رقم الشيك" FontWeight="Medium" Margin="0,0,0,5"/>
                                    <TextBox Name="CheckNumberTextBox" Padding="8" BorderBrush="LightGray" BorderThickness="1"/>
                                </StackPanel>

                                <!-- التاريخ -->
                                <StackPanel Grid.Row="0" Grid.Column="1" Margin="10,0,0,0">
                                    <TextBlock Text="التاريخ" FontWeight="Medium" Margin="0,0,0,5"/>
                                    <DatePicker Name="CheckDatePicker" Padding="8" BorderBrush="LightGray" BorderThickness="1"/>
                                </StackPanel>

                                <!-- اسم المستفيد -->
                                <StackPanel Grid.Row="1" Grid.ColumnSpan="2" Margin="0,15,0,0">
                                    <TextBlock Text="اسم المستفيد" FontWeight="Medium" Margin="0,0,0,5"/>
                                    <TextBox Name="PayeeNameTextBox" Padding="8" BorderBrush="LightGray" BorderThickness="1"/>
                                </StackPanel>

                                <!-- المبلغ -->
                                <StackPanel Grid.Row="2" Grid.Column="0" Margin="0,15,10,0">
                                    <TextBlock Text="المبلغ (بالأرقام)" FontWeight="Medium" Margin="0,0,0,5"/>
                                    <TextBox Name="AmountTextBox" Padding="8" BorderBrush="LightGray" BorderThickness="1"
                                             TextChanged="AmountTextBox_TextChanged"/>
                                </StackPanel>

                                <!-- البنك -->
                                <StackPanel Grid.Row="2" Grid.Column="1" Margin="10,15,0,0">
                                    <TextBlock Text="البنك" FontWeight="Medium" Margin="0,0,0,5"/>
                                    <ComboBox Name="BankComboBox" Padding="8" BorderBrush="LightGray" BorderThickness="1"/>
                                </StackPanel>

                                <!-- المبلغ بالكلمات -->
                                <StackPanel Grid.Row="3" Grid.ColumnSpan="2" Margin="0,15,0,0">
                                    <TextBlock Text="المبلغ بالكلمات" FontWeight="Medium" Margin="0,0,0,5"/>
                                    <TextBox Name="AmountWordsTextBox" Padding="8" BorderBrush="LightGray" BorderThickness="1"
                                             IsReadOnly="True" Background="LightGray"/>
                                </StackPanel>

                                <!-- رقم الحساب -->
                                <StackPanel Grid.Row="4" Grid.Column="0" Margin="0,15,10,0">
                                    <TextBlock Text="رقم الحساب" FontWeight="Medium" Margin="0,0,0,5"/>
                                    <TextBox Name="AccountNumberTextBox" Padding="8" BorderBrush="LightGray" BorderThickness="1"/>
                                </StackPanel>

                                <!-- التوقيع -->
                                <StackPanel Grid.Row="4" Grid.Column="1" Margin="10,15,0,0">
                                    <TextBlock Text="التوقيع" FontWeight="Medium" Margin="0,0,0,5"/>
                                    <TextBox Name="SignatureTextBox" Padding="8" BorderBrush="LightGray" BorderThickness="1"/>
                                </StackPanel>

                                <!-- الملاحظة -->
                                <StackPanel Grid.Row="5" Grid.ColumnSpan="2" Margin="0,15,0,0">
                                    <TextBlock Text="ملاحظة" FontWeight="Medium" Margin="0,0,0,5"/>
                                    <TextBox Name="MemoTextBox" Padding="8" BorderBrush="LightGray" BorderThickness="1"/>
                                </StackPanel>

                                <!-- خيارات الشطب -->
                                <StackPanel Grid.Row="6" Grid.ColumnSpan="2" Margin="0,15,0,0">
                                    <CheckBox Name="CrossedCheckBox" Content="شيك مشطوب" FontWeight="Medium" 
                                              Checked="CrossedCheckBox_Checked" Unchecked="CrossedCheckBox_Unchecked"/>
                                    <TextBox Name="CrossedTextTextBox" Padding="8" BorderBrush="LightGray" BorderThickness="1"
                                             Text="للمستفيد فقط" IsEnabled="False" Margin="0,5,0,0"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>

                        <!-- لوحة التحكم -->
                        <StackPanel Grid.Column="1">
                            <TextBlock Text="إعدادات الطباعة" FontSize="18" FontWeight="SemiBold" Margin="0,0,0,15"/>
                            
                            <!-- قالب الشيك -->
                            <StackPanel Margin="0,0,0,15">
                                <TextBlock Text="قالب الشيك" FontWeight="Medium" Margin="0,0,0,5"/>
                                <ComboBox Name="CheckTemplateComboBox" Padding="8" BorderBrush="LightGray" BorderThickness="1"/>
                            </StackPanel>

                            <!-- أزرار العمليات -->
                            <StackPanel>
                                <Button Name="PreviewCheckButton" Content="🔍 معاينة الشيك" 
                                        Padding="15,10" Margin="0,5" Background="LightBlue" BorderThickness="0"
                                        Click="PreviewCheckButton_Click"/>
                                
                                <Button Name="PrintCheckButton" Content="🖨️ طباعة الشيك" 
                                        Padding="15,10" Margin="0,5" Background="LightGreen" BorderThickness="0"
                                        Click="PrintCheckButton_Click"/>
                                
                                <Button Name="SaveCheckTemplateButton" Content="💾 حفظ كقالب" 
                                        Padding="15,10" Margin="0,5" Background="Orange" BorderThickness="0"
                                        Click="SaveCheckTemplateButton_Click"/>
                                
                                <Button Name="ClearCheckFormButton" Content="🗑️ مسح النموذج" 
                                        Padding="15,10" Margin="0,5" Background="LightCoral" BorderThickness="0"
                                        Click="ClearCheckFormButton_Click"/>
                            </StackPanel>
                        </StackPanel>
                    </Grid>
                </Border>
            </TabItem>

            <!-- تبويب طباعة الأظرفة -->
            <TabItem Header="✉️ طباعة الأظرفة">
                <Border Background="White" CornerRadius="10" Padding="20" Margin="10">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="10"/>
                    </Border.Effect>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- نموذج بيانات الظرف -->
                        <StackPanel Grid.Column="0" Margin="0,0,20,0">
                            <TextBlock Text="بيانات الظرف" FontSize="18" FontWeight="SemiBold" Margin="0,0,0,15"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- بيانات المرسل -->
                                <TextBlock Grid.Row="0" Grid.ColumnSpan="2" Text="بيانات المرسل" 
                                           FontWeight="SemiBold" FontSize="16" Margin="0,0,0,10" Foreground="Blue"/>

                                <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="اسم المرسل" FontWeight="Medium" Margin="0,0,0,5"/>
                                    <TextBox Name="SenderNameTextBox" Padding="8" BorderBrush="LightGray" BorderThickness="1"/>
                                </StackPanel>

                                <StackPanel Grid.Row="1" Grid.Column="1" Margin="10,0,0,0">
                                    <TextBlock Text="مدينة المرسل" FontWeight="Medium" Margin="0,0,0,5"/>
                                    <TextBox Name="SenderCityTextBox" Padding="8" BorderBrush="LightGray" BorderThickness="1"/>
                                </StackPanel>

                                <StackPanel Grid.Row="2" Grid.ColumnSpan="2" Margin="0,15,0,0">
                                    <TextBlock Text="عنوان المرسل" FontWeight="Medium" Margin="0,0,0,5"/>
                                    <TextBox Name="SenderAddressTextBox" Padding="8" BorderBrush="LightGray" BorderThickness="1"/>
                                </StackPanel>

                                <!-- بيانات المستقبل -->
                                <TextBlock Grid.Row="3" Grid.ColumnSpan="2" Text="بيانات المستقبل" 
                                           FontWeight="SemiBold" FontSize="16" Margin="0,25,0,10" Foreground="Blue"/>

                                <StackPanel Grid.Row="4" Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="اسم المستقبل" FontWeight="Medium" Margin="0,0,0,5"/>
                                    <TextBox Name="RecipientNameTextBox" Padding="8" BorderBrush="LightGray" BorderThickness="1"/>
                                </StackPanel>

                                <StackPanel Grid.Row="4" Grid.Column="1" Margin="10,0,0,0">
                                    <TextBlock Text="مدينة المستقبل" FontWeight="Medium" Margin="0,0,0,5"/>
                                    <TextBox Name="RecipientCityTextBox" Padding="8" BorderBrush="LightGray" BorderThickness="1"/>
                                </StackPanel>

                                <StackPanel Grid.Row="5" Grid.ColumnSpan="2" Margin="0,15,0,0">
                                    <TextBlock Text="عنوان المستقبل" FontWeight="Medium" Margin="0,0,0,5"/>
                                    <TextBox Name="RecipientAddressTextBox" Padding="8" BorderBrush="LightGray" BorderThickness="1"/>
                                </StackPanel>

                                <!-- خيارات إضافية -->
                                <StackPanel Grid.Row="6" Grid.ColumnSpan="2" Margin="0,20,0,0">
                                    <TextBlock Text="خيارات إضافية" FontWeight="SemiBold" FontSize="16" 
                                               Margin="0,0,0,10" Foreground="Blue"/>
                                    
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                        <CheckBox Name="RegisteredCheckBox" Content="بريد مسجل" Margin="0,0,20,0"/>
                                        <CheckBox Name="UrgentCheckBox" Content="بريد عاجل"/>
                                    </StackPanel>
                                    
                                    <TextBlock Text="تعليمات خاصة" FontWeight="Medium" Margin="0,0,0,5"/>
                                    <TextBox Name="SpecialInstructionsTextBox" Padding="8" BorderBrush="LightGray" BorderThickness="1"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>

                        <!-- لوحة التحكم -->
                        <StackPanel Grid.Column="1">
                            <TextBlock Text="إعدادات الطباعة" FontSize="18" FontWeight="SemiBold" Margin="0,0,0,15"/>
                            
                            <!-- حجم الظرف -->
                            <StackPanel Margin="0,0,0,15">
                                <TextBlock Text="حجم الظرف" FontWeight="Medium" Margin="0,0,0,5"/>
                                <ComboBox Name="EnvelopeSizeComboBox" Padding="8" BorderBrush="LightGray" BorderThickness="1"/>
                            </StackPanel>

                            <!-- أزرار العمليات -->
                            <StackPanel>
                                <Button Name="PreviewEnvelopeButton" Content="🔍 معاينة الظرف" 
                                        Padding="15,10" Margin="0,5" Background="LightBlue" BorderThickness="0"
                                        Click="PreviewEnvelopeButton_Click"/>
                                
                                <Button Name="PrintEnvelopeButton" Content="🖨️ طباعة الظرف" 
                                        Padding="15,10" Margin="0,5" Background="LightGreen" BorderThickness="0"
                                        Click="PrintEnvelopeButton_Click"/>
                                
                                <Button Name="SaveEnvelopeTemplateButton" Content="💾 حفظ كقالب" 
                                        Padding="15,10" Margin="0,5" Background="Orange" BorderThickness="0"
                                        Click="SaveEnvelopeTemplateButton_Click"/>
                                
                                <Button Name="ClearEnvelopeFormButton" Content="🗑️ مسح النموذج" 
                                        Padding="15,10" Margin="0,5" Background="LightCoral" BorderThickness="0"
                                        Click="ClearEnvelopeFormButton_Click"/>
                            </StackPanel>
                        </StackPanel>
                    </Grid>
                </Border>
            </TabItem>
        </TabControl>
    </Grid>
</Page>
