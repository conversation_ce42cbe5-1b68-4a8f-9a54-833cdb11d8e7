using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AvocatPro.Models;

namespace AvocatPro.Services
{
    /// <summary>
    /// خدمة إدارة المواعيد
    /// </summary>
    public class AppointmentsService
    {
        #region Appointment Types and Categories

        /// <summary>
        /// الحصول على أنواع المواعيد
        /// </summary>
        public List<string> GetAppointmentTypes()
        {
            return new List<string>
            {
                "استشارة قانونية",
                "اجتماع عمل",
                "جلسة محكمة",
                "توقيع عقد",
                "مراجعة وثائق",
                "متابعة قضية",
                "استقبال موكل",
                "اجتماع فريق",
                "مؤتمر هاتفي",
                "زيارة ميدانية",
                "تدريب",
                "ورشة عمل",
                "مناقشة استراتيجية",
                "مراجعة ملف"
            };
        }

        /// <summary>
        /// الحصول على فئات المواعيد
        /// </summary>
        public List<string> GetAppointmentCategories()
        {
            return new List<string>
            {
                "استشارة",
                "اجتماع",
                "محكمة",
                "عقد",
                "متابعة",
                "شخصي",
                "إداري",
                "تدريب"
            };
        }

        /// <summary>
        /// الحصول على حالات المواعيد
        /// </summary>
        public List<string> GetAppointmentStatuses()
        {
            return new List<string>
            {
                "مجدول",
                "جاري",
                "مكتمل",
                "مؤجل",
                "ملغي",
                "متأخر"
            };
        }

        /// <summary>
        /// الحصول على مستويات الأولوية
        /// </summary>
        public List<string> GetPriorityLevels()
        {
            return new List<string>
            {
                "عاجل",
                "مهم",
                "عادي"
            };
        }

        /// <summary>
        /// الحصول على قائمة المحامين
        /// </summary>
        public List<string> GetLawyers()
        {
            return new List<string>
            {
                "الأستاذ محمد العلوي",
                "الأستاذة فاطمة الزهراء",
                "الأستاذ عبد الرحمن الفاسي",
                "الأستاذة خديجة الإدريسي",
                "الأستاذ يوسف الحسني",
                "الأستاذة عائشة المغربي",
                "الأستاذ أحمد الأندلسي"
            };
        }

        /// <summary>
        /// الحصول على أنماط التكرار
        /// </summary>
        public List<string> GetRecurrencePatterns()
        {
            return new List<string>
            {
                "يومي",
                "أسبوعي",
                "شهري",
                "سنوي",
                "كل يومين",
                "كل أسبوعين",
                "كل شهرين",
                "كل ثلاثة أشهر",
                "كل ستة أشهر"
            };
        }

        /// <summary>
        /// الحصول على أوقات التذكير المتاحة
        /// </summary>
        public Dictionary<string, int> GetReminderOptions()
        {
            return new Dictionary<string, int>
            {
                { "5 دقائق", 5 },
                { "10 دقائق", 10 },
                { "15 دقيقة", 15 },
                { "30 دقيقة", 30 },
                { "ساعة واحدة", 60 },
                { "ساعتان", 120 },
                { "يوم واحد", 1440 },
                { "يومان", 2880 },
                { "أسبوع واحد", 10080 }
            };
        }

        #endregion

        #region Statistics

        /// <summary>
        /// حساب إحصائيات المواعيد
        /// </summary>
        public AppointmentStatistics CalculateStatistics(List<AdvancedAppointmentModel> appointments)
        {
            var now = DateTime.Now;
            var today = now.Date;
            var tomorrow = today.AddDays(1);
            var startOfWeek = today.AddDays(-(int)today.DayOfWeek);
            var endOfWeek = startOfWeek.AddDays(6);

            var stats = new AppointmentStatistics
            {
                TotalAppointments = appointments.Count,
                TodayAppointments = appointments.Count(a => a.AppointmentDate.Date == today),
                TomorrowAppointments = appointments.Count(a => a.AppointmentDate.Date == tomorrow),
                ThisWeekAppointments = appointments.Count(a => a.AppointmentDate.Date >= startOfWeek && a.AppointmentDate.Date <= endOfWeek),
                ScheduledAppointments = appointments.Count(a => a.Status == "مجدول"),
                CompletedAppointments = appointments.Count(a => a.Status == "مكتمل"),
                CancelledAppointments = appointments.Count(a => a.Status == "ملغي"),
                OverdueAppointments = appointments.Count(a => a.IsOverdue),
                PendingReminders = appointments.Count(a => a.NeedsReminder),
                TotalFees = appointments.Sum(a => a.Fees)
            };

            // حساب متوسط المواعيد اليومية
            if (appointments.Any())
            {
                var daysCount = appointments.GroupBy(a => a.AppointmentDate.Date).Count();
                stats.AverageAppointmentsPerDay = daysCount > 0 ? (double)appointments.Count / daysCount : 0;
            }

            // أكثر أنواع المواعيد نشاطاً
            var typeGroups = appointments.GroupBy(a => a.AppointmentType).OrderByDescending(g => g.Count());
            stats.MostActiveType = typeGroups.FirstOrDefault()?.Key ?? "غير محدد";

            // أكثر المحامين نشاطاً
            var lawyerGroups = appointments.GroupBy(a => a.AssignedLawyer).OrderByDescending(g => g.Count());
            stats.MostActiveLawyer = lawyerGroups.FirstOrDefault()?.Key ?? "غير محدد";

            return stats;
        }

        /// <summary>
        /// الحصول على إحصائيات يومية
        /// </summary>
        public Dictionary<DateTime, int> GetDailyStatistics(List<AdvancedAppointmentModel> appointments, DateTime startDate, DateTime endDate)
        {
            var dailyStats = new Dictionary<DateTime, int>();
            
            for (var date = startDate.Date; date <= endDate.Date; date = date.AddDays(1))
            {
                var count = appointments.Count(a => a.AppointmentDate.Date == date);
                dailyStats[date] = count;
            }

            return dailyStats;
        }

        /// <summary>
        /// الحصول على إحصائيات حسب نوع الموعد
        /// </summary>
        public Dictionary<string, int> GetAppointmentTypeStatistics(List<AdvancedAppointmentModel> appointments)
        {
            return appointments
                .GroupBy(a => a.AppointmentType)
                .ToDictionary(g => g.Key, g => g.Count())
                .OrderByDescending(kvp => kvp.Value)
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }

        /// <summary>
        /// الحصول على إحصائيات حسب المحامي
        /// </summary>
        public Dictionary<string, int> GetLawyerStatistics(List<AdvancedAppointmentModel> appointments)
        {
            return appointments
                .GroupBy(a => a.AssignedLawyer)
                .ToDictionary(g => g.Key, g => g.Count())
                .OrderByDescending(kvp => kvp.Value)
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }

        #endregion

        #region Calendar Helpers

        /// <summary>
        /// الحصول على مواعيد شهر معين
        /// </summary>
        public List<AdvancedAppointmentModel> GetAppointmentsForMonth(List<AdvancedAppointmentModel> appointments, int year, int month)
        {
            return appointments.Where(a => a.AppointmentDate.Year == year && a.AppointmentDate.Month == month).ToList();
        }

        /// <summary>
        /// الحصول على مواعيد يوم معين
        /// </summary>
        public List<AdvancedAppointmentModel> GetAppointmentsForDay(List<AdvancedAppointmentModel> appointments, DateTime date)
        {
            return appointments.Where(a => a.AppointmentDate.Date == date.Date)
                              .OrderBy(a => a.AppointmentTime)
                              .ToList();
        }

        /// <summary>
        /// الحصول على مواعيد الأسبوع الحالي
        /// </summary>
        public List<AdvancedAppointmentModel> GetThisWeekAppointments(List<AdvancedAppointmentModel> appointments)
        {
            var now = DateTime.Now;
            var startOfWeek = now.Date.AddDays(-(int)now.DayOfWeek);
            var endOfWeek = startOfWeek.AddDays(6);

            return appointments.Where(a => a.AppointmentDate.Date >= startOfWeek && a.AppointmentDate.Date <= endOfWeek)
                              .OrderBy(a => a.AppointmentDate)
                              .ThenBy(a => a.AppointmentTime)
                              .ToList();
        }

        /// <summary>
        /// الحصول على المواعيد القادمة
        /// </summary>
        public List<AdvancedAppointmentModel> GetUpcomingAppointments(List<AdvancedAppointmentModel> appointments, int days = 7)
        {
            var now = DateTime.Now;
            var endDate = now.AddDays(days);

            return appointments
                .Where(a => a.AppointmentDateTime >= now && a.AppointmentDateTime <= endDate && a.Status == "مجدول")
                .OrderBy(a => a.AppointmentDateTime)
                .ToList();
        }

        /// <summary>
        /// الحصول على المواعيد المتأخرة
        /// </summary>
        public List<AdvancedAppointmentModel> GetOverdueAppointments(List<AdvancedAppointmentModel> appointments)
        {
            return appointments
                .Where(a => a.IsOverdue)
                .OrderBy(a => a.AppointmentDateTime)
                .ToList();
        }

        /// <summary>
        /// الحصول على المواعيد التي تحتاج تذكير
        /// </summary>
        public List<AdvancedAppointmentModel> GetAppointmentsNeedingReminder(List<AdvancedAppointmentModel> appointments)
        {
            return appointments
                .Where(a => a.NeedsReminder)
                .OrderBy(a => a.AppointmentDateTime)
                .ToList();
        }

        #endregion

        #region Validation

        /// <summary>
        /// التحقق من صحة بيانات الموعد
        /// </summary>
        public (bool IsValid, List<string> Errors) ValidateAppointment(AdvancedAppointmentModel appointment)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(appointment.Title))
                errors.Add("عنوان الموعد مطلوب");

            if (string.IsNullOrWhiteSpace(appointment.AppointmentType))
                errors.Add("نوع الموعد مطلوب");

            if (string.IsNullOrWhiteSpace(appointment.AssignedLawyer))
                errors.Add("المحامي المكلف مطلوب");

            if (appointment.AppointmentDateTime < DateTime.Now && appointment.Status == "مجدول")
                errors.Add("لا يمكن جدولة موعد في وقت سابق");

            if (appointment.Duration.TotalMinutes <= 0)
                errors.Add("مدة الموعد يجب أن تكون أكبر من صفر");

            if (appointment.Fees < 0)
                errors.Add("الرسوم لا يمكن أن تكون سالبة");

            if (appointment.ReminderMinutes < 0)
                errors.Add("وقت التذكير لا يمكن أن يكون سالباً");

            return (errors.Count == 0, errors);
        }

        /// <summary>
        /// التحقق من تعارض المواعيد
        /// </summary>
        public List<AdvancedAppointmentModel> CheckConflicts(AdvancedAppointmentModel newAppointment, List<AdvancedAppointmentModel> existingAppointments)
        {
            var conflicts = new List<AdvancedAppointmentModel>();
            
            foreach (var existing in existingAppointments.Where(a => a.Id != newAppointment.Id && a.Status == "مجدول"))
            {
                // التحقق من تداخل الأوقات
                var newStart = newAppointment.AppointmentDateTime;
                var newEnd = newAppointment.EndTime;
                var existingStart = existing.AppointmentDateTime;
                var existingEnd = existing.EndTime;

                if ((newStart < existingEnd && newEnd > existingStart) ||
                    (existingStart < newEnd && existingEnd > newStart))
                {
                    conflicts.Add(existing);
                }
            }

            return conflicts;
        }

        #endregion

        #region Recurrence

        /// <summary>
        /// إنشاء مواعيد متكررة
        /// </summary>
        public List<AdvancedAppointmentModel> CreateRecurringAppointments(AdvancedAppointmentModel baseAppointment)
        {
            var appointments = new List<AdvancedAppointmentModel>();
            
            if (!baseAppointment.IsRecurring || baseAppointment.RecurrenceEndDate == null)
                return appointments;

            var currentDate = baseAppointment.AppointmentDate.AddDays(GetRecurrenceInterval(baseAppointment.RecurrencePattern));
            var endDate = baseAppointment.RecurrenceEndDate.Value;

            while (currentDate <= endDate)
            {
                var newAppointment = CloneAppointment(baseAppointment);
                newAppointment.Id = 0; // سيتم تعيين ID جديد
                newAppointment.AppointmentDate = currentDate;
                newAppointment.AppointmentNumber = GenerateAppointmentNumber();
                newAppointment.ReminderSent = false;
                newAppointment.ReminderSentDate = null;

                appointments.Add(newAppointment);

                currentDate = currentDate.AddDays(GetRecurrenceInterval(baseAppointment.RecurrencePattern));
            }

            return appointments;
        }

        private int GetRecurrenceInterval(string pattern)
        {
            return pattern switch
            {
                "يومي" => 1,
                "كل يومين" => 2,
                "أسبوعي" => 7,
                "كل أسبوعين" => 14,
                "شهري" => 30,
                "كل شهرين" => 60,
                "كل ثلاثة أشهر" => 90,
                "كل ستة أشهر" => 180,
                "سنوي" => 365,
                _ => 1
            };
        }

        private AdvancedAppointmentModel CloneAppointment(AdvancedAppointmentModel original)
        {
            return new AdvancedAppointmentModel
            {
                Title = original.Title,
                Description = original.Description,
                AppointmentTime = original.AppointmentTime,
                Duration = original.Duration,
                AppointmentType = original.AppointmentType,
                Location = original.Location,
                ClientName = original.ClientName,
                ClientPhone = original.ClientPhone,
                ClientEmail = original.ClientEmail,
                AssignedLawyer = original.AssignedLawyer,
                Status = original.Status,
                Priority = original.Priority,
                Notes = original.Notes,
                ReminderEnabled = original.ReminderEnabled,
                ReminderMinutes = original.ReminderMinutes,
                EmailReminderEnabled = original.EmailReminderEnabled,
                SmsReminderEnabled = original.SmsReminderEnabled,
                Fees = original.Fees,
                Documents = original.Documents,
                Category = original.Category,
                RelatedFileNumber = original.RelatedFileNumber,
                CreatedBy = original.CreatedBy,
                CreatedDate = DateTime.Now
            };
        }

        #endregion

        #region Export

        /// <summary>
        /// تحضير بيانات التصدير
        /// </summary>
        public List<object> PrepareExportData(List<AdvancedAppointmentModel> appointments)
        {
            return appointments.Select(a => new
            {
                رقم_الموعد = a.AppointmentNumber,
                العنوان = a.Title,
                الوصف = a.Description,
                التاريخ = a.AppointmentDate.ToString("dd/MM/yyyy"),
                الوقت = a.AppointmentTimeText,
                المدة = a.DurationText,
                نوع_الموعد = a.AppointmentType,
                المكان = a.Location,
                اسم_الموكل = a.ClientName,
                هاتف_الموكل = a.ClientPhone,
                بريد_الموكل = a.ClientEmail,
                المحامي_المكلف = a.AssignedLawyer,
                الحالة = a.Status,
                الأولوية = a.Priority,
                الفئة = a.Category,
                الرسوم = a.Fees,
                التذكير = a.ReminderText,
                الملاحظات = a.Notes,
                النتيجة = a.Outcome,
                تاريخ_الإنشاء = a.CreatedDate.ToString("dd/MM/yyyy")
            }).Cast<object>().ToList();
        }

        #endregion

        #region Utilities

        /// <summary>
        /// توليد رقم موعد جديد
        /// </summary>
        public string GenerateAppointmentNumber()
        {
            var year = DateTime.Now.Year;
            var timestamp = DateTime.Now.ToString("MMddHHmmss");
            return $"A{year}{timestamp}";
        }

        #endregion
    }
}
