<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت منصة المحامين الذكية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        .install-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .install-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .install-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .install-header h1 {
            margin: 0;
            font-weight: 700;
            font-size: 2rem;
        }
        .install-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }
        .progress-container {
            padding: 1.5rem 2rem 0;
        }
        .step-progress {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2rem;
            position: relative;
        }
        .step-progress::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 0;
            right: 0;
            height: 2px;
            background: #e9ecef;
            z-index: 1;
        }
        .step-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 2;
            background: white;
            padding: 0 10px;
        }
        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .step-circle.active {
            background: #667eea;
            color: white;
        }
        .step-circle.completed {
            background: #28a745;
            color: white;
        }
        .step-label {
            font-size: 0.8rem;
            text-align: center;
            color: #6c757d;
            max-width: 80px;
        }
        .install-content {
            padding: 2rem;
        }
        .form-group {
            margin-bottom: 1.5rem;
        }
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .requirement-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .requirement-status {
            font-weight: 600;
        }
        .requirement-status.passed {
            color: #28a745;
        }
        .requirement-status.failed {
            color: #dc3545;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        .feature-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
        }
        .feature-icon {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 2rem;
        }
        .success-animation {
            text-align: center;
            padding: 3rem;
        }
        .success-icon {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 1rem;
            animation: bounce 1s infinite;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-card">
            <div class="install-header">
                <h1><i class="fas fa-gavel"></i> منصة المحامين الذكية</h1>
                <p>مرحباً بك في معالج التثبيت السهل والسريع</p>
            </div>

            <div class="progress-container">
                <div class="step-progress">
                    <?php foreach ($this->steps as $stepKey => $stepLabel): ?>
                        <div class="step-item">
                            <div class="step-circle <?php 
                                echo $stepKey === $this->currentStep ? 'active' : '';
                                echo array_search($stepKey, array_keys($this->steps)) < array_search($this->currentStep, array_keys($this->steps)) ? 'completed' : '';
                            ?>">
                                <?php if (array_search($stepKey, array_keys($this->steps)) < array_search($this->currentStep, array_keys($this->steps))): ?>
                                    <i class="fas fa-check"></i>
                                <?php else: ?>
                                    <?php echo array_search($stepKey, array_keys($this->steps)) + 1; ?>
                                <?php endif; ?>
                            </div>
                            <div class="step-label"><?php echo $stepLabel; ?></div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <div class="install-content">
                <?php if (!empty($this->errors)): ?>
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-triangle"></i> يوجد أخطاء تحتاج إلى إصلاح:</h5>
                        <ul class="mb-0">
                            <?php foreach ($this->errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php switch ($this->currentStep): 
                    case 'requirements': ?>
                        <h3><i class="fas fa-clipboard-check"></i> فحص متطلبات النظام</h3>
                        <p class="text-muted">نتأكد من توفر جميع المتطلبات اللازمة لتشغيل المنصة</p>
                        
                        <div class="requirements-list">
                            <?php
                            $requirements = [
                                'PHP >= 8.1' => version_compare(PHP_VERSION, '8.1.0', '>='),
                                'OpenSSL Extension' => extension_loaded('openssl'),
                                'PDO Extension' => extension_loaded('pdo'),
                                'Mbstring Extension' => extension_loaded('mbstring'),
                                'Tokenizer Extension' => extension_loaded('tokenizer'),
                                'XML Extension' => extension_loaded('xml'),
                                'Ctype Extension' => extension_loaded('ctype'),
                                'JSON Extension' => extension_loaded('json'),
                                'BCMath Extension' => extension_loaded('bcmath'),
                                'Fileinfo Extension' => extension_loaded('fileinfo'),
                                'GD Extension' => extension_loaded('gd'),
                                'cURL Extension' => extension_loaded('curl'),
                                'Zip Extension' => extension_loaded('zip'),
                            ];

                            foreach ($requirements as $requirement => $passed): ?>
                                <div class="requirement-item">
                                    <span><?php echo $requirement; ?></span>
                                    <span class="requirement-status <?php echo $passed ? 'passed' : 'failed'; ?>">
                                        <i class="fas fa-<?php echo $passed ? 'check' : 'times'; ?>"></i>
                                        <?php echo $passed ? 'متوفر' : 'غير متوفر'; ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <form method="POST" class="mt-4">
                            <button type="submit" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-arrow-left"></i> متابعة إلى الخطوة التالية
                            </button>
                        </form>
                        
                    <?php break; case 'database': ?>
                        <h3><i class="fas fa-database"></i> إعداد قاعدة البيانات</h3>
                        <p class="text-muted">أدخل معلومات الاتصال بقاعدة البيانات</p>
                        
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="form-group">
                                        <label class="form-label">خادم قاعدة البيانات</label>
                                        <input type="text" name="db_host" class="form-control" value="127.0.0.1" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">المنفذ</label>
                                        <input type="number" name="db_port" class="form-control" value="3306" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">اسم قاعدة البيانات</label>
                                <input type="text" name="db_database" class="form-control" value="smartlawyers_web" required>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">اسم المستخدم</label>
                                        <input type="text" name="db_username" class="form-control" value="root" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">كلمة المرور</label>
                                        <input type="password" name="db_password" class="form-control">
                                    </div>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-check"></i> اختبار الاتصال والمتابعة
                            </button>
                        </form>
                        
                    <?php break; case 'complete': ?>
                        <div class="success-animation">
                            <div class="success-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <h3>تم التثبيت بنجاح!</h3>
                            <p class="text-muted">منصة المحامين الذكية جاهزة للاستخدام</p>
                            
                            <div class="feature-grid">
                                <div class="feature-card">
                                    <div class="feature-icon"><i class="fas fa-tachometer-alt"></i></div>
                                    <h5>لوحة التحكم</h5>
                                    <p>إحصائيات شاملة ومتقدمة</p>
                                </div>
                                <div class="feature-card">
                                    <div class="feature-icon"><i class="fas fa-calendar"></i></div>
                                    <h5>التقويم المتكامل</h5>
                                    <p>ربط مع Google و Outlook</p>
                                </div>
                                <div class="feature-card">
                                    <div class="feature-icon"><i class="fab fa-whatsapp"></i></div>
                                    <h5>WhatsApp Business</h5>
                                    <p>تواصل مباشر مع العملاء</p>
                                </div>
                                <div class="feature-card">
                                    <div class="feature-icon"><i class="fas fa-gavel"></i></div>
                                    <h5>المحاكم المغربية</h5>
                                    <p>تكامل مع mahakim.ma</p>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <a href="/" class="btn btn-primary btn-lg me-3">
                                    <i class="fas fa-home"></i> الذهاب إلى المنصة
                                </a>
                                <a href="/admin" class="btn btn-outline-primary btn-lg">
                                    <i class="fas fa-cog"></i> لوحة الإدارة
                                </a>
                            </div>
                        </div>
                        
                    <?php break; endswitch; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Add smooth animations and interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Animate step circles
            const circles = document.querySelectorAll('.step-circle');
            circles.forEach((circle, index) => {
                setTimeout(() => {
                    circle.style.transform = 'scale(1.1)';
                    setTimeout(() => {
                        circle.style.transform = 'scale(1)';
                    }, 200);
                }, index * 100);
            });
            
            // Form validation
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const button = form.querySelector('button[type="submit"]');
                    if (button) {
                        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';
                        button.disabled = true;
                    }
                });
            });
        });
    </script>
</body>
</html>
