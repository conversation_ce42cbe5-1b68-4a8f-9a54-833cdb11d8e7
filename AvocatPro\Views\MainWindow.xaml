<Window x:Class="AvocatPro.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="AvocatPro - نظام إدارة مكتب المحاماة"
        Height="800" Width="1400"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        FlowDirection="RightToLeft"
        Background="{StaticResource BackgroundBrush}"
        AllowsTransparency="False"
        WindowStyle="SingleBorderWindow">

    <Window.Resources>
        <!-- أنماط الشريط الجانبي الحديث -->
        <Style x:Key="ModernSidebarButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Height" Value="60"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
            <Setter Property="Padding" Value="20,15"/>
            <Setter Property="FontSize" Value="15"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="12,6"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="15"
                                Padding="{TemplateBinding Padding}">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" Opacity="0" BlurRadius="8" ShadowDepth="2"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                            <GradientStop Color="White" Offset="0" />
                                            <GradientStop Color="#F0F8FF" Offset="1" />
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                                <Setter Property="Foreground" Value="#1E3A8A"/>
                                <Setter TargetName="border" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="#3B82F6" Opacity="0.2" BlurRadius="12" ShadowDepth="0"/>
                                    </Setter.Value>
                                </Setter>
                                <Setter TargetName="border" Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.02" ScaleY="1.02"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background">
                                    <Setter.Value>
                                        <SolidColorBrush Color="White" Opacity="0.9"/>
                                    </Setter.Value>
                                </Setter>
                                <Setter Property="Foreground" Value="#1E3A8A"/>
                                <Setter TargetName="border" Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ActiveSidebarButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernSidebarButtonStyle}">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="White" Offset="0" />
                        <GradientStop Color="#E0F2FE" Offset="1" />
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="#1E3A8A"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#3B82F6" Opacity="0.3" BlurRadius="12" ShadowDepth="0"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- أنماط البطاقات المتطورة -->
        <Style x:Key="GlassCardStyle" TargetType="Border">
            <Setter Property="Background">
                <Setter.Value>
                    <SolidColorBrush Color="White" Opacity="0.95"/>
                </Setter.Value>
            </Setter>
            <Setter Property="CornerRadius" Value="16"/>
            <Setter Property="Padding" Value="24"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#000000" Opacity="0.08" BlurRadius="20" ShadowDepth="4" Direction="270"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="280"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- الشريط الجانبي الحديث -->
        <Border Grid.Column="0">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#1E3A8A" Offset="0"/>
                    <GradientStop Color="#3B82F6" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="#000000" Opacity="0.15" BlurRadius="15" ShadowDepth="5" Direction="0"/>
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- رأس الشريط الجانبي المتطور -->
                <Border Grid.Row="0" Padding="24,32,24,24">
                    <Border.Background>
                        <SolidColorBrush Color="White" Opacity="0.1"/>
                    </Border.Background>
                    <StackPanel>
                        <Ellipse Width="80" Height="80" Margin="0,0,0,16">
                            <Ellipse.Fill>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                    <GradientStop Color="#F59E0B" Offset="0"/>
                                    <GradientStop Color="#FBBF24" Offset="1"/>
                                </LinearGradientBrush>
                            </Ellipse.Fill>
                            <Ellipse.Effect>
                                <DropShadowEffect Color="#000000" Opacity="0.2" BlurRadius="10" ShadowDepth="3"/>
                            </Ellipse.Effect>
                        </Ellipse>
                        <TextBlock Text="AvocatPro" FontSize="24" FontWeight="Bold" Foreground="White"
                                   HorizontalAlignment="Center" Margin="0,0,0,4"/>
                        <TextBlock Text="نظام إدارة مكتب المحاماة" FontSize="13" Foreground="White"
                                   HorizontalAlignment="Center" Opacity="0.8"/>
                        <TextBlock Text="المغرب" FontSize="12" Foreground="White"
                                   HorizontalAlignment="Center" Opacity="0.6" Margin="0,4,0,0"/>
                    </StackPanel>
                </Border>

                <!-- قائمة التنقل المتطورة -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="16,24,16,0">
                    <StackPanel>
                        <!-- لوحة التحكم -->
                        <Button Name="DashboardButton" Style="{StaticResource ActiveSidebarButtonStyle}"
                               Click="NavigationButton_Click" Tag="Dashboard">
                            <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
                                <TextBlock Text="لوحة التحكم" VerticalAlignment="Center" FontWeight="SemiBold" Margin="0,0,12,0"/>
                                <Border Background="#1E3A8A" CornerRadius="10" Width="36" Height="36">
                                    <TextBlock Text="📊" FontSize="18" Foreground="White"
                                               HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                            </StackPanel>
                        </Button>

                        <!-- إدارة الموكلين -->
                        <Button Name="ClientsButton" Style="{StaticResource ModernSidebarButtonStyle}"
                               Click="NavigationButton_Click" Tag="Clients">
                            <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
                                <TextBlock Text="إدارة الموكلين" VerticalAlignment="Center" FontWeight="Medium" Margin="0,0,12,0"/>
                                <Border Background="Transparent" BorderBrush="White" BorderThickness="2"
                                        CornerRadius="10" Width="36" Height="36">
                                    <TextBlock Text="👥" FontSize="18" Foreground="White"
                                               HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                            </StackPanel>
                        </Button>

                        <!-- إدارة الملفات -->
                        <Button Name="CasesButton" Style="{StaticResource ModernSidebarButtonStyle}"
                               Click="NavigationButton_Click" Tag="Cases">
                            <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
                                <TextBlock Text="إدارة الملفات" VerticalAlignment="Center" FontWeight="Medium" Margin="0,0,12,0"/>
                                <Border Background="Transparent" BorderBrush="White" BorderThickness="2"
                                        CornerRadius="10" Width="36" Height="36">
                                    <TextBlock Text="📁" FontSize="18" Foreground="White"
                                               HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                            </StackPanel>
                        </Button>

                        <!-- إدارة الجلسات -->
                        <Button Name="SessionsButton" Style="{StaticResource ModernSidebarButtonStyle}"
                               Click="NavigationButton_Click" Tag="Sessions">
                            <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
                                <TextBlock Text="إدارة الجلسات" VerticalAlignment="Center" FontWeight="Medium" Margin="0,0,12,0"/>
                                <Border Background="Transparent" BorderBrush="White" BorderThickness="2"
                                        CornerRadius="10" Width="36" Height="36">
                                    <TextBlock Text="⚖️" FontSize="18" Foreground="White"
                                               HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                            </StackPanel>
                        </Button>

                        <!-- إدارة المواعيد -->
                        <Button Name="AppointmentsButton" Style="{StaticResource ModernSidebarButtonStyle}"
                               Click="NavigationButton_Click" Tag="Appointments">
                            <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
                                <TextBlock Text="إدارة المواعيد" VerticalAlignment="Center" FontWeight="Medium" Margin="0,0,12,0"/>
                                <Border Background="Transparent" BorderBrush="White" BorderThickness="2"
                                        CornerRadius="10" Width="36" Height="36">
                                    <TextBlock Text="📅" FontSize="18" Foreground="White"
                                               HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                            </StackPanel>
                        </Button>

                        <!-- الإدارة المالية -->
                        <Button Name="FinanceButton" Style="{StaticResource ModernSidebarButtonStyle}"
                               Click="NavigationButton_Click" Tag="Finance">
                            <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
                                <TextBlock Text="الإدارة المالية" VerticalAlignment="Center" FontWeight="Medium" Margin="0,0,12,0"/>
                                <Border Background="Transparent" BorderBrush="White" BorderThickness="2"
                                        CornerRadius="10" Width="36" Height="36">
                                    <TextBlock Text="💰" FontSize="18" Foreground="White"
                                               HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                            </StackPanel>
                        </Button>

                        <!-- التقارير -->
                        <Button Name="ReportsButton" Style="{StaticResource ModernSidebarButtonStyle}"
                               Click="NavigationButton_Click" Tag="Reports">
                            <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
                                <TextBlock Text="التقارير والإحصائيات" VerticalAlignment="Center" FontWeight="Medium" Margin="0,0,12,0"/>
                                <Border Background="Transparent" BorderBrush="White" BorderThickness="2"
                                        CornerRadius="10" Width="36" Height="36">
                                    <TextBlock Text="📈" FontSize="18" Foreground="White"
                                               HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                            </StackPanel>
                        </Button>

                        <!-- إدارة المستخدمين -->
                        <Button Name="UserManagementButton" Style="{StaticResource ModernSidebarButtonStyle}"
                               Click="NavigationButton_Click" Tag="UserManagement">
                            <StackPanel Orientation="Horizontal">
                                <Border Background="Transparent" CornerRadius="8" Width="32" Height="32" Margin="0,0,12,0">
                                    <TextBlock Text="👤" FontSize="16" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="إدارة المستخدمين" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <!-- النسخ الاحتياطية الشاملة -->
                        <Button Name="ComprehensiveBackupButton" Style="{StaticResource ModernSidebarButtonStyle}"
                               Click="NavigationButton_Click" Tag="ComprehensiveBackup">
                            <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
                                <TextBlock Text="النسخ الاحتياطية" VerticalAlignment="Center" FontWeight="Medium" Margin="0,0,12,0"/>
                                <Border Background="Transparent" BorderBrush="White" BorderThickness="2"
                                        CornerRadius="10" Width="36" Height="36">
                                    <TextBlock Text="💾" FontSize="18" Foreground="White"
                                               HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                            </StackPanel>
                        </Button>

                        <!-- المساعد الذكي المتطور -->
                        <Button Name="SmartAssistantButton" Style="{StaticResource ModernSidebarButtonStyle}"
                               Click="NavigationButton_Click" Tag="SmartAssistant">
                            <StackPanel Orientation="Horizontal">
                                <Border Background="Transparent" CornerRadius="8" Width="32" Height="32" Margin="0,0,12,0">
                                    <TextBlock Text="🤖" FontSize="16" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="المساعد الذكي" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <!-- لوحة التحكم التفاعلية -->
                        <Button Name="InteractiveDashboardButton" Style="{StaticResource ModernSidebarButtonStyle}"
                               Click="NavigationButton_Click" Tag="InteractiveDashboard">
                            <StackPanel Orientation="Horizontal">
                                <Border Background="Transparent" CornerRadius="8" Width="32" Height="32" Margin="0,0,12,0">
                                    <TextBlock Text="📊" FontSize="16" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="لوحة التحكم التفاعلية" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <!-- فاصل أنيق -->
                        <Border Height="1" Background="White" Opacity="0.2" Margin="24,16,24,16"/>

                        <!-- الإعدادات -->
                        <Button Name="SettingsButton" Style="{StaticResource ModernSidebarButtonStyle}"
                               Click="NavigationButton_Click" Tag="Settings">
                            <StackPanel Orientation="Horizontal">
                                <Border Background="Transparent" CornerRadius="8" Width="32" Height="32" Margin="0,0,12,0">
                                    <TextBlock Text="⚙️" FontSize="16" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="الإعدادات" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </ScrollViewer>

                <!-- أسفل الشريط الجانبي المتطور -->
                <Border Grid.Row="2" Padding="16,24">
                    <Border.Background>
                        <SolidColorBrush Color="White" Opacity="0.05"/>
                    </Border.Background>
                    <StackPanel>
                        <Border Background="White" CornerRadius="12" Padding="16,12" Margin="0,0,0,16" Opacity="0.9">
                            <StackPanel>
                                <TextBlock Name="UserInfoTextBlock" Text="المحامي أحمد محمد"
                                          Foreground="{StaticResource TextPrimaryBrush}" FontSize="14" FontWeight="SemiBold"
                                          HorizontalAlignment="Center"/>
                                <TextBlock Text="مدير النظام"
                                          Foreground="{StaticResource TextSecondaryBrush}" FontSize="12"
                                          HorizontalAlignment="Center" Margin="0,2,0,0"/>
                            </StackPanel>
                        </Border>
                        <Button Name="LogoutButton" Style="{StaticResource ModernSidebarButtonStyle}"
                               Click="LogoutButton_Click">
                            <Button.Background>
                                <SolidColorBrush Color="#EF4444" Opacity="0.8"/>
                            </Button.Background>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <Border Background="Transparent" CornerRadius="8" Width="32" Height="32" Margin="0,0,8,0">
                                    <TextBlock Text="🚪" FontSize="16" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="تسجيل الخروج" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي المتطور -->
        <Grid Grid.Column="1" Background="{StaticResource BackgroundBrush}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- شريط العنوان المتطور -->
            <Border Grid.Row="0" Background="{StaticResource SurfaceBrush}"
                    BorderBrush="{StaticResource BorderLightBrush}" BorderThickness="0,0,0,1">
                <Border.Effect>
                    <DropShadowEffect Color="#000000" Opacity="0.05" BlurRadius="8" ShadowDepth="2" Direction="270"/>
                </Border.Effect>
                <Grid Height="80" Margin="32,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" VerticalAlignment="Center">
                        <TextBlock Name="PageTitleTextBlock" Text="لوحة التحكم"
                                  Style="{StaticResource HeaderTextStyle}"/>
                        <TextBlock Name="PageSubtitleTextBlock" Text="نظرة عامة على أنشطة المكتب"
                                  Style="{StaticResource BodyTextStyle}"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                        <!-- شريط البحث المتطور -->
                        <Border Background="{StaticResource SurfaceBrush}"
                                BorderBrush="{StaticResource BorderBrush}"
                                BorderThickness="2" CornerRadius="12" Margin="0,0,16,0">
                            <StackPanel Orientation="Horizontal" Margin="16,8">
                                <TextBlock Text="🔍" FontSize="16" VerticalAlignment="Center"
                                          Foreground="{StaticResource TextMutedBrush}" Margin="0,0,8,0"/>
                                <TextBox Name="QuickSearchTextBox" Width="200" BorderThickness="0" Background="Transparent"
                                        VerticalContentAlignment="Center" Text="البحث السريع..."
                                        Foreground="{StaticResource TextMutedBrush}"
                                        GotFocus="QuickSearchTextBox_GotFocus" LostFocus="QuickSearchTextBox_LostFocus"
                                        KeyDown="QuickSearchTextBox_KeyDown"/>
                            </StackPanel>
                        </Border>

                        <!-- أزرار الإجراءات السريعة -->
                        <Button Style="{StaticResource ModernButtonStyle}" Margin="0,0,8,0" Padding="12,8">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="➕" FontSize="14" Margin="0,0,6,0"/>
                                <TextBlock Text="جديد" FontSize="13"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource InfoButtonStyle}" Padding="12,8">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🔔" FontSize="14" Margin="0,0,6,0"/>
                                <TextBlock Text="الإشعارات" FontSize="13"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- منطقة المحتوى المتطورة -->
            <Frame Name="MainContentFrame" Grid.Row="1" NavigationUIVisibility="Hidden"
                   Background="Transparent" Margin="16,16,16,0"/>
        </Grid>
    </Grid>
</Window>
