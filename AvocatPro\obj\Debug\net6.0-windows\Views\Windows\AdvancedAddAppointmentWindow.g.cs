﻿#pragma checksum "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "E85924F54024EDE175A4114114661251F52E030C"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using FontAwesome.WPF;
using FontAwesome.WPF.Converters;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AvocatPro.Views.Windows {
    
    
    /// <summary>
    /// AdvancedAddAppointmentWindow
    /// </summary>
    public partial class AdvancedAddAppointmentWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 127 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WindowTitle;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AppointmentNumberText;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TitleTextBox;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AppointmentTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CategoryComboBox;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker AppointmentDatePicker;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox HourComboBox;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox MinuteComboBox;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DurationTextBox;
        
        #line default
        #line hidden
        
        
        #line 230 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PriorityComboBox;
        
        #line default
        #line hidden
        
        
        #line 236 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 261 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ClientNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 267 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ClientPhoneTextBox;
        
        #line default
        #line hidden
        
        
        #line 273 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ClientEmailTextBox;
        
        #line default
        #line hidden
        
        
        #line 279 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AssignedLawyerComboBox;
        
        #line default
        #line hidden
        
        
        #line 285 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LocationTextBox;
        
        #line default
        #line hidden
        
        
        #line 292 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RelatedFileNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 298 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FeesTextBox;
        
        #line default
        #line hidden
        
        
        #line 309 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ReminderEnabledCheckBox;
        
        #line default
        #line hidden
        
        
        #line 316 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ReminderTimeComboBox;
        
        #line default
        #line hidden
        
        
        #line 323 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox DesktopReminderCheckBox;
        
        #line default
        #line hidden
        
        
        #line 325 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EmailReminderCheckBox;
        
        #line default
        #line hidden
        
        
        #line 327 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox SmsReminderCheckBox;
        
        #line default
        #line hidden
        
        
        #line 332 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsRecurringCheckBox;
        
        #line default
        #line hidden
        
        
        #line 336 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel RecurrencePanel;
        
        #line default
        #line hidden
        
        
        #line 346 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox RecurrencePatternComboBox;
        
        #line default
        #line hidden
        
        
        #line 352 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker RecurrenceEndDatePicker;
        
        #line default
        #line hidden
        
        
        #line 360 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 390 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 396 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveAndAddAnotherButton;
        
        #line default
        #line hidden
        
        
        #line 403 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AvocatPro;V1.0.0.0;component/views/windows/advancedaddappointmentwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.WindowTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.AppointmentNumberText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TitleTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.AppointmentTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            this.CategoryComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            this.AppointmentDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 7:
            this.HourComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.MinuteComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.DurationTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.PriorityComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.ClientNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.ClientPhoneTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.ClientEmailTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.AssignedLawyerComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 16:
            this.LocationTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.RelatedFileNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 18:
            this.FeesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.ReminderEnabledCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 20:
            this.ReminderTimeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 21:
            this.DesktopReminderCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 22:
            this.EmailReminderCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 23:
            this.SmsReminderCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 24:
            this.IsRecurringCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 334 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
            this.IsRecurringCheckBox.Checked += new System.Windows.RoutedEventHandler(this.IsRecurringCheckBox_Checked);
            
            #line default
            #line hidden
            
            #line 334 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
            this.IsRecurringCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.IsRecurringCheckBox_Unchecked);
            
            #line default
            #line hidden
            return;
            case 25:
            this.RecurrencePanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 26:
            this.RecurrencePatternComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 27:
            this.RecurrenceEndDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 28:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 29:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 390 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            this.SaveAndAddAnotherButton = ((System.Windows.Controls.Button)(target));
            
            #line 397 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
            this.SaveAndAddAnotherButton.Click += new System.Windows.RoutedEventHandler(this.SaveAndAddAnotherButton_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 404 "..\..\..\..\..\Views\Windows\AdvancedAddAppointmentWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

