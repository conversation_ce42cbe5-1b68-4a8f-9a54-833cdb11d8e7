using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Threading;
using AvocatPro.Views.Pages;

namespace AvocatPro.Views.Windows
{
    /// <summary>
    /// النافذة الرئيسية الحديثة للنظام
    /// </summary>
    public partial class ModernMainWindow : Window
    {
        private DispatcherTimer dateTimeTimer;
        private DispatcherTimer systemMonitorTimer;

        public ModernMainWindow()
        {
            InitializeComponent();
            InitializeWindow();
            StartTimers();
            LoadDashboard();

            // ربط معالج أحداث القائمة الجانبية
            SidebarControl.NavigationRequested += SidebarControl_NavigationRequested;
        }

        /// <summary>
        /// تهيئة النافذة والإعدادات الأولية
        /// </summary>
        private void InitializeWindow()
        {
            try
            {
                // تطبيق الأنماط الحديثة
                Style = (Style)FindResource("ModernWindowStyle");
                
                // تحديث معلومات النظام
                UpdateSystemInfo();
                
                // تحميل إعدادات النافذة المحفوظة
                LoadWindowSettings();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النافذة: {ex.Message}", 
                               "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// بدء تشغيل المؤقتات
        /// </summary>
        private void StartTimers()
        {
            try
            {
                // مؤقت التاريخ والوقت
                dateTimeTimer = new DispatcherTimer();
                dateTimeTimer.Interval = TimeSpan.FromSeconds(1);
                dateTimeTimer.Tick += DateTimeTimer_Tick;
                dateTimeTimer.Start();

                // مؤقت مراقبة النظام
                systemMonitorTimer = new DispatcherTimer();
                systemMonitorTimer.Interval = TimeSpan.FromMinutes(1);
                systemMonitorTimer.Tick += SystemMonitorTimer_Tick;
                systemMonitorTimer.Start();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في بدء المؤقتات: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث التاريخ والوقت
        /// </summary>
        private void DateTimeTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                var now = DateTime.Now;
                DateTimeDisplay.Text = $"{now:dddd، dd MMMM yyyy - HH:mm:ss}";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث التاريخ والوقت: {ex.Message}");
            }
        }

        /// <summary>
        /// مراقبة النظام وتحديث المعلومات
        /// </summary>
        private void SystemMonitorTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                UpdateSystemInfo();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في مراقبة النظام: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث معلومات النظام
        /// </summary>
        private void UpdateSystemInfo()
        {
            try
            {
                // تحديث استخدام الذاكرة
                var memoryUsage = GC.GetTotalMemory(false) / (1024 * 1024);
                MemoryUsageDisplay.Text = $"{memoryUsage} MB";

                // تحديث عدد المستخدمين النشطين
                // يمكن الحصول على هذه المعلومات من قاعدة البيانات
                UserCountDisplay.Text = "المستخدمين النشطين: 3";

                // تحديث معلومات النسخ الاحتياطي
                LastBackupDisplay.Text = "اليوم 14:30";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث معلومات النظام: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل لوحة التحكم الافتراضية
        /// </summary>
        private void LoadDashboard()
        {
            try
            {
                var dashboardPage = new ModernDashboardPage();
                MainContentFrame.Navigate(dashboardPage);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل لوحة التحكم: {ex.Message}", 
                               "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالج طلبات التنقل من القائمة الجانبية
        /// </summary>
        private void SidebarControl_NavigationRequested(object sender, string pageName)
        {
            try
            {
                if (string.IsNullOrEmpty(pageName))
                {
                    MessageBox.Show("اسم الصفحة غير صحيح", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                ShowLoadingOverlay($"جاري تحميل {GetPageDisplayName(pageName)}...");

                Page pageToNavigate = null;

                switch (pageName)
                {
                    case "Dashboard":
                        try { pageToNavigate = new ModernDashboardPage(); }
                        catch (Exception ex) { MessageBox.Show($"خطأ في تحميل لوحة التحكم: {ex.Message}"); return; }
                        break;
                    case "Clients":
                        try { pageToNavigate = new SimpleClientsPage(); }
                        catch (Exception ex) { MessageBox.Show($"خطأ في تحميل إدارة الموكلين: {ex.Message}"); return; }
                        break;
                    case "SimpleClientsTest":
                        try { pageToNavigate = new SimpleClientsPageTest(); }
                        catch (Exception ex) { MessageBox.Show($"خطأ في تحميل صفحة الموكلين الاختبارية: {ex.Message}"); return; }
                        break;
                    case "BasicClients":
                        try { pageToNavigate = new BasicClientsPage(); }
                        catch (Exception ex) { MessageBox.Show($"خطأ في تحميل صفحة الموكلين الأساسية: {ex.Message}"); return; }
                        break;
                    case "ComprehensiveClients":
                        try { pageToNavigate = new ComprehensiveClientsPage(); }
                        catch (Exception ex) { MessageBox.Show($"خطأ في تحميل صفحة إدارة الموكلين الشاملة: {ex.Message}"); return; }
                        break;
                    case "Files":
                        try { pageToNavigate = new SimpleFilesPage(); }
                        catch (Exception ex) { MessageBox.Show($"خطأ في تحميل إدارة الملفات: {ex.Message}"); return; }
                        break;
                    case "ComprehensiveFiles":
                        try { pageToNavigate = new AdvancedFilesPage(); }
                        catch (Exception ex) { MessageBox.Show($"خطأ في تحميل إدارة الملفات المتقدمة: {ex.Message}"); return; }
                        break;
                    case "Sessions":
                        try { pageToNavigate = new ComprehensiveSessionsPage(); }
                        catch (Exception ex) { MessageBox.Show($"خطأ في تحميل إدارة الجلسات الشاملة: {ex.Message}"); return; }
                        break;
                    case "Appointments":
                        try { pageToNavigate = new ComprehensiveAppointmentsPage(); }
                        catch (Exception ex) { MessageBox.Show($"خطأ في تحميل إدارة المواعيد: {ex.Message}"); return; }
                        break;
                    case "Finance":
                        try { pageToNavigate = new ModernFinancePage(); }
                        catch (Exception ex) { MessageBox.Show($"خطأ في تحميل الإدارة المالية: {ex.Message}"); return; }
                        break;
                    case "Invoices":
                        try { pageToNavigate = new ModernInvoicesPage(); }
                        catch (Exception ex) { MessageBox.Show($"خطأ في تحميل إدارة الفواتير: {ex.Message}"); return; }
                        break;
                    case "Reports":
                        try { pageToNavigate = new ModernReportsPage(); }
                        catch (Exception ex) { MessageBox.Show($"خطأ في تحميل التقارير: {ex.Message}"); return; }
                        break;
                    case "Users":
                        // pageToNavigate = new UsersPage();
                        break;
                    case "Backup":
                        // pageToNavigate = new BackupPage();
                        break;
                    case "Settings":
                        // pageToNavigate = new SettingsPage();
                        break;
                    case "Help":
                        // pageToNavigate = new HelpPage();
                        break;
                    case "About":
                        // pageToNavigate = new AboutPage();
                        break;
                    case "Logout":
                        HandleLogout();
                        return;
                    default:
                        ShowNotification("الصفحة غير متوفرة حالياً", "تحت التطوير");
                        HideLoadingOverlay();
                        return;
                }

                if (pageToNavigate != null)
                {
                    if (MainContentFrame != null)
                    {
                        MainContentFrame.Navigate(pageToNavigate);
                    }
                    else
                    {
                        MessageBox.Show("خطأ في تحميل إطار المحتوى", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else
                {
                    ShowNotification("الصفحة غير متوفرة حالياً", "تحت التطوير");
                }

                HideLoadingOverlay();
            }
            catch (Exception ex)
            {
                HideLoadingOverlay();
                MessageBox.Show($"خطأ في التنقل إلى الصفحة: {ex.Message}", 
                               "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// الحصول على الاسم المعروض للصفحة
        /// </summary>
        private string GetPageDisplayName(string pageName)
        {
            return pageName switch
            {
                "Dashboard" => "لوحة التحكم",
                "Clients" => "إدارة الموكلين",
                "Files" => "إدارة الملفات",
                "Sessions" => "إدارة الجلسات",
                "Appointments" => "إدارة المواعيد",
                "Finance" => "الإدارة المالية",
                "Invoices" => "إدارة الفواتير",
                "Reports" => "التقارير",
                "Users" => "إدارة المستخدمين",
                "Backup" => "النسخ الاحتياطي",
                "Settings" => "الإعدادات",
                "Help" => "المساعدة",
                "About" => "حول البرنامج",
                _ => "الصفحة"
            };
        }

        /// <summary>
        /// معالجة تسجيل الخروج
        /// </summary>
        private void HandleLogout()
        {
            try
            {
                // حفظ إعدادات النافذة
                SaveWindowSettings();
                
                // إغلاق النافذة الحالية
                this.Close();
                
                // فتح نافذة تسجيل الدخول
                // var loginWindow = new LoginWindow();
                // loginWindow.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسجيل الخروج: {ex.Message}", 
                               "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إظهار طبقة التحميل
        /// </summary>
        private void ShowLoadingOverlay(string message = "جاري التحميل...")
        {
            LoadingText.Text = message;
            LoadingOverlay.Visibility = Visibility.Visible;
        }

        /// <summary>
        /// إخفاء طبقة التحميل
        /// </summary>
        private void HideLoadingOverlay()
        {
            LoadingOverlay.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// إظهار إشعار
        /// </summary>
        private void ShowNotification(string title, string message)
        {
            try
            {
                // يمكن إضافة نظام إشعارات متقدم هنا
                MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إظهار الإشعار: {ex.Message}");
            }
        }

        /// <summary>
        /// حفظ إعدادات النافذة
        /// </summary>
        private void SaveWindowSettings()
        {
            try
            {
                // حفظ موضع وحجم النافذة في الإعدادات
                Properties.Settings.Default.WindowLeft = this.Left;
                Properties.Settings.Default.WindowTop = this.Top;
                Properties.Settings.Default.WindowWidth = this.Width;
                Properties.Settings.Default.WindowHeight = this.Height;
                Properties.Settings.Default.WindowState = this.WindowState;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ إعدادات النافذة: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل إعدادات النافذة المحفوظة
        /// </summary>
        private void LoadWindowSettings()
        {
            try
            {
                if (Properties.Settings.Default.WindowWidth > 0)
                {
                    this.Left = Properties.Settings.Default.WindowLeft;
                    this.Top = Properties.Settings.Default.WindowTop;
                    this.Width = Properties.Settings.Default.WindowWidth;
                    this.Height = Properties.Settings.Default.WindowHeight;
                    this.WindowState = Properties.Settings.Default.WindowState;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل إعدادات النافذة: {ex.Message}");
            }
        }

        #region معالجات أحداث شريط العنوان

        /// <summary>
        /// سحب النافذة من شريط العنوان
        /// </summary>
        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ClickCount == 2)
            {
                MaximizeWindow_Click(sender, e);
            }
            else
            {
                this.DragMove();
            }
        }

        /// <summary>
        /// تصغير النافذة
        /// </summary>
        private void MinimizeWindow_Click(object sender, RoutedEventArgs e)
        {
            this.WindowState = WindowState.Minimized;
        }

        /// <summary>
        /// تكبير/استعادة النافذة
        /// </summary>
        private void MaximizeWindow_Click(object sender, RoutedEventArgs e)
        {
            this.WindowState = this.WindowState == WindowState.Maximized 
                ? WindowState.Normal 
                : WindowState.Maximized;
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void CloseWindow_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// المساعدة السريعة
        /// </summary>
        private void QuickHelp_Click(object sender, RoutedEventArgs e)
        {
            ShowNotification("المساعدة السريعة", "يمكنك الوصول للمساعدة من القائمة الجانبية");
        }

        /// <summary>
        /// إغلاق لوحة الإشعارات
        /// </summary>
        private void CloseNotifications_Click(object sender, RoutedEventArgs e)
        {
            NotificationPanel.Visibility = Visibility.Collapsed;
        }

        #endregion

        /// <summary>
        /// تنظيف الموارد عند إغلاق النافذة
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            try
            {
                dateTimeTimer?.Stop();
                systemMonitorTimer?.Stop();
                SaveWindowSettings();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تنظيف الموارد: {ex.Message}");
            }
            
            base.OnClosed(e);
        }
    }
}
